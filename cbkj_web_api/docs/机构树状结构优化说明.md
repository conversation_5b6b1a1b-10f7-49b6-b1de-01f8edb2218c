# 机构树状结构优化说明

## 优化概述

将原来返回平铺结构的机构列表优化为树状结构，支持多层级的上下级关系展示。每个节点都包含 `insCode`、`insName`、`insParentCode` 和 `children` 字段。

## 优化前后对比

### 优化前（平铺结构）
```json
[
  {"insCode": "001", "insName": "总公司", "insParentCode": "0"},
  {"insCode": "001001", "insName": "分公司A", "insParentCode": "001"},
  {"insCode": "001002", "insName": "分公司B", "insParentCode": "001"},
  {"insCode": "001001001", "insName": "部门A1", "insParentCode": "001001"},
  {"insCode": "001001002", "insName": "部门A2", "insParentCode": "001001"}
]
```

### 优化后（树状结构）
```json
[
  {
    "insCode": "001",
    "insName": "总公司", 
    "insParentCode": "0",
    "children": [
      {
        "insCode": "001001",
        "insName": "分公司A",
        "insParentCode": "001",
        "children": [
          {
            "insCode": "001001001",
            "insName": "部门A1",
            "insParentCode": "001001",
            "children": []
          }
        ]
      }
    ]
  }
]
```

## 实现方案

### 1. 核心工具类 `TreeUtils`
- `buildInsTree()` - 构建机构树状结构
- `buildTree()` - 通用树构建方法
- `printTree()` - 打印树结构（调试用）
- `getTreeDepth()` - 获取树的深度
- `countNodes()` - 统计节点总数

### 2. Mapper接口优化
```java
// 原始查询方法（保持不变）
List<HashMap<String, String>> getInsListFlat(String appId);

// 新增树状结构方法
default List<HashMap<String, Object>> getInsList(String appId) {
    List<HashMap<String, String>> flatList = getInsListFlat(appId);
    return TreeUtils.buildInsTree(flatList);
}
```

## 核心算法

### 树构建算法（两遍遍历）
1. **第一遍**：创建所有节点对象并建立映射
2. **第二遍**：建立父子关系

### 根节点判断规则
- `insParentCode` 为 `null`、空字符串、`"0"` 或 `"null"`

### 异常数据处理
- **孤儿节点**：父节点不存在时作为根节点处理
- **空数据**：返回空列表

## 使用方法

### 1. 基本使用
```java
@Autowired
private AdminInfoMapper adminInfoMapper;

public List<HashMap<String, Object>> getInsTreeList(String appId) {
    return adminInfoMapper.getInsList(appId);
}
```

### 2. 直接使用工具类
```java
List<HashMap<String, String>> flatData = getFlatData();
List<HashMap<String, Object>> treeData = TreeUtils.buildInsTree(flatData);
```

### 3. 通用树构建
```java
List<HashMap<String, Object>> tree = TreeUtils.buildTree(
    flatList, "id", "parentId", "children");
```

## 性能特点

- **时间复杂度**：O(n)，两遍遍历
- **空间复杂度**：O(n)，HashMap存储映射
- **性能优势**：避免递归查找，一次性构建

## 测试覆盖

- 正常场景：多层级机构树构建
- 边界条件：空数据、null数据
- 异常场景：孤儿节点处理
- 工具方法：深度计算、节点统计

## 向后兼容性

- 保持原有 `getInsListFlat()` 方法不变
- 新增 `getInsList()` 返回树状结构
- 支持渐进式迁移

## 最佳实践

### 缓存策略
```java
@Cacheable(value = "insTree", key = "#appId")
public List<HashMap<String, Object>> getInsTreeList(String appId) {
    return adminInfoMapper.getInsList(appId);
}
```

### 异步处理
```java
@Async
public CompletableFuture<List<HashMap<String, Object>>> buildTreeAsync(
    List<HashMap<String, String>> flatData) {
    return CompletableFuture.completedFuture(TreeUtils.buildInsTree(flatData));
}
```

## 总结

通过这次优化，机构数据展示从平铺结构升级为树状结构，具有以下优势：

- **层次清晰**：直观展示上下级关系
- **性能优秀**：O(n)时间复杂度
- **扩展性强**：通用树构建工具
- **向后兼容**：保持原有接口
- **测试完善**：全面单元测试覆盖

这个解决方案不仅解决了当前需求，还为未来类似的树状结构需求提供了可复用的工具。
