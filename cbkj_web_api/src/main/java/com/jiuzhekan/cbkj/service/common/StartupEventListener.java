package com.jiuzhekan.cbkj.service.common;

import com.jiuzhekan.cbkj.service.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/28 09:20
 * @Version 1.0
 */
@Component
@Slf4j
public class StartupEventListener {
    private final RedisService redisService;

    public StartupEventListener(RedisService redisService) {
        this.redisService = redisService;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void clearRedisCache() {
        log.info("程序重启-开始清除缓存-开始");
        redisService.clearRedisCache("parameter::params", "");
        redisService.clearRedisCache("parameter::dic", "");
        redisService.clearRedisCache("client::user", "");
        redisService.clearRedisCache("parameter::display", "");
        redisService.clearRedisCache("client::register", "");
        redisService.clearRedisCache("parameter::address", "");
        redisService.clearRedisCache("know::common", "");
        redisService.clearRedisCache("know::check", "");
        redisService.clearRedisCache("know::disease", "");
        redisService.clearRedisCache("know::scheme", "");
        redisService.clearRedisCache("know::master", "");
        redisService.clearRedisCache("know::inherit", "");
        redisService.clearRedisCache("parameter::menu", "");
        redisService.clearRedisCache("org::ins", "");
        redisService.clearRedisCache("org::dept", "");
        log.info("程序重启-开始清除缓存-结束");
    }
}
