package com.jiuzhekan.recordquality.beans;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/26 14:01
 * @Version 1.0
 */
@Data
public class RecordQualityListReq {
    private Integer page;
    private Integer limit;

    @ApiModelProperty(value = "规则类型:名称")
    private String recordQualityRuleCalssValue;

    @ApiModelProperty(value = "规则名")
    private String recordQualityRuleName;

    @ApiModelProperty(value = "0启用1不启用")
    private Integer recordQualityRuleStatus;
}
