package com.jiuzhekan.statistics.service.register;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.statistics.beans.register.StatisticsRegister;
import com.jiuzhekan.statistics.beans.register.TStatisticsRegister;
import com.jiuzhekan.statistics.mapper.register.TStatisticsRegisterMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class TStatisticsRegisterService {


    private final TStatisticsRegisterMapper tStatisticsRegisterMapper;

    @Autowired
    TStatisticsRegisterService(TStatisticsRegisterMapper tStatisticsRegisterMapper
    ) {
        this.tStatisticsRegisterMapper = tStatisticsRegisterMapper;
    }

    /**
     * 加载分页数据
     *
     * @param tStatisticsRegister 就诊人次统计表
     * @param page                分页
     * @return Object
     * <AUTHOR>
     * @date 2022-05-23
     */
    public Object getPageDatas(TStatisticsRegister tStatisticsRegister, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TStatisticsRegister> list = tStatisticsRegisterMapper.getPageListByObj(tStatisticsRegister);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 就诊人次统计表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TStatisticsRegister tStatisticsRegister = tStatisticsRegisterMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tStatisticsRegister);
    }


    /**
     * 插入新数据
     *
     * @param tStatisticsRegister 就诊人次统计表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TStatisticsRegister tStatisticsRegister) {

        long rows = tStatisticsRegisterMapper.insert(tStatisticsRegister);

        return ResEntity.success(tStatisticsRegister);
    }


    /**
     * 修改
     *
     * @param tStatisticsRegister 就诊人次统计表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TStatisticsRegister tStatisticsRegister) {

        long rows = tStatisticsRegisterMapper.updateByPrimaryKey(tStatisticsRegister);

        return ResEntity.success(tStatisticsRegister);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tStatisticsRegisterMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    /**
     * 就诊人次-电子病历
     * @param curDate 查询截止日期
     * @param timeDiff 从截止日期往前推 0代表截止日期当天 1代表截止日期当天以及该天的前一天。
     * @param type  1,2
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertDate(String curDate,String timeDiff, String type) {
        Map<String, Object> param = new HashMap<>(16);
        //传1是昨天。2前天。0今天。-1明天
        if (StringUtils.isBlank(timeDiff)) {
            timeDiff = "1";
        }
        if (StringUtils.isBlank(type)) {
            type = "1";
        }
        param.put("timeDiff", timeDiff);
        if (StringUtils.isBlank(curDate)){
            param.put("curDate", new SimpleDateFormat("yyyy-MM-dd").format(new Date()).toString());
        }else {
            param.put("curDate",curDate);
        }

        List<StatisticsRegister> statisticsRegisters;
        //电子病历
        if (Constant.BASIC_STRING_ONE.equals(type)) {
            List<StatisticsRegister> statisticsRegisters1 = tStatisticsRegisterMapper.selectStatisticsRegisterEle(param);
            statisticsRegisters = statisticsRegisters1;
        } else {
            //就诊人次
            List<StatisticsRegister> statisticsRegisters2 = tStatisticsRegisterMapper.selectStatisticsRegisterResult(param);
            statisticsRegisters = statisticsRegisters2;
        }
        List<StatisticsRegister> insertRe = new ArrayList<StatisticsRegister>();
        List<StatisticsRegister> updateRe = new ArrayList<StatisticsRegister>();
        for (StatisticsRegister prescription : statisticsRegisters) {
            if (Constant.BASIC_STRING_ONE.equals(type)) {
                prescription.setElectronicRecordNum(prescription.getNumber());
            } else {
                prescription.setRegisterTimes(prescription.getNumber());
            }
            if (null == prescription.getId()) {
                insertRe.add(prescription);
            } else {
                updateRe.add(prescription);
            }
        }
        //新增
        if (insertRe.size() > 0) {
            tStatisticsRegisterMapper.insertStatisticsRegister(insertRe);
        }
        //更新操作
        //List<StatisticsRegister> objects = new ArrayList<>();
        if (updateRe.size() > 0) {
            //for (StatisticsRegister prescription : updateRe) {
                //TStatisticsRegister tStatisticsPrescription = new TStatisticsRegister();
                //BeanUtils.copyProperties(prescription, tStatisticsPrescription);
                //tStatisticsPrescription.setId(prescription.getId());
                //tStatisticsPrescription.setElectronicRecordNum(prescription.getElectronicRecordNum());
                //tStatisticsPrescription.setRegisterTimes(prescription.getRegisterTimes());
                //objects.add(tStatisticsPrescription);
               // tStatisticsRegisterMapper.updateByPrimaryKey(tStatisticsPrescription);
            //}
            if (updateRe.size()>0){
                tStatisticsRegisterMapper.replaceinsertStatisticsRegister(updateRe);
            }
        }
        return ResEntity.success(null);
    }

    public TStatisticsRegister getLastData() {
        return tStatisticsRegisterMapper.selectLastDate();
    }

}
