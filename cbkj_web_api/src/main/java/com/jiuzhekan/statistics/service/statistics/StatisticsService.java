package com.jiuzhekan.statistics.service.statistics;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.PlatformRestTemplate;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.cbkj.common.utils.RedisLockUtil;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.statistics.beans.analysis.TStatisticsAnalysis;
import com.jiuzhekan.statistics.beans.personal.TStatisticsPersonalPrescription;
import com.jiuzhekan.statistics.beans.prescription.TStatisticsPrescription;
import com.jiuzhekan.statistics.beans.register.*;
import com.jiuzhekan.statistics.mapper.analysis.TStatisticsAnalysisMapper;
import com.jiuzhekan.statistics.service.analysis.TStatisticsAnalysisService;
import com.jiuzhekan.statistics.service.personal.TStatisticsPersonalPrescriptionService;
import com.jiuzhekan.statistics.service.prescription.TStatisticsPrescriptionService;
import com.jiuzhekan.statistics.service.register.TStatisticsRegisterService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @Date $ $
 **/
@Service
public class StatisticsService {
    private final TStatisticsAnalysisMapper tStatisticsAnalysisMapper;
    private final PlatformRestTemplate platformApi;
    /**
     * 电子病历和就诊人次
     */
    private final TStatisticsRegisterService tStatisticsRegisterService;
    /**
     * 中药饮片处方数
     */
    private final TStatisticsPrescriptionService tStatisticsPrescriptionService;
    /**
     * 体制辨识报告
     */
    private final TStatisticsAnalysisService tStatisticsAnalysisService;
    /**
     * 协定方
     */
    private final TStatisticsPersonalPrescriptionService tStatisticsPersonalPrescriptionService;
    /**
     * redis
     */
    private final RedisLockUtil redisLockUtil;
    private final TSysParamService tSysParamService;

    @Autowired
    StatisticsService(
            PlatformRestTemplate platformApi,
            TStatisticsAnalysisMapper tStatisticsAnalysisMapper,
            TStatisticsRegisterService tStatisticsRegisterService,
            TStatisticsPrescriptionService tStatisticsPrescriptionService,
            TStatisticsAnalysisService tStatisticsAnalysisService,
            TStatisticsPersonalPrescriptionService tStatisticsPersonalPrescriptionService,
            RedisLockUtil redisLockUtil, TSysParamService tSysParamService) {

        this.platformApi = platformApi;
        this.tStatisticsAnalysisMapper = tStatisticsAnalysisMapper;
        this.tStatisticsRegisterService = tStatisticsRegisterService;
        this.tStatisticsPrescriptionService = tStatisticsPrescriptionService;
        this.tStatisticsAnalysisService = tStatisticsAnalysisService;
        this.tStatisticsPersonalPrescriptionService = tStatisticsPersonalPrescriptionService;
        this.redisLockUtil = redisLockUtil;
        this.tSysParamService = tSysParamService;
    }

    @Transactional(rollbackFor = Exception.class)
    public StatisticsChangeResult getData(QueryParam queryParam) throws ParseException {
        String parValues = tSysParamService.getSysParam(Constant.OPEN_CDSS_MODE).getParValues();

        StatisticsChangeResult statisticsChangeResult = new StatisticsChangeResult();
        if (null == queryParam.getQueryStartDate()) {
            // 格式化日期
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
            String beginDate = null;
            String endDate = null;
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.YEAR, -1);
            beginDate = DateUtil.getDateFormats(DateUtil.YYYY_MM, cal.getTime());
            endDate = DateUtil.getDateFormats(DateUtil.YYYY_MM, new Date());

            beginDate += "-01 00:00:00";
            endDate += "-31 23:59:59";
            queryParam.setQueryStartDate(sdf2.parse(beginDate));
            queryParam.setQueryEndDate(sdf2.parse(endDate));
        }
        long l = queryParam.getQueryEndDate().getTime() - System.currentTimeMillis();
        long oneDayMi = 86400000;
        if (l >= -oneDayMi) {
            //同步今天,设置返回值，等待完成同步。
            //从数据获取最新插入的数据日期。
            String s = statisticsTongLu("0");
        }
        Map<String, Object> params = new HashMap<>(16);
        params.put("appId", queryParam.getAppId());
        params.put("insCode", queryParam.getInsCode());
        ResEntity resEntity = platformApi.post("ins/tree", params);
        if (!resEntity.getStatus()) {
            return statisticsChangeResult;
        }
        List<ChangeMap> changeMaps = JSON.parseArray(JSON.toJSONString(resEntity.getData()), ChangeMap.class);
        QueryParam queryParam2 = new QueryParam();
        queryParam2.setQueryStartDate(queryParam.getQueryStartDate());
        queryParam2.setQueryEndDate(queryParam.getQueryEndDate());
        queryParam2.setQueryParamList(new ArrayList<QueryParam>());
//        queryParam2.setDoctorId(queryParam.getDoctorId());
        List<QueryParam> queryParamList = queryParam2.getQueryParamList();
        AtomicInteger atomicInteger = new AtomicInteger(1);


        //把所有查询到的的appId和InsCode放入list作为查询参数。
        changeMaps.forEach(
                a -> {
                    a.setKey(atomicInteger.getAndIncrement());
                    a.getInsList().forEach(
                            b -> {
                                b.setKey(atomicInteger.getAndIncrement());
                                QueryParam queryParam3 = new QueryParam();
                                queryParam3.setAppId(b.getAppId());
                                queryParam3.setInsCode(b.getInsCode());
                                queryParamList.add(queryParam3);
                                b.getInsList().forEach(
                                        changeMap -> {
                                            changeMap.setKey(atomicInteger.getAndIncrement());
                                            QueryParam queryParam4 = new QueryParam();
                                            queryParam4.setAppId(changeMap.getAppId());
                                            queryParam4.setInsCode(changeMap.getInsCode());
                                            queryParamList.add(queryParam4);

                                            //第四层循环，不需要了，上一层就是到医工体-》医院-》机构了 这一层是机构的下级机构
                                            //这一层改成医生。
                                            changeMap.getInsList().forEach(
                                                    c -> {
                                                        //获取这个机构下医生
//                                                        c.setDoctorList(tStatisticsRegisterService.getDoctorList(c.getAppId(), c.getInsCode()));

                                                        c.setKey(atomicInteger.getAndIncrement());
                                                        QueryParam queryParam5 = new QueryParam();
                                                        queryParam5.setAppId(c.getAppId());
                                                        queryParam5.setInsCode(c.getInsCode());
                                                        queryParamList.add(queryParam5);
                                                    });
                                        }
                                );
                            }
                    );
                }
        );
        //体质辨识
        List<QueryResult> queryResults = tStatisticsAnalysisMapper.sumResult(queryParam2);
        List<QueryResult> queryResultsDoctor = tStatisticsAnalysisMapper.sumResultDoctor(queryParam2);
        //协定方
        List<QueryResult> sumPersonalPrescription = tStatisticsAnalysisMapper.sumPersonalPrescription(queryParam2);
        List<QueryResult> sumPersonalPrescriptionDoctor = tStatisticsAnalysisMapper.sumPersonalPrescriptionDoctor(queryParam2);
        //适宜技术方,内服+外用
        List<QueryResult> sumStatisticsPrescription = tStatisticsAnalysisMapper.sumStatisticsPrescription(queryParam2);
        List<QueryResult> sumStatisticsPrescriptionDoctor = tStatisticsAnalysisMapper.sumStatisticsPrescriptionDoctor(queryParam2);
        //就诊人次和电子病历
        List<QueryResult> sumStatisticsRegister = tStatisticsAnalysisMapper.sumStatisticsRegister(queryParam2);
        List<QueryResult> sumStatisticsRegisterDoc = tStatisticsAnalysisMapper.sumStatisticsRegisterDoc(queryParam2);

        //查询内服+外用（cdss）
        List<QueryResult> sumStatisticsPrescriptionCDSS = tStatisticsAnalysisMapper.sumStatisticsPrescriptionCDSS(queryParam2);
        List<QueryResult> sumStatisticsPrescriptionCDSSDoc = tStatisticsAnalysisMapper.sumStatisticsPrescriptionCDSSDoc(queryParam2);

        //查询内服+外用（cdss）
        List<QueryResult> sumStatisticsPrescriptionMzZy = tStatisticsAnalysisMapper.sumStatisticsPrescriptionMzZy(queryParam2);
        List<QueryResult> sumStatisticsPrescriptionMzZyDoc = tStatisticsAnalysisMapper.sumStatisticsPrescriptionMzZyDoc(queryParam2);


        //把结果插入到changeMap中
        if (StringUtils.isNotBlank(queryParam.getSearchDoctor()) && Constant.BASIC_STRING_ZERO.equals(queryParam.getSearchDoctor())) {
            changeMaps.forEach(
                    a -> {

                        a.getInsList().forEach(
                                b -> {
                                    setChangeMaps(queryResultsDoctor, sumPersonalPrescriptionDoctor, sumStatisticsPrescriptionDoctor,
                                            sumStatisticsRegisterDoc, sumStatisticsPrescriptionCDSSDoc, sumStatisticsPrescriptionMzZyDoc, b,a,null);

                                    b.getInsList().forEach(
                                            changeMap -> {

                                                setChangeMaps(queryResultsDoctor, sumPersonalPrescriptionDoctor, sumStatisticsPrescriptionDoctor,
                                                        sumStatisticsRegisterDoc, sumStatisticsPrescriptionCDSSDoc, sumStatisticsPrescriptionMzZyDoc, changeMap,b,a);
                                                //第四层循环，不需要了，上一层就是到医工体-》医院-》机构了 这一层是机构的下级机构
                                                //这一层改成医生。
//                                            changeMap.getInsList().forEach(
//                                                    c -> {
//                                                        setChangeMaps(queryResultsDoctor,sumPersonalPrescriptionDoctor,sumStatisticsPrescriptionDoctor,
//                                                                sumStatisticsRegisterDoc, sumStatisticsPrescriptionCDSSDoc,c );
//                                                    });
                                            }
                                    );
                                }
                        );
                    }
            );

        }

        //把查询到的结果存入changeMaps中
        if (StringUtils.isNotBlank(queryParam.getSearchDoctor()) && Constant.BASIC_STRING_ONE.equals(queryParam.getSearchDoctor())) {
            changeMaps.forEach(
                    a -> {
                        a.getInsList().forEach(
                                b -> {
                                    queryResults.forEach(a1 -> {
                                        if (a1.getAppId().equals(b.getAppId()) && a1.getInsCode().equals(b.getInsCode())) {
                                            b.setNumber(a1.getSumResult());
                                            a.setNumber(a.getNumber() + a1.getSumResult());
                                        }
                                    });
                                    sumPersonalPrescription.forEach(a1 -> {
                                        if (a1.getAppId().equals(b.getAppId()) && a1.getInsCode().equals(b.getInsCode())) {
                                            b.setSumPersonalPrescription(a1.getSumResult());
                                            a.setSumPersonalPrescription(a.getSumPersonalPrescription() + a1.getSumResult());
                                        }
                                    });
                                    sumStatisticsPrescription.forEach(a1 -> {
                                        if (a1.getAppId().equals(b.getAppId()) && a1.getInsCode().equals(b.getInsCode())) {
                                            b.setSumStatisticsPrescription(a1.getSumResult());
                                            b.setSumInnerAndExtPrescription(a1.getSumResult2());
                                            a.setSumStatisticsPrescription(a.getSumStatisticsPrescription() + a1.getSumResult());
                                            a.setSumInnerAndExtPrescription(a.getSumInnerAndExtPrescription() + a1.getSumResult2());
                                        }
                                    });
                                    if (Constant.BASIC_STRING_ONE.equals(parValues)) {
                                        //内服+外用（CDSS）
                                        sumStatisticsPrescriptionCDSS.forEach(a1 -> {
                                            if (a1.getAppId().equals(b.getAppId()) && a1.getInsCode().equals(b.getInsCode())) {
                                                b.setSumInnerAndExtPrescriptionCDSS(a1.getSumResult2());
                                                a.setSumInnerAndExtPrescriptionCDSS(a.getSumInnerAndExtPrescriptionCDSS() + a1.getSumResult2());
                                            }
                                        });
                                    }


                                    sumStatisticsRegister.forEach(a1 -> {
                                        if (a1.getAppId().equals(b.getAppId()) && a1.getInsCode().equals(b.getInsCode())) {
                                            b.setRegisterTimes(a1.getSumResult());
                                            b.setElectronicRecordNum(a1.getSumResult2());
                                            a.setElectronicRecordNum(a.getElectronicRecordNum() + a1.getSumResult2());
                                            a.setRegisterTimes(a.getRegisterTimes() + a1.getSumResult());
                                        }
                                    });
                                    sumStatisticsPrescriptionMzZy.forEach(a1 -> {
                                        if (a1.getAppId().equals(b.getAppId()) && a1.getInsCode().equals(b.getInsCode())) {
                                            b.setSumOutPatientPrescription(a1.getSumResult());
                                            b.setSumInPatientPrescription(a1.getSumResult2());
                                            a.setSumOutPatientPrescription(a.getSumOutPatientPrescription() + a1.getSumResult());
                                            a.setSumInPatientPrescription(a.getSumInPatientPrescription() + a1.getSumResult2());
                                        }
                                    });
                                    b.getInsList().forEach(
                                            changeMap -> {
                                                //体质辨识
                                                queryResults.forEach(
                                                        a1 -> {
                                                            if (a1.getAppId().equals(changeMap.getAppId()) && a1.getInsCode().equals(changeMap.getInsCode())) {
                                                                changeMap.setNumber(a1.getSumResult());
                                                                a.setNumber(a.getNumber() + a1.getSumResult());
                                                                b.setNumber(b.getNumber() + a1.getSumResult());
                                                            }
                                                        });
                                                sumPersonalPrescription.forEach(a1 -> {
                                                    if (a1.getAppId().equals(changeMap.getAppId()) && a1.getInsCode().equals(changeMap.getInsCode())) {
                                                        changeMap.setSumPersonalPrescription(a1.getSumResult());
                                                        a.setSumPersonalPrescription(a.getSumPersonalPrescription() + a1.getSumResult());
                                                        b.setSumPersonalPrescription(b.getSumPersonalPrescription() + a1.getSumResult());
                                                    }
                                                });
                                                sumStatisticsPrescription.forEach(a1 -> {
                                                    if (a1.getAppId().equals(changeMap.getAppId()) && a1.getInsCode().equals(changeMap.getInsCode())) {
                                                        changeMap.setSumStatisticsPrescription(a1.getSumResult());
                                                        changeMap.setSumInnerAndExtPrescription(a1.getSumResult2());
                                                        a.setSumStatisticsPrescription(a.getSumStatisticsPrescription() + a1.getSumResult());
                                                        a.setSumInnerAndExtPrescription(a.getSumInnerAndExtPrescription() + a1.getSumResult2());
                                                        b.setSumStatisticsPrescription(b.getSumStatisticsPrescription() + a1.getSumResult());
                                                        b.setSumInnerAndExtPrescription(b.getSumInnerAndExtPrescription() + a1.getSumResult2());
                                                    }
                                                });
                                                if (Constant.BASIC_STRING_ONE.equals(parValues)) {
                                                    sumStatisticsPrescriptionCDSS.forEach(a1 -> {
                                                        if (a1.getAppId().equals(changeMap.getAppId()) && a1.getInsCode().equals(changeMap.getInsCode())) {
                                                            changeMap.setSumInnerAndExtPrescriptionCDSS(a1.getSumResult2());
                                                            a.setSumInnerAndExtPrescriptionCDSS(a.getSumInnerAndExtPrescriptionCDSS() + a1.getSumResult2());
                                                            b.setSumInnerAndExtPrescriptionCDSS(b.getSumInnerAndExtPrescriptionCDSS() + a1.getSumResult2());
                                                        }
                                                    });
                                                }

                                                sumStatisticsRegister.forEach(a1 -> {
                                                    if (a1.getAppId().equals(changeMap.getAppId()) && a1.getInsCode().equals(changeMap.getInsCode())) {
                                                        changeMap.setRegisterTimes(a1.getSumResult());
                                                        changeMap.setElectronicRecordNum(a1.getSumResult2());
                                                        a.setElectronicRecordNum(a.getElectronicRecordNum() + a1.getSumResult2());
                                                        a.setRegisterTimes(a.getRegisterTimes() + a1.getSumResult());

                                                        b.setElectronicRecordNum(b.getElectronicRecordNum() + a1.getSumResult2());
                                                        b.setRegisterTimes(b.getRegisterTimes() + a1.getSumResult());
                                                    }
                                                });
                                                sumStatisticsPrescriptionMzZy.forEach(a1 -> {
                                                    if (a1.getAppId().equals(changeMap.getAppId()) && a1.getInsCode().equals(changeMap.getInsCode())) {
                                                        changeMap.setSumOutPatientPrescription(a1.getSumResult());
                                                        changeMap.setSumInPatientPrescription(a1.getSumResult2());
                                                        a.setSumOutPatientPrescription(a.getSumOutPatientPrescription() + a1.getSumResult());
                                                        a.setSumInPatientPrescription(a.getSumInPatientPrescription() + a1.getSumResult2());

                                                        b.setSumOutPatientPrescription(b.getSumOutPatientPrescription() + a1.getSumResult());
                                                        b.setSumInPatientPrescription(b.getSumInPatientPrescription() + a1.getSumResult2());
                                                    }
                                                });
                                                if (null != changeMap.getInsList()) {
                                                    changeMap.getInsList().forEach(
                                                            c -> {

                                                                queryResults.forEach(
                                                                        a1 -> {
                                                                            if (a1.getAppId().equals(c.getAppId()) && a1.getInsCode().equals(c.getInsCode())) {
                                                                                c.setNumber(a1.getSumResult());
                                                                                a.setNumber(a.getNumber() + a1.getSumResult());
                                                                                b.setNumber(b.getNumber() + a1.getSumResult());
                                                                            }
                                                                        });
                                                                sumPersonalPrescription.forEach(a1 -> {
                                                                    if (a1.getAppId().equals(c.getAppId()) && a1.getInsCode().equals(c.getInsCode())) {
                                                                        c.setSumPersonalPrescription(a1.getSumResult());
                                                                        a.setSumPersonalPrescription(a.getSumPersonalPrescription() + a1.getSumResult());
                                                                        b.setSumPersonalPrescription(b.getSumPersonalPrescription() + a1.getSumResult());
                                                                    }
                                                                });
                                                                sumStatisticsPrescription.forEach(a1 -> {
                                                                    if (a1.getAppId().equals(c.getAppId()) && a1.getInsCode().equals(c.getInsCode())) {
                                                                        c.setSumStatisticsPrescription(a1.getSumResult());
                                                                        c.setSumInnerAndExtPrescription(a1.getSumResult2());
                                                                        a.setSumStatisticsPrescription(a.getSumStatisticsPrescription() + a1.getSumResult());
                                                                        a.setSumInnerAndExtPrescription(a.getSumInnerAndExtPrescription() + a1.getSumResult2());

                                                                        b.setSumStatisticsPrescription(b.getSumStatisticsPrescription() + a1.getSumResult());
                                                                        b.setSumInnerAndExtPrescription(b.getSumInnerAndExtPrescription() + a1.getSumResult2());
                                                                    }
                                                                });
                                                                if (Constant.BASIC_STRING_ONE.equals(parValues)) {
                                                                    sumStatisticsPrescriptionCDSS.forEach(a1 -> {
                                                                        if (a1.getAppId().equals(c.getAppId()) && a1.getInsCode().equals(c.getInsCode())) {
                                                                            c.setSumInnerAndExtPrescriptionCDSS(a1.getSumResult2());
                                                                            a.setSumInnerAndExtPrescriptionCDSS(a.getSumInnerAndExtPrescriptionCDSS() + a1.getSumResult2());
                                                                            b.setSumInnerAndExtPrescriptionCDSS(b.getSumInnerAndExtPrescriptionCDSS() + a1.getSumResult2());
                                                                        }
                                                                    });
                                                                }

                                                                sumStatisticsRegister.forEach(a1 -> {
                                                                    if (a1.getAppId().equals(c.getAppId()) && a1.getInsCode().equals(c.getInsCode())) {
                                                                        c.setRegisterTimes(a1.getSumResult());
                                                                        c.setElectronicRecordNum(a1.getSumResult2());
                                                                        a.setElectronicRecordNum(a.getElectronicRecordNum() + a1.getSumResult2());
                                                                        a.setRegisterTimes(a1.getSumResult() + a.getRegisterTimes());

                                                                        b.setElectronicRecordNum(b.getElectronicRecordNum() + a1.getSumResult2());
                                                                        b.setRegisterTimes(a1.getSumResult() + b.getRegisterTimes());
                                                                    }
                                                                });
                                                                sumStatisticsPrescriptionMzZy.forEach(a1 -> {
                                                                    if (a1.getAppId().equals(c.getAppId()) && a1.getInsCode().equals(c.getInsCode())) {
                                                                        c.setSumOutPatientPrescription(a1.getSumResult());
                                                                        c.setSumInPatientPrescription(a1.getSumResult2());
                                                                        a.setSumOutPatientPrescription(a.getSumOutPatientPrescription() + a1.getSumResult());
                                                                        a.setSumInPatientPrescription(a1.getSumResult2() + a.getSumInPatientPrescription());

                                                                        b.setSumOutPatientPrescription(b.getSumOutPatientPrescription() + a1.getSumResult());
                                                                        b.setSumInPatientPrescription(a1.getSumResult2() + b.getSumInPatientPrescription());
                                                                    }
                                                                });

                                                            });
                                                }


                                            }
                                    );
                                }
                        );
                    }
            );
        }
        //计算所有的统计数据总和
        changeMaps.forEach(a -> {
            statisticsChangeResult.setNumber(statisticsChangeResult.getNumber() + a.getNumber());
            statisticsChangeResult.setSumStatisticsPrescription(statisticsChangeResult.getSumStatisticsPrescription() + a.getSumStatisticsPrescription());
            statisticsChangeResult.setRegisterTimes(statisticsChangeResult.getRegisterTimes() + a.getRegisterTimes());
            statisticsChangeResult.setElectronicRecordNum(statisticsChangeResult.getElectronicRecordNum() + a.getElectronicRecordNum());
            statisticsChangeResult.setSumPersonalPrescription(statisticsChangeResult.getSumPersonalPrescription() + a.getSumPersonalPrescription());
            statisticsChangeResult.setSumInnerAndExtPrescription(statisticsChangeResult.getSumInnerAndExtPrescription() + a.getSumInnerAndExtPrescription());
            statisticsChangeResult.setSumInnerAndExtPrescriptionCDSS(statisticsChangeResult.getSumInnerAndExtPrescriptionCDSS() + a.getSumInnerAndExtPrescriptionCDSS());
            statisticsChangeResult.setSumOutPatientPrescription(statisticsChangeResult.getSumOutPatientPrescription() + a.getSumOutPatientPrescription());
            statisticsChangeResult.setSumInPatientPrescription(statisticsChangeResult.getSumInPatientPrescription() + a.getSumInPatientPrescription());
        });
        if (StringUtils.isNotBlank(queryParam.getSearchDoctor()) && Constant.BASIC_STRING_ZERO.equals(queryParam.getSearchDoctor())) {
            removeEmptyInsLists(changeMaps);
        }
        statisticsChangeResult.setChangeMapList(changeMaps);
        return statisticsChangeResult;
    }

    private void removeEmptyInsLists(List<ChangeMap> changeMaps) {
        Iterator<ChangeMap> iterator = changeMaps.iterator();
        while (iterator.hasNext()) {
            ChangeMap changeMap = iterator.next();
            if (changeMap.getInsList() != null && changeMap.getInsList().isEmpty() && !changeMap.getInsCode().equals("-1")) {
                iterator.remove();
            } else if (changeMap.getInsList() != null) {
                removeEmptyInsLists(changeMap.getInsList());
            }
        }
    }

    /**
     * 统计 门诊与住院数据的处方
     *
     * @param timeDiff
     * @return
     */
    public void statisticsMzZy(String timeDiff) {
    }

    /**
     * @param timeDiff 统计那天 0 今天 1昨天。以此类推
     * @return 随便返回的一个值。
     */
    public String statisticsTongLu(String timeDiff) {
        long diffTime = 30;
        //就诊人次-电子病历
        TStatisticsRegister register = tStatisticsRegisterService.getLastData();
        if (null == register) {
            ResEntity resEntity = tStatisticsRegisterService.insertDate(null, timeDiff, "1");
            ResEntity resEntity1 = tStatisticsRegisterService.insertDate(null, timeDiff, "2");
        } else {
            Date insertDate = register.getInsertDate();
            if (null != insertDate) {
                long time = System.currentTimeMillis();
                long l = time - insertDate.getTime();
                long l1 = l / 1000 / 60;
                if (l1 >= diffTime) {
                    ResEntity resEntity = tStatisticsRegisterService.insertDate(null, timeDiff, "1");
                    ResEntity resEntity1 = tStatisticsRegisterService.insertDate(null, timeDiff, "2");
                }
            }
        }

        //1内服中药方 2外用中药方 3中成药方 4适宜技术方 5制剂）
        TStatisticsPrescription prescription = tStatisticsPrescriptionService.getLastData();
        if (null == prescription) {
            tStatisticsPrescriptionService.insertDate(null, timeDiff, "1");
            tStatisticsPrescriptionService.insertDate(null, timeDiff, "2");
            tStatisticsPrescriptionService.insertDate(null, timeDiff, "3");
            tStatisticsPrescriptionService.insertDate(null, timeDiff, "4");
            tStatisticsPrescriptionService.insertDate(null, timeDiff, "5");
        } else {
            Date insertDate = prescription.getInsertDate();
            if (null != insertDate) {
                long time = System.currentTimeMillis();
                long l = time - insertDate.getTime();
                long l1 = l / 1000 / 60;
                if (l1 >= diffTime) {
                    tStatisticsPrescriptionService.insertDate(null, timeDiff, "1");
                    tStatisticsPrescriptionService.insertDate(null, timeDiff, "2");
                    tStatisticsPrescriptionService.insertDate(null, timeDiff, "3");
                    tStatisticsPrescriptionService.insertDate(null, timeDiff, "4");
                    tStatisticsPrescriptionService.insertDate(null, timeDiff, "5");
                }
            }
        }

        //体质辨识
        TStatisticsAnalysis tStatisticsAnalysis = tStatisticsAnalysisMapper.selectLastDate();
        if (null == tStatisticsAnalysis) {
            tStatisticsAnalysisService.settStatisticsAnalysis(null, timeDiff);
        } else {
            Date insertDate = tStatisticsAnalysis.getInsertDate();
            if (null != insertDate) {
                long time = System.currentTimeMillis();
                long l = time - insertDate.getTime();
                long l1 = l / 1000 / 60;
                if (l1 >= diffTime) {
                    tStatisticsAnalysisService.settStatisticsAnalysis(null, timeDiff);
                }
            }

        }

//        （0私有 1科室 2全院 3专家经验）
        TStatisticsPersonalPrescription prescription1 = tStatisticsPersonalPrescriptionService.getLastData();
        if (null == prescription1) {
            ResEntity resEntity7 = tStatisticsPersonalPrescriptionService.setPersonalPrescriptionResult(null, timeDiff, "0");
            ResEntity resEntity8 = tStatisticsPersonalPrescriptionService.setPersonalPrescriptionResult(null, timeDiff, "1");
            ResEntity resEntity9 = tStatisticsPersonalPrescriptionService.setPersonalPrescriptionResult(null, timeDiff, "2");
            ResEntity resEntity10 = tStatisticsPersonalPrescriptionService.setPersonalPrescriptionResult(null, timeDiff, "3");
        } else {
            Date insertDate = prescription1.getInsertDate();
            if (null != insertDate) {
                long time = System.currentTimeMillis();
                long l = time - insertDate.getTime();
                long l1 = l / 1000 / 60;
                if (l1 >= diffTime) {
                    ResEntity resEntity7 = tStatisticsPersonalPrescriptionService.setPersonalPrescriptionResult(null, timeDiff, "0");
                    ResEntity resEntity8 = tStatisticsPersonalPrescriptionService.setPersonalPrescriptionResult(null, timeDiff, "1");
                    ResEntity resEntity9 = tStatisticsPersonalPrescriptionService.setPersonalPrescriptionResult(null, timeDiff, "2");
                    ResEntity resEntity10 = tStatisticsPersonalPrescriptionService.setPersonalPrescriptionResult(null, timeDiff, "3");
                }
            }

        }
        return Constant.BASIC_STRING_ONE;
    }


    /**
     * 获取数据
     *
     * @param queryParam
     * @return
     */
    public int getAllData(QueryParam queryParam) {
        Integer i = tStatisticsAnalysisMapper.sumStatistics(queryParam);
        return null == i ? 0 : i;
    }


    public void setChangeMaps(List<QueryResult> queryResultsDoctor, List<QueryResult> sumPersonalPrescriptionDoctor,
                              List<QueryResult> sumStatisticsPrescriptionDoctor, List<QueryResult> sumStatisticsRegisterDoc,
                              List<QueryResult> sumStatisticsPrescriptionCDSSDoc,
                              List<QueryResult> sumStatisticsPrescriptionMzZyDoc,
                              ChangeMap a,ChangeMap p0,ChangeMap p1) {


        List<ChangeMap> insList1 = a.getInsList();
        if (null != insList1) {
            for (int i = 0; i < insList1.size(); i++) {
                ChangeMap changeMap = insList1.get(i);
                for (QueryResult queryResult : queryResultsDoctor) {
                    if (queryResult.getAppId().equals(changeMap.getAppId()) && queryResult.getInsCode().equals(changeMap.getInsCode()) && !"-1".equals(changeMap.getInsCode())) {
                        //先要找里面有没有，没有才add，有的话取出来修改。
                        if (changeMap.getInsList().stream().anyMatch(
                                b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                        )) {
                            ChangeMap changeMap2 = a.getInsList().stream().filter(
                                    b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                            ).findAny().get();
                            changeMap2.setNumber(changeMap2.getNumber() + queryResult.getSumResult());
                            a.setNumber(a.getNumber() + queryResult.getSumResult());
                            if (p1 != null){
                                p1.setNumber(p1.getNumber() + queryResult.getSumResult());
                            }
                            if (p0 != null){
                                p0.setNumber(p0.getNumber() + queryResult.getSumResult());
                            }
                            //a.setInsName(queryResult.getUserName());
                            //changeMap2.setInsName(queryResult.getUserName());
                        } else {
                            ChangeMap changeMapNew = new ChangeMap();
                            changeMapNew.setAppId(queryResult.getAppId());
                            changeMapNew.setNumber(queryResult.getSumResult());
                            a.setNumber(a.getNumber() + queryResult.getSumResult());
                            changeMapNew.setInsName(queryResult.getUserName());
                            changeMapNew.setInsCode("-1");
                            changeMapNew.setUserName(queryResult.getUserName());
                            changeMapNew.setUserId(queryResult.getUserId());

                            if (p1 != null){
                                p1.setNumber(p1.getNumber() + queryResult.getSumResult());
                            }
                            if (p0 != null){
                                p0.setNumber(p0.getNumber() + queryResult.getSumResult());
                            }

                            List<ChangeMap> insList = changeMap.getInsList();
                            if (insList == null) {
                                insList = new ArrayList<>();
                            }
                            insList.add(changeMapNew);
                        }
                    }
                }
            }
        }

        for (QueryResult queryResult : queryResultsDoctor) {
            if (queryResult.getAppId().equals(a.getAppId()) && queryResult.getInsCode().equals(a.getInsCode()) && !"-1".equals(a.getInsCode())) {
                //先要找里面有没有，没有才add，有的话取出来修改。
                if (a.getInsList() != null && a.getInsList().stream().anyMatch(
                        b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                )) {
                    ChangeMap changeMap = a.getInsList().stream().filter(
                            b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                    ).findAny().get();
                    changeMap.setNumber(changeMap.getNumber() + queryResult.getSumResult());
                    a.setNumber(a.getNumber() + queryResult.getSumResult());
                    a.setInsName(queryResult.getUserName());
                    changeMap.setInsName(queryResult.getUserName());
                    if (p1 != null){
                        p1.setNumber(p1.getNumber() + queryResult.getSumResult());
                    }
                    if (p0 != null){
                        p0.setNumber(p0.getNumber() + queryResult.getSumResult());
                    }
                } else {
                    ChangeMap changeMap = new ChangeMap();
                    changeMap.setAppId(queryResult.getAppId());
                    changeMap.setNumber(queryResult.getSumResult());
                    a.setNumber(a.getNumber() + queryResult.getSumResult());
                    changeMap.setInsName(queryResult.getUserName());
                    changeMap.setInsCode("-1");
                    changeMap.setUserName(queryResult.getUserName());
                    changeMap.setUserId(queryResult.getUserId());
                    a.getInsList().add(changeMap);
                    if (p1 != null){
                        p1.setNumber(p1.getNumber() + queryResult.getSumResult());
                    }
                    if (p0 != null){
                        p0.setNumber(p0.getNumber() + queryResult.getSumResult());
                    }
                }
            }
        }


        if (null != insList1) {
            for (int i = 0; i < insList1.size(); i++) {
                ChangeMap changeMap = insList1.get(i);
                for (QueryResult queryResult : sumPersonalPrescriptionDoctor) {
                    if (queryResult.getAppId().equals(changeMap.getAppId()) && queryResult.getInsCode().equals(changeMap.getInsCode()) && !"-1".equals(changeMap.getInsCode())) {
                        //先要找里面有没有，没有才add，有的话取出来修改。
                        if (changeMap.getInsList().stream().anyMatch(
                                b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                        )) {
                            ChangeMap changeMap2 = a.getInsList().stream().filter(
                                    b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                            ).findAny().get();
                            changeMap2.setSumPersonalPrescription(changeMap2.getSumPersonalPrescription() + queryResult.getSumResult());
                            a.setSumPersonalPrescription(a.getSumPersonalPrescription() + queryResult.getSumResult());
                            if (p1 != null){
                                p1.setSumPersonalPrescription(p1.getSumPersonalPrescription() + queryResult.getSumResult());
                            }
                            if (p0 != null){
                                p0.setSumPersonalPrescription(p0.getSumPersonalPrescription() + queryResult.getSumResult());
                            }
                        } else {
                            ChangeMap changeMapNew = new ChangeMap();
                            changeMapNew.setAppId(queryResult.getAppId());
                            changeMapNew.setSumPersonalPrescription(queryResult.getSumResult());
                            a.setSumPersonalPrescription(a.getSumPersonalPrescription() + queryResult.getSumResult());
                            changeMapNew.setInsName(queryResult.getUserName());
                            changeMapNew.setInsCode("-1");
                            changeMapNew.setUserName(queryResult.getUserName());
                            changeMapNew.setUserId(queryResult.getUserId());
                            List<ChangeMap> insList = changeMap.getInsList();
                            if (p1 != null){
                                p1.setSumPersonalPrescription(p1.getSumPersonalPrescription() + queryResult.getSumResult());
                            }
                            if (p0 != null){
                                p0.setSumPersonalPrescription(p0.getSumPersonalPrescription() + queryResult.getSumResult());
                            }
                            if (insList == null) {
                                insList = new ArrayList<>();
                            }
                            insList.add(changeMapNew);

                        }
                    }
                }
            }
        }
        for (QueryResult queryResult : sumPersonalPrescriptionDoctor) {
            if (queryResult.getAppId().equals(a.getAppId()) && queryResult.getInsCode().equals(a.getInsCode()) && !"-1".equals(a.getInsCode())) {
                //先要找里面有没有，没有才add，有的话取出来修改。
                if (a.getInsList() != null && a.getInsList().stream().anyMatch(
                        b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                )) {
                    ChangeMap changeMap = a.getInsList().stream().filter(
                            b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                    ).findAny().get();
                    changeMap.setSumPersonalPrescription(changeMap.getSumPersonalPrescription() + queryResult.getSumResult());
                    a.setSumPersonalPrescription(a.getSumPersonalPrescription() + queryResult.getSumResult());
                    if (p1 != null){
                        p1.setSumPersonalPrescription(p1.getSumPersonalPrescription() + queryResult.getSumResult());
                    }
                    if (p0 != null){
                        p0.setSumPersonalPrescription(p0.getSumPersonalPrescription() + queryResult.getSumResult());
                    }
                } else {
                    ChangeMap changeMapNew = new ChangeMap();
                    changeMapNew.setAppId(queryResult.getAppId());
                    changeMapNew.setSumPersonalPrescription(queryResult.getSumResult());
                    a.setSumPersonalPrescription(a.getSumPersonalPrescription() + queryResult.getSumResult());
                    changeMapNew.setInsName(queryResult.getUserName());
                    changeMapNew.setInsCode("-1");
                    changeMapNew.setUserName(queryResult.getUserName());
                    changeMapNew.setUserId(queryResult.getUserId());
                    a.getInsList().add(changeMapNew);
                    if (p1 != null){
                        p1.setSumPersonalPrescription(p1.getSumPersonalPrescription() + queryResult.getSumResult());
                    }
                    if (p0 != null){
                        p0.setSumPersonalPrescription(p0.getSumPersonalPrescription() + queryResult.getSumResult());
                    }
                }
            }
        }


        if (null != insList1) {
            for (int i = 0; i < insList1.size(); i++) {
                ChangeMap changeMap = insList1.get(i);
                int temp = 1;
                for (QueryResult queryResult : sumStatisticsPrescriptionDoctor) {
                    if (queryResult.getAppId().equals(changeMap.getAppId()) && queryResult.getInsCode().equals(changeMap.getInsCode()) && !"-1".equals(changeMap.getInsCode())) {
                        //先要找里面有没有，没有才add，有的话取出来修改。
                        if (changeMap.getInsList().stream().anyMatch(
                                b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                        )) {
                            ChangeMap changeMap2 = a.getInsList().stream().filter(
                                    b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                            ).findAny().get();
                            changeMap2.setSumStatisticsPrescription(changeMap2.getSumStatisticsPrescription() + queryResult.getSumResult());
                            a.setSumStatisticsPrescription(a.getSumStatisticsPrescription() + queryResult.getSumResult());
                            changeMap2.setSumInnerAndExtPrescription(changeMap2.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                            a.setSumInnerAndExtPrescription(a.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                            if (p1 != null){
                                p1.setSumStatisticsPrescription(p1.getSumStatisticsPrescription() + queryResult.getSumResult());
                                p1.setSumInnerAndExtPrescription(p1.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                            }
                            if (p0 != null){
                                p0.setSumStatisticsPrescription(p0.getSumStatisticsPrescription() + queryResult.getSumResult());
                                p0.setSumInnerAndExtPrescription(p0.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                            }

                        } else {
                            ChangeMap changeMapNew = new ChangeMap();
                            changeMapNew.setAppId(queryResult.getAppId());
                            changeMapNew.setSumStatisticsPrescription(queryResult.getSumResult());
                            a.setSumStatisticsPrescription(a.getSumStatisticsPrescription() + queryResult.getSumResult());
                            changeMapNew.setSumInnerAndExtPrescription( queryResult.getSumResult2());
                            a.setSumInnerAndExtPrescription(a.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                            changeMapNew.setInsCode("-1");
                            changeMapNew.setInsName(queryResult.getUserName());
                            changeMapNew.setUserId(queryResult.getUserId());
                            if (p1 != null){
                                p1.setSumStatisticsPrescription(p1.getSumStatisticsPrescription() + queryResult.getSumResult());
                                p1.setSumInnerAndExtPrescription(p1.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                            }
                            if (p0 != null){
                                p0.setSumStatisticsPrescription(p0.getSumStatisticsPrescription() + queryResult.getSumResult());
                                p0.setSumInnerAndExtPrescription(p0.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                            }
                            List<ChangeMap> insList = changeMap.getInsList();
                            if (insList == null) {
                                insList = new ArrayList<>();
                            }
                            insList.add(changeMapNew);
                        }
                    }
                }
            }
        }

        for (QueryResult queryResult : sumStatisticsPrescriptionDoctor) {
            if (queryResult.getAppId().equals(a.getAppId()) && queryResult.getInsCode().equals(a.getInsCode()) && !"-1".equals(a.getInsCode())) {
                //先要找里面有没有，没有才add，有的话取出来修改。
                if (a.getInsList() != null && a.getInsList().stream().anyMatch(
                        b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                )) {
                    ChangeMap changeMap = a.getInsList().stream().filter(
                            b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                    ).findAny().get();
                    changeMap.setSumStatisticsPrescription(changeMap.getSumStatisticsPrescription() + queryResult.getSumResult());
                    a.setSumStatisticsPrescription(a.getSumStatisticsPrescription() + queryResult.getSumResult());
                    changeMap.setSumInnerAndExtPrescription(changeMap.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                    a.setSumInnerAndExtPrescription(a.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                    if (p1 != null){
                        p1.setSumStatisticsPrescription(p1.getSumStatisticsPrescription() + queryResult.getSumResult());
                        p1.setSumInnerAndExtPrescription(p1.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                    }
                    if (p0 != null){
                        p0.setSumStatisticsPrescription(p0.getSumStatisticsPrescription() + queryResult.getSumResult());
                        p0.setSumInnerAndExtPrescription(p0.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                    }

                } else {
                    ChangeMap changeMapNew = new ChangeMap();

                    changeMapNew.setAppId(queryResult.getAppId());
                    changeMapNew.setSumStatisticsPrescription( queryResult.getSumResult());
                    a.setSumStatisticsPrescription(a.getSumStatisticsPrescription() + queryResult.getSumResult());
                    changeMapNew.setSumInnerAndExtPrescription( queryResult.getSumResult2());
                    a.setSumInnerAndExtPrescription(a.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                    changeMapNew.setInsCode("-1");
                    changeMapNew.setInsName(queryResult.getUserName());
                    changeMapNew.setUserId(queryResult.getUserId());

                    if (p1 != null){
                        p1.setSumStatisticsPrescription(p1.getSumStatisticsPrescription() + queryResult.getSumResult());
                        p1.setSumInnerAndExtPrescription(p1.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                    }
                    if (p0 != null){
                        p0.setSumStatisticsPrescription(p0.getSumStatisticsPrescription() + queryResult.getSumResult());
                        p0.setSumInnerAndExtPrescription(p0.getSumInnerAndExtPrescription() + queryResult.getSumResult2());
                    }

                    a.getInsList().add(changeMapNew);

                }
            }
        }


        if (null != insList1) {
            for (int i = 0; i < insList1.size(); i++) {
                ChangeMap changeMap = insList1.get(i);
                for (QueryResult queryResult : sumStatisticsPrescriptionCDSSDoc) {
                    if (queryResult.getAppId().equals(changeMap.getAppId()) && queryResult.getInsCode().equals(changeMap.getInsCode()) && !"-1".equals(changeMap.getInsCode())) {
                        //先要找里面有没有，没有才add，有的话取出来修改。
                        if (changeMap.getInsList().stream().anyMatch(
                                b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                        )) {
                            ChangeMap changeMap2 = a.getInsList().stream().filter(
                                    b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                            ).findAny().get();
                            changeMap2.setSumInnerAndExtPrescriptionCDSS(changeMap2.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());
                            a.setSumInnerAndExtPrescriptionCDSS(a.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());

                            if (p1 != null){
                                p1.setSumInnerAndExtPrescriptionCDSS(p1.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());
                            }
                            if (p0 != null){
                                p0.setSumInnerAndExtPrescriptionCDSS(p0.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());
                            }
                        } else {
                            ChangeMap changeMapNew = new ChangeMap();
                            changeMapNew.setAppId(queryResult.getAppId());
                            changeMapNew.setSumInnerAndExtPrescriptionCDSS( queryResult.getSumResult2());
                            a.setSumInnerAndExtPrescriptionCDSS(a.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());
                            changeMapNew.setInsCode("-1");
                            changeMapNew.setInsName(queryResult.getUserName());
                            changeMapNew.setUserName(queryResult.getUserName());
                            changeMapNew.setUserId(queryResult.getUserId());

                            if (p1 != null){
                                p1.setSumInnerAndExtPrescriptionCDSS(p1.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());
                            }
                            if (p0 != null){
                                p0.setSumInnerAndExtPrescriptionCDSS(p0.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());
                            }

                            List<ChangeMap> insList = changeMap.getInsList();
                            if (insList == null) {
                                insList = new ArrayList<>();
                            }
                            insList.add(changeMapNew);
                        }
                    }
                }
            }
        }

        for (QueryResult queryResult : sumStatisticsPrescriptionCDSSDoc) {
            if (queryResult.getAppId().equals(a.getAppId()) && queryResult.getInsCode().equals(a.getInsCode()) && !"-1".equals(a.getInsCode())) {
                //先要找里面有没有，没有才add，有的话取出来修改。
                if (a.getInsList() != null && a.getInsList().stream().anyMatch(
                        b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                )) {
                    ChangeMap changeMap = a.getInsList().stream().filter(
                            b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                    ).findAny().get();
                    changeMap.setSumInnerAndExtPrescriptionCDSS(changeMap.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());
                    a.setSumInnerAndExtPrescriptionCDSS(a.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());

                    if (p1 != null){
                        p1.setSumInnerAndExtPrescriptionCDSS(p1.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());
                    }
                    if (p0 != null){
                        p0.setSumInnerAndExtPrescriptionCDSS(p0.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());
                    }
                } else {
                    ChangeMap changeMapNew = new ChangeMap();
                    changeMapNew.setAppId(queryResult.getAppId());
                    changeMapNew.setSumInnerAndExtPrescriptionCDSS(queryResult.getSumResult2());
                    a.setSumInnerAndExtPrescriptionCDSS(a.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());
                    changeMapNew.setInsCode("-1");
                    changeMapNew.setInsName(queryResult.getUserName());
                    changeMapNew.setUserName(queryResult.getUserName());
                    changeMapNew.setUserId(queryResult.getUserId());
                    if (p1 != null){
                        p1.setSumInnerAndExtPrescriptionCDSS(p1.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());
                    }
                    if (p0 != null){
                        p0.setSumInnerAndExtPrescriptionCDSS(p0.getSumInnerAndExtPrescriptionCDSS() + queryResult.getSumResult2());
                    }
                    a.getInsList().add(changeMapNew);
                }
            }
        }
        if (null != insList1) {
            for (int i = 0; i < insList1.size(); i++) {
                ChangeMap changeMap = insList1.get(i);
                int temp = 1;
                for (QueryResult queryResult : sumStatisticsPrescriptionMzZyDoc) {
                    if (queryResult.getAppId().equals(changeMap.getAppId()) && queryResult.getInsCode().equals(changeMap.getInsCode()) && !"-1".equals(changeMap.getInsCode())) {
                        //先要找里面有没有，没有才add，有的话取出来修改。
                        if (changeMap.getInsList().stream().anyMatch(
                                b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                        )) {
                            ChangeMap changeMap2 = a.getInsList().stream().filter(
                                    b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                            ).findAny().get();
                            changeMap2.setSumOutPatientPrescription(changeMap2.getSumOutPatientPrescription() + queryResult.getSumResult());
                            a.setSumOutPatientPrescription(a.getSumOutPatientPrescription() + queryResult.getSumResult());
                            changeMap2.setSumInPatientPrescription(changeMap2.getSumInPatientPrescription() + queryResult.getSumResult2());
                            a.setSumInPatientPrescription(a.getSumInPatientPrescription() + queryResult.getSumResult2());
                            if (p1 != null){
                                p1.setSumOutPatientPrescription(p1.getSumOutPatientPrescription() + queryResult.getSumResult());
                                p1.setSumInPatientPrescription(p1.getSumInPatientPrescription() + queryResult.getSumResult2());
                            }
                            if (p0 != null){
                                p0.setSumOutPatientPrescription(p0.getSumOutPatientPrescription() + queryResult.getSumResult());
                                p0.setSumInPatientPrescription(p0.getSumInPatientPrescription() + queryResult.getSumResult2());
                            }
                        } else {
                            ChangeMap changeMapNew = new ChangeMap();
                            changeMapNew.setAppId(queryResult.getAppId());

                            changeMapNew.setSumOutPatientPrescription( queryResult.getSumResult());
                            a.setSumOutPatientPrescription(a.getSumOutPatientPrescription() + queryResult.getSumResult());

                            changeMapNew.setSumInPatientPrescription(queryResult.getSumResult2());
                            a.setSumInPatientPrescription(a.getSumInPatientPrescription() + queryResult.getSumResult2());


                            changeMapNew.setInsCode("-1");
                            changeMapNew.setInsName(queryResult.getUserName());
                            changeMapNew.setUserName(queryResult.getUserName());
                            changeMapNew.setUserId(queryResult.getUserId());

                            if (p1 != null){
                                p1.setSumOutPatientPrescription(p1.getSumOutPatientPrescription() + queryResult.getSumResult());
                                p1.setSumInPatientPrescription(p1.getSumInPatientPrescription() + queryResult.getSumResult2());
                            }
                            if (p0 != null){
                                p0.setSumOutPatientPrescription(p0.getSumOutPatientPrescription() + queryResult.getSumResult());
                                p0.setSumInPatientPrescription(p0.getSumInPatientPrescription() + queryResult.getSumResult2());
                            }
                            List<ChangeMap> insList = changeMap.getInsList();
                            if (insList == null) {
                                insList = new ArrayList<>();
                            }
                            temp = 0;
                            insList.add(changeMapNew);
                        }
                    }
                }
            }
        }

        for (QueryResult queryResult : sumStatisticsPrescriptionMzZyDoc) {
            if (queryResult.getAppId().equals(a.getAppId()) && queryResult.getInsCode().equals(a.getInsCode()) && !"-1".equals(a.getInsCode())) {
                //先要找里面有没有，没有才add，有的话取出来修改。
                if (a.getInsList() != null && a.getInsList().stream().anyMatch(
                        b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                )) {
                    ChangeMap changeMap = a.getInsList().stream().filter(
                            b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                    ).findAny().get();
                    changeMap.setSumOutPatientPrescription(changeMap.getSumOutPatientPrescription() + queryResult.getSumResult());
                    a.setSumOutPatientPrescription(a.getSumOutPatientPrescription() + queryResult.getSumResult());
                    changeMap.setSumInPatientPrescription(changeMap.getSumInPatientPrescription() + queryResult.getSumResult2());
                    a.setSumInPatientPrescription(a.getSumInPatientPrescription() + queryResult.getSumResult2());
                    if (p1 != null){
                        p1.setSumOutPatientPrescription(p1.getSumOutPatientPrescription() + queryResult.getSumResult());
                        p1.setSumInPatientPrescription(p1.getSumInPatientPrescription() + queryResult.getSumResult2());
                    }
                    if (p0 != null){
                        p0.setSumOutPatientPrescription(p0.getSumOutPatientPrescription() + queryResult.getSumResult());
                        p0.setSumInPatientPrescription(p0.getSumInPatientPrescription() + queryResult.getSumResult2());
                    }
                } else {
                    ChangeMap changeMapNew = new ChangeMap();
                    changeMapNew.setAppId(queryResult.getAppId());
                    changeMapNew.setSumOutPatientPrescription( queryResult.getSumResult());
                    a.setSumOutPatientPrescription(a.getSumOutPatientPrescription() + queryResult.getSumResult());
                    changeMapNew.setSumInPatientPrescription(queryResult.getSumResult2());
                    a.setSumInPatientPrescription(a.getSumInPatientPrescription() + queryResult.getSumResult2());
                    changeMapNew.setInsCode("-1");
                    changeMapNew.setInsName(queryResult.getUserName());
                    changeMapNew.setUserName(queryResult.getUserName());
                    changeMapNew.setUserId(queryResult.getUserId());

                    if (p1 != null){
                        p1.setSumOutPatientPrescription(p1.getSumOutPatientPrescription() + queryResult.getSumResult());
                        p1.setSumInPatientPrescription(p1.getSumInPatientPrescription() + queryResult.getSumResult2());
                    }
                    if (p0 != null){
                        p0.setSumOutPatientPrescription(p0.getSumOutPatientPrescription() + queryResult.getSumResult());
                        p0.setSumInPatientPrescription(p0.getSumInPatientPrescription() + queryResult.getSumResult2());
                    }
                    a.getInsList().add(changeMapNew);
                }
            }
        }

        if (null != insList1) {
            for (int i = 0; i < insList1.size(); i++) {
                ChangeMap changeMap = insList1.get(i);
                int temp = 1;
                for (QueryResult queryResult : sumStatisticsRegisterDoc) {
                    if (queryResult.getAppId().equals(changeMap.getAppId()) && queryResult.getInsCode().equals(changeMap.getInsCode()) && !"-1".equals(changeMap.getInsCode())) {
                        //先要找里面有没有，没有才add，有的话取出来修改。
                        if (changeMap.getInsList().stream().anyMatch(
                                b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                        )) {
                            ChangeMap changeMap2 = a.getInsList().stream().filter(
                                    b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                            ).findAny().get();
                            changeMap2.setRegisterTimes(changeMap2.getRegisterTimes() + queryResult.getSumResult());
                            a.setRegisterTimes(a.getRegisterTimes() + queryResult.getSumResult());
                            changeMap2.setElectronicRecordNum(changeMap2.getElectronicRecordNum() + queryResult.getSumResult2());
                            a.setElectronicRecordNum(a.getElectronicRecordNum() + queryResult.getSumResult2());

                            if (p1 != null){
                                p1.setRegisterTimes(p1.getRegisterTimes() + queryResult.getSumResult());
                                p1.setElectronicRecordNum(p1.getElectronicRecordNum() + queryResult.getSumResult2());
                            }
                            if (p0 != null){
                                p0.setRegisterTimes(p0.getRegisterTimes() + queryResult.getSumResult());
                                p0.setElectronicRecordNum(p0.getElectronicRecordNum() + queryResult.getSumResult2());
                            }

                        } else {
                            ChangeMap changeMapNew = new ChangeMap();
                            changeMapNew.setRegisterTimes( queryResult.getSumResult());
                            a.setRegisterTimes(a.getRegisterTimes() + queryResult.getSumResult());
                            changeMapNew.setElectronicRecordNum( queryResult.getSumResult2());
                            a.setElectronicRecordNum(a.getElectronicRecordNum() + queryResult.getSumResult2());
                            changeMapNew.setInsCode("-1");
                            changeMapNew.setInsName(queryResult.getUserName());
                            changeMapNew.setUserName(queryResult.getUserName());
                            changeMapNew.setUserId(queryResult.getUserId());
                            if (p1 != null){
                                p1.setRegisterTimes(p1.getRegisterTimes() + queryResult.getSumResult());
                                p1.setElectronicRecordNum(p1.getElectronicRecordNum() + queryResult.getSumResult2());
                            }
                            if (p0 != null){
                                p0.setRegisterTimes(p0.getRegisterTimes() + queryResult.getSumResult());
                                p0.setElectronicRecordNum(p0.getElectronicRecordNum() + queryResult.getSumResult2());
                            }
                            List<ChangeMap> insList = changeMap.getInsList();
                            if (insList == null) {
                                insList = new ArrayList<>();
                            }
                            temp = 0;
                            insList.add(changeMapNew);
                        }
                    }
                }
            }
        }

        for (QueryResult queryResult : sumStatisticsRegisterDoc) {
            if (queryResult.getAppId().equals(a.getAppId()) && queryResult.getInsCode().equals(a.getInsCode()) && !"-1".equals(a.getInsCode())) {
                //先要找里面有没有，没有才add，有的话取出来修改。
                if (a.getInsList() != null && a.getInsList().stream().anyMatch(
                        b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                )) {
                    ChangeMap changeMap = a.getInsList().stream().filter(
                            b -> null != b.getUserId() && b.getUserId().equals(queryResult.getUserId())
                    ).findAny().get();
                    changeMap.setRegisterTimes(changeMap.getRegisterTimes() + queryResult.getSumResult());
                    a.setRegisterTimes(a.getRegisterTimes() + queryResult.getSumResult());
                    changeMap.setElectronicRecordNum(changeMap.getElectronicRecordNum() + queryResult.getSumResult2());
                    a.setElectronicRecordNum(a.getElectronicRecordNum() + queryResult.getSumResult2());
                    if (p1 != null){
                        p1.setRegisterTimes(p1.getRegisterTimes() + queryResult.getSumResult());
                        p1.setElectronicRecordNum(p1.getElectronicRecordNum() + queryResult.getSumResult2());
                    }
                    if (p0 != null){
                        p0.setRegisterTimes(p0.getRegisterTimes() + queryResult.getSumResult());
                        p0.setElectronicRecordNum(p0.getElectronicRecordNum() + queryResult.getSumResult2());
                    }
                } else {
                    ChangeMap changeMapNew = new ChangeMap();
                    changeMapNew.setRegisterTimes( queryResult.getSumResult());
                    a.setRegisterTimes(a.getRegisterTimes() + queryResult.getSumResult());
                    changeMapNew.setElectronicRecordNum( queryResult.getSumResult2());
                    a.setElectronicRecordNum(a.getElectronicRecordNum() + queryResult.getSumResult2());
                    changeMapNew.setInsCode("-1");
                    changeMapNew.setInsName(queryResult.getUserName());
                    changeMapNew.setUserName(queryResult.getUserName());
                    changeMapNew.setUserId(queryResult.getUserId());
                    if (p1 != null){
                        p1.setRegisterTimes(p1.getRegisterTimes() + queryResult.getSumResult());
                        p1.setElectronicRecordNum(p1.getElectronicRecordNum() + queryResult.getSumResult2());
                    }
                    if (p0 != null){
                        p0.setRegisterTimes(p0.getRegisterTimes() + queryResult.getSumResult());
                        p0.setElectronicRecordNum(p0.getElectronicRecordNum() + queryResult.getSumResult2());
                    }
                    a.getInsList().add(changeMapNew);
                }
            }
        }

    }
}
