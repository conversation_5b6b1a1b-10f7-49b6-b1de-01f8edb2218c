```text
tcm-cloud.*.zip 为前端压缩包
pre_api_5.1.jar 为后端jar包
pre_api_v5.*.sql 为数据库升级脚本
config/application.properties 为后端配置文件
config/recordTemplate.json 为处方一件事的配置文件
config/增加recordDefault.json 为处方一件事的配置的默认值文件
templates 为病历模板存放的文件目录文件夹
application.properties中的数据库用户名密码加密方式：请求"http://**************:83/pre_ai/noAuth/encrypt?val=你的用户名"，把返回值填入spring.datasource.primary.username，密码同理
```
### 5.21.2
`date:2023-03-06`
- nginx需要配置 client_max_body_size 20m;
### 5.20.9
`date:2022.11.22` `git版本:` `版本MD5值：`
- 参数改造:F006 - 医保限制第一次保存时提醒内容
- 参数新增：F014 - 医保限制开药时提醒 1开0关 --线上该功能不启用。
- 增加搜药、更多药、转换药接口返回字段-xzsm（医保说明）--未和药品查询服务对接。线上该功能不启用。
- 桂林-监管看板
- t_order_status 增加 IS_DEL 字段 '0正常1删除（药房pdf手持）
### 5.20.8
`date:2022.11.17`  `版本MD5值：`
- 中医四诊术语分类修改
- 处方一件事页面调整-“小儿”、“成人”、“其他”症状显示调整及分类调整
- 新增参数：中医治法必填 B110
  *云系统配置文件夹config目录：需要替换recordTemplate.json 并  增加recordDefault.json 文件。否则处方一件事页面无法正常显示内容。*
### 5.20.7
`date:2022.10.31`  `版本MD5值：`
- 云系统速度优化
- EMS物流查询
- K002参数优化：名称：处方状态变更调用接口项目
- 智能开方配送：浙里办获取患者配送地址
#### 注意
*云系统配置文件中新增参数配置china.digital.medicine.url=http://ip:port/tzbs/*

### 5.20.6
`date:2022.10.21`  `版本MD5值：56405d2547e1e83b987dfddf27707901`
- 海盐膏方优化

### 5.20.5
`date:2022.09.26` `Git版本：70f45f3b` `版本MD5值：3567b3d7418ccd2b45d65d6fef60716c`
- 中医处方一件事插件迁移，受到D001 - 中医电子病历保存后跳转功能、D003 - 中医病历是否必填 控制。
- 增加菜单 处方一件事（前端显示名称为电子病历）和电子病历只能同时展示一个菜单。
- 电子病历跳转：就诊记录-历次就诊-转病历 。受到当前账号配置菜单权限控制。
- 知识库导入西医诊断国临2.0映射中医GB2021字典数据
- 处方、病历打印优化：智能开方新增打印按钮
- 处方用法优化
- 取药方式（配送、自提）优化
- 中医疾病与西医疾病交互优化

### 5.20.4-rc3
`date:2022.10.14` `Git版本：17f6245` `版本MD5值：8de21a67cd86f7397bac7ab8795008d5`
- 修复推导转换药未取NotPayAlone
  ```text
  该BUG导致参数配置：F001 - 开方页面“医保”列显示数据来源 使用2.医保外配置，且药品明细配置医保外药品，页面推导不显示医保还是自费。如配置1his接口数据，无影响。
  ```

### 5.20.4-rc2
`date:2022.09.16` `Git版本：7c88800` `版本MD5值：81fe7a982e4a4c0070f8804cb917a15d`
- 修复自动作废处方时，会作废处方状态大于100的处方的问题。
  ```text
  该BUG导致历史处方数据异常，需执行 ../补丁SQL/pre_api_V5.20.4-rc2.sql 修复问题。
  ```

### 5.20.4-rc1
`date:2022.09.14` `Git版本：7c28d10` `版本MD5值：65518d0a6d5d90728987f5132cca5f68`
- 修复维护科室协定方时，因为医生的执业机构没有当前挂号科室，造成丢失协定方权限的问题

### 5.20.4
`date:2022.09.02` `Git版本：`77050c79` 版本MD5值：` `3d842d3e9dc1c1fb8063082d27185729`
- 颗粒开方增加提纯系数计算
- 药房管理：增加预扣库存开关
- 药房管理：保存处方接口校验库存
- 《临床业务监管-处方分析》报表调整
- 增加处方“患者类型”选项
- 【系统使用统计、学习统计】历史数据同步


### 5.20.3-rc1
`date:2022.08.30` `Git版本：`e142ca5` 版本MD5值：` `32eb42fb8bc8d91c044804ed2cf5477b`
- 修复BUG:保存电子病历对象门诊住院标志错误


### 5.20.3
`date:2022.08.19` `Git版本：`53e365b` 版本MD5值：` `6ec21ff1eb0ddddb3057a8834c6c9c57`
- 病历、处方打印模板新增患者联系方式、联系地址传值
- 订单查询界面调整，新增操作时间、操作人、物流类型及单号
- 业务管理B102西医诊断优化
- 新增西医诊断版本参数配置
- 特慢病患者取消“智能推导”限制

### 5.20.2-rc2
`date:2022.08.12` `Git版本：`7b44619` 版本MD5值：` `43c02baf03d09cb54d96f72d3f829ce3`
- 修复打印处方无显示机构名

### 5.20.2-rc1
`date:2022.07.27` `Git版本：`839e0f0` 版本MD5值：` `a22d0bf54c1aec7599b2b9d6981b3853`
- 配方接口增加phaId药房id
- 开发第一次保存校验库存

### 5.20.2
`date:2022.07.04` `Git版本：` `版本MD5值：`

#### 注意
*云系统配置文件中redis的host,port,database需要与综合平台保持一致*

#### 功能优化
- 支持同一药房开多剂型药品
- 支持使用药房系统维护的配方
- 对接综合平台药房配置
- 处方增加煎药方式、包装方式、取药方式
- 综合平台维护数据后自动清除相关缓存

#### BUG修复
- 修复新都特病功能
- 炮制品提醒校验移植到知识库接口
- 修复保存协定方权限时，科室ID可能为空的异常


### 5.20.1
`date:2022.06.16` `Git版本：4c8edb4` `版本MD5值：469886ec28e5ccae055d4717c791aa29`

#### 功能优化
- 支持开中药制剂
- 药房管理加限制，判断当前操作账号，非系统管理员不允许操作
- 处方增加字段：药房名称；处方明细增加字段：his药品规格代码，药房药品规格代码
- 优化就诊记录查询sql
- 协定方维护时授权逻辑优化，医生不在当前科室时，默认授权当前科室

#### BUG修复
- 修复药房配置字典不生效的问题
- 修复校验中药制剂日最大开药量
- 调整处方触发器预扣库存处理，直接操作综合平台预扣库存表
- 修复配送到医院地址字段错误
- 搜索药品不搜索无库存药品
- 修复修改共享权限后，协定方重复问题
- 修复协定方搜索全部与搜索文件夹返回数据不一致的问题


### 5.20.0
`date:2022.05.12` `Git版本：ebd9c51` `版本MD5值：a6a46dd205c74c0e33f711b0473d86df`

*该版本需更新对应知识库接口项目，知识库图片和知识库数据库*

*该版本医联体、医疗机构、科室、菜单、角色、用户、医生执业机构、参数、公用代码 迁移到了综合管理平台*

*该版本药房配置仍需在云系统配置，药品目录、药品映射等迁移到了综合管理平台*

#### 需求
- 对接综合业务管理平台，对接药品查询服务
  ```text
  配置文件application.properties增加三项配置：
  #内部系统交互平台ID
  providerId=336699
  #综合平台接口服务
  address.platform.api.url=http://************:65/platform/api/
  #药品查询公共服务接口
  address.drugs.api.url=http://************:8010/
  ```
- 691 【海盐-中药处方一件事】增加证件类型
  ```text
  患者表增加字段，详见pre_api_v5.15.2.sql
  ```
- 696 【传承系统】传承系统专病对接
  ```text
  A002参数增加“专病辨证”；
  ```
- 761 【知识库】增加脉诊概述
- 760 【知识库】临床诊疗指南展示维度优化
- 759 【知识库】中成药-关联疾病增加链接
- 758 【知识库】方剂查询-增加关联疾病
- 828 电子病历模板改造
- 834 对接综合平台权限管理
- 835 专家经验共享对接药品查询搜索药品
- 836 协定方权限对接综合平台
- 849 智能辩证自动填充电子病历


### 5.15.8
`date:2022.12.14` `git版本:`
- 安吉医保限制提醒范围控制:参数值【复选】：1门诊医保、2门诊自费、3住院医保、4住院自费、5门诊特病
- 对接安吉省中胃脾病传承接口
- 执业机构A的医生登录至B科室，在【协定方】菜单中维护协定方，当前页面可见，返回A科室登录不可见（5.20.9版本是可见。）。B科室其他医生可见。
- 新增参数： 参数编号：C014   参数类型：安全合理用药  参数名称：无中医资质限制提醒 参数说明：1开0关
- 根据参数：C014如果打开：无中医资质的医生点击“保存与推送”，同样弹窗提示“暂无中医处方权限，无法开方！仅可使用学习功能！”，不可开方
### 5.12.5.2-rc2
`date:2022.11.14` `git版本:ad2054f`
- 不允许跨域访问

### 5.12.5.2
`date:2022.09.27` `git版本:ec69aba`
- 安吉医保限制提醒范围控制:参数值【复选】：1门诊医保、2门诊自费、3住院医保、4住院自费、5门诊特病
### 5.12.5.1
`date:2022.09.27` `git版本:ec69aba`
- 合并5.12.5-rc1~rc6版本

### 5.12.5-rc1
`date:2022.09.19` `git版本:`
- 参数改造:F006 - 医保限制第一次保存时提醒内容
- 参数新增：F014 - 医保限制开药时提醒 1开0关
- 增加搜药、更多药、转换药接口返回字段-xzsm（医保说明）


### 5.15.7
`date:2022.10.09` `git版本:` `版本MD5值：17e22dce5febbd0fc343e3bfdf0ab2fe`
- 参数改造:F006 - 医保限制第一次保存时提醒内容
- 参数新增：F014 - 医保限制开药时提醒 1开0关
- 增加搜药、更多药、转换药接口返回字段-xzsm（医保说明）
- 桂林-监管看板
### 5.15.6
`date:2022年8月8日 `Git版本：974a9f8` `版本MD5值：e7a6e78c427a3257a2ac363b3dcb8733`

-  特慢病患者取消“智能推导”限制
-  增加处方“患者类型”选项(参数控制-参数值-PATIENTS_TYPES_SHOW)
### 5.15.5
`date:2022.07.27` `Git版本：2596202` `版本MD5值：b76105e4afc37295e64b466810a8830f`

-  药房管理：增加预扣库存开关
-  药房管理：保存处方接口校验库存
-  处方分析报表调整

### 5.15.4
`date:2022.08.03` `Git版本：464cb36` `版本MD5值：`

- 1345 颗粒开方增加提纯系数计算

### 5.15.3-rc2
`date:2022.06.09` `Git版本：0e2cd1b` `版本MD5值：7c9a9df981e29e451fae41a8eb1b56dc`

- 支付后处方推送到HIS改为推送到药房

### 5.15.3-rc1
`date:2022.06.07` `Git版本：4f1af2c` `版本MD5值：4b83c4a3b085547c9bfe9b40dc3983af`

- HIS疾病证型转换系统疾病证型，支持一对多，取第一条

### 5.15.3
`date:2022.06.02` `Git版本：cb85227` `版本MD5值：5a601ebc77718d7d2a4eb03facd2f677`
- 882 系统使用统计（桐庐需求）
- 865 系统学习统计报表（桐庐需求）


### 5.15.1-rc2
`date: 2022.5.17` `Git版本：c2300ec` `版本MD5值：fed5ac1cd8e11c3b3cf96b04b76eed26`

>- 打印处方二维码路径改为全路径+preNo
   >
   >  ```
   >  仅5.12.3-rc4、5.15.1-rc2 和5.15.2之后版本支持该功能
   >  
   >  配置文件调整：
   >  示例：
   >  #中药煎配查询移动端
   >  address.mobile.decoct = http://**************:91/pre/common/toInfo?preNo=
   >  ```

### 5.15.1-rc1
`date:2022.04.29` `Git版本：9186a2b` `版本MD5值：2ad664fa67418f882a6f4a591f292fbd`

```text
  合并5.12.6-rc1分支，修复5.12.6-rc1BUG
```
- 修复BUG：（常熟）天安加解密工具在使用过程中偶尔会导致服务异常

### 5.15.1
`date: 2022.03.15` `Git版本：babb8eb` `版本MD5值：9a45364090c9cb52c8dd586ef4f4b962`
*该版本需更新对应知识库接口项目*

#### 需求
- 1441 新增知识库内容同步至云系统
- 1442 方剂查询增加外用处方、适宜技术方
- 1443 增加舌脉象的解释说明
- 1444 增加疾病名概述展示
  ```text
  新增参数：
  参数名称：是否显示病名描述
  参数代码：DISEASE_DESCRIPTION
  参数说明：1显示，0隐藏（默认显示）
  ```
- 1445 95修订版类名是否可用
  ```text
  新增参数：
  参数名称：开方时不可搜索类名疾病（95修订带.的疾病）
  参数代码：DIS_FILTER_CATEGORY
  参数说明：0可以搜索 1不可以搜索（默认不可以搜索，安吉上线改为0）
  ```
- 1446 协定方使用权限增加搜索条件
- 1447 增加“参数值”文本的模糊查找搜索条件
- 1448 增加一键清除缓存及缓存分类
- 1449 增加“兼症加减”
  ```text
  新增参数：
  参数名称：兼证加减按钮隐藏显示
  参数代码：JIANZHENG_BTN_SHOW
  参数说明：1显示，0隐藏（默认隐藏）
  ```


### 5.12.6-rc1
`date:2022.04.29` `Git版本：b13cc77` `版本MD5值：bdccb4794cb094f60210ffefec35dc04`

- 修复BUG：（常熟）天安加解密工具在使用过程中偶尔会导致服务异常


### 5.12.6
`date:2022.01.24 ` `Git版本：58a7fdc`

*该版本需更新对应知识库接口项目*

#### 需求
- 1414 中医知识库-增加我的收藏入口
- 1415 中医知识库-增加“经方查询”模块	
- 1416 中医知识库详情页收藏后返回，收藏状态显示优化	
- 1417 增加处方状态：药房审核通过、药房审核未通过	
- 1418 临床业务监管平台-增加“处方分析(新都)”
- 1419 临床业务监管平台-处方金额分析，增加导出报表	
- 1420 临床业务监管平台-中药处方选择统计-增加列表展示及报表导出
- 1421 智能开方-推导出“扩充”的诊断，红色提醒，限制是否强制保存
  ```text
  新增参数：
  参数名称：诊断范围验证（扩充诊断）
  参数代码：DIAGNOSIS_RANGE_VERIFY
  参数说明：扩充诊断（95标准 或 95修订之外）是否强制限制保存
      0：表示无限制（默认）
      1：表示限制
  ```
- 1422 增加参数分类管理及参数编号
- 1423 导出报表名称优化
- 1424 舌诊、脉诊收藏功能优化
- 1427 配方颗粒开方时无需判断明细库存
  
#### 修复BUG
- 开方页药品库存（真实库存-预扣库存）可能出现负数
- 安吉第二次挂号开方，修改上次的处方，参数“未缴费处方去修改使用的挂号ID”配置为new，保存的处方对应挂号错误问题


### 5.12.5
`date:2022.01.04`
- KFXT-880 华润德信行大药房项目蓝色调主题
- KFXT-881 成都医保接入国家医保平台改造-中药门特改造 （该功能已合并到5.12.4）
- KFXT-882 电子病历打印优化
  ```text
  修改参数：
  参数名称：病历打印按钮是否显示
  参数代码：PRINT_RECORD_SHOW
  参数说明：1显示 0不显示（按钮位置：第一位： 中医电子病历；第二位： 今日病人/我的历史病历/病历管理-查看订单；）示例：1,0
  
  新增参数：
  参数名称：中医电子病历打印模板参数控制
  参数代码：REC_PDF_TEMPLATE
  参数说明：1.A4模板 2.A5模板
  ```
- KFXT-883 配送增加备用手机号码
  ```text
  增加参数
  参数名称：配送是否启用备用号码
  参数代码：DC_MOBILE_BAK   
  参数说明：参数第一位为备用号码的数量，默认为0不开启，第二位为备用号码的必填数量。示例：2,1，可添加2个备用号码，一个为必填
  ```
- KFXT-885 三终端禁止重复登陆
  ```text
  新增配置项：login.one
  启用单点登录：login.one=true
  禁用单点登录：login.one=false
  ```
- KFXT-886 支持多医联体使用同一个药品目录【安吉】
- KFXT-901 药房管理配置公用代码明细
- KFXT-904 医共体管理禁用按钮逻辑修改
- KFXT-930 打印模板文件外置
  ```text
  打印模板默认使用jar包中的模板文件，如需调整打印模板，在pre_api_5.1文件夹下新增文件夹templates/，添加对应模板文件：
  内服/外用处方模板（单页）：prescriptionTemplate.pdf
  内服/外用处方模板（双页）：prescriptionTemplate2.pdf
  中药制剂处方模板：preparationTemplate.pdf
  病历模板（A4）：recordTemplateA4.pdf
  病历模板（A5）：recordTemplateA5.pdf
  ```

#### ********
`date:2021.12.28`
- 登陆密码使用非对称加密
- KFXT-909 版本更新说明增加更新类型
- KFXT-910 临床业务监管-医共体处方分析：增加导出功能
- KFXT-911 临床业务监管-中药处方分析-中药处方覆盖率：增加图片导出功能
- KFXT-912 调整业务管理-业务管理-查看订单的二级页面显示
- KFXT-913 专家经验共享模块优化
- KFXT-914 换处方后方解未显示优化
- KFXT-915 中医电子病历-检查附件优化
- KFXT-928 增加膏方贴数参数控制
  ```text
  新增参数：
  参数名称：膏方控制开方贴数
  参数代码：PRESCRIPTION_INTERNAL_PRODUCTION_CONTROL_NUM
  参数说明：参数值为膏方最高贴数，0不限制，勾选膏方后提示：“是否改为膏方？膏方一张处方最多开*贴”，贴数自动调整并不可修改
  ```

### 5.12.4
`date:2021.12.24`
>- KFXT-773 新都前置审方对接
>- KFXT-829 中医知识库增加中成药的查询
>- KFXT-830 知识库维护后台——临床诊疗录入
>- KFXT-832 中医知识库增加“临床诊疗指南”
>- KFXT-834 中医知识库增加舌诊和脉诊
>- KFXT-838 执业机构展示医疗机构代码
>- KFXT-839 中药查询——点击“更多”交互方式更改
>- KFXT-875 内服中药方：膏方不受药位数及处方最高价格限制
>- KFXT-879 库存数量不足优化提醒
>- KFXT-905 新都门特治疗周期限制
```text
【新增参数】
参数名称：第三方审核者
参数代码：PRESCRIPTION_CHECK_AUDITOR
参数说明：self: 云系统审核（默认）；medicom: 美康


【新增参数】
参数名称：特病：选择日期时间范围禁用配置
参数代码：PRESCRIPTION_SPECIAL_DISEASE_RANGE_PICKER_DISABLED_OPTIONS
参数说明：（已当前时间为基准）强制包含|开始时间禁用,结束时间禁用,开始时间偏离量,结束时间偏离量
示例1：当前日期 2021年12月13日 17:29:30
1. -14D,3M,0,-14D 可选范围：2021-11-29 00:00:00 ~ 2022-02-28 23:59:59
2. -14D:s,3M:h,0,-14D 可选范围：2021-11-29- 17:29:30 ~ 2022-02-28 17:59:59
3. -14D:D,3M:m 可选范围：2021-11-29 00:00:00 ~ 2022-03-13 17:29:59
示例2：当前日期 2021年12月14日 17:29:30 星期二
1. T 本月（12-01 ~ 12-31）本周（12-13 ~ 12-19）
2. F 本月（12-14 ~ 12-31）本周（12-14 ~ 12-19）

【修改参数】
1、内服中药方最高药味数：PRESCRIPTION_INTERNAL_HIGHEST_MATS
参数说明：通用药味数|膏方药味数（0为无限制）；
示例：5|4；
2、内服中草药方处方金额限制最高价格：PRESCRIPTION_INTERNAL_HIGHEST_MONEY
参数说明：通用处方金额|膏方处方金额（0为无限制）；
示例：20|15；
3、内服中草药单帖最高限价金额：PRESCRIPTION_INTERNAL_MAXIMUM_AMOUNT
参数说明：通用单帖金额:T|膏方单帖金额:T（0为无限制，:T是否限制可以继续开方）
示例：20:T|30:T

```

### 5.12.3-rc4
`date: 2022.5.17`

>- 打印处方二维码路径改为全路径+preNo
   >
   >  ```
>  配置文件调整：
>  示例：
>  #中药煎配查询移动端
>  address.mobile.decoct = http://**************:91/pre/common/toInfo?preNo=
>  ```

### 5.12.3-rc3
`date: 2021.12.28`
>- 修复BUG：药品目录导入报错

### 5.12.3-rc2
`date: 2021.12.17`
>- 解决高危漏洞：Apache Log4j2远程代码执行漏洞 
```text
注意：application.properties中数据库URL需删掉 log4jdbc:
```

### 5.12.3
`date:2021.10.25`
>- 海盐迭代 包含********, ********, ********

>- 修订 `date:2021.11.4`
>>- 我的患者条件查询BUG
>>- 历史处方查看患者性别BUG
>>- 配方推送增加药房ID和药品类型（对接药房系统）
>>- 传承疾病证型区分95标准95修订（对接传承系统）

### ********
`date:2021.10.25`
>- KFXT-666：转方操作-转病证治及西医诊断 
>- KFXT-804：中药搜索结果排序优化
>- KFXT-183：增加配方付费方式
>- KFXT-803：中药查询 -详情页 排版/文案优化
>- KFXT-805：经络穴位查询 -页面优化 
>- KFXT-811：疾病查询 -详情页 增加【出处】字段
>- KFXT-812：名医验案 -详情页 增加字段
```text
新增参数：
1、转方操作-转病证配置：PRESCRIPTION_TRANSFER_ILLNESS_OPTIONS
    0cc00：【就诊记录】和【历史处方】转方转病证  
    c000c：【未缴费处方】和【专家经验共享】转方转病证
```

### ********
`date:2021.10.11`
`该版本知识库系统有更新`
>- KFXT-164 处方笺双签名，并将双签名的药传给his/药房
>- KFXT-715 医保患者是否自费处方的判断和自动勾选
>- KFXT-716 显示患者住院/门诊号
>- KFXT-717 要开无糖膏方
>- KFXT-777 同一药品不同规格的药可以开在一个处方中，但是必须让医生双签名
>- KFXT-779 单贴金额超出最高限价时，增加提醒功能
```text
参数：
1、医保限制提醒弹框内容
    1日最大剂量、2医保外药品、3基金不支付
2、是否弹框双签提醒
    1是0否
3、安全用药特殊签字配置
    1禁用药、2忌用药、3慎用药、4十八反、5十九畏、6不宜同用、7孕妇忌用、8孕妇慎用、9使用注意、10剂量超标、11毒、12病症禁忌、13饮食禁忌、14禁孕、15超规定用药量、16剂量偏低、20多规格同时开
4、开方界面是否显示膏方类型
    1显示（默认） 0不显示
5、处方同种草药判定规则
    [0或1且][知识库ID][药房药品代码][HIS药品代码][上级药品代码][HIS规格ID]
    示例：
    	0101：默认值等同下值
    	010100：知识库ID 或 HIS药品代码 相等
        100011：上级药品代码 且 HIS规格ID 全等
6、内服中草药单帖最高限价金额
    0不限制
7、外用中草药单帖最高限价金额
    0不限制
```
前端增加无糖疾病配置文件：/database/sugar-free.json
```json
{
  "describe": "疾病与膏方无糖关系数据",
  "date": 1633750538184,
  "version": 1,
  "data": [
    {
      "describe": "无糖选项",
      "option": "pt-0",
      "disease": [
        {
          "id": "zyjb12170",
          "name": "消渴病"
        },
        "zyjb12170"
      ],
      "ICD": []
    },
    {
      "describe": "不限糖选项",
      "option": "pt-1",
      "disease": [],
      "ICD": []
    },
    {
      "option": "pt-0",
      "type": "disease",
      "id": "zyjb12170",
      "name": "消渴病"
    }
  ]
}
```

### ********
`date: 2021.9.30`
>- KFXT-182 中药配方
>- KFXT-644 设置处方审核医生上下班时间
>- KFXT-699 增加“互联网在线就诊患者“信息
>- KFXT-700 处方审核状态和通过条件
>- KFXT-701 处方审核列表展示形式修改、增加查询条件
>- KFXT-702 处方审核新消息提醒
>- KFXT-705 预扣库存
>- KFXT-719 专家经验共享维护时，证型不必填
>- KFXT-728 数据迁移
```text
配置文件 config/application.properties更新了以下内容：
新增药房路径配置：
address.cbkj.core.port = http://ip:85/
删除以下配置：
address.cbkj.core.pushCFYZ、
address.cbkj.core.getYinPianDZ、
address.cbkj.core.drugSynchronization
address.cbkj.zb.url
```
```text
参数：
1、开方时搜索处方：
    类型和排序及默认选中；协定方：self|dept|ins，配方：formula，方剂：know（以|分隔，:T 表示选中）
2、开方时搜索处方：
    类型和排序；协定方：self|dept|ins，配方：formula，方剂：know（以|分隔）
3、搜索(协定方|方剂|配方)处方默认类型：
    方剂(默认值): know ; 配方: formula ; 个人协定方: self ; 科室协定方: dept ; 全院协定方: ins
4、配方自动审核通过：
    1开启0关闭 默认开启
5、审核工作时间：
    星期与时间段用中文冒号：分割，多个时间段用|分割，多个星期用中文分号；分隔。
    不在审方时间段将自动审核通过，处方标记为‘下班自动通过’。
    默认空，不支持下班自动审核
    如：8:00-11:00|14:00-17:00；（每天8点到11点和14点到17点）
    如：星期一：8:00-11:00；星期三：8:00-10:00|14:00-16:00；（星期一8:00-11:00、星期三8:00-10:00和14:00-16:00）
6、系统审核人名称：
    当系统审核通过后，处方审核人为该值
7、是否可以系统审核
   0：全部时间段不可以系统审核；   
   1：全部时间段可以系统审核；   
   2：审方工作时间内不可以系统审核，审方工作时间外可以系统审核；   
   3：审方工作时间内不可以系统审核，审方工作时间外不系统审核；   
   4：审方工作时间内可以系统审核，审方工作时间外不系统审核
```

### ********_1
`date: 2021.9.28`
`桐庐揭榜挂帅`
>- KFXT-698 处方笺增加二维码，患者扫描这个二维码查看自己的处方溯源信息
>- KFXT-770 区分配送到医院还是其他地址
```text
增加参数：打印处方笺是否显示二维码
```

### ********
`date: 2021.8.23`

>- KFXT-645 传承系统的专病在云系统使用
>- KFXT-658 治法可选
>- KFXT-659 增加参数：维护贴数的默认值
```
参数名称：内服中草药方默认贴数值
参数代码：PRESCRIPTION_INTERNAL_DEFAULT_NUMBER
参数说明：正整数值，0或缺省时不默认

参数名称：外用中草药方默认贴数值
参数代码：PRESCRIPTION_EXTERNAL_DEFAULT_NUMBER
参数说明：正整数值，0或缺省时不默认
```
>- KFXT-660 his的传过来的建档信息里的手机号码大于11位，接口直接取其前11位。
>- KFXT-661 将药品剂量的提醒放在下方
>- KFXT-665 协定方修改权限调整为：允许修改、不允许修改、仅允许加药
>- KFXT-747 使用协定方时，药不全的提醒文字调整

## 5.12.2
`date: 2021.8.20`

**同时更新知识库疾病数据、知识库接口**

>- KFXT-212 知识库区分男性、女性疾病
>- KFXT-577 医共体业务分析和国考指标分析
>- KFXT-600 育龄期外的人不提示妊娠禁忌
>- KFXT-652 配送地址默认患者地址还是医院地址由参数控制
```
参数名称：配送地址默认(寄送)类型
参数代码：PRESCRIPTION_CONSIGNEE_DEFAULT_TYPE
参数说明：开方时勾选配送默认地址填充并切换；1寄到医院；2寄到患者家里

默认值（缺省）：
2

示例：
1（填充值：医院地址，按钮：寄到患者家里）
2（填充值：患者地址，按钮：寄到医院）
```

## ********
`date: 2021.9.2`

>- KFXT-741 增加双签确认---安吉
```text
参数修改：医保支付条件限制的对象（INSURANCE_LIMIT_OBJECT）的参数值增加一项：3）限制所有人。
参数修改：安全用药特殊签字配置（SAFETY_EVALUATION_SIGN）：增加 超规定用药量(15) 签名
增加参数：超剂量提醒文字设置：弹框标题/弹框按钮/开药提醒。默认值：医保限制报销|强制保存|超医保规定用药量。
```
```text
安吉：
   1、参数1配置成：3
   2、参数2配置成：双签确认|双签保存|超药典规定用药量
   3、参数3配置成：15
   4、药品明细中设置需要的最大剂量值
```

## 5.12.1
`date: 2021.7.15`

**更新后清除“公用代码“缓存。**
**更新后验证协定方使用权限是否正常，验证药房配置是否正常，搜索药品是否混开。**

>- 支持中药制剂模板打印
>- KFXT-296 解决参数管理查询麻烦配置麻烦的问题
>- KFXT-543 增加医嘱时间选择
>- KFXT-548 版本通知
>- KFXT-576 用户表拆分成用户信息表和执业范围权限表
>- KFXT-648 圣美孚演示
>- KFXT-649 开方与新药房对接，使用新接口系统
>- KFXT-650 打开开方系统速度优化

## 5.12.0
`date: 2021.7.1`

>- KFXT-549 安吉临时-协定方增加配方的勾选

# 5.11

### 5.11.1.2
`date: 2021.6.23`

>- 5.9.2的修订版本v5.11.1.2  用户更换科室时协定方BUG

## 5.11.1
`date: 2021.6.28`

>-  KFXT-532 与国医大师-浊瘀痹对接中药制剂推导
>-  KFXT-536 增加系统管理-日志查询
>-  KFXT-542 体质辨识报告word转pdf传给接口方

# 5.10

**更新后验证协定方使用权限异常时，需执行pre_api_v5.9_2.sql**