<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.EncryptFieldsMapper">

    <select id="getTableFields" resultType="com.jiuzhekan.cbkj.beans.EncryptTableField">
        SELECT
            c.TABLE_NAME tableName,
            cu.COLUMN_NAME primaryField,
            c.COLUMN_NAME fieldName,
            c.DATA_TYPE fieldType,
            c.CHARACTER_MAXIMUM_LENGTH fieldLength
        FROM
            INFORMATION_SCHEMA.COLUMNS c
        JOIN INFORMATION_SCHEMA.TABLES t
             ON t.TABLE_SCHEMA = c.TABLE_SCHEMA AND t.TABLE_NAME = c.TABLE_NAME
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE cu
             ON cu.CONSTRAINT_NAME = 'PRIMARY' AND cu.CONSTRAINT_SCHEMA = c.TABLE_SCHEMA AND cu.TABLE_NAME = c.TABLE_NAME
        WHERE c.TABLE_SCHEMA ='zyznyxt_basic' and c.TABLE_NAME like 'b_%';
    </select>

    <select id="getFieldValue" parameterType="com.jiuzhekan.cbkj.beans.EncryptTableField" resultType="map">
        select ${primaryField}, ${fieldName} from ${tableName}
    </select>

    <update id="encryptFieldValue" parameterType="com.jiuzhekan.cbkj.beans.EncryptTableField">
        update ${tableName}
            set ${fieldName} = #{fieldValue}
        where ${primaryField} = #{primaryValue}
    </update>

</mapper>