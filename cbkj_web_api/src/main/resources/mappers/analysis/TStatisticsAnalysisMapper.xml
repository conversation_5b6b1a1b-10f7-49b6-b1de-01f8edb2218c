<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.statistics.mapper.analysis.TStatisticsAnalysisMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.statistics.beans.analysis.TStatisticsAnalysis">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="usage_times" jdbcType="INTEGER" property="usageTimes" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="insert_date" jdbcType="DATE" property="insertDate" />
    </resultMap>


    <sql id="Base_Column_List">
    id,app_id,app_name,ins_code,ins_name,dept_id,dept_name,user_id,user_name,usage_times,create_date
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.analysis.TStatisticsAnalysis">
        delete from t_statistics_analysis where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_statistics_analysis where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.statistics.beans.analysis.TStatisticsAnalysis">
        insert into t_statistics_analysis (<include refid="Base_Column_List" />) values
        (#{id},#{appId},#{appName},#{insCode},#{insName},#{deptId},#{deptName},#{userId},#{userName},#{usageTimes},#{createDate})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_statistics_analysis (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.deptId},#{item.deptName},#{item.userId},#{item.userName},#{item.usageTimes},#{item.createDate})
        </foreach>
    </insert>


    <resultMap id="BaseResultMap2" type="com.jiuzhekan.statistics.beans.analysis.StatisticsResult">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
    </resultMap>


    <select id="selectStatisticsResult" parameterType="Map" resultMap="BaseResultMap2">
        select b.id,a.app_id,a.ins_code,a.doctor_id as userId,count(a.analy_id) as number,b.usage_times usageTimes,CAST(a.`analy_time` AS DATE) as dataDate
        from `t_user_analysis_result` as a  LEFT JOIN t_statistics_analysis AS b ON
                    a.app_id=b.app_id AND a.ins_code=b.ins_code AND a.doctor_id=b.user_id
                AND b.create_date=CAST(IFNULL(a.analy_time,'1900-01-01 00:00:00') as DATE)
        where
            TIMESTAMPDIFF(DAY,a.analy_time, DATE_ADD(#{curDate},INTERVAL 1 DAY))&lt;= #{timeDiff} and a.app_id is not null

        GROUP BY a.app_id,a.ins_code,a.doctor_id,dataDate
    </select>

    <select id="selectStatisticsResult2"  resultMap="BaseResultMap2">
        SELECT  b.id,a.app_id,a.ins_code,a.doctor_id AS userId,COUNT(a.analy_id) AS number,b.usage_times usageTimes,CAST(a.`analy_time` AS DATE) AS dataDate
        FROM `t_user_analysis_result` AS a  JOIN t_statistics_analysis AS b ON
                    a.app_id=b.app_id AND a.ins_code=b.ins_code AND a.doctor_id=b.user_id
                AND b.create_date=CAST(IFNULL(a.analy_time,'1900-01-01 00:00:00') AS DATE)
        WHERE
                TIMESTAMPDIFF(DAY,a.analy_time, DATE_ADD(CURDATE(),INTERVAL 1 DAY))='1' AND a.app_id IS NOT NULL
          AND a.is_del='1' AND b.id IS NOT NULL
        GROUP BY a.app_id,a.ins_code,a.doctor_id
    </select>

    <insert id="insertStatistics" parameterType="List">
        insert into t_statistics_analysis (
        app_id,ins_code,user_id,usage_times,create_date,insert_date
        ) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.appId},#{item.insCode},#{item.userId},#{item.number},#{item.dataDate},now())
        </foreach>
    </insert>
    <insert id="replaceStatistics" parameterType="List">
        replace into t_statistics_analysis (
        id,app_id,ins_code,user_id,usage_times,create_date
        ) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.insCode},#{item.userId},#{item.number},#{item.dataDate})
        </foreach>
    </insert>



    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.analysis.TStatisticsAnalysis">
        update t_statistics_analysis
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="deptName != null">
                dept_name = #{ deptName },
             </if>
             <if test="userId != null">
                user_id = #{ userId },
             </if>
             <if test="userName != null">
                user_name = #{ userName },
             </if>
             <if test="usageTimes != null">
                usage_times = #{ usageTimes },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_statistics_analysis where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.statistics.beans.analysis.TStatisticsAnalysis" resultMap="BaseResultMap">
        SELECT id,app_id,app_name,ins_code,ins_name,dept_id,dept_name,user_id,user_name,usage_times,create_date
        from t_statistics_analysis
        <where>
            <if test=" id != null ">
                and id =#{id}
            </if>
        </where>
    </select>

    <resultMap id="BaseResultMap3" type="com.jiuzhekan.statistics.beans.register.QueryResult">
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
    </resultMap>
    <select id="sumResult" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam" resultMap="BaseResultMap3">
        SELECT SUM(usage_times) sumResult,app_id,ins_code  FROM `t_statistics_analysis`
            <where>
                (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
                <if test="queryStartDate != null and queryEndDate!= null">
                    and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
                </if>
            </where>
group by app_id,ins_code
    </select>
    <select id="sumResultDoctor" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam" resultMap="BaseResultMap3">
        SELECT SUM(usage_times) sumResult,app_id,ins_code,user_id,user_name  FROM `t_statistics_analysis`
            <where>
                (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
                <if test="queryStartDate != null and queryEndDate!= null">
                    and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
                </if>
                <if test="doctorId != null">
                    and user_id=#{doctorId}
                </if>
            </where>
group by app_id,ins_code,user_id
    </select>
    <select id="sumPersonalPrescription" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam" resultMap="BaseResultMap3">
        SELECT SUM(total_num) sumResult,app_id,ins_code  FROM `t_statistics_personal_prescription`
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
        </where>
        group by app_id,ins_code
    </select>
    <select id="sumPersonalPrescriptionDoctor" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam" resultMap="BaseResultMap3">
        SELECT SUM(total_num) sumResult,app_id,ins_code,user_id,user_name  FROM `t_statistics_personal_prescription`
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
            <if test="doctorId != null">
                and user_id=#{doctorId}
            </if>
        </where>
        group by app_id,ins_code,user_id
    </select>
    <select id="sumStatisticsPrescription" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam" resultMap="BaseResultMap3">
        SELECT SUM(acu_num) sumResult,SUM(inner_num+exter_num) sumResult2,app_id,ins_code  FROM `t_statistics_prescription`
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
        </where>
        group by app_id,ins_code

    </select>
    <select id="sumStatisticsPrescriptionDoctor" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam" resultMap="BaseResultMap3">
        SELECT SUM(acu_num) sumResult,SUM(inner_num+exter_num) sumResult2,app_id,ins_code,user_id,user_name  FROM `t_statistics_prescription`
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
            <if test="doctorId != null">
                and user_id=#{doctorId}
            </if>
        </where>
        group by app_id,ins_code,user_id

    </select>
    <select id="sumStatisticsPrescriptionCDSS" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam" resultMap="BaseResultMap3">
        SELECT SUM(inner_num+exter_num) sumResult2,app_id,ins_code  FROM `t_statistics_prescription_cdss`
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
        </where>
        group by app_id,ins_code

    </select>
    <select id="sumStatisticsPrescriptionMzZy" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam" resultMap="BaseResultMap3">
        SELECT SUM(inner_mz_num+exter_mz_num) sumResult,SUM(inner_zy_num+exter_zy_num) sumResult2,app_id,ins_code  FROM `t_statistics_prescription_mz_zy`
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
        </where>
        group by app_id,ins_code

    </select>
    <select id="sumStatisticsPrescriptionCDSSDoc" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam" resultMap="BaseResultMap3">
        SELECT SUM(inner_num+exter_num) sumResult2,app_id,ins_code,user_id,user_name  FROM `t_statistics_prescription_cdss`
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
            <if test="doctorId != null">
                and user_id=#{doctorId}
            </if>
        </where>
        group by app_id,ins_code,user_id

    </select>
    <select id="sumStatisticsPrescriptionMzZyDoc" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam" resultMap="BaseResultMap3">
        SELECT SUM(inner_mz_num+exter_mz_num) sumResult,SUM(inner_zy_num+exter_zy_num) sumResult2,app_id,ins_code,user_id,user_name  FROM `t_statistics_prescription_mz_zy`
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
            <if test="doctorId != null">
                and user_id=#{doctorId}
            </if>
        </where>
        group by app_id,ins_code,user_id

    </select>
    <select id="sumStatisticsRegister" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam" resultMap="BaseResultMap3">
        SELECT SUM(register_times) sumResult,SUM(electronic_record_num) sumResult2,app_id,ins_code  FROM `t_statistics_register`
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
        </where>
        group by app_id,ins_code
    </select>
    <select id="sumStatisticsRegisterDoc" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam" resultMap="BaseResultMap3">
        SELECT SUM(register_times) sumResult,SUM(electronic_record_num) sumResult2,app_id,ins_code,user_id,user_name  FROM `t_statistics_register`
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
            <if test="doctorId != null">
                and user_id=#{doctorId}
            </if>
        </where>
        group by app_id,ins_code,user_id
    </select>
    <select id="selectLastDate" resultMap="BaseResultMap">
        select * from t_statistics_analysis order by  insert_date desc limit 0,1
    </select>
    <select id="sumStatistics" resultType="java.lang.Integer">
        select
        SUM(register_times) as number
        from t_statistics_register
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id = #{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code = #{insCode}
            </if>
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
        </where>
    </select>
</mapper>