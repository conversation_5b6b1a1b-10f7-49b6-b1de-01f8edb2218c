<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.analysis.TUserAnalysisResultMapper">
    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.analysis.TUserAnalysisResult">
        <id column="analy_id" jdbcType="VARCHAR" property="analyId"/>
        <result column="analy_code" jdbcType="VARCHAR" property="analyCode"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="register_id" jdbcType="VARCHAR" property="registerId"/>
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId"/>
        <result column="patient_id" jdbcType="VARCHAR" property="patientId"/>
        <result column="analy_time" jdbcType="TIMESTAMP" property="analyTime"/>
        <result column="sub_result" jdbcType="VARCHAR" property="subResult"/>
        <result column="display_result" jdbcType="VARCHAR" property="displayResult"/>
        <result column="display_result_item" jdbcType="VARCHAR" property="resultList"/>
        <result column="deputy_result" jdbcType="VARCHAR" property="deputyResult"/>
        <result column="biased_result" jdbcType="VARCHAR" property="biasedResult"/>
        <result column="sub_group_id" jdbcType="VARCHAR" property="subGroupId"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="tt_rule" jdbcType="VARCHAR" property="ttRule"/>
        <result column="tt_point" jdbcType="VARCHAR" property="ttPoint"/>
        <result column="tt_prescription" jdbcType="VARCHAR" property="ttPrescription"/>
        <result column="lzjj" jdbcType="VARCHAR" property="lzjj"/>
        <result column="vegetables" jdbcType="VARCHAR" property="vegetables"/>
        <result column="sports" jdbcType="VARCHAR" property="sports"/>
        <result column="emotional_adjustment" jdbcType="VARCHAR" property="emotionalAdjustment"/>
        <result column="diet_recuperation" jdbcType="VARCHAR" property="dietRecuperation"/>
        <result column="daily_life_adjustment" jdbcType="VARCHAR" property="dailyLifeAdjustment"/>
        <result column="acupoint_health_care" jdbcType="VARCHAR" property="acupointHealthCare"/>
        <result column="medicated_diet" jdbcType="VARCHAR" property="medicatedDiet"/>
        <result column="flower_tea" jdbcType="VARCHAR" property="flowerTea"/>
        <result column="other" jdbcType="VARCHAR" property="other"/>
        <result column="doc_options" jdbcType="VARCHAR" property="docOptions"/>
        <result column="score_img" jdbcType="VARCHAR" property="scoreImg"/>
        <result column="analy_type" jdbcType="INTEGER" property="analyType"/>
        <result column="is_del" jdbcType="VARCHAR" property="isDel"/>
        <result column="word_path" jdbcType="VARCHAR" property="wordPath"/>
        <result column="pdf_path" jdbcType="VARCHAR" property="pdfPath"/>
    </resultMap>


    <sql id="Base_Column_List">
        analy_id,analy_code,app_id,ins_code,register_id,doctor_id,patient_id,analy_time,sub_result,display_result,display_result_item,deputy_result,biased_result,sub_group_id,description,tt_rule,tt_point,tt_prescription,lzjj,vegetables,sports,emotional_adjustment,diet_recuperation,daily_life_adjustment,acupoint_health_care,medicated_diet,flower_tea,other,doc_options,score_img,analy_type,is_del,word_path,pdf_path
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TUserAnalysisResult">
        delete
        from t_user_analysis_result
        where analy_id = #{ analyId }
    </delete>

    <!--根据主键批量删除-->
    <update id="deleteBylist" parameterType="ArrayList">
        update t_user_analysis_result set is_del = '1' where analy_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!--单个插入-->
    <insert id="insert" parameterType="TUserAnalysisResult">
        insert into t_user_analysis_result (<include refid="Base_Column_List"/>) values
        (#{analyId},#{analyCode},#{appId},#{insCode},#{registerId},#{doctorId},#{patientId},#{analyTime},#{subResult},#{displayResult},#{resultList},#{deputyResult},#{biasedResult},#{subGroupId},#{description},#{ttRule},#{ttPoint},#{ttPrescription},#{lzjj},#{vegetables},#{sports},#{emotionalAdjustment},#{dietRecuperation},#{dailyLifeAdjustment},#{acupointHealthCare},#{medicatedDiet},#{flowerTea},#{other},#{docOptions},#{scoreImg},#{analyType},#{isDel},#{wordPath},#{pdfPath})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_user_analysis_result (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.analyId},#{item.analyCode},#{item.appId},#{item.insCode},#{item.registerId},#{item.doctorId},#{item.patientId},#{item.analyTime},#{item.subResult},#{item.displayResult},#{item.resultList},#{item.deputyResult},#{item.biasedResult},#{item.subGroupId},#{item.description},#{item.ttRule},#{item.ttPoint},#{item.ttPrescription},#{item.lzjj},#{item.vegetables},#{item.sports},#{item.emotionalAdjustment},#{item.dietRecuperation},#{item.dailyLifeAdjustment},#{item.acupointHealthCare},#{item.medicatedDiet},#{item.flowerTea},#{item.other},#{item.docOptions},#{item.scoreImg},#{item.analyType},#{item.isDel},#{item.wordPath},#{item.pdfPath})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TUserAnalysisResult">
        update t_user_analysis_result
        <set>
            <if test="appId != null">
                app_id = #{ appId },
            </if>
            <if test="analyCode != null">
                analy_code = #{ analyCode },
            </if>
            <if test="insCode != null">
                ins_code = #{ insCode },
            </if>
            <if test="registerId != null">
                register_id = #{ registerId },
            </if>
            <if test="doctorId != null">
                doctor_id = #{ doctorId },
            </if>
            <if test="patientId != null">
                patient_id = #{ patientId },
            </if>
            <if test="analyTime != null">
                analy_time = #{ analyTime },
            </if>
            <if test="subResult != null">
                sub_result = #{ subResult },
            </if>
            <if test="displayResult != null">
                display_result = #{ displayResult },
            </if>
            <if test="resultList != null">
                display_result_item = #{ resultList },
            </if>
            <if test="deputyResult != null">
                deputy_result = #{ deputyResult },
            </if>
            <if test="biasedResult != null">
                biased_result = #{ biasedResult },
            </if>
            <if test="subGroupId != null">
                sub_group_id = #{ subGroupId },
            </if>
            <if test="ttRule != null">
                tt_rule = #{ ttRule },
            </if>
            <if test="description != null">
                description = #{ description },
            </if>
            <if test="ttPoint != null">
                tt_point = #{ ttPoint },
            </if>
            <if test="ttPrescription != null">
                tt_prescription = #{ ttPrescription },
            </if>
            <if test="lzjj != null">
                lzjj = #{ lzjj },
            </if>
            <if test="vegetables != null">
                vegetables = #{ vegetables },
            </if>
            <if test="scoreImg != null">
                score_img = #{ scoreImg },
            </if>
            <if test="isDel != null">
                is_del = #{ isDel },
            </if>
            <if test="wordPath != null">
                word_path = #{ wordPath },
            </if>
            <if test="pdfPath != null">
                pdf_path = #{ pdfPath },
            </if>
            sports = #{ sports },
            emotional_adjustment = #{ emotionalAdjustment },
            diet_recuperation = #{ dietRecuperation },
            daily_life_adjustment = #{ dailyLifeAdjustment },
            acupoint_health_care = #{ acupointHealthCare },
            medicated_diet = #{ medicatedDiet },
            flower_tea = #{ flowerTea },
            other = #{ other },
            doc_options = #{ docOptions },
        </set>
        where analy_id = #{ analyId }
    </update>

    <update id="updateWordPdf" parameterType="TUserAnalysisResult">
        update t_user_analysis_result
        set word_path = #{ wordPath },
            pdf_path  = #{ pdfPath }
        where analy_id = #{ analyId }
    </update>

    <update id="updateSorceImg" parameterType="TUserAnalysisResult">
        update t_user_analysis_result
        set score_img = #{ scoreImg }
        where analy_id = #{ analyId }
    </update>


    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_user_analysis_result where analy_id = #{id}
    </select>


    <select id="getMapById" resultType="Map" parameterType="String">
        select u.analy_id              analyId,
               u.analy_code            analyCode,
               u.analy_time            analyTime,
               u.sub_result            subResult,
               u.display_result        displayResult,
               u.display_result_item   resultList,
               u.biased_result         biasedResult,
               u.sub_group_id          subGroupId,
               u.description           description,
               u.tt_rule               ttRule,
               u.tt_point              ttPoint,
               u.tt_prescription       ttPrescription,
               u.lzjj                  lzjj,
               u.vegetables            vegetables,
               u.sports                sports,
               u.emotional_adjustment  emotionalAdjustment,
               u.diet_recuperation     dietRecuperation,
               u.daily_life_adjustment dailyLifeAdjustment,
               u.acupoint_health_care  acupointHealthCare,
               u.medicated_diet        medicatedDiet,
               u.flower_tea            flowerTea,
               u.other                 other,
               u.doc_options           docOptions,
               u.score_img             scoreImg,
               u.analy_type            analyType,
               u.doctor_id             doctorId,
               p.patient_name          patientName,
               p.patient_id            patientId
        from t_user_analysis_result u
                 left join t_patients p on p.patient_id = u.patient_id
        where analy_id = #{id}
    </select>

    <select id="getAnalysisWordPdf" resultType="TUserAnalysisResult" parameterType="String">
        select u.word_path    wordPath,
               u.pdf_path     pdfPath,
               p.patient_name patientName
        from t_user_analysis_result u
                 left join t_patients p on p.patient_id = u.patient_id
        where analy_id = #{id}
    </select>

    <select id="getAnalysisResultById" resultType="TUserAnalysisResult" parameterType="String">
        select u.analy_id                                                            analyId,
               u.analy_code                                                          analyCode,
               u.analy_time                                                          analyTime,
               u.sub_result                                                          subResult,
               u.display_result                                                      displayResult,
               u.display_result_item                                                 resultList,
               u.deputy_result                                                       deputyResult,
               u.biased_result                                                       biasedResult,
               u.description                                                         description,
               u.tt_rule                                                             ttRule,
               u.tt_point                                                            ttPoint,
               u.tt_prescription                                                     ttPrescription,
               u.lzjj                                                                lzjj,
               u.vegetables                                                          vegetables,
               u.sports                                                              sports,
               u.emotional_adjustment                                                emotionalAdjustment,
               u.diet_recuperation                                                   dietRecuperation,
               u.daily_life_adjustment                                               dailyLifeAdjustment,
               u.acupoint_health_care                                                acupointHealthCare,
               u.medicated_diet                                                      medicatedDiet,
               u.flower_tea                                                          flowerTea,
               u.other                                                               other,
               u.doc_options                                                         docOptions,
               u.score_img                                                           scoreImg,
               u.analy_type                                                          analyType,
               u.doctor_id                                                           doctorId,
               p.patient_name                                                        patientName,
               p.patient_birthday                                                    patientBirthday,
               CASE p.patient_gender WHEN 'M' THEN '男' WHEN 'F' THEN '女' ELSE '' END patientGender,
               u.doctor_id                                                           doctorId,
               u.app_id                                                              appId,
               u.ins_code                                                            insCode
        from t_user_analysis_result u
                 left join t_patients p on p.patient_id = u.patient_id
        where analy_id = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageListByObj" parameterType="TUserAnalysisResult" resultType="TUserAnalysisResult">
        SELECT a.analyId,a.analyCode,a.patientId,a.analyTime,a.subResult,a.deputyResult,a.biasedResult,a.analyType,
        p.PATIENT_GENDER patientGender, p.PATIENT_BIRTHDAY patientBirthday, p.patient_name patientName
        from (
        SELECT u.analy_id analyId
        ,u.analy_code analyCode
        ,MAX(u.analy_time) analyTime
        ,u.patient_id patientId
        ,u.sub_result subResult
        ,u.deputy_result deputyResult
        ,u.biased_result biasedResult
        ,u.analy_type analyType
        from t_user_analysis_result u
        <where>
            u.is_del = '0'
            <if test="appId != null and appId != ''">
                AND u.app_id = #{appId}
            </if>
            <if test="insCode != null and insCode != ''">
                AND u.ins_code = #{insCode}
            </if>
        </where>
        GROUP BY patient_id
        ) a JOIN t_patients p on a.patientId = p.PATIENT_ID
        <choose>
            <when test="patientName != null and patientName != ''">
                AND p.PATIENT_NAME like CONCAT(TRIM(#{patientName}),'%')
            </when>
            <when test="patientPy != null and patientPy != ''">
                AND p.PATIENT_PY like CONCAT(TRIM(#{patientPy}),'%')
            </when>
            <when test="patientWb != null and patientWb != ''">
                AND p.PATIENT_WB like CONCAT(TRIM(#{patientWb}),'%')
            </when>
        </choose>
        ORDER BY a.analyTime desc
    </select>

    <select id="getUserResultByRes" parameterType="TUserAnalysisResult" resultType="TUserAnalysisResult">
        SELECT
        u.analy_id analyId,
        u.doctor_id doctorId,
        u.analy_code analyCode,
        u.analy_time analyTime,
        u.patient_id patientId,
        u.sub_result subResult,
        u.display_result displayResult,
        u.display_result_item resultList,
        u.deputy_result deputyResult,
        u.biased_result biasedResult,
        u.analy_type analyType,
        p.PATIENT_BIRTHDAY patientBirthday,
        p.PATIENT_GENDER patientGender,
        p.patient_name patientName
        FROM t_user_analysis_result AS u
        left JOIN t_patients p on u.patient_id = p.PATIENT_ID
        left join t_register r on r.register_id = u.register_id
        where u.is_del = '0'
        <if test="patientId != null and patientId != ''">
            AND u.patient_id = #{patientId}
        </if>
        <if test="appId != null and appId != ''">
            AND u.app_id = #{appId}
        </if>
        <if test="insCode != null and insCode != ''">
            AND u.ins_code = #{insCode}
        </if>
        <if test="deptId != null and deptId != ''">
            AND r.dept_id = #{deptId}
        </if>
        <if test="patientName != null and patientName != ''">
            AND p.PATIENT_NAME like concat(#{patientName}, '%')
        </if>
        <if test="analyType != null and analyType != ''">
            AND u.analy_type = #{analyType}
        </if>
        <if test="subResult != null and subResult != ''">
            AND u.sub_result like concat('%', #{subResult}, '%')
        </if>
        <if test="beginTime != null and beginTime != ''">
            AND u.analy_time >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND u.analy_time &lt; concat(#{endTime}, ' 23:59:59')
        </if>
        ORDER BY u.analy_time DESC
    </select>

    <select id="getGroupResults" parameterType="String"
            resultType="com.jiuzhekan.cbkj.beans.business.analysis.TUserAnalysisGroupResult">
        SELECT g.group_name groupName,
               g.score      score,
               g.is_main    isMain,
               g.is_sub     isSub
        FROM t_user_analysis_group_result AS g
        WHERE g.analy_id = #{analyId}
        order by g.seqn
    </select>

    <!-- 往分组结果表插入数据 -->
    <insert id="insertGroupResult" parameterType="list">
        INSERT INTO t_user_analysis_group_result (analy_type_id, analy_id, group_name, score, seqn, is_main, is_sub)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.analyTypeId},#{item.analyId},#{item.groupName},#{item.score},#{item.seqn},#{item.isMain},#{item.isSub})
        </foreach>
    </insert>

    <select id="getResultItemList" parameterType="String"
            resultType="com.jiuzhekan.cbkj.beans.business.analysis.TUSerAnalysusResultItem">
        SELECT item_id itemId,
               score   score
        FROM t_user_analysis_item
        WHERE analy_id = #{analyId}
        order by seqn
    </select>

    <!-- 往分组结果表插入数据 -->
    <insert id="insertResultItemList" parameterType="list">
        INSERT INTO t_user_analysis_item (analy_item_id, analy_id, item_id, score, seqn) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.analyItemId},#{item.analyId},#{item.itemId},#{item.score},#{item.seqn})
        </foreach>
    </insert>
</mapper>