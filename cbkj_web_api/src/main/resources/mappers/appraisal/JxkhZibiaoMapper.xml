<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.appraisal.mapper.JxkhZibiaoMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.appraisal.bean.JxkhZibiao">
        <id column="zibiaodm" jdbcType="VARCHAR"  property="zibiaodm" />
        <result column="zibiaomc" jdbcType="VARCHAR" property="zibiaomc" />
        <result column="zibiaosx" jdbcType="VARCHAR" property="zibiaosx" />
        <result column="jiliangdw" jdbcType="VARCHAR" property="jiliangdw" />
        <result column="zibiaody" jdbcType="VARCHAR" property="zibiaody" />
        <result column="jisuanff" jdbcType="VARCHAR" property="jisuanff" />
        <result column="jisuangs" jdbcType="VARCHAR" property="jisuangs" />
        <result column="zibiaosm" jdbcType="VARCHAR" property="zibiaosm" />
        <result column="XiuGaiSJ" jdbcType="TIMESTAMP" property="xiugaisj" />
        <result column="zibiaolb" jdbcType="INTEGER" property="zibiaolb" />
        <result column="significance_indicators" jdbcType="VARCHAR" property="significanceIndicators" />
        <result column="explanation_indicators" jdbcType="VARCHAR" property="explanationIndicators" />
        <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
        <result column="indicator_oriented" jdbcType="VARCHAR" property="indicatorOriented" />
    </resultMap>


    <sql id="Base_Column_List">
    zibiaodm,zibiaomc,zibiaosx,jiliangdw,zibiaody,jisuanff,jisuangs,zibiaosm,XiuGaiSJ,zibiaolb,
    significance_indicators,explanation_indicators,data_source,indicator_oriented
    </sql>

    <select id="getObjectById" parameterType="String" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        from jxkh_zibiao
        where  zibiaodm = #{zibiaodm}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.appraisal.bean.JxkhZibiao" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from jxkh_zibiao
        <where>
            <if test="zibiaodms != null and zibiaodms.length > 0">
                and zibiaodm in
                <foreach collection="zibiaodms" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
     <!--
     <if test="zibiaolb != null ">
            and zibiaolb = #{zibiaolb}
            </if>
        -->
        </where>
        ORDER BY zibiaodm ASC
    </select>

</mapper>