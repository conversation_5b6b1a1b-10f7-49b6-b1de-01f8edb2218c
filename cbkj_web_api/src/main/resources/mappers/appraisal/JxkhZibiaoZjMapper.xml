<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.appraisal.mapper.JxkhZibiaoZjMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.appraisal.bean.JxkhZibiaoZj">
        <id column="zibiaodm" jdbcType="CHAR"  property="zibiaodm" />
        <result column="zibiaomc" jdbcType="VARCHAR" property="zibiaomc" />
        <result column="jiliangdw" jdbcType="VARCHAR" property="jiliangdw" />
        <result column="zibiaosm" jdbcType="VARCHAR" property="zibiaosm" />
        <result column="XiuGaiSJ" jdbcType="TIMESTAMP" property="xiugaisj" />
        <result column="zibiaolb" jdbcType="INTEGER" property="zibiaolb" />
    </resultMap>


    <sql id="Base_Column_List">
    zibiaodm,zibiaomc,jiliangdw,zibiaosm,XiuGaiSJ
    </sql>


    <select id="getPageListByObj" parameterType="com.jiuzhekan.appraisal.bean.JxkhZibiaoZj" resultMap="BaseResultMap">
        SELECT zibiaodm,zibiaomc,jiliangdw,zibiaosm,XiuGaiSJ,zibiaolb
        from jxkh_zibiao_zj
        <where>
            <if test="zibiaodms != null and zibiaodms.length > 0">
                and zibiaodm in
                <foreach collection="zibiaodms" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="zibiaolb != null and zibiaolb == 0">
                and zibiaolb = 0
            </if>
        </where>
        ORDER BY zibiaodm ASC
    </select>

</mapper>