<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.appraisal.mapper.JxkhZibiaoZjzbtjMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.appraisal.bean.JxkhZibiaoZjzbtj">
        <id column="ID" jdbcType="CHAR" property="id"/>
        <result column="KeShiDM" jdbcType="VARCHAR" property="keshidm"/>
        <result column="JiGouDM" jdbcType="VARCHAR" property="jigoudm"/>
        <result column="ShenFenZH" jdbcType="VARCHAR" property="shenfenzh"/>
        <result column="ZiBiaodm" jdbcType="CHAR" property="zibiaodm"/>
        <result column="YeWuRQ" jdbcType="DATE" property="yewurq"/>
        <result column="ZiBiaoZ" jdbcType="DECIMAL" property="zibiaoz"/>
        <result column="TongJiRQ" jdbcType="TIMESTAMP" property="tongjirq"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID,JiGouDM,KeShiDM,ShenFenZH,ZiBiaodm,YeWuRQ,ZiBiaoZ,TongJiRQ
    </sql>
    <select id="getPageListByObj" parameterType="com.jiuzhekan.appraisal.bean.JxkhZibiaoZjzbtj"
            resultMap="BaseResultMap">
        SELECT ID,JiGouDM,KeShiDM,ShenFenZH,ZiBiaodm,YeWuRQ,ZiBiaoZ,TongJiRQ
        from jxkh_zibiao_zjzbtj
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getPageListByPerformanceAppraisalRe"
            parameterType="com.jiuzhekan.appraisal.bean.dao.PerformanceAppraisalDao" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jxkh_zibiao_zjzbtj
        <where>
            <if test="insCodes != null and insCodes.length > 0">
                and JiGouDM in
                <foreach collection="insCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="zibiaodms != null and zibiaodms.length > 0">
                and JiGouDM in
                <foreach collection="zibiaodms" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="yeWuRQStart != null  ">
                and YeWuRQ >= #{yeWuRQStart}
            </if>
            <if test="yeWuRQEnd != null  ">
                and YeWuRQ &lt;= #{yeWuRQEnd}
            </if>
        </where>
        group by JiGouDM
    </select>
    <select id="getMZBPageDatas" parameterType="com.jiuzhekan.appraisal.bean.dao.PerformanceAppraisalDao"
            resultMap="BaseResultMap">
        select
        JiGouDM,KeShiDM,ZiBiaodm,ZiBiaoZ
        from jxkh_zibiao_zjzbtj
        <where>
            KeShiDM != '0'
            <if test="insCodes != null and insCodes.length > 0">
                and JiGouDM in
                <foreach collection="insCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="yeWuRQStart != null  ">
                and YeWuRQ >= #{yeWuRQStart}
            </if>
            <if test="yeWuRQEnd != null  ">
                and YeWuRQ &lt;= #{yeWuRQEnd}
            </if>
        </where>
    </select>
    <!-- 查全院的指标 -->
    <select id="getMZBPageDatasOnlyINS" parameterType="com.jiuzhekan.appraisal.bean.dao.PerformanceAppraisalDao"
            resultMap="BaseResultMap">
        SELECT
        a.ID,
        a.JiGouDM,
        a.KeShiDM,
        a.ZiBiaodm,
        a.ZiBiaoZ,
        a.YeWuRQ
        FROM
        jxkh_zibiao_zjzbtj a
        JOIN (
        SELECT
        ZiBiaodm,
        MAX(YeWuRQ) AS max_date
        FROM
        jxkh_zibiao_zjzbtj
        WHERE

        KeShiDM = '0'
        <if test="insCodes != null and insCodes.length > 0">
            and JiGouDM in
            <foreach collection="insCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.length > 0">
            and KeShiDM in
            <foreach collection="deptIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        JiGouDM,ZiBiaodm
        ) b ON a.ZiBiaodm = b.ZiBiaodm AND a.YeWuRQ = b.max_date
        WHERE
        a.KeShiDM = '0'
    </select>
    <select id="getZZBDeptPageDatas" parameterType="com.jiuzhekan.appraisal.bean.dto.PerformanceAppraisalRe"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jxkh_zibiao_zjzbtj
        <where>
            <if test="insCode != null and insCode!='' ">
                and JiGouDM = #{insCode}
            </if>
            <if test="zibiaodm != null and zibiaodm!='' ">
                and ZiBiaodm = #{zibiaodm}
            </if>
            <if test="yeWuRQStart != null ">
                and YeWuRQ >= #{yeWuRQStart}
            </if>
            <if test="yeWuRQEnd != null ">
                and YeWuRQ &lt;= #{yeWuRQEnd}
            </if>
        </where>
        group by JiGouDM,KeShiDM
    </select>

</mapper>