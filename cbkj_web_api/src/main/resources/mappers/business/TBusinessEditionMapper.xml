<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TBusinessEditionMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TBusinessEdition">
        <id column="id" jdbcType="VARCHAR"  property="id" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
        <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
        <result column="delete_user_id" jdbcType="VARCHAR" property="deleteUserId" />
        <result column="edition_num" jdbcType="VARCHAR" property="editionNum" />
        <result column="edition_content" jdbcType="VARCHAR" property="editionContent" />
        <result column="edition_time" jdbcType="TIMESTAMP" property="editionTime" />
        <result column="is_del" jdbcType="TINYINT" property="isDel" />
        <result column="edition_type" jdbcType="TINYINT" property="editionType" />
    </resultMap>

    <sql id="Base_Column_List">
    id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,edition_num,edition_content,edition_time,is_del,edition_type
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TBusinessEdition">
        delete from t_business_edition where id = #{ id }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_business_edition where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TBusinessEdition">
        insert into t_business_edition (id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,edition_num,edition_content,edition_time,is_del,edition_type) values
        (#{id},#{createTime},#{createUserId},#{createUserName},#{updateTime},#{updateUserId},#{deleteTime},#{deleteUserId},#{editionNum},#{editionContent},#{editionTime},#{isDel},#{editionType})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_business_edition (id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,edition_num,edition_content,edition_time,is_del,edition_type) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.createTime},#{item.createUserId},#{item.createUserName},#{item.updateTime},#{item.updateUserId},#{item.deleteTime},#{item.deleteUserId},#{item.editionNum},#{item.editionContent},#{item.editionTime},#{item.isDel},#{item.editionType})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TBusinessEdition">
        update t_business_edition
        <set>
             <if test="createTime != null">
                create_time = #{ createTime },
             </if>
             <if test="createUserId != null">
                create_user_id = #{ createUserId },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
             <if test="updateTime != null">
                update_time = #{ updateTime },
             </if>
             <if test="updateUserId != null">
                update_user_id = #{ updateUserId },
             </if>
             <if test="deleteTime != null">
                delete_time = #{ deleteTime },
             </if>
             <if test="deleteUserId != null">
                delete_user_id = #{ deleteUserId },
             </if>
             <if test="editionNum != null">
                edition_num = #{ editionNum },
             </if>
             <if test="editionContent != null">
                edition_content = #{ editionContent },
             </if>
             <if test="editionTime != null">
                edition_time = #{ editionTime },
             </if>
             <if test="isDel != null">
                is_del = #{ isDel },
             </if>
            <if test="editionType != null">
                edition_type = #{editionType},
            </if>
        </set>
        where id = #{ id }
    </update>


    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_business_edition where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_business_edition where id = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_business_edition where id = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TBusinessEdition" resultType="Map">
        SELECT id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,edition_num,edition_content,edition_time,is_del,edition_type  from t_business_edition
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TBusinessEdition" resultMap="BaseResultMap">
        SELECT id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,edition_num,edition_content,edition_time,is_del,edition_type
        from t_business_edition
        <where>
            <if test="null != isDel">
                and is_del = #{isDel}
            </if>
            <if test="null != editionType">
                and edition_type = #{editionType}
            </if>
        </where>
        order by edition_time desc,create_time desc
    </select>

    <update id="deleteByEditionS" parameterType="list">
        UPDATE t_business_edition
        SET is_del = #{edtion.isDel},
        delete_time = #{edtion.deleteTime},
        delete_user_id = #{edtion.deleteUserId}
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="unreadEdition" parameterType="String" resultMap="BaseResultMap">
        select e.id, e.edition_num, e.edition_content, e.edition_time,e.edition_type
        from t_business_edition e
        where e.is_del = '0' AND not exists(select 1 from t_business_edition_read r where r.edition_id = e.id and r.admin_id = #{adminId})
        order by e.create_time desc
    </select>

</mapper>