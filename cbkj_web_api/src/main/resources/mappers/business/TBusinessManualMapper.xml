<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TBusinessManualMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TBusinessManual">
        <id column="id" jdbcType="VARCHAR"  property="id" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
        <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
        <result column="delete_user_id" jdbcType="VARCHAR" property="deleteUserId" />
        <result column="manual_num" jdbcType="INTEGER" property="manualNum" />
        <result column="manual_title" jdbcType="VARCHAR" property="manualTitle" />
        <result column="manual_down_times" jdbcType="INTEGER" property="manualDownTimes" />
        <result column="manual_state" jdbcType="TINYINT" property="manualState" />
        <result column="is_del" jdbcType="TINYINT" property="isDel" />
    </resultMap>


    <sql id="Base_Column_List">
    id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,manual_num,manual_title,manual_down_times,manual_state,is_del
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TBusinessManual">
        delete from t_business_manual where id = #{ id }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_business_manual where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TBusinessManual">
        insert into t_business_manual (id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,manual_num,manual_title,manual_down_times,manual_state,is_del) values
        (#{id},#{createTime},#{createUserId},#{createUserName},#{updateTime},#{updateUserId},#{deleteTime},#{deleteUserId},#{manualNum},#{manualTitle},#{manualDownTimes},#{manualState},#{isDel})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_business_manual (id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,manual_num,manual_title,manual_down_times,manual_state,is_del) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.createTime},#{item.createUserId},#{item.createUserName},#{item.updateTime},#{item.updateUserId},#{item.deleteTime},#{item.deleteUserId},#{item.manualNum},#{item.manualTitle},#{item.manualDownTimes},#{item.manualState},#{item.isDel})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TBusinessManual">
        update t_business_manual
        <set>
             <if test="createTime != null">
                create_time = #{ createTime },
             </if>
             <if test="createUserId != null">
                create_user_id = #{ createUserId },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
             <if test="updateTime != null">
                update_time = #{ updateTime },
             </if>
             <if test="updateUserId != null">
                update_user_id = #{ updateUserId },
             </if>
             <if test="deleteTime != null">
                delete_time = #{ deleteTime },
             </if>
             <if test="deleteUserId != null">
                delete_user_id = #{ deleteUserId },
             </if>
             <if test="manualNum != null">
                manual_num = #{ manualNum },
             </if>
             <if test="manualTitle != null">
                manual_title = #{ manualTitle },
             </if>
             <if test="manualDownTimes != null">
                manual_down_times = #{ manualDownTimes },
             </if>
             <if test="manualState != null">
                manual_state = #{ manualState },
             </if>
             <if test="isDel != null">
                is_del = #{ isDel },
             </if>
        </set>
        where id = #{ id }
    </update>


    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_business_manual where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_business_manual where id = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_business_manual where id = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TBusinessManual" resultType="Map">
        SELECT id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,manual_num,manual_title,manual_down_times,manual_state,is_del  from t_business_manual
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TBusinessManual" resultMap="BaseResultMap">
        SELECT id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,manual_num,manual_title,manual_down_times,manual_state,is_del
        from t_business_manual
        <where>
            <if test=" isDel != null ">
                is_del = #{isDel}
            </if>
            <if test=" manualState != null ">
                and manual_state = #{manualState}
            </if>
        </where>
        order  by create_time desc
    </select>

    <update id="deleteByManualList" parameterType="list">
        UPDATE t_business_manual
        SET is_del = #{manual.isDel},
        delete_time = #{manual.deleteTime},
        delete_user_id = #{manual.deleteUserId}
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateDownTimes" parameterType="String">
        update t_business_manual set manual_down_times = manual_down_times +1 WHERE
            id = #{id};
    </update>

</mapper>