<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TBusinessProposalMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TBusinessProposal">
        <id column="id" jdbcType="VARCHAR"  property="id" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
        <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
        <result column="delete_user_id" jdbcType="VARCHAR" property="deleteUserId" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="proposal_num" jdbcType="INTEGER" property="proposalNum" />
        <result column="proposal_title" jdbcType="VARCHAR" property="proposalTitle" />
        <result column="proposal_content" jdbcType="VARCHAR" property="proposalContent" />
        <result column="proposal_liaison" jdbcType="VARCHAR" property="proposalLiaison" />
        <result column="proposal_open_state" jdbcType="TINYINT" property="proposalOpenState" />
        <result column="proposal_receive_state" jdbcType="TINYINT" property="proposalReceiveState" />
        <result column="proposal_receive_opinion" jdbcType="VARCHAR" property="proposalReceiveOpinion" />
        <result column="proposal_receive_id" jdbcType="VARCHAR" property="proposalReceiveId" />
        <result column="proposal_receive_name" jdbcType="VARCHAR" property="proposalReceiveName" />
        <result column="is_del" jdbcType="TINYINT" property="isDel" />
        <result column="EMPLOYEE_ID" jdbcType="VARCHAR" property="employeeId" />
        <result column="phone" jdbcType="VARCHAR" property="phone" />
    </resultMap>


    <sql id="Base_Column_List">
    id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,app_id,app_name,ins_code,ins_name,proposal_num,proposal_title,proposal_content,proposal_liaison,proposal_open_state,proposal_receive_state,proposal_receive_opinion,proposal_receive_id,proposal_receive_name,is_del
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TBusinessProposal">
        delete from t_business_proposal where id = #{ id }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_business_proposal where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TBusinessProposal">
        insert into t_business_proposal (id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,app_id,app_name,ins_code,ins_name,proposal_num,proposal_title,proposal_content,proposal_liaison,proposal_open_state,proposal_receive_state,proposal_receive_opinion,proposal_receive_id,proposal_receive_name,is_del) values
        (#{id},#{createTime},#{createUserId},#{createUserName},#{updateTime},#{updateUserId},#{deleteTime},#{deleteUserId},#{appId},#{appName},#{insCode},#{insName},#{proposalNum},#{proposalTitle},#{proposalContent},#{proposalLiaison},#{proposalOpenState},#{proposalReceiveState},#{proposalReceiveOpinion},#{proposalReceiveId},#{proposalReceiveName},#{isDel})
    </insert>
    <insert id="insertProposal"  parameterType="TBusinessProposal">
        insert into t_business_proposal (id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,app_id,app_name
        ,ins_name ,ins_code ,proposal_title,proposal_content,proposal_liaison,proposal_open_state,proposal_receive_state,proposal_receive_opinion,proposal_receive_id,proposal_receive_name,is_del) values
        (#{id},#{createTime},#{createUserId},#{createUserName},#{updateTime},#{updateUserId},#{deleteTime},#{deleteUserId},#{appId},#{appName},#{insCode}
        ,#{insName},#{proposalTitle},#{proposalContent},#{proposalLiaison},#{proposalOpenState},#{proposalReceiveState},#{proposalReceiveOpinion},#{proposalReceiveId},#{proposalReceiveName},#{isDel})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_business_proposal (id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,app_id,app_name,ins_code,ins_name,proposal_num,proposal_title,proposal_content,proposal_liaison,proposal_open_state,proposal_receive_state,proposal_receive_opinion,proposal_receive_id,proposal_receive_name,is_del) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.createTime},#{item.createUserId},#{item.createUserName},#{item.updateTime},#{item.updateUserId},#{item.deleteTime},#{item.deleteUserId},#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.proposalNum},#{item.proposalTitle},#{item.proposalContent},#{item.proposalLiaison},#{item.proposalOpenState},#{item.proposalReceiveState},#{item.proposalReceiveOpinion},#{item.proposalReceiveId},#{item.proposalReceiveName},#{item.isDel})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TBusinessProposal">
        update t_business_proposal
        <set>
             <if test="createTime != null">
                create_time = #{ createTime },
             </if>
             <if test="createUserId != null">
                create_user_id = #{ createUserId },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
             <if test="updateTime != null">
                update_time = #{ updateTime },
             </if>
             <if test="updateUserId != null">
                update_user_id = #{ updateUserId },
             </if>
             <if test="deleteTime != null">
                delete_time = #{ deleteTime },
             </if>
             <if test="deleteUserId != null">
                delete_user_id = #{ deleteUserId },
             </if>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="proposalNum != null">
                proposal_num = #{ proposalNum },
             </if>
             <if test="proposalTitle != null">
                proposal_title = #{ proposalTitle },
             </if>
             <if test="proposalContent != null">
                proposal_content = #{ proposalContent },
             </if>
             <if test="proposalLiaison != null">
                proposal_liaison = #{ proposalLiaison },
             </if>
             <if test="proposalOpenState != null">
                proposal_open_state = #{ proposalOpenState },
             </if>
             <if test="proposalReceiveState != null">
                proposal_receive_state = #{ proposalReceiveState },
             </if>
             <if test="proposalReceiveOpinion != null">
                proposal_receive_opinion = #{ proposalReceiveOpinion },
             </if>
             <if test="proposalReceiveId != null">
                proposal_receive_id = #{ proposalReceiveId },
             </if>
             <if test="proposalReceiveName != null">
                proposal_receive_name = #{ proposalReceiveName },
             </if>
             <if test="isDel != null">
                is_del = #{ isDel },
             </if>
        </set>
        where id = #{ id }
    </update>


    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_business_proposal where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_business_proposal where id = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        SELECT
            bp.id,bp.create_time,bp.create_user_id,bp.create_user_name,bp.update_time,bp.update_user_id,bp.delete_time,bp.delete_user_id,
            bp.app_id,bp.app_name,bp.ins_code,bp.ins_name,bp.proposal_num,bp.proposal_title,bp.proposal_content,bp.proposal_liaison,
            bp.proposal_open_state,bp.proposal_receive_state,bp.proposal_receive_opinion,bp.proposal_receive_id,bp.proposal_receive_name,
            bp.is_del,bp.create_user_id
        FROM t_business_proposal AS bp
        WHERE bp.id = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TBusinessProposal" resultType="Map">
        SELECT id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,app_id,app_name,ins_code,ins_name,proposal_num,proposal_title,proposal_content,proposal_liaison,proposal_open_state,proposal_receive_state,proposal_receive_opinion,proposal_receive_id,proposal_receive_name,is_del  from t_business_proposal
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TBusinessProposal" resultMap="BaseResultMap">
        SELECT id,create_time,create_user_id,create_user_name,update_time,update_user_id,delete_time,delete_user_id,app_id,app_name,ins_code,ins_name
        ,proposal_num,proposal_title,proposal_content,proposal_liaison,proposal_open_state,proposal_receive_state,proposal_receive_opinion,proposal_receive_id
        ,proposal_receive_name,is_del
        from t_business_proposal
        <where>
            <if test="proposalReceiveState != null">
                and proposal_receive_state = #{proposalReceiveState}
            </if>
            <if test="isDel != null">
                and is_del = #{isDel}
            </if>
            <if test="createUserId != null and createUserId != ''">
                and create_user_id = #{createUserId}
            </if>
        </where>
        order by  proposal_num DESC
    </select>

    <!-- 根据主键批量作废数据 -->
    <update id="deleteByProArr" parameterType="list">
        UPDATE t_business_proposal
        SET is_del = #{pro.isDel},
        delete_time = #{pro.deleteTime},
        delete_user_id = #{pro.deleteUserId}
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>