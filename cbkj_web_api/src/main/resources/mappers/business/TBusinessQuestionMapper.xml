<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TBusinessQuestionMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TBusinessQuestion">
        <id column="id" jdbcType="VARCHAR"  property="id" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
        <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
        <result column="delete_user_id" jdbcType="VARCHAR" property="deleteUserId" />
        <result column="question_num" jdbcType="INTEGER" property="questionNum" />
        <result column="question_title" jdbcType="VARCHAR" property="questionTitle" />
        <result column="question_answer" jdbcType="VARCHAR" property="questionAnswer" />
        <result column="question_key" jdbcType="VARCHAR" property="questionKey" />
        <result column="question_see_times" jdbcType="INTEGER" property="questionSeeTimes" />
        <result column="question_praise_times" jdbcType="INTEGER" property="questionPraiseTimes" />
        <result column="question_state" jdbcType="TINYINT" property="questionState" />
        <result column="is_del" jdbcType="TINYINT" property="isDel" />
    </resultMap>


    <sql id="Base_Column_List">
    id,create_time,create_user_id,update_time,update_user_id,delete_time,delete_user_id,question_num,question_title,question_answer,question_key,question_see_times,question_praise_times,question_state,is_del
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TBusinessQuestion">
        delete from t_business_question where id = #{ id }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_business_question where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TBusinessQuestion">
        insert into t_business_question (id,create_time,create_user_id,update_time,update_user_id,delete_time,delete_user_id,question_num,question_title,question_answer,question_key,question_see_times,question_praise_times,question_state,is_del) values
        (#{id},#{createTime},#{createUserId},#{updateTime},#{updateUserId},#{deleteTime},#{deleteUserId},#{questionNum},#{questionTitle},#{questionAnswer},#{questionKey},#{questionSeeTimes},#{questionPraiseTimes},#{questionState},#{isDel})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_business_question (id,create_time,create_user_id,update_time,update_user_id,delete_time,delete_user_id,question_num,question_title,question_answer,question_key,question_see_times,question_praise_times,question_state,is_del) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.createTime},#{item.createUserId},#{item.updateTime},#{item.updateUserId},#{item.deleteTime},#{item.deleteUserId},#{item.questionNum},#{item.questionTitle},#{item.questionAnswer},#{item.questionKey},#{item.questionSeeTimes},#{item.questionPraiseTimes},#{item.questionState},#{item.isDel})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TBusinessQuestion">
        update t_business_question
        <set>
             <if test="createTime != null">
                create_time = #{ createTime },
             </if>
             <if test="createUserId != null">
                create_user_id = #{ createUserId },
             </if>
             <if test="updateTime != null">
                update_time = #{ updateTime },
             </if>
             <if test="updateUserId != null">
                update_user_id = #{ updateUserId },
             </if>
             <if test="deleteTime != null">
                delete_time = #{ deleteTime },
             </if>
             <if test="deleteUserId != null">
                delete_user_id = #{ deleteUserId },
             </if>
             <if test="questionNum != null">
                question_num = #{ questionNum },
             </if>
             <if test="questionTitle != null">
                question_title = #{ questionTitle },
             </if>
             <if test="questionAnswer != null">
                question_answer = #{ questionAnswer },
             </if>
             <if test="questionKey != null">
                question_key = #{ questionKey },
             </if>
             <if test="questionSeeTimes != null">
                question_see_times = #{ questionSeeTimes },
             </if>
             <if test="questionPraiseTimes != null">
                question_praise_times = #{ questionPraiseTimes },
             </if>
             <if test="questionState != null">
                question_state = #{ questionState },
             </if>
             <if test="isDel != null">
                is_del = #{ isDel },
             </if>
        </set>
        where id = #{ id }
    </update>


    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_business_question where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_business_question where id = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_business_question where id = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TBusinessQuestion" resultType="Map">
        SELECT id,create_time,create_user_id,update_time,update_user_id,delete_time,delete_user_id,question_num,question_title,question_answer,question_key,question_see_times,question_praise_times,question_state,is_del  from t_business_question
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TBusinessQuestion" resultMap="BaseResultMap">
        SELECT q.id,q.create_time,q.create_user_id,q.update_time,q.update_user_id,q.delete_time,q.delete_user_id,q.question_num
        ,q.question_title,q.question_answer,q.question_key,q.question_see_times,q.question_state,
        IFNULL(a.coun,0) as question_praise_times
        from t_business_question q LEFT JOIN (
        SELECT COUNT(1) as coun,question_id from t_business_question_ex where helpful='0'
        GROUP BY question_id
        ) a ON q.id=a.question_id
        <where>
            q.is_del = '0'
            <if test=" questionState != null ">
                and question_state = #{questionState}
            </if>
            <if test=" questionKey != null and questionKey!='' ">
                and (q.question_key like CONCAT('%',trim(#{questionKey}),'%')
                or q.question_answer like CONCAT('%',trim(#{questionKey}),'%')
                or q.question_title like CONCAT('%',trim(#{questionKey}),'%'))
            </if>
        </where>
        order by create_time desc
    </select>

    <update id="deleteByQuestionS" parameterType="list">
        UPDATE t_business_question
        SET is_del = #{obj.isDel},
        delete_time = #{obj.deleteTime},
        delete_user_id = #{obj.deleteUserId}
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!-- 用于新增/修改 问题扩展表,记录是否点赞 -->
    <insert id="insertUpdateEx" parameterType="TBusinessQuestionEx">
        INSERT INTO `t_business_question_ex` (
            `user_id`,
            `question_id`,
            `helpful`,
            `create_time`,
            `update_time`,
            `delete_time`,
            `is_del`
        )
        VALUES
            (
                #{userId},
                #{questionId},
                #{helpful},
                 now(),
                 now(),
                #{deleteTime},
                #{isDel}
            ) ON DUPLICATE KEY UPDATE helpful = #{helpful},
	update_time = now();
    </insert>

    <select id="findObj" parameterType="TBusinessQuestionEx" resultType="TBusinessQuestion">
        SELECT
            q.id,q.question_num questionNum,q.question_title questionTitle,q.question_answer questionAnswer,
            q.question_key questionKey,q.question_see_times questionSeeTimes,
            q.question_state questionState,IFNULL(e.helpful,'1') helpful,q.create_time createTime,
            q.create_user_id createUserId, q.update_time updateTime,q.update_user_id updateUserId,
            a.coun questionPraiseTimes
            FROM t_business_question AS q
			LEFT JOIN (
                SELECT COUNT(1) as coun,question_id from t_business_question_ex where helpful='0'
                GROUP BY question_id
            ) a ON q.id=a.question_id
            LEFT JOIN t_business_question_ex e on q.id = e.question_id and e.is_del = '0' and e.user_id = #{userId}
            WHERE
            q.id = #{questionId} AND q.is_del = '0'

    </select>

    <update id="updateSeeTimes" parameterType="TBusinessQuestionEx">
        update t_business_question set question_see_times = question_see_times +1 WHERE
            id = #{questionId}
    </update>
</mapper>