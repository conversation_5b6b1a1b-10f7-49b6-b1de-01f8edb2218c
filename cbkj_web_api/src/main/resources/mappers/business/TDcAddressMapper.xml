<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.patients.TDcAddressMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.patients.TDcAddress">
        <id column="DC_ADDRESS_ID" jdbcType="VARCHAR"  property="dcAddressId" />
        <result column="PATIENT_ID" jdbcType="VARCHAR" property="patientId" />
        <result column="DC_NAME" jdbcType="VARCHAR" property="dcName" />
        <result column="DC_MOBILE" jdbcType="VARCHAR" property="dcMobile" />
        <result column="DC_ADDRESS" jdbcType="VARCHAR" property="dcAddress" />
        <result column="DC_COUNTY" jdbcType="VARCHAR" property="dcCounty" />
        <result column="DC_TOWN" jdbcType="VARCHAR" property="dcTown" />
        <result column="DC_VILLAGE" jdbcType="VARCHAR" property="dcVillage" />
        <result column="DC_STREET" jdbcType="VARCHAR" property="dcStreet" />

        <result column="DC_COUNTY_CODE" jdbcType="VARCHAR" property="dcCountyCode" />
        <result column="DC_TOWN_CODE" jdbcType="VARCHAR" property="dcTownCode" />
        <result column="DC_VILLAGE_CODE" jdbcType="VARCHAR" property="dcVillageCode" />
        <result column="DC_STREET_CODE" jdbcType="VARCHAR" property="dcStreetCode" />

        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="CREATE_USERNAME" jdbcType="VARCHAR" property="createUsername" />
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
        <result column="UPDATE_USERNAME" jdbcType="VARCHAR" property="updateUsername" />
        <result column="DEL_DATE" jdbcType="TIMESTAMP" property="delDate" />
        <result column="DEL_USER" jdbcType="VARCHAR" property="delUser" />
        <result column="DEL_USERNAME" jdbcType="VARCHAR" property="delUsername" />
        <result column="IS_DEL" jdbcType="VARCHAR" property="isDel" />
    </resultMap>


    <sql id="Base_Column_List">
        DC_ADDRESS_ID,PATIENT_ID,DC_NAME,DC_MOBILE,DC_ADDRESS,DC_COUNTY,DC_TOWN,DC_VILLAGE,DC_STREET,DC_COUNTY_CODE,DC_TOWN_CODE,DC_VILLAGE_CODE,DC_STREET_CODE
    ,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.business.patients.TDcAddress">
        delete from t_dc_address where DC_ADDRESS_ID = #{ dcAddressId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_dc_address where DC_ADDRESS_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="com.jiuzhekan.cbkj.beans.business.patients.TDcAddress">
        insert into t_dc_address (DC_ADDRESS_ID,PATIENT_ID,DC_NAME,DC_MOBILE,DC_ADDRESS,DC_COUNTY,DC_TOWN,DC_VILLAGE,DC_STREET,DC_COUNTY_CODE,DC_TOWN_CODE,DC_VILLAGE_CODE,DC_STREET_CODE
                                 ,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL) values
            (#{dcAddressId},#{patientId},#{dcName},#{dcMobile},#{dcAddress},#{dcCounty},#{dcTown},#{dcVillage},#{dcStreet},#{dcCountyCode},#{dcTownCode},#{dcVillageCode},#{dcStreetCode}
            ,#{createDate},#{createUser},#{createUsername},#{updateDate},#{updateUser},#{updateUsername},#{delDate},#{delUser},#{delUsername},#{isDel})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_dc_address (DC_ADDRESS_ID,PATIENT_ID,DC_NAME,DC_MOBILE,DC_ADDRESS,DC_COUNTY,DC_TOWN,DC_VILLAGE,DC_STREET,
        DC_COUNTY_CODE,DC_TOWN_CODE,DC_VILLAGE_CODE,DC_STREET_CODE,CREATE_DATE,CREATE_USER,CREATE_USERNAME,
        UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.dcAddressId},#{item.patientId},#{item.dcName},#{item.dcMobile},#{item.dcAddress}
            ,#{item.dcCounty},#{item.dcTown},#{item.dcVillage},#{item.dcStreet}
            ,#{item.dcCountyCode},#{item.dcTownCode},#{item.dcVillageCode},#{item.dcStreetCode}, #{item.createDate},#{item.createUser}
            ,#{item.createUsername},#{item.updateDate},#{item.updateUser},#{item.updateUsername},#{item.delDate}
            ,#{item.delUser},#{item.delUsername},#{item.isDel})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.business.patients.TDcAddress">
        update t_dc_address
        <set>
            <if test="patientId != null">
                PATIENT_ID = #{ patientId },
            </if>
            <if test="dcName != null">
                DC_NAME = #{ dcName },
            </if>
            <if test="dcMobile != null">
                DC_MOBILE = #{ dcMobile },
            </if>
            <if test="dcAddress != null">
                DC_ADDRESS = #{ dcAddress },
            </if>
            <if test="dcCounty != null">
                DC_COUNTY = #{ dcCounty },
            </if>
            <if test="dcTown != null">
                DC_TOWN = #{ dcTown },
            </if>
            <if test="dcVillage != null">
                DC_VILLAGE = #{ dcVillage },
            </if>
            <if test="dcStreet != null">
                DC_STREET = #{ dcStreet },
            </if>
            <if test="dcCountyCode != null">
                DC_COUNTY_CODE = #{ dcCountyCode },
            </if>
            <if test="dcTownCode != null">
                DC_TOWN_CODE = #{ dcTownCode },
            </if>
            <if test="dcVillageCode != null">
                DC_VILLAGE_CODE = #{ dcVillageCode },
            </if>
            <if test="dcStreetCode != null">
                DC_STREET_CODE = #{ dcStreetCode },
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{ createDate },
            </if>
            <if test="createUser != null">
                CREATE_USER = #{ createUser },
            </if>
            <if test="createUsername != null">
                CREATE_USERNAME = #{ createUsername },
            </if>
            <if test="updateDate != null">
                UPDATE_DATE = #{ updateDate },
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{ updateUser },
            </if>
            <if test="updateUsername != null">
                UPDATE_USERNAME = #{ updateUsername },
            </if>
            <if test="delDate != null">
                DEL_DATE = #{ delDate },
            </if>
            <if test="delUser != null">
                DEL_USER = #{ delUser },
            </if>
            <if test="delUsername != null">
                DEL_USERNAME = #{ delUsername },
            </if>
            <if test="isDel != null">
                IS_DEL = #{ isDel },
            </if>
        </set>
        where DC_ADDRESS_ID = #{ dcAddressId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update t_dc_address set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_dc_address where DC_ADDRESS_ID = #{id}
    </select>

    <select id="getDcByStreetAndAddress" resultMap="BaseResultMap" parameterType="com.jiuzhekan.cbkj.beans.business.patients.TDcAddress">
        select <include refid="Base_Column_List" />
        from t_dc_address
        <where>
            is_del = '0'
            <if test="patientId != null and patientId!='' ">
                and PATIENT_ID = #{patientId}
            </if>
            <if test="dcName != null and dcName!='' ">
                and DC_NAME = #{dcName}
            </if>
            <if test="dcMobile != null and dcMobile!='' ">
                and DC_MOBILE = #{dcMobile}
            </if>
            <if test="dcCountyCode != null and dcCountyCode!='' ">
                and DC_COUNTY_CODE = #{dcCountyCode}
            </if>

            <if test="dcTownCode != null and dcTownCode!='' ">
                and DC_TOWN_CODE = #{dcTownCode}
            </if>

            <if test="dcVillageCode != null and dcVillageCode!='' ">
                and DC_VILLAGE_CODE = #{dcVillageCode}
            </if>

            <if test="dcStreetCode != null and dcStreetCode!='' ">
                and DC_STREET_CODE = #{dcStreetCode}
            </if>
            <if test="dcAddress != null and dcAddress!='' ">
                and DC_ADDRESS = #{dcAddress}
            </if>
        </where>
        limit 1
    </select>

    <select id="getLastAddressByPatientId" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_dc_address
        where is_del = '0' and PATIENT_ID = #{patientId}
        order by CREATE_DATE desc
        limit 1
    </select>
    <select id="getLastAddressByPatientPre" resultMap="BaseResultMap" parameterType="String">
        SELECT dc.*
        FROM t_dc_address dc
        WHERE dc.`DC_ADDRESS_ID` = (
            SELECT tp.DC_ID FROM t_prescription AS tp WHERE tp.PATIENT_ID = #{patientId} AND tp.DC_TYPE = '1' ORDER BY tp.`PRE_TIME` DESC LIMIT 1 )
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="com.jiuzhekan.cbkj.beans.business.patients.TDcAddress" resultMap="BaseResultMap">
        SELECT DC_ADDRESS_ID,PATIENT_ID,DC_NAME,DC_MOBILE,DC_ADDRESS,DC_COUNTY,DC_TOWN,DC_VILLAGE,DC_STREET,DC_COUNTY_CODE,DC_TOWN_CODE,DC_VILLAGE_CODE,DC_STREET_CODE,CREATE_DATE
             ,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL
        from t_dc_address
        where 1=1
    </select>

</mapper>