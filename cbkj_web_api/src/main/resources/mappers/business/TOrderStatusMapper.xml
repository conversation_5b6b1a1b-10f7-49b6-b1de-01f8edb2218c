<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TOrderStatusMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TOrderStatus">
        <id column="STATUS_ID" jdbcType="VARCHAR"  property="statusId" />
        <result column="REGISTER_ID" jdbcType="VARCHAR" property="registerId" />
        <result column="PRE_ID" jdbcType="VARCHAR" property="preId" />
        <result column="OPERATION_USERID" jdbcType="VARCHAR" property="operationUserid" />
        <result column="OPERATION_USERNAME" jdbcType="VARCHAR" property="operationUsername" />
        <result column="OPERATION_TIME" jdbcType="TIMESTAMP" property="operationTime" />
        <result column="OPERATION_TYPE" jdbcType="INTEGER" property="operationType" />
        <result column="OPERATION_CONTENT" jdbcType="VARCHAR" property="operationContent" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>


    <sql id="Base_Column_List">
    STATUS_ID,REGISTER_ID,PRE_ID,OPERATION_USERID,OPERATION_USERNAME,OPERATION_TIME,OPERATION_TYPE,OPERATION_CONTENT,CREATE_TIME
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TOrderStatus">
        delete from t_order_status where STATUS_ID = #{ statusId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_order_status where STATUS_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TOrderStatus">
        insert into t_order_status (STATUS_ID,REGISTER_ID,PRE_ID,OPERATION_USERID,OPERATION_USERNAME,OPERATION_TIME,OPERATION_TYPE,OPERATION_CONTENT,CREATE_TIME) values
        (#{statusId},#{registerId},#{preId},#{operationUserid},#{operationUsername},#{operationTime},#{operationType},#{operationContent},#{createTime})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_order_status (STATUS_ID,REGISTER_ID,PRE_ID,OPERATION_USERID,OPERATION_USERNAME,OPERATION_TIME,OPERATION_TYPE,OPERATION_CONTENT,CREATE_TIME) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.statusId},#{item.registerId},#{item.preId},#{item.operationUserid},#{item.operationUsername},#{item.operationTime}
            ,#{item.operationType},#{item.operationContent},#{item.createTime})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TOrderStatus">
        update t_order_status
        <set>
             <if test="registerId != null">
                REGISTER_ID = #{ registerId },
             </if>
             <if test="preId != null">
                PRE_ID = #{ preId },
             </if>
             <if test="operationUserid != null">
                OPERATION_USERID = #{ operationUserid },
             </if>
             <if test="operationUsername != null">
                OPERATION_USERNAME = #{ operationUsername },
             </if>
             <if test="operationTime != null">
                OPERATION_TIME = #{ operationTime },
             </if>
             <if test="operationType != null">
                OPERATION_TYPE = #{ operationType },
             </if>
             <if test="operationContent != null">
                OPERATION_CONTENT = #{ operationContent },
             </if>
            <if test="operationContent != null">
                CREATE_TIME = #{ createTime },
            </if>
        </set>
        where STATUS_ID = #{ statusId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update t_order_status set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_order_status where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_order_status where STATUS_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_order_status where STATUS_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TOrderStatus" resultType="Map">
        SELECT STATUS_ID,REGISTER_ID,PRE_ID,OPERATION_USERID,OPERATION_USERNAME,OPERATION_TIME,OPERATION_TYPE,OPERATION_CONTENT,CREATE_TIME  from t_order_status
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TOrderStatus" resultMap="BaseResultMap">
        SELECT STATUS_ID,REGISTER_ID,PRE_ID,OPERATION_USERID,OPERATION_USERNAME,OPERATION_TIME,OPERATION_TYPE,OPERATION_CONTENT,CREATE_TIME
        from t_order_status
        where 1=1
        <!--条件-->
        <if test="preId != null and preId!='' ">
        and PRE_ID = #{preId}
        </if>
        order by CREATE_TIME
    </select>

    <select id="getPageListByObj2" parameterType="TOrderStatus" resultMap="BaseResultMap">
        SELECT STATUS_ID,REGISTER_ID,PRE_ID,OPERATION_USERID,OPERATION_USERNAME,OPERATION_TIME,OPERATION_TYPE,OPERATION_CONTENT,CREATE_TIME
        from t_order_status
        where OPERATION_TYPE>=50
        <!--条件-->
        <if test="preId != null and preId!='' ">
            and PRE_ID = #{preId}
        </if>
        order by CREATE_TIME
    </select>

</mapper>