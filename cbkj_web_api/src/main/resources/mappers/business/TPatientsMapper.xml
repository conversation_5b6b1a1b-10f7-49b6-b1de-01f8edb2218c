<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.patients.TPatientsMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.patients.TPatients">
        <id column="PATIENT_ID" jdbcType="VARCHAR"  property="patientId" />
        <result column="APP_ID" jdbcType="VARCHAR" property="appId" />
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode" />
        <result column="PATIENT_NAME" jdbcType="VARCHAR" property="patientName" />
        <result column="PATIENT_GENDER" jdbcType="VARCHAR" property="patientGender" />
        <result column="PATIENT_BIRTHDAY" jdbcType="DATE" property="patientBirthday" />
        <result column="PATIENT_CERTIFICATE_TYPE" jdbcType="VARCHAR" property="patientCertificateType" />
        <result column="PATIENT_CERTIFICATE" jdbcType="VARCHAR" property="patientCertificate" />
        <result column="PATIENT_MOBILE" jdbcType="VARCHAR" property="patientMobile" />
        <result column="PATIENT_PY" jdbcType="VARCHAR" property="patientPy" />
        <result column="PATIENT_WB" jdbcType="VARCHAR" property="patientWb" />
        <result column="MEDICAL_CARD_NO" jdbcType="VARCHAR" property="medicalCardNo" />
        <result column="ORIGIN_PATIENT_ID" jdbcType="VARCHAR" property="originPatientId" />
        <result column="PATIENT_COUNTY" jdbcType="VARCHAR" property="patientCounty" />
        <result column="PATIENT_TOWN" jdbcType="VARCHAR" property="patientTown" />
        <result column="PATIENT_VILLAGE" jdbcType="VARCHAR" property="patientVillage" />
        <result column="PATIENT_STREET" jdbcType="VARCHAR" property="patientStreet" />
        <result column="PATIENT_ADDRESS" jdbcType="VARCHAR" property="patientAddress" />
        <result column="DC_ADDRESS_ID" jdbcType="VARCHAR" property="dcAddressId" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="CREATE_USERNAME" jdbcType="VARCHAR" property="createUsername" />
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
        <result column="UPDATE_USERNAME" jdbcType="VARCHAR" property="updateUsername" />
        <result column="DEL_DATE" jdbcType="TIMESTAMP" property="delDate" />
        <result column="DEL_USER" jdbcType="VARCHAR" property="delUser" />
        <result column="DEL_USERNAME" jdbcType="VARCHAR" property="delUsername" />
        <result column="IS_DEL" jdbcType="VARCHAR" property="isDel" />
        <result column="PATIENT_COUNTY_CODE" jdbcType="VARCHAR" property="patientCountyCode" />
        <result column="PATIENT_TOWN_CODE" jdbcType="VARCHAR" property="patientTownCode" />
        <result column="PATIENT_VILLAGE_CODE" jdbcType="VARCHAR" property="patientVillageCode" />
        <result column="PATIENT_STREET_CODE" jdbcType="VARCHAR" property="patientStreetCode" />
    </resultMap>


    <sql id="Base_Column_List">
    PATIENT_ID,APP_ID,INS_CODE,PATIENT_NAME,PATIENT_GENDER,PATIENT_BIRTHDAY,PATIENT_CERTIFICATE_TYPE,PATIENT_CERTIFICATE,PATIENT_MOBILE,PATIENT_PY,PATIENT_WB,MEDICAL_CARD_NO,ORIGIN_PATIENT_ID,PATIENT_COUNTY,PATIENT_TOWN,PATIENT_VILLAGE,PATIENT_STREET,PATIENT_ADDRESS,DC_ADDRESS_ID,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPatients">
        delete from t_patients where PATIENT_ID = #{ patientId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_patients where PATIENT_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TPatients" useGeneratedKeys="true" keyProperty="parientId">
      insert into t_patients (PATIENT_ID,APP_ID,INS_CODE,PATIENT_NAME,PATIENT_GENDER,PATIENT_BIRTHDAY,PATIENT_CERTIFICATE_TYPE,PATIENT_CERTIFICATE,PATIENT_MOBILE,PATIENT_PY,PATIENT_WB,MEDICAL_CARD_NO,ORIGIN_PATIENT_ID,PATIENT_COUNTY,PATIENT_TOWN,PATIENT_VILLAGE,PATIENT_STREET,PATIENT_ADDRESS,DC_ADDRESS_ID,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL) values
        (#{patientId},#{appId},#{insCode},#{patientName},#{patientGender},#{patientBirthday},#{patientCertificateType},#{patientCertificate},#{patientMobile},#{patientPy},#{patientWb},#{medicalCardNo},#{originPatientId},#{patientCounty},#{patientTown},#{patientVillage},#{patientStreet},#{patientAddress},#{dcAddressId},#{createDate},#{createUser},#{createUsername},#{updateDate},#{updateUser},#{updateUsername},#{delDate},#{delUser},#{delUsername},#{isDel})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_patients (PATIENT_ID,APP_ID,INS_CODE,PATIENT_NAME,PATIENT_GENDER,PATIENT_BIRTHDAY,PATIENT_CERTIFICATE_TYPE,PATIENT_CERTIFICATE,PATIENT_MOBILE,PATIENT_PY,PATIENT_WB,MEDICAL_CARD_NO,ORIGIN_PATIENT_ID,PATIENT_COUNTY,PATIENT_TOWN,PATIENT_VILLAGE,PATIENT_STREET,PATIENT_ADDRESS,DC_ADDRESS_ID,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.patientId},#{item.appId},#{item.insCode},#{item.patientName},#{item.patientGender},#{item.patientBirthday},#{item.patientCertificateType},#{item.patientCertificate},#{item.patientMobile},#{item.patientPy},#{item.patientWb},#{item.medicalCardNo},#{item.originPatientId},#{item.patientCounty},#{item.patientTown},#{item.patientVillage},#{item.patientStreet},#{item.patientAddress},#{item.dcAddressId},#{item.createDate},#{item.createUser},#{item.createUsername},#{item.updateDate},#{item.updateUser},#{item.updateUsername},#{item.delDate},#{item.delUser},#{item.delUsername},#{item.isDel})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPatients">
        update t_patients
        <set>
            <if test="appId != null">
                APP_ID = #{ appId },
            </if>
            <if test="insCode != null">
                INS_CODE = #{ insCode },
            </if>
            <if test="patientName != null">
                PATIENT_NAME = #{ patientName },
            </if>
            <if test="patientGender != null">
                PATIENT_GENDER = #{ patientGender },
            </if>
            <if test="patientBirthday != null">
                PATIENT_BIRTHDAY = #{ patientBirthday },
            </if>
            <if test="patientCertificateType != null">
                PATIENT_CERTIFICATE_TYPE = #{ patientCertificateType },
            </if>
            <if test="patientCertificate != null">
                PATIENT_CERTIFICATE = #{ patientCertificate },
            </if>
            <if test="patientMobile != null">
                PATIENT_MOBILE = #{ patientMobile },
            </if>
            <if test="patientPy != null">
                PATIENT_PY = #{ patientPy },
            </if>
            <if test="patientWb != null">
                PATIENT_WB = #{ patientWb },
            </if>
                MEDICAL_CARD_NO = #{ medicalCardNo },
            <if test="originPatientId != null">
                ORIGIN_PATIENT_ID = #{ originPatientId },
            </if>
                PATIENT_COUNTY = #{ patientCounty },
                PATIENT_TOWN = #{ patientTown },
                PATIENT_VILLAGE = #{ patientVillage },
                PATIENT_STREET = #{ patientStreet },
                PATIENT_ADDRESS = #{ patientAddress },
            <if test="dcAddressId != null">
                DC_ADDRESS_ID = #{ dcAddressId },
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{ createDate },
            </if>
            <if test="createUser != null">
                CREATE_USER = #{ createUser },
            </if>
            <if test="createUsername != null">
                CREATE_USERNAME = #{ createUsername },
            </if>
            <if test="updateDate != null">
                UPDATE_DATE = #{ updateDate },
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{ updateUser },
            </if>
            <if test="updateUsername != null">
                UPDATE_USERNAME = #{ updateUsername },
            </if>
            <if test="delDate != null">
                DEL_DATE = #{ delDate },
            </if>
            <if test="delUser != null">
                DEL_USER = #{ delUser },
            </if>
            <if test="delUsername != null">
                DEL_USERNAME = #{ delUsername },
            </if>
            <if test="isDel != null">
                IS_DEL = #{ isDel },
            </if>
        </set>
        where PATIENT_ID = #{ patientId }
    </update>


    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        t.PATIENT_ID, t.APP_ID, t.INS_CODE, t.PATIENT_NAME, t.PATIENT_GENDER, t.PATIENT_BIRTHDAY, t.PATIENT_CERTIFICATE_TYPE, t.PATIENT_CERTIFICATE,
        t.PATIENT_MOBILE, t.PATIENT_PY, t.PATIENT_WB, t.MEDICAL_CARD_NO, t.ORIGIN_PATIENT_ID,
        t.PATIENT_COUNTY, t.PATIENT_TOWN, t.PATIENT_VILLAGE, t.PATIENT_STREET, t.PATIENT_ADDRESS, t.DC_ADDRESS_ID,
        d.DC_COUNTY_CODE as PATIENT_COUNTY_CODE,
        d.DC_TOWN_CODE as PATIENT_TOWN_CODE,
        d.DC_VILLAGE_CODE as PATIENT_VILLAGE_CODE,
        d.DC_STREET_CODE as PATIENT_STREET_CODE
        from t_patients t
        left join t_dc_address d on t.DC_ADDRESS_ID = d.DC_ADDRESS_ID
        where t.PATIENT_ID = #{id}
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TPatients" resultMap="BaseResultMap">
        SELECT PATIENT_ID,APP_ID,INS_CODE,PATIENT_NAME,PATIENT_GENDER,PATIENT_BIRTHDAY,PATIENT_CERTIFICATE_TYPE,PATIENT_CERTIFICATE,PATIENT_MOBILE,PATIENT_PY,PATIENT_WB,MEDICAL_CARD_NO,ORIGIN_PATIENT_ID,PATIENT_COUNTY,PATIENT_TOWN,PATIENT_VILLAGE,PATIENT_STREET,PATIENT_ADDRESS,DC_ADDRESS_ID,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL
        from t_patients
        where 1=1
        <!--条件-->
        <!--<if test="patientName != null and patientName!='' ">-->
        <!--and PATIENT_NAME like CONCAT('%',trim(#{patientName}),'%')-->
        <!--</if>-->
    </select>

    <select id="getPatientsCountByPatients" parameterType="TPatients" resultType="int">
        select count(*) from t_patients where 1=1
        <if test="patientCertificate!=null and patientCertificate!=''">
            and PATIENT_CERTIFICATE = #{patientCertificate}
        </if>
        <if test="medicalCardNo!=null and medicalCardNo!=''">
            and MEDICAL_CARD_NO = #{medicalCardNo}
        </if>
        <if test="patientMobile!=null and patientMobile!=''">
            and PATIENT_MOBILE = #{patientMobile}
        </if>
    </select>
    <select id="getPatientsByPatients" parameterType="TPatients" resultMap="BaseResultMap">
        select * from t_patients where 1=1
        <if test="patientCertificate!=null and patientCertificate!=''">
            and PATIENT_CERTIFICATE = #{patientCertificate}
        </if>
        <if test="medicalCardNo!=null and medicalCardNo!=''">
            and MEDICAL_CARD_NO = #{medicalCardNo}
        </if>
        <if test="patientMobile!=null and patientMobile!=''">
            and PATIENT_MOBILE = #{patientMobile}
        </if>
        <if test="patientAddress!=null and patientAddress!=''">
            and PATIENT_ADDRESS = #{patientAddress}
        </if>
        <if test="patientName!=null and patientName!=''">
            and PATIENT_NAME = #{patientName}
        </if>
        <if test="patientGender!=null and patientGender!=''">
            and PATIENT_GENDER = #{patientGender}
        </if>
        <if test="sdateStr!=null">
            and PATIENT_BIRTHDAY = #{sdateStr}
        </if>
        limit 1
    </select>
    <select id="checkPatientsByPatients" parameterType="TPatients" resultMap="BaseResultMap">
        select * from t_patients where 1=1
        <if test="patientCertificate!=null and patientCertificate!=''">
            and PATIENT_CERTIFICATE = #{patientCertificate}
        </if>
        <if test="medicalCardNo!=null and medicalCardNo!=''">
            and MEDICAL_CARD_NO = #{medicalCardNo}
        </if>
        <if test="patientMobile!=null and patientMobile!=''">
            and PATIENT_MOBILE = #{patientMobile}
        </if>
        <if test="patientAddress!=null and patientAddress!=''">
            and PATIENT_ADDRESS = #{patientAddress}
        </if>
        <if test="patientName!=null and patientName!=''">
            and PATIENT_NAME = #{patientName}
        </if>
        <if test="patientGender!=null and patientGender!=''">
            and PATIENT_GENDER = #{patientGender}
        </if>
        <if test="sdateStr!=null">
            and PATIENT_BIRTHDAY = #{sdateStr}
        </if>
        <if test="patientId!=null">
            and PATIENT_ID != #{patientId}
        </if>
        limit 1
    </select>
    <sql id="patientSql">
        SELECT
            p.PATIENT_ID,p.APP_ID,p.INS_CODE,p.PATIENT_NAME,p.PATIENT_GENDER,p.PATIENT_BIRTHDAY,p.PATIENT_CERTIFICATE_TYPE,p.PATIENT_CERTIFICATE,p.PATIENT_MOBILE,
            p.PATIENT_PY,p.PATIENT_WB,p.MEDICAL_CARD_NO,p.ORIGIN_PATIENT_ID,p.PATIENT_COUNTY,p.PATIENT_TOWN,p.PATIENT_VILLAGE,
            p.PATIENT_STREET,p.PATIENT_ADDRESS,p.DC_ADDRESS_ID,p.CREATE_DATE,p.CREATE_USER,p.CREATE_USERNAME,p.UPDATE_DATE,
            p.UPDATE_USER,p.UPDATE_USERNAME,p.DEL_DATE,p.DEL_USER,p.DEL_USERNAME,p.IS_DEL
        FROM
           t_patients AS p
    </sql>
    <sql id="patientWhereSql">
        WHERE
        p.IS_DEL = '0'
        <!--条件-->
        <if test="appId != null and appId!='' ">
            and p.APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and p.INS_CODE = #{insCode}
        </if>
        <choose>
            <when test="patientName != null and patientName!='' ">
                and p.PATIENT_NAME like CONCAT(trim( #{patientName}),'%')
            </when>
            <when test="medicalCardNo != null and medicalCardNo!='' ">
                and p.MEDICAL_CARD_NO like CONCAT('%',trim( #{medicalCardNo}),'%')
            </when>
            <when test="patientMobile != null and patientMobile!='' ">
                and p.PATIENT_MOBILE like CONCAT('%',trim( #{patientMobile}),'%')
            </when>
            <when test="patientCertificate != null and patientCertificate!='' ">
                and p.PATIENT_CERTIFICATE like CONCAT('%',trim( #{patientCertificate}),'%')
            </when>
        </choose>
    </sql>
    <!-- 病人信息查询(0 病人姓名,1 就诊卡号,2 病人手机号,3 病人身份证号) -->
    <select id="getTodayPatients" parameterType="com.jiuzhekan.cbkj.beans.business.patients.VO.TPatientsReqVO" resultMap="BaseResultMap">
        <include refid="patientSql"/>
        <include refid="patientWhereSql"/>
       order by p.CREATE_DATE desc
    </select>
    <select id="getMyPatients" parameterType="com.jiuzhekan.cbkj.beans.business.patients.VO.TPatientsReqVO" resultMap="BaseResultMap">
        select
            p.PATIENT_ID,p.APP_ID,p.INS_CODE,p.PATIENT_NAME,p.PATIENT_GENDER,p.PATIENT_BIRTHDAY,p.PATIENT_CERTIFICATE_TYPE,p.PATIENT_CERTIFICATE,p.PATIENT_MOBILE,
            p.PATIENT_PY,p.PATIENT_WB,p.MEDICAL_CARD_NO,p.ORIGIN_PATIENT_ID,p.PATIENT_COUNTY,p.PATIENT_TOWN,p.PATIENT_VILLAGE,
            p.PATIENT_STREET,p.PATIENT_ADDRESS,p.DC_ADDRESS_ID,p.CREATE_DATE,p.CREATE_USER,p.CREATE_USERNAME,p.UPDATE_DATE,
            p.UPDATE_USER,p.UPDATE_USERNAME,p.DEL_DATE,p.DEL_USER,p.DEL_USERNAME,p.IS_DEL
        from (
            select distinct PATIENT_ID from t_record
            <where>
                <if test="appId != null and appId!='' ">
                    and APP_ID = #{appId}
                </if>
                <if test="insCode != null and insCode!='' ">
                    and INS_CODE = #{insCode}
                </if>
                <if test="docId != null and docId != ''">
                    AND DOC_ID = #{docId}
                </if>
                <!-- 中医病名 建议前端进行约束 【搜索下拉框】 ，这样 disName 就可以去掉 -->
                <if test="disId != null and disId != ''">
                    AND DIS_ID = #{disId}
                </if>
                <if test="disName != null and disName != ''">
                    AND DIS_NAME like concat('%',trim(#{disName}),'%')
                </if>
            </where>
        ) r
        join (
            select * from t_patients where IS_DEL = '0'
                <if test="patientName != null and patientName!='' ">
                    and PATIENT_NAME like CONCAT(trim(#{patientName}),'%')
                </if>
                <if test="medicalCardNo != null and medicalCardNo!='' ">
                    and MEDICAL_CARD_NO like CONCAT('%',trim( #{medicalCardNo}),'%')
                </if>
                <!-- 手机号 可以做成前3位 和 后4位匹配 -->
                <if test="patientMobile != null and patientMobile!='' ">
                    and PATIENT_MOBILE like CONCAT('%',trim( #{patientMobile}),'%')
                </if>
                <if test="patientCertificate != null and patientCertificate!='' ">
                    and PATIENT_CERTIFICATE like CONCAT('%',trim( #{patientCertificate}),'%')
                </if>
        ) p on r.PATIENT_ID=p.PATIENT_ID
        order by p.CREATE_DATE DESC
    </select>
<!--    <select id="getMyPatients" parameterType="com.jiuzhekan.cbkj.beans.business.patients.VO.TPatientsReqVO"-->
<!--            resultMap="BaseResultMap">-->
<!--        <include refid="patientSql"/>-->
<!--        join t_record r on r.PATIENT_ID = p.PATIENT_ID-->
<!--        JOIN t_register regi ON r.REGISTER_ID = regi.REGISTER_ID AND regi.REGISTER_DIAGNOSIS_STATUS IN ('5','8')-->
<!--        <if test="docId != null and docId != ''">-->
<!--            AND r.DOC_ID = #{docId}-->
<!--        </if>-->
<!--        <if test="disId != null and disId != ''">-->
<!--            AND r.DIS_ID = #{disId}-->
<!--        </if>-->
<!--        <if test="disName != null and disName != ''">-->
<!--            AND r.DIS_NAME like concat('%',trim(#{disName}),'%')-->
<!--        </if>-->
<!--        <if test="patientName != null and patientName!='' ">-->
<!--            and p.PATIENT_NAME like CONCAT('%',trim( #{patientName}),'%')-->
<!--        </if>-->
<!--        <if test="medicalCardNo != null and medicalCardNo!='' ">-->
<!--            and p.MEDICAL_CARD_NO like CONCAT('%',trim( #{medicalCardNo}),'%')-->
<!--        </if>-->
<!--        <if test="patientMobile != null and patientMobile!='' ">-->
<!--            and p.PATIENT_MOBILE like CONCAT('%',trim( #{patientMobile}),'%')-->
<!--        </if>-->
<!--        <if test="patientCertificate != null and patientCertificate!='' ">-->
<!--            and p.PATIENT_CERTIFICATE like CONCAT('%',trim( #{patientCertificate}),'%')-->
<!--        </if>-->
<!--        <include refid="patientWhereSql"/>-->
<!--        group by p.CREATE_DATE DESC-->
<!--    </select>-->

    <select id="findByInsCodeAndPatientId" parameterType="String" resultMap="BaseResultMap">
        <include refid="patientSql"/>
        where p.IS_DEL = '0'
        <!--条件-->
        <if test=" patientId  != null and  patientId !='' ">
            and p.PATIENT_ID = #{ patientId }
        </if>
        <if test="insCode != null and insCode!='' ">
            and p.INS_CODE = #{insCode}
        </if>
    </select>

    <!--单个插入-->
    <insert id="insertPatients"  parameterType="TPatients" useGeneratedKeys="true" keyProperty="parientId">
        replace into t_patients (PATIENT_ID,APP_ID,INS_CODE,PATIENT_NAME,PATIENT_GENDER,PATIENT_BIRTHDAY,PATIENT_CERTIFICATE_TYPE,PATIENT_CERTIFICATE,PATIENT_MOBILE,PATIENT_PY,PATIENT_WB,MEDICAL_CARD_NO,ORIGIN_PATIENT_ID,PATIENT_COUNTY,PATIENT_TOWN,PATIENT_VILLAGE,PATIENT_STREET,PATIENT_ADDRESS,DC_ADDRESS_ID,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL) values
        (#{patientId},#{appId},#{insCode},#{patientName},#{patientGender},#{patientBirthday},#{patientCertificateType},#{patientCertificate},#{patientMobile},#{patientPy},#{patientWb},#{medicalCardNo},#{originPatientId},#{patientCounty},#{patientTown},#{patientVillage},#{patientStreet},#{patientAddress},#{dcAddressId},#{createDate},#{createUser},#{createUsername},#{updateDate},#{updateUser},#{updateUsername},#{delDate},#{delUser},#{delUsername},#{isDel})
    </insert>


    <select id="findModelByRegisterId" parameterType="String"
            resultType="com.jiuzhekan.cbkj.beans.business.record.model.SickPersonModel">
        SELECT tp.PATIENT_GENDER as sickPersonSex,tp.PATIENT_BIRTHDAY  as sickPersonBirthDay
        FROM
        t_register tr
        LEFT JOIN t_patients tp on tr.PATIENT_ID=tp.PATIENT_ID
        where is_del = '0'
        <if test="registerId != null and registerId!='' ">
            and REGISTER_ID= #{registerId}
        </if> limit 1
    </select>
    <select id="getOneByMobileAndName" parameterType="TPatients" resultMap="BaseResultMap">

        select * from t_patients where PATIENT_MOBILE=#{patientMobile} and PATIENT_NAME=#{patientName} and IS_DEL='0' order by  limit 1
    </select>
</mapper>