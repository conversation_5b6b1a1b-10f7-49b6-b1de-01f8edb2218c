<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.TPreReferralMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.TPreReferral">
        <id column="pre_referral_id" jdbcType="BIGINT"  property="preReferralId" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId" />
        <result column="doctor_name" jdbcType="VARCHAR" property="doctorName" />
        <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
        <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
        <result column="patient_address" jdbcType="VARCHAR" property="patientAddress" />
        <result column="patient_gender" jdbcType="VARCHAR" property="patientGender" />
        <result column="patient_gender_name" jdbcType="VARCHAR" property="patientGenderName" />
        <result column="patient_nation_name" jdbcType="VARCHAR" property="patientNationName" />
        <result column="patient_nation_code" jdbcType="VARCHAR" property="patientNationCode" />
        <result column="patient_age" jdbcType="VARCHAR" property="patientAge" />
        <result column="patient_certificate" jdbcType="VARCHAR" property="patientCertificate" />
        <result column="patient_occupation_code" jdbcType="VARCHAR" property="patientOccupationCode" />
        <result column="patient_occupation_name" jdbcType="VARCHAR" property="patientOccupationName" />
        <result column="dis_id" jdbcType="VARCHAR" property="disId" />
        <result column="dis_name" jdbcType="VARCHAR" property="disName" />
        <result column="sym_id" jdbcType="VARCHAR" property="symId" />
        <result column="sym_name" jdbcType="VARCHAR" property="symName" />
        <result column="the_code" jdbcType="VARCHAR" property="theCode" />
        <result column="the_name" jdbcType="VARCHAR" property="theName" />
        <result column="referral_result" jdbcType="VARCHAR" property="referralResult" />
        <result column="PATIENT_CONTENT" jdbcType="VARCHAR" property="patientContent" />
        <result column="NOW_DESC" jdbcType="VARCHAR" property="nowDesc" />
        <result column="PAST_DESC" jdbcType="VARCHAR" property="pastDesc" />
        <result column="PHYSICAL" jdbcType="VARCHAR" property="physical" />
        <result column="FOUR_DIAGNOSIS" jdbcType="VARCHAR" property="fourDiagnosis" />
        <result column="AUXILIARY_EXAM" jdbcType="VARCHAR" property="auxiliaryExam" />
        <result column="referral_images" jdbcType="VARCHAR" property="referralImages" />
        <result column="referral_time" jdbcType="TIMESTAMP" property="referralTime" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="western_disease" jdbcType="VARCHAR" property="westernDisease" />
        <result column="western_disease_id" jdbcType="VARCHAR" property="westernDiseaseId" />
        <result column="marital_code" jdbcType="VARCHAR" property="maritalCode" />
        <result column="marital_name" jdbcType="VARCHAR" property="maritalName" />
    </resultMap>


    <sql id="Base_Column_List">
    pre_referral_id,app_id,ins_code,ins_name,doctor_id,doctor_name,patient_id,patient_name,patient_address,patient_gender,patient_nation_name,patient_nation_code,patient_age,patient_certificate,patient_occupation_code,patient_occupation_name,dis_id,dis_name,sym_id,sym_name,the_code,the_name,referral_result,PATIENT_CONTENT,NOW_DESC,PAST_DESC,PHYSICAL,FOUR_DIAGNOSIS,AUXILIARY_EXAM,referral_images,referral_time,dept_id,dept_name,status
    ,patient_gender_name,western_disease,western_disease_id,marital_code,marital_name</sql>

    <delete id="deleteByPrimaryKey" parameterType="TPreReferral">
        delete from t_pre_referral where pre_referral_id = #{ preReferralId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_pre_referral where pre_referral_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TPreReferral">
        insert into t_pre_referral (<include refid="Base_Column_List" />) values
        (#{preReferralId},#{appId},#{insCode},#{insName},#{doctorId},#{doctorName},#{patientId},#{patientName},#{patientAddress},#{patientGender},#{patientNationName},#{patientNationCode},#{patientAge},#{patientCertificate},#{patientOccupationCode},#{patientOccupationName},#{disId},#{disName},#{symId},#{symName},#{theCode},#{theName},#{referralResult},#{patientContent},#{nowDesc},#{pastDesc},#{physical},#{fourDiagnosis},#{auxiliaryExam},#{referralImages},
         #{referralTime},#{deptId},#{deptName},#{status},#{patientGenderName},#{westernDisease},#{westernDiseaseId},#{maritalCode},#{maritalName} )

    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_pre_referral (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.preReferralId},#{item.appId},#{item.insCode},#{item.insName},#{item.doctorId},#{item.doctorName},#{item.patientId},#{item.patientName},
             #{item.patientAddress},#{item.patientGender},#{item.patientNationName},#{item.patientNationCode},#{item.patientAge},#{item.patientCertificate},
             #{item.patientOccupationCode},#{item.patientOccupationName},#{item.disId},#{item.disName},#{item.symId},#{item.symName},#{item.theCode},
             #{item.theName},#{item.referralResult},#{item.patientContent},#{item.nowDesc},#{item.pastDesc},#{item.physical},#{item.fourDiagnosis},#{item.auxiliaryExam},
             #{item.referralImages},#{item.referralTime},#{item.deptId},#{item.deptName},#{item.status},#{item.patientGenderName},
            #{item.westernDisease},#{item.westernDiseaseId},#{item.maritalCode},#{item.maritalName} )
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPreReferral">
        update t_pre_referral
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="maritalCode != null">
                 marital_code = #{maritalCode},
             </if>
             <if test="maritalName != null">
                 marital_name = #{maritalName},
             </if>

            <if test="westernDisease != null">
                western_disease = #{westernDisease},
            </if>

            <if test="westernDiseaseId != null">
                western_disease_id = #{westernDiseaseId},
            </if>

             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="doctorId != null">
                doctor_id = #{ doctorId },
             </if>
             <if test="doctorName != null">
                doctor_name = #{ doctorName },
             </if>
             <if test="patientId != null">
                patient_id = #{ patientId },
             </if>
             <if test="patientName != null">
                patient_name = #{ patientName },
             </if>
             <if test="patientAddress != null">
                patient_address = #{ patientAddress },
             </if>
             <if test="patientGender != null">
                patient_gender = #{ patientGender },
             </if>
             <if test="patientGenderName != null">
                patient_gender_name = #{patientGenderName},
             </if>
             <if test="patientNationName != null">
                patient_nation_name = #{ patientNationName },
             </if>
             <if test="patientNationCode != null">
                patient_nation_code = #{ patientNationCode },
             </if>
             <if test="patientAge != null">
                patient_age = #{ patientAge },
             </if>
             <if test="patientCertificate != null">
                patient_certificate = #{ patientCertificate },
             </if>
             <if test="patientOccupationCode != null">
                patient_occupation_code = #{ patientOccupationCode },
             </if>
             <if test="patientOccupationName != null">
                patient_occupation_name = #{ patientOccupationName },
             </if>
             <if test="disId != null">
                dis_id = #{ disId },
             </if>
             <if test="disName != null">
                dis_name = #{ disName },
             </if>
             <if test="symId != null">
                sym_id = #{ symId },
             </if>
             <if test="symName != null">
                sym_name = #{ symName },
             </if>
             <if test="theCode != null">
                the_code = #{ theCode },
             </if>
             <if test="theName != null">
                the_name = #{ theName },
             </if>
             <if test="referralResult != null">
                referral_result = #{ referralResult },
             </if>
             <if test="patientContent != null">
                PATIENT_CONTENT = #{ patientContent },
             </if>
             <if test="nowDesc != null">
                NOW_DESC = #{ nowDesc },
             </if>
             <if test="pastDesc != null">
                PAST_DESC = #{ pastDesc },
             </if>
             <if test="physical != null">
                PHYSICAL = #{ physical },
             </if>
             <if test="fourDiagnosis != null">
                FOUR_DIAGNOSIS = #{ fourDiagnosis },
             </if>
             <if test="auxiliaryExam != null">
                AUXILIARY_EXAM = #{ auxiliaryExam },
             </if>
             <if test="referralImages != null">
                referral_images = #{ referralImages },
             </if>
             <if test="referralTime != null">
                referral_time = #{ referralTime },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="deptName != null">
                dept_name = #{ deptName },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
        </set>
        where pre_referral_id = #{ preReferralId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_pre_referral where pre_referral_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TPreReferral" resultMap="BaseResultMap">
        SELECT pre_referral_id,app_id,ins_code,ins_name,doctor_id,doctor_name,patient_id,patient_name,patient_address,patient_gender,
               patient_nation_name,patient_nation_code,patient_age,patient_certificate,patient_occupation_code,patient_occupation_name,dis_id,
               dis_name,sym_id,sym_name,the_code,the_name,referral_result,PATIENT_CONTENT,NOW_DESC,PAST_DESC,PHYSICAL,FOUR_DIAGNOSIS,AUXILIARY_EXAM,
               referral_images,referral_time,dept_id,dept_name,status,patient_gender_name,western_disease_id,western_disease,marital_name,
               marital_code
        from t_pre_referral
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getTPreReferralList" resultType="com.jiuzhekan.cbkj.beans.referral.TPreReferralList" parameterType="com.jiuzhekan.cbkj.beans.referral.TPreReferralListQuery">
        select pre_referral_id  preReferralId,
        app_id appId,
        ins_code insCode,
        ins_name insName,
        doctor_id doctorId,
        doctor_name doctorName,
        patient_id patientId,
        patient_name patientName,
        patient_age patientAge,
        CONCAT(dis_name,'-',sym_name) disNameAndSymName ,the_code theCode,the_name  theName,referral_result referralResult
               ,referral_time referralTime
        from t_pre_referral
        <where>
            status = '0'
            <if test="doctorId != null and doctorId != '' ">
                and doctor_id = #{doctorId}
            </if>
            <if test="patientId != null and patientId != '' ">
                and patient_id = #{patientId}
            </if>
        </where>
        order by referral_time desc
    </select>
    <select id="getTPreReferralByCard" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_pre_referral where patient_certificate = #{patientCertificate} order by referral_time desc limit 1
    </select>
</mapper>