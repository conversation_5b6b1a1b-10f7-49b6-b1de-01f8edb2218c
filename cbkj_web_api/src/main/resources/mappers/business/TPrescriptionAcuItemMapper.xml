<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionAcuItemMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TPrescriptionAcuItem">
        <id column="ACU_ITEM_ID" jdbcType="VARCHAR"  property="acuItemId" />
        <result column="PRE_ID" jdbcType="VARCHAR" property="preId" />
        <result column="ACU_ID" jdbcType="VARCHAR" property="acuId" />
        <result column="ACU_CODE" jdbcType="VARCHAR" property="acuCode" />
        <result column="ACU_NAME" jdbcType="VARCHAR" property="acuName" />
        <result column="ACU_NUM" jdbcType="SMALLINT" property="acuNum" />
        <result column="ACU_SEQN" jdbcType="INTEGER" property="acuSeqn" />
        <result column="INSERT_TIME" jdbcType="TIMESTAMP" property="insertTime" />
    </resultMap>


    <sql id="Base_Column_List">
    ACU_ITEM_ID,PRE_ID,ACU_ID,ACU_CODE,ACU_NAME,ACU_NUM,ACU_SEQN,INSERT_TIME
    </sql>

    <delete id="physicalDeleteByPreId" parameterType="String">
        delete from t_prescription_acu_item where PRE_ID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TPrescriptionAcuItem">
        insert into t_prescription_acu_item (ACU_ITEM_ID,PRE_ID,ACU_ID,ACU_CODE,ACU_NAME,ACU_NUM,ACU_SEQN,INSERT_TIME) values
        (#{acuItemId},#{preId},#{acuId},#{acuCode},#{acuName},#{acuNum},#{acuSeqn},NOW())
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_prescription_acu_item (ACU_ITEM_ID,PRE_ID,ACU_ID,ACU_CODE,ACU_NAME,ACU_NUM,ACU_SEQN,INSERT_TIME) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.acuItemId},#{item.preId},#{item.acuId},#{item.acuCode},#{item.acuName},#{item.acuNum},#{item.acuSeqn},NOW())
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPrescriptionAcuItem">
        update t_prescription_acu_item
        <set>
             <if test="preId != null">
                PRE_ID = #{ preId },
             </if>
             <if test="acuId != null">
                ACU_ID = #{ acuId },
             </if>
             <if test="acuCode != null">
                ACU_CODE = #{ acuCode },
             </if>
             <if test="acuName != null">
                ACU_NAME = #{ acuName },
             </if>
             <if test="acuNum != null">
                ACU_NUM = #{ acuNum },
             </if>
             <if test="acuSeqn != null">
                 ACU_SEQN = #{ acuSeqn },
             </if>
             <if test="insertTime != null">
                 INSERT_TIME = NOW(),
             </if>
        </set>
        where ACU_ITEM_ID = #{ acuItemId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update t_prescription_acu_item set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_prescription_acu_item where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_prescription_acu_item where ACU_ITEM_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_prescription_acu_item where ACU_ITEM_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TPrescriptionAcuItem" resultType="Map">
        SELECT ACU_ITEM_ID,PRE_ID,ACU_ID,ACU_CODE,ACU_NAME,ACU_NUM,ACU_SEQN,INSERT_TIME  from t_prescription_acu_item
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TPrescriptionAcuItem" resultMap="BaseResultMap">
        SELECT ACU_ITEM_ID,PRE_ID,ACU_ID,ACU_CODE,ACU_NAME,ACU_NUM,ACU_SEQN
        from t_prescription_acu_item
        where PRE_ID = #{preId}
        order by ACU_SEQN
    </select>


    <select id="getListByPreId" parameterType="String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from t_prescription_acu_item
        where PRE_ID = #{preId}
        order by ACU_SEQN
    </select>

</mapper>