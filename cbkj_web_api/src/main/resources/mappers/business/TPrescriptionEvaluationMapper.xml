<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionEvaluationMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TPrescriptionEvaluation">
        <id column="EVA_ID" jdbcType="VARCHAR"  property="evaId" />
        <result column="PRE_ID" jdbcType="VARCHAR" property="preId" />
        <result column="EVA_TYPE" jdbcType="VARCHAR" property="evaType" />
        <result column="EVA_SJJ" jdbcType="VARCHAR" property="evaSjj" />
        <result column="EVA_SBF" jdbcType="VARCHAR" property="evaSbf" />
        <result column="EVA_SJW" jdbcType="VARCHAR" property="evaSjw" />
        <result column="EVA_YFSJJ" jdbcType="VARCHAR" property="evaYfsjj" />
        <result column="EVA_YSJJ" jdbcType="VARCHAR" property="evaYsjj" />
        <result column="EVA_DXSM" jdbcType="VARCHAR" property="evaDxsm" />
        <result column="EVA_BZJJ" jdbcType="VARCHAR" property="evaBzjj" />
        <result column="EVA_BY" jdbcType="VARCHAR" property="evaBy" />
        <result column="EVA_JLCB" jdbcType="VARCHAR" property="evaJlcb" />
        <result column="EVA_JLPD" jdbcType="VARCHAR" property="evaJlpd" />
        <result column="EVA_JLCGD" jdbcType="VARCHAR" property="evaJlcgd" />
    </resultMap>


    <sql id="Base_Column_List">
    EVA_ID,PRE_ID,EVA_TYPE,EVA_SJJ,EVA_SBF,EVA_SJW,EVA_YFSJJ,EVA_YSJJ,EVA_DXSM,EVA_BZJJ,EVA_BY,EVA_JLCB,EVA_JLPD,EVA_JLCGD
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPrescriptionEvaluation">
        delete from t_prescription_evaluation where EVA_ID = #{ evaId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_prescription_evaluation where EVA_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="physicalDeleteByPreId" parameterType="String">
        delete from t_prescription_evaluation where PRE_ID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TPrescriptionEvaluation">
        insert into t_prescription_evaluation (EVA_ID,PRE_ID,EVA_TYPE,EVA_SJJ,EVA_SBF,EVA_SJW,EVA_YFSJJ,EVA_YSJJ,EVA_DXSM,EVA_BZJJ,EVA_BY,EVA_JLCB,EVA_JLPD,EVA_JLCGD) values
        (#{evaId},#{preId},#{evaType},#{evaSjj},#{evaSbf},#{evaSjw},#{evaYfsjj},#{evaYsjj},#{evaDxsm},#{evaBzjj},#{evaBy},#{evaJlcb},#{evaJlpd},#{evaJlcgd})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_prescription_evaluation (EVA_ID,PRE_ID,EVA_TYPE,EVA_SJJ,EVA_SBF,EVA_SJW,EVA_YFSJJ,EVA_YSJJ,EVA_DXSM,EVA_BZJJ,EVA_BY,EVA_JLCB,EVA_JLPD,EVA_JLCGD) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.evaId},#{item.preId},#{item.evaType},#{item.evaSjj},#{item.evaSbf},#{item.evaSjw},#{item.evaYfsjj},#{item.evaYsjj},#{item.evaDxsm},#{item.evaBzjj},#{item.evaBy},#{item.evaJlcb},#{item.evaJlpd},#{item.evaJlcgd})
        </foreach>
    </insert>

    <insert id="insertListSDK" parameterType="List">
        insert into t_prescription_evaluation_sdk ( evaluation_sdk_id, pre_id, sdk_name,content) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.evaluationSdkId},#{item.preId},#{item.sdkName},#{item.content} )
        </foreach>
    </insert>

    <insert id="insertListSDKOther" parameterType="List">
        insert into t_prescription_evaluation_sdk_other ( sdk_other_id, pre_id, ypml_id, total_json) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.sdkOtherId},#{item.preId},#{item.ypmlId},#{item.totalJson} )
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPrescriptionEvaluation">
        update t_prescription_evaluation
        <set>
            <if test="preId != null">
                PRE_ID = #{ preId },
            </if>
            <if test="evaType != null">
                EVA_TYPE = #{ evaType },
            </if>
            <if test="evaSjj != null">
                EVA_SJJ = #{ evaSjj },
            </if>
            <if test="evaSbf != null">
                EVA_SBF = #{ evaSbf },
            </if>
            <if test="evaSjw != null">
                EVA_SJW = #{ evaSjw },
            </if>
            <if test="evaYfsjj != null">
                EVA_YFSJJ = #{ evaYfsjj },
            </if>
            <if test="evaYsjj != null">
                EVA_YSJJ = #{ evaYsjj },
            </if>
            <if test="evaDxsm != null">
                EVA_DXSM = #{ evaDxsm },
            </if>
            <if test="evaBzjj != null">
                EVA_BZJJ = #{ evaBzjj },
            </if>
            <if test="evaBy != null">
                EVA_BY = #{ evaBy },
            </if>
            <if test="evaJlcb != null">
                EVA_JLCB = #{ evaJlcb },
            </if>
            <if test="evaJlpd != null">
                EVA_JLPD = #{ evaJlpd },
            </if>
            <if test="evaJlcgd != null">
                EVA_JLCGD = #{ evaJlcgd },
            </if>
        </set>
        where EVA_ID = #{ evaId }
    </update>


    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_prescription_evaluation where EVA_ID = #{id}
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TPrescriptionEvaluation" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from t_prescription_evaluation
        <where>
            <if test=" preId != null and preId!='' ">
                and PRE_ID = #{name}
            </if>
        </where>
    </select>
    <select id="getSDKOtherJsonByPreId" resultType="java.lang.String">
        SELECT total_json from t_prescription_evaluation_sdk_other where PRE_ID = #{preId} limit 1
    </select>

</mapper>