<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionExamineMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TPrescriptionExamine">
        <id column="PRE_EXA_ID" jdbcType="VARCHAR" property="preExaId"/>
        <result column="PRE_ID" jdbcType="VARCHAR" property="preId"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="CHECK_TYPE" jdbcType="VARCHAR" property="checkType"/>
        <result column="IS_CHECK" jdbcType="VARCHAR" property="isCheck"/>
        <result column="CHECK_ADVISE" jdbcType="VARCHAR" property="checkAdvise"/>
        <result column="CHECK_USERID" jdbcType="VARCHAR" property="checkUserid"/>
        <result column="CHECK_USERNAME" jdbcType="VARCHAR" property="checkUsername"/>
        <result column="CHECK_TIME" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="DEL_DATE" jdbcType="TIMESTAMP" property="delDate"/>
        <result column="IS_DEL" jdbcType="VARCHAR" property="isDel"/>
        <result column="checkTimeStr" jdbcType="VARCHAR" property="checkTimeStr"/>
    </resultMap>


    <sql id="Base_Column_List">
    PRE_EXA_ID,PRE_ID,APP_ID,INS_CODE,CHECK_TYPE,IS_CHECK,CHECK_ADVISE,CHECK_USERID,CHECK_USERNAME,CHECK_TIME,DEL_DATE,IS_DEL
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPrescriptionExamine">
        delete from t_prescription_examine where PRE_EXA_ID = #{ preExaId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_prescription_examine where PRE_EXA_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" parameterType="TPrescriptionExamine">
        insert into t_prescription_examine (PRE_EXA_ID,PRE_ID,APP_ID,INS_CODE,CHECK_TYPE,IS_CHECK,CHECK_ADVISE,CHECK_USERID,CHECK_USERNAME,CHECK_TIME,DEL_DATE,IS_DEL) values
        (#{preExaId},#{preId},#{appId},#{insCode},#{checkType},#{isCheck},#{checkAdvise},#{checkUserid},#{checkUsername},#{checkTime},#{delDate},#{isDel})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_prescription_examine
        (PRE_EXA_ID,PRE_ID,APP_ID,INS_CODE,CHECK_TYPE,IS_CHECK,CHECK_ADVISE,CHECK_USERID,CHECK_USERNAME,CHECK_TIME,DEL_DATE,IS_DEL) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.preExaId},#{item.preId},#{item.appId},#{item.insCode},#{item.checkType},#{item.isCheck},#{item.checkAdvise},#{item.checkUserid},#{item.checkUsername},#{item.checkTime},#{item.delDate},#{item.isDel})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPrescriptionExamine">
        update t_prescription_examine
        <set>
            <if test="preId != null">
                PRE_ID = #{ preId },
            </if>
            <if test="appId != null">
                APP_ID = #{ appId },
            </if>
            <if test="insCode != null">
                INS_CODE = #{ insCode },
            </if>
            <if test="checkType != null">
                CHECK_TYPE = #{ checkType },
            </if>
            <if test="isCheck != null">
                IS_CHECK = #{ isCheck },
            </if>
            <if test="checkAdvise != null">
                CHECK_ADVISE = #{ checkAdvise },
            </if>
            <if test="checkUserid != null">
                CHECK_USERID = #{ checkUserid },
            </if>
            <if test="checkUsername != null">
                CHECK_USERNAME = #{ checkUsername },
            </if>
            <if test="checkTime != null">
                CHECK_TIME = #{ checkTime },
            </if>
            <if test="delDate != null">
                DEL_DATE = #{ delDate },
            </if>
            <if test="isDel != null">
                IS_DEL = #{ isDel },
            </if>
        </set>
        where PRE_EXA_ID = #{ preExaId }
    </update>


    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_prescription_examine where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_prescription_examine where PRE_EXA_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_prescription_examine where PRE_EXA_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TPrescriptionExamine" resultType="Map">
        SELECT PRE_EXA_ID,PRE_ID,APP_ID,INS_CODE,CHECK_TYPE,IS_CHECK,CHECK_ADVISE,CHECK_USERID,CHECK_USERNAME,CHECK_TIME,DEL_DATE,IS_DEL  from t_prescription_examine
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TPrescriptionExamine" resultMap="BaseResultMap">
        SELECT PRE_EXA_ID,PRE_ID,APP_ID,INS_CODE,CHECK_TYPE,IS_CHECK,CHECK_ADVISE,CHECK_USERID,CHECK_USERNAME,CHECK_TIME,
        DATE_FORMAT(CHECK_TIME,'%Y-%m-%d %H:%i:%s') as checkTimeStr
        from t_prescription_examine
        <where>
            IS_DEL = '0'
            <if test=" preId != null and preId!='' ">
                and PRE_ID = #{preId}
            </if>
        </where>
    </select>

    <select id="getExamineListByPre" parameterType="com.jiuzhekan.cbkj.beans.business.record.VO.TPreRespVO" resultMap="BaseResultMap">
        SELECT exa.CHECK_TYPE, exa.IS_CHECK, exa.CHECK_ADVISE, exa.CHECK_USERID, exa.CHECK_USERNAME,
               exa.CHECK_TIME, DATE_FORMAT(exa.CHECK_TIME,'%Y-%m-%d %H:%i:%s') as checkTimeStr
        FROM t_prescription_examine exa
        LEFT JOIN t_prescription pre ON pre.PRE_ID = exa.PRE_ID
        LEFT JOIN t_record rec ON rec.REC_ID = pre.REC_ID
        WHERE rec.REC_ID = #{recId} and pre.PRE_TYPE = #{preType}
        ORDER BY exa.CHECK_TIME
    </select>
</mapper>