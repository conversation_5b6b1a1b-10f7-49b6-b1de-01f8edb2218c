<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionItemMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TPrescriptionItem">
        <id column="PRE_ITEM_ID" jdbcType="VARCHAR"  property="preItemId" />
        <result column="PRE_ID" jdbcType="VARCHAR" property="preId" />
        <result column="MAT_ID" jdbcType="VARCHAR" property="matId" />
        <result column="MAT_NAME" jdbcType="VARCHAR" property="matName" />
        <result column="YPML_CENTER" jdbcType="VARCHAR" property="ypmlCenter" />
        <result column="YPDM_CENTER" jdbcType="VARCHAR" property="ypdmCenter" />
        <result column="YPML_HIS" jdbcType="VARCHAR" property="ypmlHis" />
        <result column="YPDM_HIS" jdbcType="VARCHAR" property="ypdmHis" />
        <result column="YAOPINDM_TY" jdbcType="VARCHAR" property="yaopindmTy" />
        <result column="YPMC_HIS" jdbcType="VARCHAR" property="ypmcHis" />
        <result column="YPGG_HIS" jdbcType="VARCHAR" property="ypggHis" />
        <result column="YPGGDM_HIS" jdbcType="VARCHAR" property="ypggdmHis" />
        <result column="YPMC_CENTER" jdbcType="VARCHAR" property="ypmcCenter" />
        <result column="YPGG_CENTER" jdbcType="VARCHAR" property="ypggCenter" />
        <result column="YPGGDM_CENTER" jdbcType="VARCHAR" property="ypggdmCenter" />
        <result column="CDID_CENTER" jdbcType="VARCHAR" property="cdidCenter" />
        <result column="CDMC_CENTER" jdbcType="VARCHAR" property="cdmcCenter" />
        <result column="MAT_DOSE" jdbcType="DECIMAL" property="matDose" />
        <result column="MAT_DOSEUNIT_ID" jdbcType="VARCHAR" property="matDoseunitId" />
        <result column="MAT_DOSEUNIT" jdbcType="VARCHAR" property="matDoseunit" />
        <result column="MAT_DAY" jdbcType="SMALLINT" property="matDay" />
        <result column="MAT_NUM" jdbcType="DECIMAL" property="matNum" />
        <result column="BZDW_HIS" jdbcType="VARCHAR" property="bzdwHis" />
        <result column="YFID_HIS" jdbcType="VARCHAR" property="yfidHis" />
        <result column="YFMC_HIS" jdbcType="VARCHAR" property="yfmcHis" />
        <result column="YFID_CENTER" jdbcType="VARCHAR" property="yfidCenter" />
        <result column="YFMC_CENTER" jdbcType="VARCHAR" property="yfmcCenter" />
        <result column="MAT_FREQUENCY_ID" jdbcType="VARCHAR" property="matFrequencyId" />
        <result column="MAT_FREQUENCY" jdbcType="VARCHAR" property="matFrequency" />
        <result column="MAT_XSJ" jdbcType="DECIMAL" property="matXsj" />
        <result column="MAT_JHJ" jdbcType="DECIMAL" property="matJhj" />
        <result column="MAT_SEQN" jdbcType="INTEGER" property="matSeqn" />
        <result column="MAT_DOCTOR" jdbcType="VARCHAR" property="matDoctor" />
        <result column="MAT_DOCTORNAME" jdbcType="VARCHAR" property="matDoctorname" />
        <result column="IS_INSURANCE" jdbcType="VARCHAR" property="isInsurance" />
        <result column="INSERT_TIME" jdbcType="TIMESTAMP" property="insertTime" />
        <result column="ZHUANHUANXS" jdbcType="DOUBLE" property="zhuanhuanxs" />
        <result column="CENTER_STORE_ID" jdbcType="VARCHAR" property="centerStoreId" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="tcxs" jdbcType="DECIMAL" property="tcxs" />
        <result column="tcxs_mat_dose" jdbcType="DECIMAL" property="tcxsMatDose" />
        <result column="mat_total_price" jdbcType="DECIMAL" property="matTotalPrice" />
    </resultMap>


    <sql id="Base_Column_List">
    PRE_ITEM_ID,PRE_ID,MAT_ID,MAT_NAME,YPML_CENTER,YPDM_CENTER,YPML_HIS,YPDM_HIS,
    YAOPINDM_TY,YPMC_HIS,YPGG_HIS,YPGGDM_HIS,YPMC_CENTER,YPGG_CENTER,YPGGDM_CENTER,
    CDID_CENTER,CDMC_CENTER,MAT_DOSE,
    MAT_DOSEUNIT_ID,MAT_DOSEUNIT,MAT_DAY,MAT_NUM,BZDW_HIS,YFID_HIS,YFMC_HIS,YFID_CENTER,
    YFMC_CENTER,MAT_FREQUENCY_ID,MAT_FREQUENCY,MAT_XSJ,MAT_JHJ,MAT_SEQN,MAT_DOCTOR,
    MAT_DOCTORNAME,IS_INSURANCE,INSERT_TIME,ZHUANHUANXS,CENTER_STORE_ID,REMARK,
    tcxs,tcxs_mat_dose,mat_total_price
    </sql>

    <delete id="physicalDeleteByPreId" parameterType="String">
        delete from t_prescription_item where PRE_ID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <!--单个插入-->
    <insert id="insert"  parameterType="TPrescriptionItem">
        insert into t_prescription_item (<include refid="Base_Column_List"/>) values
        (#{preItemId},#{preId},#{matId},#{matName},#{ypmlCenter},#{ypdmCenter},#{ypmlHis},#{ypdmHis},
        #{yaopindmTy},#{ypmcHis},#{ypggHis},#{ypggdmHis},#{ypmcCenter},#{ypggCenter},#{ypggdmCenter},
        #{cdidCenter},#{cdmcCenter},#{matDose},
        #{matDoseunitId},#{matDoseunit},#{matDay},#{matNum},#{bzdwHis},#{yfidHis},#{yfmcHis},#{yfidCenter},
        #{yfmcCenter},#{matFrequencyId},#{matFrequency},#{matXsj},#{matJhj},#{matSeqn},#{matDoctor},
        #{matDoctorname},#{isInsurance},NOW(),#{zhuanhuanxs},#{centerStoreId},#{remark},
        #{tcxs},#{tcxsMatDose},#{matTotalPrice})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_prescription_item (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.preItemId},#{item.preId},#{item.matId},#{item.matName},#{item.ypmlCenter},#{item.ypdmCenter},#{item.ypmlHis},#{item.ypdmHis},
            #{item.yaopindmTy},#{item.ypmcHis},#{item.ypggHis},#{item.ypggdmHis},#{item.ypmcCenter},#{item.ypggCenter},#{item.ypggdmCenter},
            #{item.cdidCenter},#{item.cdmcCenter},#{item.matDose},
            #{item.matDoseunitId},#{item.matDoseunit},#{item.matDay},#{item.matNum},#{item.bzdwHis},#{item.yfidHis},#{item.yfmcHis},#{item.yfidCenter},
            #{item.yfmcCenter},#{item.matFrequencyId},#{item.matFrequency},#{item.matXsj},#{item.matJhj},#{item.matSeqn},#{item.matDoctor},
            #{item.matDoctorname},#{item.isInsurance},NOW(),#{item.zhuanhuanxs},#{item.centerStoreId},#{item.remark},
            #{item.tcxs},#{item.tcxsMatDose},#{item.matTotalPrice})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPrescriptionItem">
        update t_prescription_item
        <set>
            <if test="preId != null">
                PRE_ID = #{ preId },
            </if>
            <if test="matId != null">
                MAT_ID = #{ matId },
            </if>
            <if test="matName != null">
                MAT_NAME = #{ matName },
            </if>
            <if test="ypmlCenter != null">
                YPML_CENTER = #{ ypmlCenter },
            </if>
            <if test="ypdmCenter != null">
                YPDM_CENTER = #{ ypdmCenter },
            </if>
            <if test="ypmlHis != null">
                YPML_HIS = #{ ypmlHis },
            </if>
            <if test="ypdmHis != null">
                YPDM_HIS = #{ ypdmHis },
            </if>
            <if test="yaopindmTy != null">
                YAOPINDM_TY = #{ yaopindmTy },
            </if>
            <if test="ypmcHis != null">
                YPMC_HIS = #{ ypmcHis },
            </if>
            <if test="ypggHis != null">
                YPGG_HIS = #{ ypggHis },
            </if>
            <if test="ypggdmHis != null">
                YPGGDM_HIS = #{ ypggdmHis },
            </if>
            <if test="ypmcCenter != null">
                YPMC_CENTER = #{ ypmcCenter },
            </if>
            <if test="ypggCenter != null">
                YPGG_CENTER = #{ ypggCenter },
            </if>
            <if test="ypggdmCenter != null">
                YPGGDM_CENTER = #{ ypggdmCenter },
            </if>
            <if test="cdidCenter != null">
                CDID_CENTER = #{ cdidCenter },
            </if>
            <if test="cdmcCenter != null">
                CDMC_CENTER = #{ cdmcCenter },
            </if>
            <if test="matDose != null">
                MAT_DOSE = #{ matDose },
            </if>
            <if test="matDoseunit != null">
                MAT_DOSEUNIT = #{ matDoseunit },
            </if>
            <if test="matDoseunitId != null">
                MAT_DOSEUNIT_ID = #{ matDoseunitId },
            </if>
            <if test="matDay != null">
                MAT_DAY = #{ matDay },
            </if>
            <if test="matNum != null">
                MAT_NUM = #{ matNum },
            </if>
            <if test="bzdwHis != null">
                BZDW_HIS = #{ bzdwHis },
            </if>
            <if test="yfidHis != null">
                YFID_HIS = #{ yfidHis },
            </if>
            <if test="yfmcHis != null">
                YFMC_HIS = #{ yfmcHis },
            </if>
            <if test="yfidCenter != null">
                YFID_CENTER = #{ yfidCenter },
            </if>
            <if test="yfmcCenter != null">
                YFMC_CENTER = #{ yfmcCenter },
            </if>
            <if test="matFrequencyId != null">
                MAT_FREQUENCY_ID = #{ matFrequencyId },
            </if>
            <if test="matFrequency != null">
                MAT_FREQUENCY = #{ matFrequency },
            </if>
            <if test="matXsj != null">
                MAT_XSJ = #{ matXsj },
            </if>
            <if test="matJhj != null">
                MAT_JHJ = #{ matJhj },
            </if>
            <if test="matSeqn != null">
                MAT_SEQN = #{ matSeqn },
            </if>
            <if test="matDoctor != null">
                MAT_DOCTOR = #{ matDoctor },
            </if>
            <if test="matDoctorname != null">
                MAT_DOCTORNAME = #{ matDoctorname },
            </if>
            <if test="isInsurance != null">
                IS_INSURANCE = #{ isInsurance },
            </if>
            <if test="zhuanhuanxs != null">
                ZHUANHUANXS = #{ zhuanhuanxs },
            </if>
            <if test="centerStoreId != null">
                CENTER_STORE_ID = #{ centerStoreId },
            </if>
            <if test="remark != null">
                REMARK = #{ remark },
            </if>
            <if test="insertTime != null">
                INSERT_TIME = NOW(),
            </if>
            <if test="tcxs != null">
                tcxs = #{tcxs},
            </if>
            <if test="tcxs_mat_dose != null">
                tcxs_mat_dose = #{ tcxsMatDose },
            </if>
            <if test="mat_total_price != null">
                mat_total_price = #{matTotalPrice},
            </if>
        </set>
        where PRE_ITEM_ID = #{ preItemId }
    </update>


    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_prescription_item where PRE_ITEM_ID = #{id}
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TPrescriptionItem" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from t_prescription_item
<!--        where PRE_ID = #{preId}-->
<!--        order by MAT_SEQN-->
    </select>

    <!--查询基础语句返回对象-->
    <select id="getListByPreId" parameterType="String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from t_prescription_item
        where PRE_ID = #{preId}
        order by MAT_SEQN
    </select>

    <!--查询基础语句返回对象-->
    <select id="getListByPreNoList" parameterType="list" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from t_prescription_item
        where PRE_ID in (select PRE_ID from t_prescription where PRE_NO in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        )
        order by MAT_SEQN
    </select>



</mapper>