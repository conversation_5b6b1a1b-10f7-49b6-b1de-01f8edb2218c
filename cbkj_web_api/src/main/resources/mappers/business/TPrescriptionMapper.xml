<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TPrescription">
        <id column="PRE_ID" jdbcType="VARCHAR" property="preId"/>
        <result column="PRE_NO" jdbcType="VARCHAR" property="preNo"/>
        <result column="REC_ID" jdbcType="VARCHAR" property="recId"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="DEPT_ID" jdbcType="VARCHAR" property="deptId"/>
        <result column="PATIENT_ID" jdbcType="VARCHAR" property="patientId"/>
        <result column="PRE_DOCTOR" jdbcType="VARCHAR" property="preDoctor"/>
        <result column="PRE_DOCTORNAME" jdbcType="VARCHAR" property="preDoctorname"/>
        <result column="PRE_TIME" jdbcType="TIMESTAMP" property="preTime"/>
        <result column="PRE_SINGLE_MONEY" jdbcType="DECIMAL" property="preSingleMoney"/>
        <result column="PRE_NUM" jdbcType="SMALLINT" property="preNum"/>
        <result column="PRE_MZ_ZY" jdbcType="SMALLINT" property="preMzZy"/>
        <result column="PRE_TYPE" jdbcType="VARCHAR" property="preType"/>
        <result column="PRE_MAT_TYPE" jdbcType="VARCHAR" property="preMatType"/>
        <result column="PRE_USAGE" jdbcType="VARCHAR" property="preUsage"/>
        <result column="PRE_DESCRIPTION_ID" jdbcType="VARCHAR" property="preDescriptionId"/>
        <result column="PRE_DESCRIPTION" jdbcType="VARCHAR" property="preDescription"/>
        <result column="PRE_FREQUENCY_ID" jdbcType="VARCHAR" property="preFrequencyId"/>
        <result column="PRE_FREQUENCY" jdbcType="VARCHAR" property="preFrequency"/>
        <result column="PRE_USETIME_ID" jdbcType="VARCHAR" property="preUsetimeId"/>
        <result column="PRE_USETIME_DES" jdbcType="VARCHAR" property="preUsetimeDes"/>
        <result column="PRE_ADVICE" jdbcType="VARCHAR" property="preAdvice"/>
        <result column="PRE_ORIGIN" jdbcType="VARCHAR" property="preOrigin"/>
        <result column="PRE_ORIGIN_ID" jdbcType="VARCHAR" property="preOriginId"/>
        <result column="PRE_ORIGIN_NAME" jdbcType="VARCHAR" property="preOriginName"/>
        <result column="IS_DEL" jdbcType="VARCHAR" property="isDel"/>
        <result column="DEL_USERID" jdbcType="VARCHAR" property="delUserid"/>
        <result column="DEL_USERNAME" jdbcType="VARCHAR" property="delUsername"/>
        <result column="CHECK_TYPE" jdbcType="VARCHAR" property="checkType"/>
        <result column="IS_CHECK" jdbcType="VARCHAR" property="isCheck"/>
        <result column="CHECK_ADVISE" jdbcType="VARCHAR" property="checkAdvise"/>
        <result column="CHECK_USERID" jdbcType="VARCHAR" property="checkUserid"/>
        <result column="CHECK_USERNAME" jdbcType="VARCHAR" property="checkUsername"/>
        <result column="CHECK_TIME" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="IS_PAY" jdbcType="VARCHAR" property="isPay"/>
        <result column="PAY_TIME" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="PAY_USERID" jdbcType="VARCHAR" property="payUserid"/>
        <result column="PAY_USERNAME" jdbcType="VARCHAR" property="payUsername"/>
        <result column="IS_PRINT" jdbcType="SMALLINT" property="isPrint"/>
        <result column="MAT_TOL_MONEY" jdbcType="DECIMAL" property="matTolMoney"/>
        <result column="PRE_TOL_MONEY" jdbcType="DECIMAL" property="preTolMoney"/>
        <result column="PRE_TOL_MONEY_REAL" jdbcType="DECIMAL" property="preTolMoneyReal"/>
        <result column="PRE_SPECIAL_FEE" jdbcType="DECIMAL" property="preSpecialFee"/>
        <result column="HIS_PRE_ID" jdbcType="VARCHAR" property="hisPreId"/>

        <result column="DECOCT_TYPE" jdbcType="VARCHAR" property="decoctType"/>
        <result column="PRE_DECOCTION_FEE" jdbcType="DECIMAL" property="preDecoctionFee"/>

        <result column="IS_PRODUCTION" jdbcType="VARCHAR" property="isProduction"/>
        <result column="PRE_PRODUCTION_FEE" jdbcType="DECIMAL" property="preProductionFee"/>

        <result column="DC_TYPE" jdbcType="VARCHAR" property="dcType"/>
        <result column="PRE_EXPRESS_FEE" jdbcType="DECIMAL" property="preExpressFee"/>

        <result column="REC_EXT_TYPE" jdbcType="VARCHAR" property="recExtType"/>
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId"/>
        <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName"/>
        <result column="DC_ID" jdbcType="VARCHAR" property="dcId"/>
        <result column="ACU_TYPE_ID" jdbcType="VARCHAR" property="acuTypeId"/>
        <result column="ACU_TYPE" jdbcType="VARCHAR" property="acuType"/>
        <result column="ACU_PROJECT_ID" jdbcType="VARCHAR" property="acuProjectId"/>
        <result column="ACU_PROJECT" jdbcType="VARCHAR" property="acuProject"/>
        <result column="ACU_OPERATION" jdbcType="VARCHAR" property="acuOperation"/>
        <result column="PRE_SMOKE_TYPE_ID" jdbcType="VARCHAR" property="preSmokeTypeId"/>
        <result column="PRE_SMOKE_TYPE" jdbcType="VARCHAR" property="preSmokeType"/>
        <result column="PRE_SMO_MONEY" jdbcType="DECIMAL" property="preSmoMoney"/>
        <result column="PRE_SMOKE_INSTRUMENT_ID" jdbcType="VARCHAR" property="preSmokeInstrumentId"/>
        <result column="PRE_SMOKE_INSTRUMENT" jdbcType="VARCHAR" property="preSmokeInstrument"/>
        <result column="PRE_N_BAG" jdbcType="VARCHAR" property="preNBag"/>
        <result column="PRE_N_ML" jdbcType="VARCHAR" property="preNMl"/>
        <result column="PRE_N_ML_NAME" jdbcType="VARCHAR" property="preNMlName"/>
        <result column="ORIGIN_PRE_ID" jdbcType="VARCHAR" property="originPreId"/>
        <result column="PRE_SCHEMEADDID" jdbcType="VARCHAR" property="preSchemeaddid"/>
        <result column="IS_SEND" jdbcType="VARCHAR" property="isSend"/>
        <result column="IS_HOSPITAL_TREATMENT" jdbcType="VARCHAR" property="isHospitalTreatment"/>
        <result column="IS_INSURANCE" jdbcType="VARCHAR" property="isInsurance"/>

        <result column="PATIENT_NAME" jdbcType="VARCHAR" property="patientName"/>
        <result column="IS_SPECIAL_DIS" jdbcType="VARCHAR" property="isSpecialDis"/>
        <result column="SPECIAL_NAME" jdbcType="VARCHAR" property="specialName"/>
        <result column="SPECIAL_CODE" jdbcType="VARCHAR" property="specialCode"/>
        <result column="PRE_OLD_NO" jdbcType="VARCHAR" property="preOldNo"/>
        <result column="PRE_ADVICE_TYPE" jdbcType="VARCHAR" property="preAdviceType"/>
        <result column="PRE_ADVICE_TIME" jdbcType="VARCHAR" property="preAdviceTime"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="BEGAN_TIME" jdbcType="VARCHAR" property="beganTime"/>
        <result column="END_TIME" jdbcType="VARCHAR" property="endTime"/>

        <result column="PRODUCTION_TYPE" jdbcType="VARCHAR" property="productionType"/>
        <result column="PRODUCTION_TYPE_ID" jdbcType="VARCHAR" property="productionTypeId"/>
        <result column="patient_types" jdbcType="VARCHAR" property="patientTypes"/>
        <result column="patient_types_name" jdbcType="VARCHAR" property="patientTypesName"/>
        <result column="pre_origin_xh" jdbcType="VARCHAR" property="preOriginXh"/>
        <result column="mat_tol_weight" jdbcType="DECIMAL" property="matTolWeight"/>
        <result column="DC_TYPE_SUBCLASS" jdbcType="VARCHAR" property="dcTypeSubclass"/>
        <result column="self_pickup_id" jdbcType="VARCHAR" property="selfPickupId"/>
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.business.record.TPrescription">
        <id column="PRE_ID" property="preId"/>
        <result column="PRE_NO" property="preNo"/>
        <result column="PRE_TYPE" property="preType"/>
        <result column="PRE_DOCTORNAME" jdbcType="VARCHAR" property="preDoctorname"/>
        <result column="PRE_TIME" jdbcType="TIMESTAMP" property="preTime"/>
        <result column="PRE_DESCRIPTION" property="preDescription"/>
        <result column="PRE_USETIME_DES" property="preUsetimeDes"/>
        <result column="PRE_FREQUENCY" property="preFrequency"/>
        <result column="PRE_ADVICE" property="preAdvice"/>
        <result column="PRE_NUM" property="preNum"/>
        <result column="PRE_DECOCTION_FEE" jdbcType="DECIMAL" property="preDecoctionFee"/>
        <result column="PRE_PRODUCTION_FEE" jdbcType="DECIMAL" property="preProductionFee"/>
        <result column="PRE_EXPRESS_FEE" jdbcType="DECIMAL" property="preExpressFee"/>
        <result column="PRE_SMO_MONEY" jdbcType="DECIMAL" property="preSmoMoney"/>
        <result column="MAT_TOL_MONEY" jdbcType="DECIMAL" property="matTolMoney"/>
        <result column="PRE_TOL_MONEY" jdbcType="DECIMAL" property="preTolMoney"/>
        <result column="CHECK_USERNAME" jdbcType="VARCHAR" property="checkUsername"/>
        <result column="PRE_N_ML" jdbcType="VARCHAR" property="preNMl"/>
        <result column="PRE_N_ML_NAME" jdbcType="VARCHAR" property="preNMlName"/>
        <result column="ACU_TYPE" jdbcType="VARCHAR" property="acuType"/>
        <result column="IS_HOSPITAL_TREATMENT" jdbcType="VARCHAR" property="isHospitalTreatment"/>
        <result column="PRE_SMOKE_TYPE" jdbcType="VARCHAR" property="preSmokeType"/>
        <result column="PRE_SMOKE_INSTRUMENT" jdbcType="VARCHAR" property="preSmokeInstrument"/>
        <result column="ACU_OPERATION" jdbcType="VARCHAR" property="acuOperation"/>
        <result column="patient_types" jdbcType="VARCHAR" property="patientTypes"/>
        <result column="patient_types_name" jdbcType="VARCHAR" property="patientTypesName"/>
        <result column="IS_INSURANCE2" jdbcType="VARCHAR" property="isInsurance"/>
        <result column="pre_origin_xh" jdbcType="VARCHAR" property="preOriginXh"/>
        <collection property="itemList" ofType="com.jiuzhekan.cbkj.beans.business.record.TPrescriptionItem">
            <id column="PRE_ITEM_ID" jdbcType="VARCHAR" property="preItemId"/>
            <result column="MAT_ID" jdbcType="VARCHAR" property="matId"/>
            <result column="MAT_NAME" jdbcType="VARCHAR" property="matName"/>
            <result column="YPML_CENTER" jdbcType="VARCHAR" property="ypmlCenter"/>
            <result column="YPDM_CENTER" jdbcType="VARCHAR" property="ypdmCenter"/>
            <result column="YPML_HIS" jdbcType="VARCHAR" property="ypmlHis"/>
            <result column="YPDM_HIS" jdbcType="VARCHAR" property="ypdmHis"/>
            <result column="YAOPINDM_TY" jdbcType="VARCHAR" property="yaopindmTy"/>
            <result column="YPMC_HIS" jdbcType="VARCHAR" property="ypmcHis"/>
            <result column="YPGG_HIS" jdbcType="VARCHAR" property="ypggHis"/>
            <result column="YPGGID_HIS" jdbcType="VARCHAR" property="ypggidHis" />
            <result column="YPMC_CENTER" jdbcType="VARCHAR" property="ypmcCenter"/>
            <result column="YPGG_CENTER" jdbcType="VARCHAR" property="ypggCenter"/>
            <result column="YPGGID_CENTER" jdbcType="VARCHAR" property="ypggidCenter" />
            <result column="CDID_CENTER" jdbcType="VARCHAR" property="cdidCenter"/>
            <result column="CDMC_CENTER" jdbcType="VARCHAR" property="cdmcCenter"/>
            <result column="MAT_DOSE" jdbcType="DECIMAL" property="matDose"/>
            <result column="MAT_DOSEUNIT_ID" jdbcType="VARCHAR" property="matDoseunitId"/>
            <result column="MAT_DOSEUNIT" jdbcType="VARCHAR" property="matDoseunit"/>
            <result column="MAT_DAY" jdbcType="SMALLINT" property="matDay"/>
            <result column="MAT_NUM" jdbcType="DECIMAL" property="matNum"/>
            <result column="BZDW_HIS" jdbcType="VARCHAR" property="bzdwHis"/>
            <result column="YFID_HIS" jdbcType="VARCHAR" property="yfidHis"/>
            <result column="YFMC_HIS" jdbcType="VARCHAR" property="yfmcHis"/>
            <result column="YFID_CENTER" jdbcType="VARCHAR" property="yfidCenter"/>
            <result column="YFMC_CENTER" jdbcType="VARCHAR" property="yfmcCenter"/>
            <result column="MAT_FREQUENCY_ID" jdbcType="VARCHAR" property="matFrequencyId"/>
            <result column="MAT_FREQUENCY" jdbcType="VARCHAR" property="matFrequency"/>
            <result column="MAT_XSJ" jdbcType="DECIMAL" property="matXsj"/>
            <result column="MAT_JHJ" jdbcType="DECIMAL" property="matJhj"/>
            <result column="MAT_SEQN" jdbcType="INTEGER" property="matSeqn"/>
            <result column="MAT_DOCTOR" jdbcType="VARCHAR" property="matDoctor"/>
            <result column="MAT_DOCTORNAME" jdbcType="VARCHAR" property="matDoctorname"/>
            <result column="IS_INSURANCE" jdbcType="VARCHAR" property="isInsurance"/>
            <result column="ZHUANHUANXS" jdbcType="DOUBLE" property="zhuanhuanxs"/>
            <result column="CENTER_STORE_ID" jdbcType="VARCHAR" property="centerStoreId"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
    PRE_ID,PRE_NO,REC_ID,APP_ID,INS_CODE,DEPT_ID,PATIENT_ID,PRE_DOCTOR,PRE_DOCTORNAME,
    PRE_TIME,PRE_SINGLE_MONEY,PRE_NUM,PRE_MZ_ZY,PRE_TYPE,PRE_MAT_TYPE,PRE_USAGE,
    PRE_DESCRIPTION_ID,PRE_DESCRIPTION,PRE_FREQUENCY_ID,PRE_FREQUENCY,PRE_USETIME_ID,PRE_USETIME_DES,
    PRE_ADVICE,PRE_ORIGIN,PRE_ORIGIN_ID,PRE_ORIGIN_NAME,IS_DEL,DEL_USERID,DEL_USERNAME,
    CHECK_TYPE,IS_CHECK,CHECK_ADVISE,CHECK_USERID,CHECK_USERNAME,CHECK_TIME,IS_PAY,PAY_TIME,
    PAY_USERID,PAY_USERNAME,IS_PRINT,MAT_TOL_MONEY,PRE_TOL_MONEY,PRE_TOL_MONEY_REAL,
    PRE_SPECIAL_FEE,HIS_PRE_ID,

    DECOCT_TYPE, PRE_DECOCT_NUM, PRE_DECOCTION_FEE,
    IS_PRODUCTION, PRODUCTION_TYPE_ID, PRODUCTION_TYPE, PRE_PRODUCTION_FEE,
    DC_TYPE,PRE_EXPRESS_FEE,

    REC_EXT_TYPE,STORE_ID,STORE_NAME,DC_ID, ACU_TYPE_ID,ACU_TYPE,
    ACU_PROJECT_ID,ACU_PROJECT,PAY_CODE,ACU_OPERATION,PRE_SMOKE_TYPE_ID,PRE_SMOKE_TYPE,PRE_SMO_MONEY,
    PRE_SMOKE_INSTRUMENT_ID,PRE_SMOKE_INSTRUMENT,
    PRE_N_BAG,PRE_N_ML,PRE_N_ML_NAME,ORIGIN_PRE_ID,PRE_SCHEMEADDID,IS_SEND,IS_HOSPITAL_TREATMENT,
    IS_INSURANCE,IS_SPECIAL_DIS,SPECIAL_NAME,SPECIAL_CODE,PRE_OLD_NO,PRE_ADVICE_TYPE,
    PRE_ADVICE_TIME,CREATE_DATE,BEGAN_TIME,END_TIME,patient_types,patient_types_name,pre_origin_xh,
    mat_tol_weight,discharge_medication_mark,DC_TYPE_SUBCLASS,self_pickup_id,IS_SCIENTIFIC_PREPARATION,INFO_SCIENTIFIC_PREPARATION,

    urgent_sign_value
    </sql>

    <delete id="deleteByRecId" parameterType="String">
        update t_prescription set is_del = '1' where REC_ID = #{ recId }
    </delete>

    <delete id="physicalDelete" parameterType="List">
        delete from t_prescription where PRE_ID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_prescription where PRE_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" parameterType="TPrescriptionSDK">
        insert into t_prescription (<include refid="Base_Column_List"/>) values
        (#{preId},#{preNo},#{recId},#{appId},#{insCode},#{deptId},#{patientId},#{preDoctor},#{preDoctorname},
        #{preTime},#{preSingleMoney},#{preNum},#{preMzZy},#{preType},#{preMatType},#{preUsage},
        #{preDescriptionId},#{preDescription},#{preFrequencyId},#{preFrequency},#{preUsetimeId},#{preUsetimeDes},
        #{preAdvice},#{preOrigin},#{preOriginId},#{preOriginName},#{isDel},#{delUserid},#{delUsername},
        #{checkType},#{isCheck},#{checkAdvise},#{checkUserid},#{checkUsername},#{checkTime},#{isPay},#{payTime},
        #{payUserid},#{payUsername},#{isPrint},#{matTolMoney},#{preTolMoney},#{preTolMoneyReal},
        #{preSpecialFee},#{hisPreId},

        #{decoctType},#{preDecoctNum},#{preDecoctionFee},
        #{isProduction},#{productionTypeId},#{productionType},#{preProductionFee},
        #{dcType},#{preExpressFee},

        #{recExtType},#{storeId},#{storeName},#{dcId},#{acuTypeId},#{acuType},
        #{acuProjectId},#{acuProject},#{payCode},#{acuOperation},#{preSmokeTypeId},#{preSmokeType},#{preSmoMoney},
        #{preSmokeInstrumentId},#{preSmokeInstrument},
        #{preNBag},#{preNMl},#{preNMlName},#{originPreId},#{preSchemeaddid},#{isSend},#{isHospitalTreatment},
        #{isInsurance},#{isSpecialDis},#{specialName},#{specialCode},#{preOldNo},#{preAdviceType},
        #{preAdviceTime},#{createDate},#{beganTime},#{endTime},#{patientTypes},#{patientTypesName},#{preOriginXh},
         #{matTolWeight},#{dischargeMedicationMark},#{dcTypeSubclass},#{selfPickupId},#{isScientificPreparation},#{infoScientificPreparation},
         #{urgentSignValue})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_prescription (<include refid="Base_Column_List"/>)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.preId},#{item.preNo},#{item.recId},#{item.appId},#{item.insCode},#{item.deptId},#{item.patientId},#{item.preDoctor},#{item.preDoctorname},
            #{item.preTime},#{item.preSingleMoney},#{item.preNum},#{item.preMzZy},#{item.preType},#{item.preMatType},#{item.preUsage},
            #{item.preDescriptionId},#{item.preDescription},#{item.preFrequencyId},#{item.preFrequency},#{item.preUsetimeId},#{item.preUsetimeDes},
            #{item.preAdvice},#{item.preOrigin},#{item.preOriginId},#{item.preOriginName},#{item.isDel},#{item.delUserid},#{item.delUsername},
            #{item.checkType},#{item.isCheck},#{item.checkAdvise},#{item.checkUserid},#{item.checkUsername},#{item.checkTime},#{item.isPay},#{item.payTime},
            #{item.payUserid},#{item.payUsername},#{item.isPrint},#{item.matTolMoney},#{item.preTolMoney},#{item.preTolMoneyReal},
            #{item.preSpecialFee},#{item.hisPreId},

            #{item.decoctType},#{item.preDecoctNum},#{item.preDecoctionFee},
            #{item.isProduction},#{item.productionTypeId},#{item.productionType},#{item.preProductionFee},
            #{item.dcType},#{item.preExpressFee},

            #{item.recExtType},#{item.storeId},#{item.storeName},#{item.dcId},#{item.acuTypeId},#{item.acuType},
            #{item.acuProjectId},#{item.acuProject},#{item.payCode},#{item.acuOperation},#{item.preSmokeTypeId},#{item.preSmokeType},#{item.preSmoMoney},
            #{item.preSmokeInstrumentId},#{item.preSmokeInstrument},
            #{item.preNBag},#{item.preNMl},#{item.preNMlName},#{item.originPreId},#{item.preSchemeaddid},#{item.isSend},#{item.isHospitalTreatment},
            #{item.isInsurance},#{item.isSpecialDis},#{item.specialName},#{item.specialCode},#{item.preOldNo},#{item.preAdviceType},
            #{item.preAdviceTime},#{item.createDate},#{item.beganTime},#{item.endTime},#{item.patientTypes},#{item.patientTypesName},
            #{item.preOriginXh},#{item.matTolWeight},#{item.dischargeMedicationMark},
             #{item.dcTypeSubclass},#{item.selfPickupId},#{item.isScientificPreparation},#{item.infoScientificPreparation},#{item.urgentSignValue} )
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPrescriptionSDK">
        update t_prescription
        <set>
            <if test="isDel != null">
                IS_DEL = #{ isDel },
            </if>
            <if test="delUserid != null">
                DEL_USERID = #{ delUserid },
            </if>
            <if test="delUsername != null">
                DEL_USERNAME = #{ delUsername },
            </if>
            <if test="recExtType != null">
                REC_EXT_TYPE = #{ recExtType },
            </if>
        </set>
        where PRE_ID = #{ preId }
    </update>

    <update id="saveCheckPre" parameterType="TPrescriptionSDK">
        update t_prescription
        <set>
            <if test="checkAdvise != null">
                CHECK_ADVISE = #{ checkAdvise },
            </if>
            <if test="checkUserid != null">
                CHECK_USERID = #{ checkUserid },
            </if>
            <if test="checkUsername != null">
                CHECK_USERNAME = #{ checkUsername },
            </if>
            <if test="checkTime != null">
                CHECK_TIME = #{ checkTime },
            </if>
            <if test="isCheck != null">
                IS_CHECK = #{ isCheck },
            </if>
            <if test="checkType != null">
                CHECK_TYPE = #{checkType},
            </if>
            <if test="recExtType != null">
                REC_EXT_TYPE = #{ recExtType },
            </if>
            <if test="preOriginName != null">
                PRE_ORIGIN_NAME = #{preOriginName},
            </if>
        </set>
        where PRE_ID = #{ preId } and IS_DEL = '0'
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_prescription where PRE_ID = #{id}
    </select>

    <select id="getObjectByPreNo" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_prescription where PRE_NO = #{preNo} and is_del = '0'  and is_check is not null order by is_check is null, pre_time limit 1
    </select>
    <select id="getObjectByPreSNo" resultType="String" parameterType="String">
        select
            PRE_ID
        from t_prescription where PRE_NO = #{preNo} and is_del = '0'  and is_check is not null order by is_check is null, pre_time
    </select>

    <select id="getPreListByPreNo" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_prescription
        where PRE_NO = #{preNo} and is_del = '0'
        <if test=" exceptPreId != null and exceptPreId!='' ">
            and PRE_ID != #{exceptPreId}
        </if>
        order by pre_time desc
    </select>

    <select id="getOrgInfoByPreNo" resultMap="BaseResultMap" parameterType="String">
        select  APP_ID,INS_CODE,DEPT_ID
        from t_prescription where PRE_NO = #{preNo} and is_del = '0' and is_check is not null order by is_check is null, pre_time limit 1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TPrescriptionSDK" resultMap="BaseResultMap">
        SELECT
        *
        from t_prescription
        <where>
            IS_DEL = '0'
            <if test=" recId != null and recId!='' ">
                AND REC_ID = #{recId}
            </if>
            <if test=" preId != null and preId!='' ">
                AND PRE_ID = #{preId}
            </if>
        </where>
    </select>

    <!--分页查询处方信息-->
    <select id="getPreListByObj" resultMap="BaseResultMap">
        SELECT p.pre_id,p.pre_no,p.app_id,p.mat_tol_money,p.rec_id,
        DATE_FORMAT(p.PRE_TIME, '%Y-%c-%d %H:%i:%s')as createTime,g.patient_name
        from t_prescription p
        LEFT JOIN t_record r ON p.REC_ID = r.REC_ID
        LEFT JOIN t_register g ON r.REGISTER_ID = g.REGISTER_ID
        where p.IS_DEL = 0 and p.is_check is not null
        <if test=" visitNo != null and visitNo!='' ">
            and g.VISIT_NO = #{visitNo}
        </if>
        <if test=" registerId != null and registerId!='' ">
            and g.REGISTER_ID = #{registerId}
        </if>
        <!--<if test=" preNo != null and preNo!='' ">-->
        <!--and p.pre_no = #{preNo}-->
        <!--</if>-->

    </select>

    <select id="getListByRecId" parameterType="TPrescriptionSDK" resultMap="BaseResultMap2">
        SELECT pre.PRE_ID,pre.PRE_NO,pre.PRE_TYPE,pre.PRE_TIME,pre.PRE_DOCTORNAME,pre.PRE_DESCRIPTION,pre.PRE_USETIME_DES,pre.PRE_FREQUENCY,
            pre.PRE_NUM,pre.PRE_ADVICE,pre.PRE_DECOCTION_FEE, pre.PRE_PRODUCTION_FEE, pre.PRE_EXPRESS_FEE, pre.PRE_SMO_MONEY,
            pre.PRE_SPECIAL_FEE,pre.MAT_TOL_MONEY,pre.PRE_TOL_MONEY, pre.CHECK_USERNAME,pre.PRE_N_ML,pre.PRE_N_ML_NAME,pre.ACU_TYPE,pre.IS_HOSPITAL_TREATMENT,
               pre.PRE_SMOKE_TYPE,pre.PRE_SMOKE_INSTRUMENT,pre.ACU_OPERATION,pre.patient_types,pre.IS_INSURANCE as IS_INSURANCE2
        from t_prescription pre
        where pre.REC_ID = #{recId} AND pre.IS_DEL = '0' and pre.is_check is not null order by pre.PRE_TYPE, pre.PRE_TIME
    </select>

    <select id="getPreAndItemByPreNo" parameterType="TPrescriptionSDK" resultMap="BaseResultMap2">
        SELECT pre.PRE_ID,pre.PRE_NO,pre.PRE_TIME,pre.PRE_DOCTORNAME,pre.PRE_DESCRIPTION,pre.PRE_USETIME_DES,pre.PRE_FREQUENCY,
            pre.PRE_NUM,pre.PRE_ADVICE,pre.PRE_DECOCTION_FEE, pre.PRE_PRODUCTION_FEE, pre.PRE_EXPRESS_FEE, pre.PRE_SMO_MONEY,
            pre.PRE_SPECIAL_FEE,pre.MAT_TOL_MONEY,pre.PRE_TOL_MONEY, pre.CHECK_USERNAME,
            item.*
        from t_prescription pre
        left join t_prescription_item item on pre.PRE_ID = item.PRE_ID
        where pre.PRE_NO = #{preNo} AND pre.IS_DEL = '0' and pre.is_check is not null ORDER BY item.MAT_SEQN ASC
    </select>


    <select id="getPreByPre" parameterType="TPrescriptionSDK" resultType="com.jiuzhekan.cbkj.beans.business.record.VO.TPreRespVO">
        SELECT
        pre.PRE_ID AS preId,pre.PRE_NO AS preNo,pre.REC_ID AS recId,pre.PATIENT_ID AS patientId,pre.PRE_DOCTORNAME AS
        preDoctorname,
        pre.patient_types  as patientTypes,
        pre.PRE_TIME AS preTime,DATE_FORMAT(PRE_TIME,'%Y-%m-%d %H:%i:%s') AS preTimeStr,
        pre.PRE_SINGLE_MONEY AS preSingleMoney,pre.PRE_NUM AS preNum,pre.PRE_MZ_ZY AS preMzZy,
        pre.PRE_TYPE AS preType,pre.PRE_MAT_TYPE AS preMatType,pre.PRE_USAGE AS preUsage,
        pre.PRE_DESCRIPTION_ID AS preDescriptionId,pre.PRE_DESCRIPTION AS preDescription,
        pre.PRODUCTION_TYPE_ID AS productionTypeId,pre.PRODUCTION_TYPE AS productionType,
        pre.PRE_FREQUENCY_ID AS preFrequencyId,pre.PRE_FREQUENCY AS preFrequency,
        pre.PRE_ADVICE AS preAdvice,pre.CHECK_TYPE AS checkType,
        pre.PRE_ORIGIN AS preOrigin, pre.PRE_ORIGIN_ID AS preOriginId,pre.pre_origin_xh as preOriginXh, pre.PRE_ORIGIN_NAME AS preOriginName,
        pre.IS_CHECK AS isCheck,pre.CHECK_ADVISE AS checkAdvise,pre.CHECK_USERNAME AS checkUsername,pre.CHECK_TIME AS
        checkTime,
        pre.IS_PAY AS isPay,pre.PAY_TIME AS payTime,pre.PAY_USERNAME AS payUsername,pre.IS_PRINT AS isPrint,pre.IS_DEL
        AS isDel,
        pre.MAT_TOL_MONEY AS matTolMoney,pre.PRE_TOL_MONEY AS preTolMoney,pre.PRE_TOL_MONEY_REAL AS preTolMoneyReal,
        pre.DC_TYPE AS dcType,pre.DECOCT_TYPE AS decoctType,pre.IS_PRODUCTION AS isProduction,pre.HIS_PRE_ID AS
        hisPreId,
        pre.PRE_DECOCTION_FEE AS preDecoctionFee,pre.PRE_PRODUCTION_FEE AS preProductionFee,pre.PRE_EXPRESS_FEE AS
        preExpressFee,
        pre.PRE_SMO_MONEY AS preSmoMoney,pre.PRE_SPECIAL_FEE AS preSpecialFee,pre.REC_EXT_TYPE AS
        recExtType, pre.STORE_ID storeId , pre.STORE_NAME storeName ,
        pre.ACU_TYPE_ID AS acuTypeId,pre.ACU_TYPE AS acuType,pre.ACU_PROJECT_ID AS acuProjectId,pre.ACU_PROJECT AS
        acuProject,pre.PAY_CODE as payCode,
        pre.ACU_OPERATION AS acuOperation,pre.PRE_SCHEMEADDID AS preSchemeaddid,
        pre.PRE_USETIME_ID preUsetimeId,pre.PRE_USETIME_DES preUsetimeDes,
        pre.PRE_N_ML preNMl,pre.PRE_N_ML_NAME preNMlName,pre.PRE_N_BAG preNBag,pre.IS_HOSPITAL_TREATMENT isHospitalTreatment,
        pre.PRE_SMOKE_INSTRUMENT_ID preSmokeInstrumentId,pre.PRE_SMOKE_INSTRUMENT preSmokeInstrument,
        pre.PRE_SMOKE_TYPE_ID preSmokeTypeId,pre.PRE_SMOKE_TYPE preSmokeType,
        pre.IS_INSURANCE isInsurance,pre.PRE_DECOCT_NUM preDecoctNum,
        pre.IS_SPECIAL_DIS isSpecialDis,pre.SPECIAL_NAME specialName,pre.SPECIAL_CODE specialCode,
        pre.PRE_OLD_NO preOldNo,pre.PRE_ADVICE_TYPE preAdviceType,pre.PRE_ADVICE_TIME preAdviceTime,
        pre.BEGAN_TIME beganTime,pre.END_TIME endTime,pre.discharge_medication_mark as dischargeMedicationMark,
        dc.DC_NAME AS dcName, dc.DC_MOBILE AS dcMobile,dc.DC_ADDRESS AS dcAddress,
        dc.DC_COUNTY AS dcCounty,dc.DC_TOWN AS dcTown, dc.DC_VILLAGE AS dcVillage, dc.DC_STREET AS dcStreet,
        dc.DC_COUNTY_CODE dcCountyCode,dc.DC_TOWN_CODE dcTownCode,dc.DC_VILLAGE_CODE dcVillageCode,dc.DC_STREET_CODE
        dcStreetCode,
        eva.EVA_ID evaId, eva.EVA_TYPE evaType, eva.EVA_SJJ evaSjj, eva.EVA_SBF evaSbf, eva.EVA_SJW evaSjw,
        eva.EVA_YFSJJ evaYfsjj,
        eva.EVA_YSJJ evaYsjj, eva.EVA_DXSM evaDxsm, eva.EVA_BZJJ evaBzjj, eva.EVA_BY evaBy, eva.EVA_JLCB evaJlcb,
        eva.EVA_JLPD evaJlpd, eva.EVA_JLCGD evaJlcgd,
        pre.DC_TYPE_SUBCLASS as dcTypeSubclass,pre.self_pickup_id as selfPickupId,pre.urgent_sign_value as urgentSignValue
        FROM
        t_prescription AS pre
        left join t_dc_address dc on pre.dc_id = dc.DC_ADDRESS_ID
        left join t_prescription_evaluation eva on pre.PRE_ID = eva.PRE_ID and eva.EVA_TYPE = '1'
        <where>
            pre.IS_CHECK is not null
            <choose>
                <when test="isDel != null and isDel != ''">
                    and pre.is_del = #{isDel}
                </when>
                <otherwise>
                    and (pre.IS_DEL = '0' or pre.REC_EXT_TYPE = '20')
                </otherwise>
            </choose>
            <if test="recId != null and recId != ''">
                and pre.REC_ID = #{recId}
            </if>
            <if test="preNo != null and preNo != ''">
                and pre.PRE_NO in
                <foreach collection="preNo.split(',')" item="preNoItem" separator="," open="(" close=")">
                    #{preNoItem}
                </foreach>
            </if>
            <if test="isCheck != null and isCheck != ''">
                and pre.IS_CHECK in
                <foreach collection="isCheck.split(',')" item="checkItem" separator="," open="(" close=")">
                    #{checkItem}
                </foreach>
            </if>
            <if test="isPay != null and isPay != ''">
                and pre.IS_PAY in
                <foreach collection="isPay.split(',')" item="payItem" separator="," open="(" close=")">
                    #{payItem}
                </foreach>
            </if>
        </where>
        order by pre.PRE_TYPE
    </select>

    <update id="updateIsPay" parameterType="list">
        update t_prescription set IS_PAY = '1',PAY_TIME = NOW(), REC_EXT_TYPE = 50,PAY_USERID = #{paramMap.payUserid}
        ,PAY_USERNAME = #{paramMap.payUsername}
        <where>
            IS_PAY != '1' AND IS_DEL = '0' and PRE_ID in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="updateIsSend" parameterType="String">
        update t_prescription  set IS_SEND = '1' where PRE_ID = #{preId}
    </update>


    <update id="updateRecExtType" parameterType="TPrescriptionSDK">
        update t_prescription set REC_EXT_TYPE = #{recExtType} where PRE_ID = #{preId}
    </update>

    <update id="deleteByPreIdList" parameterType="List">
        update t_prescription set is_del = '1', DEL_USERNAME = '修改处方系统自动作废原处方'
        where PRE_ID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="deleteByPreNoList" parameterType="List">
        update t_prescription set is_del = '1', DEL_USERNAME = '修改处方系统自动作废原处方'
        where PRE_NO in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="delPreIfNoPayForMz">
        update t_prescription set is_del = '1', DEL_USERNAME = '门诊处方系统自动作废(默认72小时，可参数配置)'
        where is_del = '0'  AND (REC_EXT_TYPE &lt; 50 OR REC_EXT_TYPE = 110) and PRE_MZ_ZY ='1'
        AND TIMESTAMPDIFF(HOUR, PRE_TIME, NOW()) > #{interval}
    </update>

    <update id="delPreIfNoPayForZy">
        update t_prescription set is_del = '1', DEL_USERNAME = '住院处方系统自动作废(默认72小时，可参数配置)'
        where is_del = '0'  AND (REC_EXT_TYPE &lt; 50 OR REC_EXT_TYPE = 110) and PRE_MZ_ZY ='2'
        AND TIMESTAMPDIFF(HOUR, PRE_TIME, NOW()) > #{interval}
    </update>

    <update id="delPreIfIsDraft">
        update t_prescription set is_del = '1', DEL_USERNAME = '草稿处方系统每天自动作废-6.1.0'
        where is_del = '0' AND REC_EXT_TYPE = '10' and is_check is null
    </update>
    <update id="updateIsCheckByPreId" parameterType="java.lang.String">
        update t_prescription set IS_CHECK = '1',REC_EXT_TYPE = '30',CHECK_TYPE = '0',CHECK_TIME = now()
        where PRE_ID = #{preId} and IS_CHECK  = '0' and is_del = '0'
    </update>


    <select id="getPreType" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT PRE_TYPE
        FROM `t_prescription`
        WHERE pre_id = #{preId}
          and is_del = '0'
    </select>

    <select id="getPreBetweenBeganEndTime" parameterType="Map" resultType="com.jiuzhekan.cbkj.beans.business.record.TPrescription">
        SELECT BEGAN_TIME beganTime,
               END_TIME endTime
        FROM t_prescription WHERE is_del = '0' AND REC_EXT_TYPE >= 50 AND REC_EXT_TYPE != 110 and PATIENT_ID = #{patientId}
        AND ((TIMESTAMPDIFF(SECOND,BEGAN_TIME, #{beganTime}) &gt; 0 AND TIMESTAMPDIFF(SECOND,END_TIME, #{beganTime}) &lt; 0)
            or (TIMESTAMPDIFF(SECOND,BEGAN_TIME, #{endTime}) &gt; 0 AND TIMESTAMPDIFF(SECOND,END_TIME, #{endTime}) &lt; 0)
            or (TIMESTAMPDIFF(SECOND,BEGAN_TIME, #{beganTime}) &lt; 0 AND TIMESTAMPDIFF(SECOND,END_TIME, #{endTime}) &gt; 0))
    </select>

    <select id="getDraftPreId" parameterType="String" resultType="String">
        select pre_id from t_prescription where is_del= '0' and rec_id = #{recId} and REC_EXT_TYPE = '10' and is_check is null
    </select>
    <select id="getPreTypeListByPreNo" resultType="com.jiuzhekan.cbkj.beans.business.record.TPrescription" parameterType="ArrayList">

        SELECT PRE_NO preNo, PRE_TYPE preType, REC_EXT_TYPE recExtType
        FROM t_prescription
        WHERE
        PRE_NO IN
        <foreach item="item" index="index" collection="array"  separator="," open="(" close=")">
            #{item}
        </foreach>
        and is_del = '0'
    </select>
    <select id="getObjectByPreId" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_prescription where PRE_ID = #{pId}
    </select>
</mapper>
