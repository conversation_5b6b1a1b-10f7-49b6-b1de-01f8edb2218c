<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionPreparationItemMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TPrescriptionPreparationItem">
        <id column="PRE_ITEM_ID" jdbcType="VARCHAR"  property="preItemId" />
        <result column="PRE_ID" jdbcType="VARCHAR" property="preId" />
        <result column="YAOPINDM_TY" jdbcType="VARCHAR" property="yaopindmTy" />
        <result column="YPML_HIS" jdbcType="VARCHAR" property="ypmlHis" />
        <result column="YPML_CENTER" jdbcType="VARCHAR" property="ypmlCenter" />
        <result column="YPDM_HIS" jdbcType="VARCHAR" property="ypdmHis" />
        <result column="YPDM_CENTER" jdbcType="VARCHAR" property="ypdmCenter" />
        <result column="YPMC_HIS" jdbcType="VARCHAR" property="ypmcHis" />
        <result column="YPMC_CENTER" jdbcType="VARCHAR" property="ypmcCenter" />
        <result column="YPGG_HIS" jdbcType="VARCHAR" property="ypggHis" />
        <result column="YPGG_CENTER" jdbcType="VARCHAR" property="ypggCenter" />
        <result column="CDID_CENTER" jdbcType="VARCHAR" property="cdidCenter" />
        <result column="CDMC_CENTER" jdbcType="VARCHAR" property="cdmcCenter" />
        <result column="MAT_DOSE" jdbcType="DECIMAL" property="matDose" />
        <result column="MAT_DOSEUNIT_ID" jdbcType="VARCHAR" property="matDoseunitId" />
        <result column="MAT_DOSEUNIT" jdbcType="VARCHAR" property="matDoseunit" />
        <result column="MAT_DAY" jdbcType="SMALLINT" property="matDay" />
        <result column="MAT_NUM" jdbcType="DECIMAL" property="matNum" />
        <result column="BZDW_HIS" jdbcType="VARCHAR" property="bzdwHis" />
        <result column="USAGE_ID" jdbcType="VARCHAR" property="usageId" />
        <result column="USAGE" jdbcType="VARCHAR" property="usage" />
        <result column="MAT_FREQUENCY_ID" jdbcType="VARCHAR" property="matFrequencyId" />
        <result column="MAT_FREQUENCY" jdbcType="VARCHAR" property="matFrequency" />
        <result column="MAT_FREQUENCY_RATE" jdbcType="VARCHAR" property="matFrequencyRate" />
        <result column="UNIT_PRICE" jdbcType="DECIMAL" property="unitPrice" />
        <result column="TOTAL_PRICE" jdbcType="DECIMAL" property="totalPrice" />
        <result column="MAT_SEQN" jdbcType="INTEGER" property="matSeqn" />
        <result column="IS_INSURANCE" jdbcType="VARCHAR" property="isInsurance" />
        <result column="INSERT_TIME" jdbcType="TIMESTAMP" property="insertTime" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="CENTER_STORE_ID" jdbcType="VARCHAR" property="centerStoreId" />
    </resultMap>


    <sql id="Base_Column_List">
        PRE_ITEM_ID,PRE_ID,YAOPINDM_TY,YPML_HIS,YPML_CENTER,YPDM_HIS,YPDM_CENTER,YPMC_HIS,YPMC_CENTER,YPGG_HIS,YPGG_CENTER,CDID_CENTER,CDMC_CENTER,MAT_DOSE,MAT_DOSEUNIT_ID,MAT_DOSEUNIT,MAT_DAY,MAT_NUM,BZDW_HIS,USAGE_ID,`USAGE`,MAT_FREQUENCY_ID,MAT_FREQUENCY,MAT_FREQUENCY_RATE,UNIT_PRICE,TOTAL_PRICE,MAT_SEQN,IS_INSURANCE,INSERT_TIME,REMARK,CENTER_STORE_ID
    </sql>

    <delete id="physicalDeleteByPreId" parameterType="String">
        delete from t_prescription_preparation_item where PRE_ID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TPrescriptionPreparationItem">
        insert into t_prescription_preparation_item (<include refid="Base_Column_List" />) values
        (#{preItemId},#{preId},#{yaopindmTy},#{ypmlHis},#{ypmlCenter},#{ypdmHis},#{ypdmCenter},#{ypmcHis},#{ypmcCenter},#{ypggHis},#{ypggCenter},#{cdidCenter},#{cdmcCenter},#{matDose},#{matDoseunitId},#{matDoseunit},#{matDay},#{matNum},#{bzdwHis},#{usageId},#{usage},#{matFrequencyId},#{matFrequency},#{matFrequencyRate},#{unitPrice},#{totalPrice},#{matSeqn},#{isInsurance},#{insertTime},#{remark},#{centerStoreId})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_prescription_preparation_item (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.preItemId},#{item.preId},#{item.yaopindmTy},#{item.ypmlHis},#{item.ypmlCenter},#{item.ypdmHis},#{item.ypdmCenter},#{item.ypmcHis},#{item.ypmcCenter},#{item.ypggHis},#{item.ypggCenter},#{item.cdidCenter},#{item.cdmcCenter},#{item.matDose},#{item.matDoseunitId},#{item.matDoseunit},#{item.matDay},#{item.matNum},#{item.bzdwHis},#{item.usageId},#{item.usage},#{item.matFrequencyId},#{item.matFrequency},#{item.matFrequencyRate},#{item.unitPrice},#{item.totalPrice},#{item.matSeqn},#{item.isInsurance},#{item.insertTime},#{item.remark},#{item.centerStoreId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPrescriptionPreparationItem">
        update t_prescription_preparation_item
        <set>
            <if test="preId != null">
                PRE_ID = #{ preId },
            </if>
            <if test="yaopindmTy != null">
                YAOPINDM_TY = #{ yaopindmTy },
            </if>
            <if test="ypmlHis != null">
                YPML_HIS = #{ ypmlHis },
            </if>
            <if test="ypmlCenter != null">
                YPML_CENTER = #{ ypmlCenter },
            </if>
            <if test="ypdmHis != null">
                YPDM_HIS = #{ ypdmHis },
            </if>
            <if test="ypdmCenter != null">
                YPDM_CENTER = #{ ypdmCenter },
            </if>
            <if test="ypmcHis != null">
                YPMC_HIS = #{ ypmcHis },
            </if>
            <if test="ypmcCenter != null">
                YPMC_CENTER = #{ ypmcCenter },
            </if>
            <if test="ypggHis != null">
                YPGG_HIS = #{ ypggHis },
            </if>
            <if test="ypggCenter != null">
                YPGG_CENTER = #{ ypggCenter },
            </if>
            <if test="cdidCenter != null">
                CDID_CENTER = #{ cdidCenter },
            </if>
            <if test="cdmcCenter != null">
                CDMC_CENTER = #{ cdmcCenter },
            </if>
            <if test="matDose != null">
                MAT_DOSE = #{ matDose },
            </if>
            <if test="matDoseunitId != null">
                MAT_DOSEUNIT_ID = #{ matDoseunitId },
            </if>
            <if test="matDoseunit != null">
                MAT_DOSEUNIT = #{ matDoseunit },
            </if>
            <if test="matDay != null">
                MAT_DAY = #{ matDay },
            </if>
            <if test="matNum != null">
                MAT_NUM = #{ matNum },
            </if>
            <if test="bzdwHis != null">
                BZDW_HIS = #{ bzdwHis },
            </if>
            <if test="usageId != null">
                USAGE_ID = #{ usageId },
            </if>
            <if test="usage != null">
                `USAGE` = #{ usage },
            </if>
            <if test="matFrequencyId != null">
                MAT_FREQUENCY_ID = #{ matFrequencyId },
            </if>
            <if test="matFrequency != null">
                MAT_FREQUENCY = #{ matFrequency },
            </if>
            <if test="matFrequencyRate != null">
                MAT_FREQUENCY_RATE = #{ matFrequencyRate },
            </if>
            <if test="unitPrice != null">
                UNIT_PRICE = #{ unitPrice },
            </if>
            <if test="totalPrice != null">
                TOTAL_PRICE = #{ totalPrice },
            </if>
            <if test="matSeqn != null">
                MAT_SEQN = #{ matSeqn },
            </if>
            <if test="isInsurance != null">
                IS_INSURANCE = #{ isInsurance },
            </if>
            <if test="insertTime != null">
                INSERT_TIME = #{ insertTime },
            </if>
            <if test="remark != null">
                REMARK = #{ remark },
            </if>
        </set>
        where PRE_ITEM_ID = #{ preItemId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_prescription_preparation_item where PRE_ITEM_ID = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TPrescriptionPreparationItem" resultMap="BaseResultMap">
        SELECT PRE_ITEM_ID,PRE_ID,YAOPINDM_TY,YPML_HIS,YPML_CENTER,YPDM_HIS,YPDM_CENTER,YPMC_HIS,YPMC_CENTER,YPGG_HIS,YPGG_CENTER,CDID_CENTER,CDMC_CENTER,MAT_DOSE,MAT_DOSEUNIT_ID,MAT_DOSEUNIT,MAT_DAY,MAT_NUM,BZDW_HIS,USAGE_ID,`USAGE`,MAT_FREQUENCY_ID,MAT_FREQUENCY,MAT_FREQUENCY_RATE,UNIT_PRICE,TOTAL_PRICE,MAT_SEQN,IS_INSURANCE,INSERT_TIME,REMARK
        from t_prescription_preparation_item
        <where>
            <if test=" ypmcHis != null and ypmcHis!='' ">
                and YPMC_HIS like CONCAT('%',trim(#{ypmcHis}),'%')
            </if>
        </where>
    </select>

    <!--查询基础语句返回对象-->
    <select id="getListByPreId" parameterType="String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from t_prescription_preparation_item
        where PRE_ID = #{preId}
        order by MAT_SEQN
    </select>


    <select id="getPreparationDailyNumSum" parameterType="TPrescriptionPreparationItem" resultType="Decimal">
        SELECT SUM(item.mat_num)
        FROM t_prescription_preparation_item item
        JOIN t_prescription pre ON pre.PRE_ID = item.PRE_ID
        WHERE pre.is_del = '0' AND pre.REC_EXT_TYPE >= 44 AND pre.REC_EXT_TYPE != 45 AND pre.REC_EXT_TYPE != 110
        AND pre.PATIENT_ID = #{patientId} AND DATE_FORMAT(pre.PRE_TIME, '%Y-%m-%d') = CURDATE()
        AND item.YPML_HIS = #{ypmlHis} AND item.YPDM_HIS = #{ypdmHis}
    </select>
</mapper>