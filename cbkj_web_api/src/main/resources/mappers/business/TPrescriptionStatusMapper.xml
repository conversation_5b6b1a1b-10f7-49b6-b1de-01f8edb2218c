<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionStatusMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TPrescriptionStatus">
        <id column="PRE_STATUS_ID" jdbcType="VARCHAR"  property="preStatusId" />
        <result column="PRE_ID" jdbcType="VARCHAR" property="preId" />
        <result column="PRE_OPERATION_USERID" jdbcType="VARCHAR" property="preOperationUserid" />
        <result column="PRE_OPERATION_USERNAME" jdbcType="VARCHAR" property="preOperationUsername" />
        <result column="PRE_OPERATION_TIME" jdbcType="TIMESTAMP" property="preOperationTime" />
        <result column="PRE_OPERATION_TYPE" jdbcType="INTEGER" property="preOperationType" />
    </resultMap>


    <sql id="Base_Column_List">
    PRE_STATUS_ID,PRE_ID,PRE_OPERATION_USERID,PRE_OPERATION_USERNAME,PRE_OPERATION_TIME,PRE_OPERATION_TYPE
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPrescriptionStatus">
        delete from t_prescription_status where PRE_STATUS_ID = #{ preStatusId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_prescription_status where PRE_STATUS_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TPrescriptionStatus">
        insert into t_prescription_status (PRE_STATUS_ID,PRE_ID,PRE_OPERATION_USERID,PRE_OPERATION_USERNAME,PRE_OPERATION_TIME,PRE_OPERATION_TYPE) values
        (#{preStatusId},#{preId},#{preOperationUserid},#{preOperationUsername},#{preOperationTime},#{preOperationType})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_prescription_status (PRE_STATUS_ID,PRE_ID,PRE_OPERATION_USERID,PRE_OPERATION_USERNAME,PRE_OPERATION_TIME,PRE_OPERATION_TYPE) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.preStatusId},#{item.preId},#{item.preOperationUserid},#{item.preOperationUsername},#{item.preOperationTime},#{item.preOperationType})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPrescriptionStatus">
        update t_prescription_status
        <set>
             <if test="preId != null">
                PRE_ID = #{ preId },
             </if>
             <if test="preOperationUserid != null">
                PRE_OPERATION_USERID = #{ preOperationUserid },
             </if>
             <if test="preOperationUsername != null">
                PRE_OPERATION_USERNAME = #{ preOperationUsername },
             </if>
             <if test="preOperationTime != null">
                PRE_OPERATION_TIME = #{ preOperationTime },
             </if>
             <if test="preOperationType != null">
                PRE_OPERATION_TYPE = #{ preOperationType },
             </if>
        </set>
        where PRE_STATUS_ID = #{ preStatusId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update t_prescription_status set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_prescription_status where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_prescription_status where PRE_STATUS_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_prescription_status where PRE_STATUS_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TPrescriptionStatus" resultType="Map">
        SELECT PRE_STATUS_ID,PRE_ID,PRE_OPERATION_USERID,PRE_OPERATION_USERNAME,PRE_OPERATION_TIME,PRE_OPERATION_TYPE  from t_prescription_status
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TPrescriptionStatus" resultMap="BaseResultMap">
        SELECT PRE_STATUS_ID,PRE_ID,PRE_OPERATION_USERID,PRE_OPERATION_USERNAME,PRE_OPERATION_TIME,PRE_OPERATION_TYPE
        from t_prescription_status
        where 1=1
        <!--条件-->
        <!--<if test="name != null and name!='' ">-->
        <!--and name like CONCAT('%',trim(#{name}),'%')-->
        <!--</if>-->
    </select>

</mapper>