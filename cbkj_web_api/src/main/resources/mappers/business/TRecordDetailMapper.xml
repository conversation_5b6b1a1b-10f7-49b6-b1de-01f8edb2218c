<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TRecordDetailMapper">


    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TRecordDetail">
        <id column="REC_DETAIL_ID" jdbcType="VARCHAR"  property="recDetailId" />
        <result column="REC_ID" jdbcType="VARCHAR" property="recId" />
        <result column="TEMPL_ID" jdbcType="VARCHAR" property="templId" />
        <result column="DETAIL_ID" jdbcType="VARCHAR" property="detailId" />
        <result column="DETAIL_NAME" jdbcType="VARCHAR" property="detailName" />
        <result column="DETAIL_TYPE" jdbcType="VARCHAR" property="detailType" />
        <result column="DETAIL_CONTENT" jdbcType="VARCHAR" property="detailContent" />
        <result column="DETAIL_NUM" jdbcType="TINYINT" property="detailNum" />
    </resultMap>


    <sql id="Base_Column_List">
    REC_DETAIL_ID,REC_ID,TEMPL_ID,DETAIL_ID,DETAIL_NAME,DETAIL_TYPE,DETAIL_CONTENT,DETAIL_NUM
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRecordDetail">
        delete from t_record_detail where REC_DETAIL_ID = #{ recDetailId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record_detail where REC_DETAIL_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TRecordDetail">
        insert into t_record_detail (REC_DETAIL_ID,REC_ID,TEMPL_ID,DETAIL_ID,DETAIL_NAME,DETAIL_TYPE,DETAIL_CONTENT,DETAIL_NUM) values
        (#{recDetailId},#{recId},#{templId},#{detailId},#{detailName},#{detailType},#{detailContent},#{detailNum})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_record_detail (REC_DETAIL_ID,REC_ID,TEMPL_ID,DETAIL_ID,DETAIL_NAME,DETAIL_TYPE,DETAIL_CONTENT,DETAIL_NUM) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.recDetailId},#{item.recId},#{item.templId},#{item.detailId},#{item.detailName},#{item.detailType},#{item.detailContent},#{item.detailNum})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRecordDetail">
        update t_record_detail
        <set>
            <if test="recId != null">
                REC_ID = #{ recId },
            </if>
            <if test="templId != null">
                TEMPL_ID = #{ templId },
            </if>
            <if test="detailId != null">
                DETAIL_ID = #{ detailId },
            </if>
            <if test="detailName != null">
                DETAIL_NAME = #{ detailName },
            </if>
            <if test="detailType != null">
                DETAIL_TYPE = #{ detailType },
            </if>
            <if test="detailContent != null">
                DETAIL_CONTENT = #{ detailContent },
            </if>
            <if test="detailNum != null">
                DETAIL_NUM = #{ detailNum },
            </if>
        </set>
        where REC_DETAIL_ID = #{ recDetailId }
    </update>


    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_record_detail where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record_detail where REC_DETAIL_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record_detail where REC_DETAIL_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TRecordDetail" resultType="Map">
        SELECT REC_DETAIL_ID,REC_ID,TEMPL_ID,DETAIL_ID,DETAIL_NAME,DETAIL_TYPE,DETAIL_CONTENT,DETAIL_NUM  from t_record_detail
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TRecordDetail" resultMap="BaseResultMap">
        SELECT REC_DETAIL_ID,REC_ID,TEMPL_ID,DETAIL_ID,DETAIL_NAME,DETAIL_TYPE,DETAIL_CONTENT,DETAIL_NUM
        from t_record_detail
        <where>
        </where>
    </select>

    <select id="getListByRecId" parameterType="TRecordDetail" resultMap="BaseResultMap">
        SELECT DETAIL_NAME,DETAIL_TYPE,DETAIL_CONTENT
        from t_record_detail
        where 1=1 and REC_ID =#{recId}
        <if test="detailType != null and detailType!='' ">
            and DETAIL_TYPE = #{detailType}
        </if>
        order by DETAIL_NUM
    </select>

    <delete id="deleteByRecId" parameterType="String">
        delete from t_record_detail where REC_ID =#{recId}
    </delete>

    <select id="getTemplateIdByRegisterId" parameterType="String" resultType="String">
        select trd.TEMPL_ID from t_record_detail trd
        where trd.rec_id in (select td.rec_id from t_record td where td.REGISTER_ID = #{registerId})
        limit 1
    </select>

    <select id="getTemplateIdByRecID" parameterType="String" resultType="String">
        select TEMPL_ID from t_record_detail where REC_ID =#{recId} limit 1
    </select>

    <select id="getCountTempDetailByRecId" parameterType="String" resultType="int">
        select count(*) from t_record_detail where REC_ID =#{recId}
    </select>
</mapper>