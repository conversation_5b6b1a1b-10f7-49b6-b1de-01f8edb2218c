<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TRecordMapper">
    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TRecord">
        <id column="REC_ID" jdbcType="VARCHAR" property="recId"/>
        <result column="REGISTER_ID" jdbcType="VARCHAR" property="registerId"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
        <result column="PATIENT_ID" jdbcType="VARCHAR" property="patientId"/>
        <result column="REC_NAME" jdbcType="VARCHAR" property="recName"/>
        <result column="REC_AGE1" jdbcType="SMALLINT" property="recAge1"/>
        <result column="REC_AGEUNIT1" jdbcType="VARCHAR" property="recAgeunit1"/>
        <result column="REC_AGE2" jdbcType="SMALLINT" property="recAge2"/>
        <result column="REC_AGEUNIT2" jdbcType="VARCHAR" property="recAgeunit2"/>
        <result column="REC_GENDER" jdbcType="VARCHAR" property="recGender"/>
        <result column="REC_ADDRESS" jdbcType="VARCHAR" property="recAddress"/>
        <result column="GRAVIDITY" jdbcType="VARCHAR" property="gravidity"/>
        <result column="REC_TRE_TIME" jdbcType="TIMESTAMP" property="recTreTime"/>
        <result column="DOC_ID" jdbcType="VARCHAR" property="docId"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="APP_NAME" jdbcType="VARCHAR" property="appName"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="INS_NAME" jdbcType="VARCHAR" property="insName"/>
        <result column="DEPT_ID" jdbcType="VARCHAR" property="deptId"/>
        <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName"/>
        <result column="HOSPITAL_NO" jdbcType="VARCHAR" property="hospitalNo"/>
        <result column="WARD_ID" jdbcType="VARCHAR" property="wardId"/>
        <result column="WARD_NAME" jdbcType="VARCHAR" property="wardName"/>
        <result column="BED_NO" jdbcType="VARCHAR" property="bedNo"/>
        <result column="INS_TRE_ID" jdbcType="VARCHAR" property="insTreId"/>
        <result column="REC_TRE_TYPE" jdbcType="VARCHAR" property="recTreType"/>
        <result column="WESTERN_DISEASE_ID" jdbcType="VARCHAR" property="westernDiseaseId"/>
        <result column="WESTERN_DISEASE" jdbcType="VARCHAR" property="westernDisease"/>
        <result column="DIS_ID" jdbcType="VARCHAR" property="disId"/>
        <result column="DIS_NAME" jdbcType="VARCHAR" property="disName"/>
        <result column="SYM_ID" jdbcType="VARCHAR" property="symId"/>
        <result column="SYM_NAME" jdbcType="VARCHAR" property="symName"/>
        <result column="THE_CODE" jdbcType="VARCHAR" property="theCode"/>
        <result column="THE_NAMES" jdbcType="VARCHAR" property="theNames"/>
        <result column="PATIENT_CONTENT" jdbcType="VARCHAR" property="patientContent"/>
        <result column="NOW_DESC" jdbcType="VARCHAR" property="nowDesc"/>
        <result column="PAST_DESC" jdbcType="VARCHAR" property="pastDesc"/>
        <result column="FAMILY_DESC" jdbcType="VARCHAR" property="familyDesc"/>
        <result column="ALLERGY_DESC" jdbcType="VARCHAR" property="allergyDesc"/>
        <result column="TONGUE" jdbcType="VARCHAR" property="tongue"/>
        <result column="PULSE" jdbcType="VARCHAR" property="pulse"/>
        <result column="FOUR_DIAGNOSIS" jdbcType="VARCHAR" property="fourDiagnosis"/>
        <result column="PHYSICAL" jdbcType="VARCHAR" property="physical"/>
        <result column="AUXILIARY_EXAM" jdbcType="VARCHAR" property="auxiliaryExam"/>
        <result column="PATIENT_HEIGHT" jdbcType="VARCHAR" property="patientHeight"/>
        <result column="PATIENT_WEIGHT" jdbcType="VARCHAR" property="patientWeight"/>
        <result column="REC_FIRSTID" jdbcType="VARCHAR" property="recFirstid"/>
        <result column="VIS_NUM" jdbcType="SMALLINT" property="visNum"/>
        <result column="REC_LASTDATE" jdbcType="DATE" property="recLastdate"/>
        <result column="REC_ONLINE_REDIAGNOSIS" jdbcType="SMALLINT" property="recOnlineRediagnosis"/>
        <result column="REC_REDIAGNOSIS_DEADLINE" jdbcType="TIMESTAMP" property="recRediagnosisDeadline"/>
        <result column="CHECK_TYPE" jdbcType="VARCHAR" property="checkType"/>
        <result column="IS_CHECK" jdbcType="VARCHAR" property="isCheck"/>
        <result column="CHECK_ADVISE" jdbcType="VARCHAR" property="checkAdvise"/>
        <result column="IS_DEL" jdbcType="VARCHAR" property="isDel"/>
        <result column="DOC_NAME" jdbcType="VARCHAR" property="docName"/>
        <result column="PATIENT_HEIGHT" jdbcType="VARCHAR" property="patientHeight"/>
        <result column="PATIENT_WEIGHT" jdbcType="VARCHAR" property="patientWeight"/>
        <result column="TREATMENT_ADVICE" jdbcType="VARCHAR" property="treatmentAdvice"/>

        <collection property="recordDetailList" ofType="com.jiuzhekan.cbkj.beans.business.record.TRecordDetail">
            <id column="REC_DETAIL_ID" property="recDetailId"/>
            <result column="REC_ID" property="recId"/>
            <result column="TEMPL_ID" property="templId"/>
            <result column="DETAIL_ID" property="detailId"/>
            <result column="REC_DETAIL_NAME" property="recDetailName"/>
            <result column="REC_DETAIL_TYPE" property="recDetailType"/>
            <result column="REC_DETAIL_CONTENT" property="recDetailContent"/>
            <result column="TEMP_INFONUM" property="templInfonum"/>
        </collection>
        <collection property="prescriptionList" ofType="com.jiuzhekan.cbkj.beans.business.record.TPrescription">
            <id column="PRE_ID" property="preId"/>
            <result column="PRE_NO" property="preNo"/>
            <result column="PRE_TYPE" property="preType"/>
            <result column="PRE_DESCRIPTION" property="preDescription"/>
            <result column="PRE_USETIME_DES" property="preUsetimeDes"/>
            <result column="PRE_FREQUENCY" property="preFrequency"/>
            <result column="PRE_ADVICE" property="preAdvice"/>
            <result column="PRE_NUM" property="preNum"/>
            <result column="MAT_TOL_MONEY" jdbcType="DECIMAL" property="matTolMoney"/>
            <result column="PRE_TOL_MONEY" jdbcType="DECIMAL" property="preTolMoney"/>
            <result column="CHECK_USERNAME" jdbcType="VARCHAR" property="checkUsername"/>
            <collection property="itemList" ofType="com.jiuzhekan.cbkj.beans.business.record.TPrescriptionItem">
                <id column="PRE_ITEM_ID" property="preItemId"/>
                <result column="MAI_ID" property="matId"/>
                <result column="MAT_NAME" property="matName"/>
                <result column="YPMC_HIS" property="ypmcHis"/>
                <result column="MAT_NUM" property="matNum"/>
                <result column="BZDW_HIS" property="bzdwHis"/>
            </collection>
        </collection>
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.business.record.TRecordVo">
        <id column="REC_ID" jdbcType="VARCHAR" property="recId"/>
        <result column="PATIENT_CONTENT" jdbcType="VARCHAR" property="patientContent"/>
        <result column="NOW_DESC" jdbcType="VARCHAR" property="nowDesc"/>
        <result column="PAST_DESC" jdbcType="VARCHAR" property="pastDesc"/>
        <result column="FOUR_DIAGNOSIS" jdbcType="VARCHAR" property="fourDiagnosis"/>
        <result column="PHYSICAL" jdbcType="VARCHAR" property="physical"/>
        <result column="AUXILIARY_EXAM" jdbcType="VARCHAR" property="auxiliaryExam"/>
        <result column="ALLERGY_DESC" jdbcType="VARCHAR" property="allergyDesc"/>
        <result column="TREATMENT_ADVICE" jdbcType="VARCHAR" property="treatmentAdvice"/>
        <result column="PATIENT_NAME" jdbcType="VARCHAR" property="patientName"/>
        <result column="PATIENT_GENDER" jdbcType="VARCHAR" property="patientGender"/>
        <result column="PRESONAL_DESC" jdbcType="VARCHAR" property="personalDesc"/>
        <result column="REC_AGE1" jdbcType="VARCHAR" property="recAge1"/>
        <result column="REC_AGEUNIT1" jdbcType="VARCHAR" property="recAgeUnit1"/>
        <result column="PATIENT_CERTIFICATE" jdbcType="VARCHAR" property="patientCertificate"/>
        <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName"/>
        <result column="name_zh" jdbcType="VARCHAR" property="nameZh"/>
        <result column="DIS_NAME" jdbcType="VARCHAR" property="disName"/>
        <result column="DIS_ID" jdbcType="VARCHAR" property="disId"/>
        <result column="SYM_NAME" jdbcType="VARCHAR" property="symName"/>
        <result column="SYM_ID" jdbcType="VARCHAR" property="symId"/>
        <result column="WESTERN_DISEASE" jdbcType="VARCHAR" property="westernDisease"/>
        <result column="THE_NAMES" jdbcType="VARCHAR" property="theNames"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="INS_NAME" jdbcType="VARCHAR" property="insName"/>
        <result column="DOC_ID" jdbcType="VARCHAR" property="docId"/>
        <collection property="prescriptionMinList" ofType="com.jiuzhekan.cbkj.beans.business.record.PrescriptionMin">
            <result column="PRE_ID" property="preId"/>
            <result column="PRE_TYPE" property="preType"/>
            <result column="PRE_NUM" property="preNum"/>
            <result column="PRE_DESCRIPTION_ID" property="preDescriptionId"/>
            <result column="PRE_DESCRIPTION" property="preDescription"/>
            <result column="PRE_FREQUENCY_ID" property="preFrequencyId"/>
            <result column="PRE_FREQUENCY" property="preFrequency"/>
            <result column="PRE_USETIME_ID" property="preUseTimeId"/>
            <result column="PRE_USETIME_DES" property="preUseTimeDes"/>
            <result column="PRE_SMOKE_TYPE_ID" property="preSmokeTypeId"/>
            <result column="PRE_SMOKE_TYPE" property="preSmokeType"/>
            <result column="PRE_DOCTORNAME" property="preDoctorName"/>
            <result column="PRE_TIME" property="preTime"/>
            <result column="PRE_N_BAG" property="preNbag"/>
            <result column="PRE_N_ML" property="preNMl"/>
            <result column="PRE_N_ML_NAME" property="preNMlName"/>
            <result column="PRE_SMOKE_INSTRUMENT_ID" property="preSmokeInstrumentId"/>
            <result column="PRE_SMOKE_INSTRUMENT" property="preSmokeInstrument"/>
            <result column="PRE_DECOCT_NUM" property="preDecoctNum"/>
            <result column="ACU_TYPE_ID" property="acuTypeId"/>
            <result column="ACU_TYPE" property="acuType"/>
            <result column="ACU_PROJECT_ID" property="acuProjectId"/>
            <result column="ACU_PROJECT" property="acuProject"/>
            <result column="ACU_OPERATION" property="acuOperation"/>
            <result column="DECOCT_TYPE" property="decoctType"/>
            <result column="IS_PRODUCTION" property="isProduction"/>
            <result column="IS_SPECIAL_DIS" property="isSpeaialDis"/>
            <result column="STORE_ID" property="storeId"/>
            <result column="DC_TYPE" property="dcType"/>
            <result column="DC_ID" property="dcId"/>
            <result column="PRE_MAT_TYPE" property="preMatType"/>
            <result column="PRE_ADVICE" property="preAdvice"/>
            <result column="PRE_ORIGIN" property="preOrigin"/>
            <result column="PRE_ORIGIN_ID" property="preOriginId"/>
            <result column="PRE_ORIGIN_NAME" property="preOriginName"/>
            <result column="PRODUCTION_TYPE" property="productionType"/>
            <result column="PRODUCTION_TYPE_ID" property="productionTypeId"/>
            <result column="BEGAN_TIME" property="beganTime"/>
            <result column="END_TIME" property="endTime"/>
            <collection property="prescriptionItemMinList"
                        ofType="com.jiuzhekan.cbkj.beans.business.record.PrescriptionItemMin">
                <result column="MAT_ID" property="matId"/>
                <result column="MAT_NAME" property="matName"/>
                <result column="YPDM_HIS" property="ypdmHis"/>
                <result column="YPMC_HIS" property="ypmcHis"/>
                <result column="YPDM_CENTER" property="ypdmCenter"/>
                <result column="YPMC_CENTER" property="ypmcCenter"/>
                <result column="YPML_CENTER" property="ypmlCenter"/>
                <result column="YPML_HIS" property="ypmlHis"/>
                <result column="YAOPINDM_TY" property="yaopindmTy"/>
                <result column="YPGG_HIS" property="ypggHis"/>
                <result column="YPGG_CENTER" property="ypggCenter"/>
                <result column="CDID_CENTER" property="cdidCenter"/>
                <result column="CDMC_CENTER" property="cdmcCenter"/>
                <result column="MAT_DOSE" property="matDose"/>
                <result column="MAT_XSJ" property="matXsj"/>
                <result column="MAT_SEQN" property="matSeqn"/>
                <result column="ZHUANHUANXS" property="zhuanhuanxs"/>
                <result column="CENTER_STORE_ID" property="centerStoreId"/>
                <result column="YFID_HIS" property="yfidHis"/>
                <result column="YFID_CENTER" property="yfidCenter"/>
                <result column="YFMC_HIS" property="yfmcHis"/>
                <result column="YFMC_CENTER" property="yfmcCenter"/>
                <result column="BZDW_HIS" property="bzdwHis"/>
                <result column="REMARK" property="remark"/>
            </collection>
            <collection property="prescriptionAcuItemList"
                        ofType="com.jiuzhekan.cbkj.beans.business.record.TPrescriptionAcuItem">
                <result column="ACU_ITEM_ID" property="acuItemId"/>
                <result column="ACU_ID" property="acuId"/>
                <result column="ACU_CODE" property="acuCode"/>
                <result column="ACU_NAME" property="acuName"/>
                <result column="ACU_NUM" property="acuNum"/>
                <result column="ACU_SEQN" property="acuSeqn"/>
            </collection>
        </collection>
    </resultMap>


    <sql id="Base_Column_List">
        REC_ID,REGISTER_ID,PATIENT_ID,REC_NAME,REC_AGE1,REC_AGEUNIT1,REC_AGE2,REC_AGEUNIT2,REC_GENDER,REC_ADDRESS,GRAVIDITY,REC_TRE_TIME,DEPT_ID,DEPT_NAME,HOSPITAL_NO,WARD_ID,WARD_NAME,BED_NO,DOC_ID,DOC_NAME,APP_ID,INS_CODE,INS_TRE_ID,REC_TRE_TYPE,WESTERN_DISEASE_ID,WESTERN_DISEASE,DIS_ID,DIS_NAME,SYM_ID,SYM_NAME,THE_CODE,THE_NAMES,PATIENT_CONTENT,NOW_DESC,PAST_DESC,FAMILY_DESC,ALLERGY_DESC,TONGUE,PULSE,FOUR_DIAGNOSIS,PHYSICAL,AUXILIARY_EXAM,REC_FIRSTID,VIS_NUM,REC_LASTDATE,REC_ONLINE_REDIAGNOSIS,REC_REDIAGNOSIS_DEADLINE,CHECK_TYPE,IS_CHECK,CHECK_ADVISE,IS_DEL,PATIENT_HEIGHT,PATIENT_WEIGHT,TREATMENT_ADVICE
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord">
        delete
        from t_record
        where REC_ID = #{ recId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record where REC_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord">
        insert into t_record (<include refid="Base_Column_List"/>) values
        (#{recId},#{registerId},#{patientId},#{recName},#{recAge1},#{recAgeunit1},#{recAge2},#{recAgeunit2},#{recGender},#{recAddress},#{gravidity},#{recTreTime},#{deptId},#{deptName},#{hospitalNo},#{wardId},#{wardName},#{bedNo},#{docId},#{docName},#{appId},#{insCode},#{insTreId},#{recTreType},#{westernDiseaseId},#{westernDisease},#{disId},#{disName},#{symId},#{symName},#{theCode},#{theNames},#{patientContent},#{nowDesc},#{pastDesc},#{familyDesc},#{allergyDesc},#{tongue},#{pulse},#{fourDiagnosis},#{physical},#{auxiliaryExam},#{recFirstid},#{visNum},#{recLastdate},#{recOnlineRediagnosis},#{recRediagnosisDeadline},#{checkType},#{isCheck},#{checkAdvise},#{isDel},#{patientHeight},#{patientWeight},#{treatmentAdvice})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_record (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.recId},#{item.registerId},#{item.patientId},#{item.recName},#{item.recAge1},#{item.recAgeunit1},#{item.recAge2},#{item.recAgeunit2},#{item.recGender},#{item.recAddress},#{item.gravidity},#{item.recTreTime},#{item.deptId},#{item.deptName},#{item.hospitalNo},#{item.wardId},#{item.wardName},#{item.bedNo},#{item.docId},#{item.docName},#{item.appId},#{item.insCode},#{item.insTreId},#{item.recTreType},#{item.westernDiseaseId},#{item.westernDisease},#{item.disId},#{item.disName},#{item.symId},#{item.symName},#{iterm.theCode},#{item.theNames},#{item.patientContent},#{item.nowDesc},#{item.pastDesc},#{item.familyDesc},#{item.allergyDesc},#{item.tongue},#{item.pulse},#{item.fourDiagnosis},#{item.physical},#{item.auxiliaryExam},#{item.recFirstid},#{item.visNum},#{item.recLastdate},#{item.recOnlineRediagnosis},#{item.recRediagnosisDeadline},#{item.checkType},#{item.isCheck},#{item.checkAdvise},#{item.isDel},
            #{item.patientHeight},#{item.patientWeight},#{treatmentAdvice})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord">
        update t_record
        <set>
            <if test="registerId != null">
                REGISTER_ID = #{ registerId },
            </if>
            <if test="patientId != null">
                PATIENT_ID = #{ patientId },
            </if>
            <if test="recName != null">
                REC_NAME = #{ recName },
            </if>
            <if test="recAge1 != null">
                REC_AGE1 = #{ recAge1 },
            </if>
            <if test="recAgeunit1 != null">
                REC_AGEUNIT1 = #{ recAgeunit1 },
            </if>
            <if test="recAge2 != null">
                REC_AGE2 = #{ recAge2 },
            </if>
            <if test="recAgeunit2 != null">
                REC_AGEUNIT2 = #{ recAgeunit2 },
            </if>
            <if test="recGender != null">
                REC_GENDER = #{ recGender },
            </if>
            <if test="recAddress != null">
                REC_ADDRESS = #{ recAddress },
            </if>
            <if test="gravidity != null">
                GRAVIDITY = #{ gravidity },
            </if>
            <if test="recTreTime != null">
                REC_TRE_TIME = #{ recTreTime },
            </if>
            <if test="deptId != null">
                DEPT_ID = #{ deptId },
            </if>
            <if test="deptName != null">
                DEPT_NAME = #{ deptName },
            </if>
            <if test="hospitalNo != null">
                HOSPITAL_NO = #{ hospitalNo },
            </if>
            <if test="wardId != null">
                WARD_ID = #{ wardId },
            </if>
            <if test="wardName != null">
                WARD_NAME = #{ wardName },
            </if>
            <if test="bedNo != null">
                BED_NO = #{ bedNo },
            </if>
            <if test="docId != null">
                DOC_ID = #{ docId },
            </if>
            <if test="appId != null">
                APP_ID = #{ appId },
            </if>
            <if test="insCode != null">
                INS_CODE = #{ insCode },
            </if>
            <if test="insTreId != null">
                INS_TRE_ID = #{ insTreId },
            </if>
            <if test="recTreType != null">
                REC_TRE_TYPE = #{ recTreType },
            </if>
            <if test="westernDiseaseId != null">
                WESTERN_DISEASE_ID = #{ westernDiseaseId },
            </if>
            <if test="westernDisease != null">
                WESTERN_DISEASE = #{ westernDisease },
            </if>
            <if test="disId != null and disId != ''">
                DIS_ID = #{ disId },
            </if>
            <if test="disName != null and disName != ''">
                DIS_NAME = #{ disName },
            </if>
            <if test="symId != null and symId != ''">
                SYM_ID = #{ symId },
            </if>
            <if test="symName != null and symName != ''">
                SYM_NAME = #{ symName },
            </if>
            <if test="theCode != null and theCode != ''">
                THE_CODE = #{theCode},
            </if>
            <if test="theNames != null and theNames != ''">
                THE_NAMES = #{ theNames },
            </if>
            <if test="patientContent != null and patientContent !=''">
                PATIENT_CONTENT = #{ patientContent },
            </if>
            <if test="nowDesc != null and nowDesc != ''">
                NOW_DESC = #{ nowDesc },
            </if>
            <if test="pastDesc != null and pastDesc != ''">
                PAST_DESC = #{ pastDesc },
            </if>
            <if test="familyDesc != null and familyDesc != ''">
                FAMILY_DESC = #{ familyDesc },
            </if>
            <if test="allergyDesc != null and allergyDesc != ''">
                ALLERGY_DESC = #{ allergyDesc },
            </if>
            <if test="tongue != null and tongue != ''">
                TONGUE = #{ tongue },
            </if>
            <if test="pulse != null and pulse  != ''">
                PULSE = #{ pulse },
            </if>
            <if test="fourDiagnosis != null and fourDiagnosis != ''">
                FOUR_DIAGNOSIS = #{ fourDiagnosis },
            </if>
            <if test="physical != null and physical != ''">
                PHYSICAL = #{ physical },
            </if>
            <if test="auxiliaryExam != null and auxiliaryExam !=''">
                AUXILIARY_EXAM = #{ auxiliaryExam },
            </if>
            <if test="recFirstid != null">
                REC_FIRSTID = #{ recFirstid },
            </if>
            <if test="visNum != null">
                VIS_NUM = #{ visNum },
            </if>
            <if test="recLastdate != null">
                REC_LASTDATE = #{ recLastdate },
            </if>
            <if test="recOnlineRediagnosis != null">
                REC_ONLINE_REDIAGNOSIS = #{ recOnlineRediagnosis },
            </if>
            <if test="recRediagnosisDeadline != null">
                REC_REDIAGNOSIS_DEADLINE = #{ recRediagnosisDeadline },
            </if>
            <if test="checkType != null">
                CHECK_TYPE = #{ checkType },
            </if>
            <if test="isCheck != null">
                IS_CHECK = #{ isCheck },
            </if>
            <if test="checkAdvise != null">
                CHECK_ADVISE = #{ checkAdvise },
            </if>
            <if test="isDel != null">
                IS_DEL = #{ isDel },
            </if>
            <if test="patientHeight != null and patientHeight != ''">
                PATIENT_HEIGHT = #{patientHeight},
            </if>
            <if test="patientWeight != null and patientWeight != ''">
                PATIENT_WEIGHT = #{patientWeight},
            </if>
        </set>
        where REC_ID = #{ recId }
    </update>

    <update id="updateRecordWhenSavePre" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord">
        update t_record
        <set>
            <if test="westernDiseaseId != null">
                WESTERN_DISEASE_ID = #{ westernDiseaseId },
            </if>
            <if test="westernDisease != null">
                WESTERN_DISEASE = #{ westernDisease },
            </if>
            <if test="disId != null and disId != ''">
                DIS_ID = #{ disId },
            </if>
            <if test="disName != null and disName != ''">
                DIS_NAME = #{ disName },
            </if>
            <if test="symId != null and symId != ''">
                SYM_ID = #{ symId },
            </if>
            <if test="symName != null and symName != ''">
                SYM_NAME = #{ symName },
            </if>
            <if test="theCode != null and theCode != ''">
                THE_CODE = #{theCode},
            </if>
            <if test="theNames != null and theNames != ''">
                THE_NAMES = #{ theNames },
            </if>
            <if test="recFirstid != null">
                REC_FIRSTID = #{ recFirstid },
            </if>
            <if test="visNum != null">
                VIS_NUM = #{ visNum },
            </if>
            <if test="recLastdate != null">
                REC_LASTDATE = #{ recLastdate },
            </if>
            <if test="recOnlineRediagnosis != null">
                REC_ONLINE_REDIAGNOSIS = #{ recOnlineRediagnosis },
            </if>
            <if test="recRediagnosisDeadline != null">
                REC_REDIAGNOSIS_DEADLINE = #{ recRediagnosisDeadline },
            </if>
            <if test="checkType != null">
                CHECK_TYPE = #{ checkType },
            </if>
            <if test="isCheck != null">
                IS_CHECK = #{ isCheck },
            </if>
            <if test="checkAdvise != null">
                CHECK_ADVISE = #{ checkAdvise },
            </if>
        </set>
        where REC_ID = #{ recId }
    </update>

    <update id="updateRecordWhenCheckPre" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord">
        update t_record
        <set>
            <if test="checkType != null">
                CHECK_TYPE = #{ checkType },
            </if>
            <if test="isCheck != null">
                IS_CHECK = #{ isCheck },
            </if>
            <if test="checkAdvise != null">
                CHECK_ADVISE = #{ checkAdvise },
            </if>
        </set>
        where REC_ID = #{ recId }
    </update>

    <update id="updateRecordWhenSaveElectronicRecord" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord">
        update t_record
        <set>
            <if test="patientContent != null ">
                PATIENT_CONTENT = #{ patientContent },
            </if>
            <if test="nowDesc != null ">
                NOW_DESC = #{ nowDesc },
            </if>
            <if test="personalDesc != null ">
                PRESONAL_DESC = #{personalDesc},
            </if>
            <if test="pastDesc != null ">
                PAST_DESC = #{ pastDesc },
            </if>
            <if test="familyDesc != null ">
                FAMILY_DESC = #{ familyDesc },
            </if>
            <if test="allergyDesc != null ">
                ALLERGY_DESC = #{ allergyDesc },
            </if>
            <if test="tongue != null ">
                TONGUE = #{ tongue },
            </if>
            <if test="pulse != null ">
                PULSE = #{ pulse },
            </if>
            <if test="fourDiagnosis != null ">
                FOUR_DIAGNOSIS = #{ fourDiagnosis },
            </if>
            <if test="physical != null ">
                PHYSICAL = #{ physical },
            </if>
            <if test="auxiliaryExam != null">
                AUXILIARY_EXAM = #{ auxiliaryExam },
            </if>
            <if test="patientHeight != null ">
                PATIENT_HEIGHT = #{patientHeight},
            </if>
            <if test="patientWeight != null">
                PATIENT_WEIGHT = #{patientWeight},
            </if>
        </set>
        where REC_ID = #{ recId }
    </update>

    <update id="updateRecordWhenSaveRecordPlus" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord">
        update t_record
        <set>
            <if test="westernDiseaseId != null">
                WESTERN_DISEASE_ID = #{ westernDiseaseId },
            </if>
            <if test="westernDisease != null">
                WESTERN_DISEASE = #{ westernDisease },
            </if>
            <if test="disId != null">
                DIS_ID = #{ disId },
            </if>
            <if test="version != null and version != ''">
                version = #{version},
            </if>
            <if test="disName != null">
                DIS_NAME = #{ disName },
            </if>
            <if test="symId != null ">
                SYM_ID = #{ symId },
            </if>
            <if test="symName != null ">
                SYM_NAME = #{ symName },
            </if>
            <if test="theCode != null ">
                THE_CODE = #{theCode},
            </if>
            <if test="theNames != null ">
                THE_NAMES = #{ theNames },
            </if>
            <if test="patientContent != null ">
                PATIENT_CONTENT = #{ patientContent },
            </if>
            <if test="nowDesc != null ">
                NOW_DESC = #{ nowDesc },
            </if>
            <if test="pastDesc != null ">
                PAST_DESC = #{ pastDesc },
            </if>
            <if test="familyDesc != null ">
                FAMILY_DESC = #{ familyDesc },
            </if>
            <if test="allergyDesc != null ">
                ALLERGY_DESC = #{ allergyDesc },
            </if>
            <if test="tongue != null ">
                TONGUE = #{ tongue },
            </if>
            <if test="pulse != null">
                PULSE = #{ pulse },
            </if>
            <if test="fourDiagnosis != null ">
                FOUR_DIAGNOSIS = #{ fourDiagnosis },
            </if>
            <if test="physical != null ">
                PHYSICAL = #{ physical },
            </if>
            <if test="auxiliaryExam != null ">
                AUXILIARY_EXAM = #{ auxiliaryExam },
            </if>
            <if test="patientHeight != null ">
                PATIENT_HEIGHT = #{patientHeight},
            </if>
            <if test="patientWeight != null ">
                PATIENT_WEIGHT = #{patientWeight},
            </if>
            <if test="treatmentAdvice != null ">
                TREATMENT_ADVICE = #{treatmentAdvice},
            </if>
        </set>
        where REC_ID = #{ recId }
    </update>

    <update id="saveCheckRec" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord">
        update t_record
        <set>
            <if test="checkType != null">
                CHECK_TYPE = #{ checkType },
            </if>
            <if test="checkAdvise != null">
                CHECK_ADVISE = #{ checkAdvise },
            </if>
            <if test="isCheck != null">
                IS_CHECK = #{ isCheck },
            </if>
        </set>
        where REC_ID = #{ recId } and IS_CHECK = '0'
    </update>


    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select r.*
        from t_record r
        where r.REC_ID = #{id}
          and r.is_del = '0'
    </select>


    <sql id="orderNameSql">
        <choose>
            <when test="patientName != null and patientName">
                and p.PATIENT_NAME like concat(trim(#{patientName}),'%')
            </when>
            <when test="patientPy != null and patientPy != ''">
                and p.PATIENT_PY like concat(trim(#{patientPy}),'%')
            </when>
            <when test="patientWb != null and patientWb != ''">
                and p.PATIENT_WB like concat(trim(#{patientWb}),'%')
            </when>
            <when test="orderName == '1'.toString() and orderValue != null and orderValue != ''">
                and p.MEDICAL_CARD_NO like CONCAT('%',trim( #{orderValue}),'%')
            </when>
            <when test="orderName == '2'.toString() and orderValue != null and orderValue != ''">
                and p.PATIENT_MOBILE like CONCAT('%',trim( #{patientMobile}),'%')
            </when>
            <when test="orderName == '3'.toString() and orderValue != null and orderValue != ''">
                and p.PATIENT_CERTIFICATE like CONCAT('%',trim( #{patientCertificate}),'%')
            </when>
        </choose>
    </sql>

    <sql id="getMyHistoryRecSelect">
        r.REC_ID AS recId,r.REGISTER_ID AS registerId,r.PATIENT_ID AS patientId,r.REC_NAME AS recName,r.REC_AGE1 AS
        recAge1,
        r.REC_AGEUNIT1 AS recAgeunit1,r.REC_AGE2 AS recAge2,r.REC_AGEUNIT2 AS recAgeunit2,r.REC_GENDER AS recGender,
        r.REC_ADDRESS AS recAddress,r.REC_TRE_TIME AS recTreTime,r.REC_TRE_TYPE AS recTreType,r.WESTERN_DISEASE AS
        westernDisease,
        r.DIS_NAME AS disName,r.SYM_NAME AS symName,r.THE_NAMES AS theNames,r.REC_FIRSTID AS recFirstid,
        p.PATIENT_CERTIFICATE_TYPE AS patientCertificateType, p.PATIENT_CERTIFICATE AS patientCertificate,
        p.PATIENT_MOBILE AS patientMobile,p.PATIENT_BIRTHDAY AS patientBirthday,p.MEDICAL_CARD_NO AS medicalCardNo,
        t.VISIT_NO visitNo
    </sql>
    <sql id="getMyHistoryRecWhere">
        <where>
            r.is_del = '0'
            <if test="disId != null and disId !=''">
                and r.DIS_ID = #{disId}
            </if>
            <if test="disName != null and disName !=''">
                and r.DIS_NAME = #{disName}
            </if>
            <if test="symId != null and symId !=''">
                and r.SYM_ID = #{symId}
            </if>
            <if test="symName != null and symName !=''">
                and r.SYM_NAME = #{symName}
            </if>
            <if test="docId != null and docId !=''">
                and r.DOC_ID = #{docId}
            </if>
            <if test="startDate != null and startDate !=''">
                and r.REC_TRE_TIME &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate !=''">
                and r.REC_TRE_TIME &lt;= concat(#{endDate},' 23:59:59')
            </if>
            <if test="orderName == '4'.toString() and orderValue != null and orderValue != ''">
                and t.VISIT_NO = trim( #{orderValue})
            </if>
        </where>
    </sql>
    <!--分页查询我的历史病例-->
    <select id="getMyHistoryRecS" parameterType="TodayPatientReqVO" resultType="TRecordRespVO">
        SELECT
        <include refid="getMyHistoryRecSelect"/>
        FROM t_record AS r
        join t_register t on t.REGISTER_ID = r.REGISTER_ID
        JOIN t_patients p ON r.PATIENT_ID = p.PATIENT_ID AND p.IS_DEL = '0'
        <include refid="orderNameSql"/>
        <include refid="getMyHistoryRecWhere"/>

        <if test="tableHistoryYears != null ">
            <foreach collection="tableHistoryYears" item="year" open=" UNION " separator=" UNION ">
                SELECT
                <include refid="getMyHistoryRecSelect"/>
                FROM t_record_${year} AS r
                join t_register_${year} t on t.REGISTER_ID = r.REGISTER_ID
                JOIN t_patients p ON r.PATIENT_ID = p.PATIENT_ID AND p.IS_DEL = '0'
                <include refid="orderNameSql"/>
                <include refid="getMyHistoryRecWhere"/>
            </foreach>
        </if>
        order by recTreTime DESC
    </select>


    <select id="getListByTodayRecord" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord" resultType="TodayPatientRespVO">
        SELECT
        rgi.REGISTER_ID registerId, r.rec_id recId, rgi.APP_ID appId, rgi.INS_CODE insCode, rgi.PATIENT_ID patientId,
        rgi.PATIENT_NAME patientName, rgi.DEPT_ID deptId, rgi.DEPT_NAME deptName, rgi.DOCTOR_ID doctorId,
        rgi.DOCTOR_NAME doctorName, DATE_FORMAT(rgi.REGISTER_TIME,'%Y-%m-%d %H:%i:%s') registerTime, rgi.REGISTER_NUM
        registerNum,
        rgi.REGISTER_TIME_ARANGE registerTimeArange,
        rgi.REGISTER_DIAGNOSIS_STATUS registerDiagnosisStatus,
        rgi.CLINIC_TYPE_ID clinicTypeId, rgi.CLINIC_TYPE_NAME clinicTypeName, rgi.CLINIC_TYPE_MONEY clinicTypeMoney,
        rgi.REC_OPEN_TIME recOpenTime, rgi.VISIT_NO visitNo, rgi.GRAVIDITY gravidity,
        p.PATIENT_GENDER patientGender, p.PATIENT_BIRTHDAY patientBirthday,
        p.PATIENT_CERTIFICATE_TYPE patientCertificateType, p.PATIENT_CERTIFICATE patientCertificate, p.PATIENT_MOBILE
        patientMobile,
        p.MEDICAL_CARD_NO medicalCardNo, p.ORIGIN_PATIENT_ID originPatientId,
        p.PATIENT_COUNTY patientCounty, p.PATIENT_TOWN patientTown, p.PATIENT_VILLAGE patientVillage, p.PATIENT_STREET
        patientStreet,
        p.PATIENT_ADDRESS patientAddress, p.DC_ADDRESS_ID dcAddressId
        FROM
        t_register AS rgi
        join t_patients AS p on rgi.PATIENT_ID = p.PATIENT_ID and p.IS_DEL = '0'
        <if test="medicalCardNo!=null and medicalCardNo!=''">
            and p.MEDICAL_CARD_NO like CONCAT('%',trim(#{medicalCardNo}),'%')
        </if>
        <if test="mobile!=null and mobile!=''">
            AND p.PATIENT_MOBILE like CONCAT('%',trim(#{patientMobile}),'%')
        </if>
        <if test="certificate!=null and certificate!=''">
            AND p.PATIENT_CERTIFICATE like CONCAT('%',trim(#{patientCertificate}),'%')
        </if>
        left join t_record r on rgi.register_id = r.register_id and r.is_del = '0'
        <where>
            DATE_FORMAT(rgi.REGISTER_TIME,'%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d')
            <if test="recDiagnosisStatus!=null and recDiagnosisStatus!=''">
                and rgi.REGISTER_DIAGNOSIS_STATUS = #{recDiagnosisStatus}
            </if>
            <if test="time!=null and time!=''">
                and rgi.REGISTER_TIME_ARANGE = #{time}
            </if>
            <if test="recName!=null and recName!=''">
                and rgi.PATIENT_NAME like CONCAT(trim(#{recName}),'%')
            </if>
            <if test="docId!=null and docId!=''">
                and rgi.DOCTOR_ID = #{docId}
            </if>
            <if test="visitNo!=null and visitNo!=''">
                and rgi.VISIT_NO = #{visitNo}
            </if>
            <if test="appId!=null and appId!=''">
                and rgi.APP_ID = #{appId}
            </if>
            <if test="insCode!=null and insCode!=''">
                and rgi.INS_CODE = #{insCode}
            </if>
        </where>
        ORDER BY rgi.REGISTER_ID desc
    </select>



    <!--根据挂号ID获取唯一病历-->
    <select id="getRecordIdByRegisterId" parameterType="String" resultType="String">
        select rec_id
        from t_record
        where REGISTER_ID = #{registerId}
          and is_del = '0'
        limit 1
    </select>

    <!--根据挂号ID获取唯一病历-->
    <select id="getRecordByRegisterId" parameterType="String" resultMap="BaseResultMap">
        select *
        from t_record
        where REGISTER_ID = #{registerId}
          and is_del = '0'
        limit 1
    </select>

    <sql id="recSql">
        SELECT r.REC_ID                                                                               recId,
               r.REGISTER_ID                                                                          registerId,
               r.PATIENT_ID                                                                           patientId,
               r.REC_NAME                                                                             recName,
               r.REC_AGE1                                                                             recAge1,
               r.REC_AGEUNIT1                                                                         recAgeunit1,
               r.REC_AGE2                                                                             recAge2,
               r.REC_AGEUNIT2                                                                         recAgeunit2,
               r.REC_GENDER                                                                           recGender,
               r.REC_ADDRESS                                                                          recAddress,
               r.GRAVIDITY                                                                            gravidity,
               DATE_FORMAT(r.REC_TRE_TIME, '%Y-%m-%d %H:%i:%s')                                       recTreTime,
               r.DEPT_ID                                                                              deptId,
               r.DEPT_NAME                                                                            deptName,
               r.DOC_ID                                                                               docId,
               r.APP_ID                                                                               appId,
               r.INS_CODE                                                                             insCode,
               r.INS_TRE_ID                                                                           insTreId,
               r.REC_TRE_TYPE                                                                         recTreType,
               rgi.CLINIC_TYPE_NO                                                                     clinicTypeNo,
               r.WESTERN_DISEASE_ID                                                                   westernDiseaseId,
               r.WESTERN_DISEASE                                                                      westernDisease,
               r.DIS_ID                                                                               disId,
               r.DIS_NAME                                                                             disName,
               r.SYM_ID                                                                               symId,
               r.SYM_NAME                                                                             symName,
               r.THE_NAMES                                                                            theNames,
               r.PATIENT_CONTENT                                                                      patientContent,
               r.NOW_DESC                                                                             nowDesc,
               r.PAST_DESC                                                                            pastDesc,
               r.FAMILY_DESC                                                                          familyDesc,
               r.ALLERGY_DESC                                                                         allergyDesc,
               r.TONGUE                                                                               tongue,
               r.PULSE                                                                                pulse,
               r.FOUR_DIAGNOSIS                                                                       fourDiagnosis,
               r.PHYSICAL                                                                             physical,
               r.AUXILIARY_EXAM                                                                       auxiliaryExam,
               r.REC_FIRSTID                                                                          recFirstid,
               r.VIS_NUM                                                                              visNum,
               r.TREATMENT_ADVICE                                                                              treatmentAdvice,
               DATE_FORMAT(r.REC_LASTDATE, '%Y-%m-%d')                                                recLastdate,
               r.REC_ONLINE_REDIAGNOSIS                                                               recOnlineRediagnosis,
               DATE_FORMAT(r.REC_REDIAGNOSIS_DEADLINE, '%Y-%m-%d %H:%i:%s')                           recRediagnosisDeadline,
               rgi.DOCTOR_NAME                                                                        docName,
               rgi.DOCTOR_NAME                                                                        doctorName,
               rgi.CLINIC_TYPE_ID                                                                     clinicTypeId,
               rgi.CLINIC_TYPE_NAME                                                                   clinicTypeName,
               rgi.CLINIC_TYPE_MONEY                                                                  clinicTypeMoney,
               rgi.REGISTER_TIME_ARANGE                                                               registerTimeArange,
               rgi.REGISTER_DIAGNOSIS_STATUS                                                          registerDiagnosisStatus,
               rgi.REGISTER_NUM                                                                       registerNum,
               DATE_FORMAT(rgi.REC_OPEN_TIME, '%Y-%m-%d %H:%i:%s')                                    recOpenTime,
               rgi.VISIT_NO                                                                           visitNo,
               p.PATIENT_BIRTHDAY                                                                     patientBirthday,
               p.PATIENT_CERTIFICATE_TYPE                                                             patientCertificateType,
               p.PATIENT_CERTIFICATE                                                                  patientCertificate,
               p.PATIENT_MOBILE                                                                       patientMobile,
               p.MEDICAL_CARD_NO                                                                      medicalCardNo,
               p.ORIGIN_PATIENT_ID                                                                    originPatientId,
               r.THE_CODE  theCode,
               r.THE_CODE  therapyCode,
               r.THE_NAMES theNames,
               r.PRESONAL_DESC personalDesc
        FROM t_record AS r
                 JOIN t_register AS rgi on r.REGISTER_ID = rgi.REGISTER_ID
                 JOIN t_patients p on r.PATIENT_ID = p.PATIENT_ID AND p.IS_DEL = '0'
    </sql>
    <select id="getRecordByRecord" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord"
            resultType="com.jiuzhekan.cbkj.beans.business.record.VO.TRecordRespVO">
        <include refid="recSql"/>

        <where>
            r.is_del = '0'
            <if test="recId != null and recId !=''">
                and r.REC_ID = #{recId}
            </if>
            <if test="appId != null and appId !=''">
                AND r.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode !=''">
                AND r.INS_CODE = #{insCode}
            </if>
        </where>
        order by r.VIS_NUM,r.REC_TRE_TIME
    </select>

    <select id="getHisRecordByRecord" parameterType="com.jiuzhekan.cbkj.beans.business.record.VO.TRecordRespVO"
            resultType="com.jiuzhekan.cbkj.beans.business.record.VO.TRecordRespVO">
        SELECT r.REC_ID                                                                             recId,
               r.DIS_ID disId,
               r.SYM_ID symId,
        (select d.DIS_CODE_HIS from b_mapping_disease d where d.DIS_ID_SYS = r.DIS_ID limit 1)      disCodeHis,
        (select s.SYM_CODE_HIS from b_mapping_symptom s where s.SYM_ID_SYS = r.SYM_ID limit 1)      symCodeHis,
        (select hr.MEDICAL_TYPE_NAME from t_his_record hr where hr.TOKEN = r.REGISTER_ID limit 1) medicalTypeName
        FROM t_record AS r
        where r.REC_ID = #{recId}
    </select>

    <select id="getBigRecord" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord"
            resultType="com.jiuzhekan.cbkj.beans.business.record.VO.TRecordRespVO">
        <include refid="recSql"/>

        join t_prescription n on n.REC_ID = r.REC_ID and n.REC_EXT_TYPE >= 50 AND n.REC_EXT_TYPE != 110
        where r.REC_FIRSTID= (SELECT r2.REC_FIRSTID FROM t_record r2 WHERE r2.REC_ID = #{recId} )
        group by r.rec_id
        order by r.REC_TRE_TIME
    </select>
    <select id="getListByRec" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord" resultType="com.jiuzhekan.cbkj.beans.business.record.TRecord">
        SELECT
        r.REC_ID recId,r.DIS_NAME disName,r.SYM_NAME symName,r.PATIENT_ID patientId,r.REC_NAME recName,
        r.REC_AGE1 recAge1,r.REC_AGEUNIT1 recAgeunit1,r.REC_AGE2 recAge2,r.REC_AGEUNIT2 recAgeunit2,
        r.REC_GENDER recGender,r.REC_TRE_TIME recTreTime
        from t_record r
        join (SELECT re.PATIENT_ID, MAX(re.REC_TRE_TIME) REC_TRE_TIME
        FROM t_record re
        join t_prescription p on p.REC_ID = re.REC_ID and p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110
        where re.IS_DEL = '0'
        <if test="patientId != null and patientId != ''">
            AND re.PATIENT_ID = #{patientId}
        </if>
        <if test="appId != null and appId != ''">
            AND re.APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode != ''">
            AND re.INS_CODE = #{insCode}
        </if>
        GROUP BY re.REC_FIRSTID) rs ON rs.REC_TRE_TIME = r.REC_TRE_TIME and rs.PATIENT_ID = r.PATIENT_ID
        ORDER BY r.REC_TRE_TIME DESC
    </select>

    <select id="getHistoryRec" parameterType="TodayPatientReqVO" resultType="TRecordRespVO">
        SELECT
        r.REC_ID recId, r.REGISTER_ID registerId, r.PATIENT_ID patientId, r.REC_NAME recName, r.REC_AGE1 recAge1,
        r.REC_AGEUNIT1 recAgeunit1, r.REC_AGE2 recAge2, r.REC_AGEUNIT2 recAgeunit2, r.REC_GENDER recGender,
        r.REC_ADDRESS recAddress, r.GRAVIDITY gravidity, r.INS_TRE_ID insTreId, r.REC_TRE_TYPE recTreType,
        r.DIS_NAME disName, r.SYM_NAME symName, r.THE_NAMES theNames, r.PATIENT_CONTENT patientContent,
        r.REC_FIRSTID recFirstid, p.PATIENT_BIRTHDAY patientBirthday, p.PATIENT_CERTIFICATE_TYPE patientCertificateType,
        p.PATIENT_CERTIFICATE patientCertificate,
        p.PATIENT_MOBILE patientMobile, p.MEDICAL_CARD_NO medicalCardNo, p.PATIENT_ADDRESS patientAddress,
        r.REC_TRE_TIME recTreTime
        FROM
        t_record AS r join
        t_patients AS p on r.PATIENT_ID = p.PATIENT_ID AND p.IS_DEL = '0'
        <include refid="orderNameSql"/>
        <where>
            r.is_del = '0'
            <if test="patientId != null and patientId != ''">
                and r.PATIENT_ID = #{patientId}
            </if>
            <if test="disId != null and disId !=''">
                and r.DIS_ID = #{disId}
            </if>
            <if test="symId != null and symId !=''">
                and r.SYM_ID = #{symId}
            </if>
            <if test="startDate != null and startDate !=''">
                and r.REC_TRE_TIME &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate !=''">
                and r.REC_TRE_TIME &lt;= concat(#{endDate},' 23:59:59')
            </if>
            <if test="appId != null and appId !=''">
                and r.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode !=''">
                and r.INS_CODE = #{insCode}
            </if>
        </where>
        order by r.REC_TRE_TIME DESC
    </select>


    <select id="recManage" parameterType="TodayPatientReqVO" resultType="TRecordRespVO">
        SELECT
        r.REC_ID recId, r.PATIENT_ID patientId, r.REC_NAME recName, r.REC_AGE1 recAge1, r.REC_AGEUNIT1 recAgeunit1,
        r.REC_AGE2 recAge2, r.REC_AGEUNIT2 recAgeunit2, r.REC_GENDER recGender, r.REC_ADDRESS recAddress,
        r.GRAVIDITY gravidity, r.REC_TRE_TIME recTreTime, r.INS_TRE_ID insTreId, r.REC_TRE_TYPE recTreType,
        r.DIS_NAME disName, r.SYM_NAME symName, r.REC_FIRSTID recFirstid, r.THE_NAMES theNames,
        r.VIS_NUM visNum, r.DOC_Name docName, r.app_id appId, r.ins_code insCode,
        p.MEDICAL_CARD_NO medicalCardNo
        FROM (
        select * from t_record where IS_DEL = '0'
        <if test="docId != null and docId != ''">
            and DOC_ID = #{docId}
        </if>
        <if test="appId != null and appId != ''">
            AND APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode != ''">
            AND INS_CODE = #{insCode}
        </if>
        <if test="startDate != null and startDate !=''">
            and REC_TRE_TIME &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate !=''">
            and REC_TRE_TIME &lt;= concat(#{endDate},' 23:59:59')
        </if>
        <if test="recTreType != null and recTreType != ''">
            AND REC_TRE_TYPE = #{recTreType}
        </if>
        <if test="docName != null and docName != ''">
            and DOC_Name like concat(trim(#{docName}),'%')
        </if>
        ) r
        left join (
        select * from t_patients where IS_DEL = '0'
        <if test="patientName != null and patientName!='' ">
            and PATIENT_NAME like CONCAT(trim( #{patientName}),'%')
        </if>
        <if test="orderName == '1'.toString() and orderValue != null and orderValue != ''">
            and MEDICAL_CARD_NO like CONCAT('%',trim( #{orderValue}),'%')
        </if>
        <if test="orderName == '2'.toString() and orderValue != null and orderValue != ''">
            and PATIENT_MOBILE like CONCAT('%',trim( #{orderValue}),'%')
        </if>
        <if test="orderName == '3'.toString() and orderValue != null and orderValue != ''">
            and PATIENT_CERTIFICATE like CONCAT('%',trim( #{orderValue}),'%')
        </if>
        ) p on r.PATIENT_ID=p.PATIENT_ID
        <where>
            <if test="patientName != null and patientName!='' ">
                and p.PATIENT_NAME like CONCAT(trim( #{patientName}),'%')
            </if>
            <if test="orderName == '1'.toString() and orderValue != null and orderValue != ''">
                and p.MEDICAL_CARD_NO like CONCAT('%',trim( #{orderValue}),'%')
            </if>
            <if test="orderName == '2'.toString() and orderValue != null and orderValue != ''">
                and p.PATIENT_MOBILE like CONCAT('%',trim( #{orderValue}),'%')
            </if>
            <if test="orderName == '3'.toString() and orderValue != null and orderValue != ''">
                and p.PATIENT_CERTIFICATE like CONCAT('%',trim( #{orderValue}),'%')
            </if>
        </where>
        order by r.REC_TRE_TIME DESC
    </select>


    <select id="getCheckResult" parameterType="String" resultType="PreCheckResultVO">
        SELECT r.REC_ID                recId,
               r.REGISTER_ID           registerId,
               r.REC_NAME              recName,
               r.IS_CHECK           AS recIsCheck,
               r.CHECK_ADVISE       AS recCheckAdvise,
               p.CHECK_ADVISE       AS checkAdvise,
               p.IS_CHECK           AS isCheck,
               p.PRE_TYPE           AS preType,
               p.PRE_DECOCTION_FEE     preDecoctionFee,
               p.PRE_PRODUCTION_FEE    preProductionFee,
               p.PRE_EXPRESS_FEE       preExpressFee,
               p.PRE_SMO_MONEY         preSmoMoney,
               p.PRE_SPECIAL_FEE       preSpecialFee,
               p.MAT_TOL_MONEY      as matTolMoney,
               p.PRE_TOL_MONEY      as preTolMoney,
               p.PRE_TOL_MONEY_REAL as preTolMoneyReal,
               p.REC_EXT_TYPE       as recExtType,
               p.PRE_ID                preId,
               p.PRE_NO                preNo
        FROM t_record AS r
                 left JOIN t_prescription AS p on r.REC_ID = p.REC_ID AND p.IS_DEL = '0'
        where r.is_del = '0'
          and r.REC_ID = #{recId}
        order by p.pre_time
    </select>

    <select id="getPreHistory" parameterType="PreCheckReqVO" resultType="PreCheckRespVO">
        SELECT
        n.REC_EXT_TYPE recType,
        n.IS_DEL isDel,
        n.PRE_ID preId,
        n.PRE_NO preNo,
        r.REC_ID recId,
        r.PATIENT_ID patientId,
        r.REC_NAME recName,
        r.REGISTER_ID registerId,
        r.SYM_NAME symName,
        r.THE_NAMES theNames,
        r.DIS_NAME disName,
        n.PRE_TIME recTreTime,
        r.IS_CHECK isCheck,
        n.PRE_TYPE preType,
        p.MEDICAL_CARD_NO cardNo
        from t_prescription n
        JOIN t_record r on r.REC_ID = n.REC_ID and n.IS_DEL = '0'
        join t_patients p on n.PATIENT_ID = p.PATIENT_ID AND p.IS_DEL = '0'
        <where>
            n.IS_DEL = '0'
            and p.PATIENT_NAME like concat(trim(#{patientName}),'%')
            and n.REC_EXT_TYPE in ('30','44','45')
            <if test="startDate != null and startDate !=''">
                and n.PRE_TIME &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate !=''">
                and n.PRE_TIME &lt;= concat(#{endDate},' 23:59:59')
            </if>
            <if test="docId != null and docId !=''">
                and n.PRE_DOCTOR = #{docId}
            </if>
        </where>
        ORDER BY n.PRE_TIME DESC
    </select>

    <sql id="getCheckPayResultSelect">
        n.REC_EXT_TYPE recType,
        n.IS_DEL isDel,
        n.PRE_ID preId,
        n.PRE_NO preNo,
        r.REC_ID recId,
        r.PATIENT_ID patientId,
        r.REC_NAME recName,
        r.REGISTER_ID registerId,
        r.SYM_ID symId,
        r.SYM_NAME symName,
        r.THE_NAMES theNames,
        r.DIS_ID disId,
        r.DIS_NAME disName,
        r.REC_TRE_TIME recTreTime,
        r.IS_CHECK isCheck,
        r.DEPT_NAME deptName,
        n.PRE_TYPE preType,
        n.PRE_MAT_TYPE preMatType,
        n.STORE_ID storeId,
        n.PRE_NUM preNum,
        n.PRE_ORIGIN_NAME preOriginName,

        n.IS_INSURANCE costType,
        n.CHECK_USERNAME checkUsername,
        n.PRE_DOCTORNAME preDoctorname,
        n.PRE_TOL_MONEY preTolMoney,
        n.MAT_TOL_MONEY matTolMoney,
        n.PRE_ADVICE preAdvice,
        n.IS_PRODUCTION isProduction,
        n.DECOCT_TYPE decoctType,
        n.PRE_DECOCT_NUM preDecoctNum,
        n.DC_TYPE dcType,
        n.IS_HOSPITAL_TREATMENT isHospitalTreatment,
        n.PRE_SMOKE_TYPE preSmokeType,
        n.PRE_SMOKE_INSTRUMENT preSmokeInstrument,
        n.PRE_DESCRIPTION preDescription,
        n.PRE_FREQUENCY preUsetimeDes,
        n.PRE_USETIME_DES preFrequency,
        n.patient_types patientTypes,

        r.REC_GENDER recGender,
        r.REC_AGE1 recAge1,
        r.REC_AGEUNIT1 recAgeunit1,
        r.REC_AGE2 recAge2,
        r.REC_AGEUNIT2 recAgeunit2,

        p.MEDICAL_CARD_NO cardNo,
        p.PATIENT_ADDRESS recAddress,
        p.PATIENT_MOBILE patientMobile,
        t.VISIT_NO visitNo
    </sql>

    <select id="getCheckPayResult" parameterType="PreCheckReqVO" resultType="PreCheckRespVO">
        SELECT
        <include refid="getCheckPayResultSelect"/>
        from t_prescription n
        JOIN t_record r on r.REC_ID = n.REC_ID and n.IS_DEL = '0' and n.PRE_ORIGIN != '13'
        join t_register t on t.REGISTER_ID = r.REGISTER_ID
        join t_patients p on r.PATIENT_ID = p.PATIENT_ID AND p.IS_DEL = '0'
        <include refid="getCheckPayResultWhere"/>

        <if test="tableHistoryYears != null ">
            <foreach collection="tableHistoryYears" item="year" open=" UNION " separator=" UNION ">
                SELECT
                <include refid="getCheckPayResultSelect"/>
                from t_prescription_${year} n
                JOIN t_record_${year} r on r.REC_ID = n.REC_ID and n.IS_DEL = '0'
                join t_register_${year} t on t.REGISTER_ID = r.REGISTER_ID
                join t_patients p on r.PATIENT_ID = p.PATIENT_ID AND p.IS_DEL = '0'
                <include refid="getCheckPayResultWhere"/>
            </foreach>
        </if>

        ORDER BY recTreTime DESC
    </select>

    <sql id="getCheckPayResultWhere">
        <where>
            r.IS_DEL = '0' and n.is_check is not null and (n.is_del = '0' or n.REC_EXT_TYPE = '20')
            <if test="patientPy != null and patientPy != ''">
                and p.PATIENT_PY like concat(trim(#{patientPy}),'%')
            </if>
            <if test="patientWb != null and patientWb != ''">
                and p.PATIENT_WB like concat(trim(#{patientWb}),'%')
            </if>
            <if test="isCheck != null and isCheck != ''">
                and r.IS_CHECK = #{isCheck}
            </if>
            <if test="recType != null and recType != ''">
                and n.REC_EXT_TYPE = #{recType}
            </if>
            <if test="recName != null and recName != ''">
                and r.REC_NAME like concat(trim(#{recName}),'%')
            </if>
            <if test="disName != null and disName != ''">
                and r.DIS_NAME like concat('%',trim(#{disName}),'%')
            </if>
            <if test="startDate != null and startDate !=''">
                and r.REC_TRE_TIME &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate !=''">
                and r.REC_TRE_TIME &lt;= concat(#{endDate},' 23:59:59')
            </if>
            <if test="appId != null and appId !=''">
                and r.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode !=''">
                and r.INS_CODE = #{insCode}
            </if>
            <if test="docId != null and docId !=''">
                and r.DOC_ID = #{docId}
            </if>
            <choose>
                <when test="orderName == '0'.toString() and orderValue != null and orderValue !=''">
                    and p.PATIENT_NAME like concat(trim(#{patientName}),'%')
                </when>
                <when test="orderName == '1'.toString() and orderValue != null and orderValue !=''">
                    and p.MEDICAL_CARD_NO = #{orderValue}
                </when>
                <when test="orderName == '2'.toString() and orderValue != null and orderValue !=''">
                    and p.PATIENT_MOBILE = #{patientMobile}
                </when>
                <when test="orderName == '3'.toString() and orderValue != null and orderValue !=''">
                    and p.PATIENT_CERTIFICATE = #{patientCertificate}
                </when>
                <when test="orderName == '4'.toString() and orderValue != null and orderValue !=''">
                    and t.VISIT_NO = #{orderValue}
                </when>
            </choose>
        </where>
    </sql>

    <select id="defineBigRecordList" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord" resultMap="BaseResultMap">
        SELECT *
        FROM t_record
        WHERE IS_DEL = '0'
          AND REC_ID != #{recId}
          AND PATIENT_ID = #{patientId}
          AND DIS_NAME = #{disName}
          AND DATEDIFF(CURDATE(), REC_LASTDATE) &lt;= 15
          AND IS_DEL = '0'
    </select>
    <!-- 我的医案查询 -->
    <select id="getMyConsiliaByRec" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRecord" resultType="com.jiuzhekan.cbkj.beans.business.record.TRecord">
        SELECT
        r.REC_ID recId,
        r.REGISTER_ID registerId,
        r.PATIENT_ID patientId,
        r.REC_NAME recName,
        r.REC_TRE_TIME recTreTime,
        r.DIS_NAME disName,
        r.SYM_NAME symName,
        r.THE_NAMES theNames,
        (select rgi.DOCTOR_NAME from t_register rgi where r.REGISTER_ID = rgi.REGISTER_ID) docName
        FROM t_record AS r
        JOIN (
        SELECT PATIENT_ID,DIS_NAME,MAX(REC_TRE_TIME) REC_TRE_TIME FROM t_record
        <where>
            IS_DEL = '0' AND DIS_NAME IS NOT NULL
            <if test="disId != null and disId != ''">
                and DIS_ID = #{disId}
            </if>
            <if test="symId != null and symId != ''">
                and SYM_ID = #{symId}
            </if>
            <if test="docId != null and docId != ''">
                and DOC_ID = #{docId}
            </if>
        </where>
        GROUP BY PATIENT_ID,DIS_NAME
        ) rb ON rb.PATIENT_ID = r.PATIENT_ID AND rb.DIS_NAME = r.DIS_NAME AND rb.REC_TRE_TIME = r.REC_TRE_TIME
        WHERE EXISTS(SELECT 1 FROM t_prescription p WHERE p.REC_ID = r.REC_ID AND p.REC_EXT_TYPE >= 50 AND
        p.REC_EXT_TYPE != 110 )
        order by r.REC_TRE_TIME desc
    </select>


    <!-- 根据recid获取病历信息和医疗机构信息 -->
    <select id="getRecInsById" resultType="com.jiuzhekan.cbkj.beans.business.record.TRecord" parameterType="String">
        SELECT rec.REC_ID                   recId,
               rec.REGISTER_ID              registerId,
               rec.PATIENT_ID               patientId,
               rec.REC_NAME                 recName,
               rec.REC_AGE1                 recAge1,
               rec.REC_AGEUNIT1             recAgeunit1,
               rec.REC_AGE2                 recAge2,
               rec.REC_AGEUNIT2             recAgeunit2,
               rec.REC_GENDER               recGender,
               rec.REC_ADDRESS              recAddress,
               rec.GRAVIDITY                gravidity,
               rec.REC_TRE_TIME             recTreTime,
               rec.DEPT_ID                  deptId,
               rec.DEPT_NAME                deptName,
               rec.DOC_ID                   docId,
               rec.APP_ID                   appId,
               rec.INS_CODE                 insCode,
               rec.INS_TRE_ID               insTreId,
               rec.REC_TRE_TYPE             recTreType,
               rec.WESTERN_DISEASE_ID       westernDiseaseId,
               rec.WESTERN_DISEASE          westernDisease,
               rec.DIS_ID                   disId,
               rec.DIS_NAME                 disName,
               rec.SYM_ID                   symId,
               rec.SYM_NAME                 symName,
               rec.THE_NAMES                theNames,
               rec.THE_CODE                 theCode,
               rec.PATIENT_CONTENT          patientContent,
               rec.NOW_DESC                 nowDesc,
               rec.PAST_DESC                pastDesc,
               rec.FAMILY_DESC              familyDesc,
               rec.ALLERGY_DESC             allergyDesc,
               rec.TONGUE                   tongue,
               rec.PULSE                    pulse,
               rec.FOUR_DIAGNOSIS           fourDiagnosis,
               rec.PHYSICAL                 physical,
               rec.AUXILIARY_EXAM           auxiliaryExam,
               rec.REC_FIRSTID              recFirstid,
               rec.VIS_NUM                  visNum,
               rec.REC_LASTDATE             recLastdate,
               rec.REC_ONLINE_REDIAGNOSIS   recOnlineRediagnosis,
               rec.REC_REDIAGNOSIS_DEADLINE recRediagnosisDeadline,
               rec.CHECK_TYPE               checkType,
               rec.IS_CHECK                 isCheck,
               rec.CHECK_ADVISE             checkAdvise,
               rec.IS_DEL                   isDel,
               rec.PATIENT_HEIGHT           patientHeight,
               rec.PATIENT_WEIGHT           patientWeight,
               p.PATIENT_MOBILE             mobile,
               rec.version                  version,
               rec.DOC_NAME                 doctorName,
               rec.TREATMENT_ADVICE         treatmentAdvice
        FROM t_record rec
                 join t_patients p on rec.patient_id = p.patient_id
        WHERE rec.is_del = '0'
          and rec.rec_id = #{recId}
    </select>

    <select id="getRecordByPatIdAndAdm" parameterType="java.lang.String"
            resultType="com.jiuzhekan.cbkj.beans.business.record.TRecordAll">
        SELECT rec.REGISTER_ID AS registerId,rec.REC_ID as recId,
        DATE_FORMAT(rec.REC_TRE_TIME, '%Y-%m-%d') AS recordTime,
        rec.DIS_NAME AS diseaseName,
        rec.REC_TRE_TYPE AS recTreType,
        IF(GROUP_CONCAT(pre.IS_SPECIAL_DIS) LIKE '%1%',1,0) AS isSpecialDis
        FROM (select * from `t_record`
            where PATIENT_ID = #{patientId}
            <if test="docId != null and docId != ''">
                and doc_id = #{docId}
            </if>
        <if test="insCode != null and insCode != ''">
            and INS_CODE = #{insCode}
        </if>
        ) rec
        INNER JOIN `t_prescription` pre ON rec.`REC_ID` = pre.`REC_ID`
        WHERE pre.`IS_PAY` = '1'
        AND pre.`REC_EXT_TYPE` >= 50
        AND pre.`REC_EXT_TYPE` != 110
        AND pre.is_del = '0'
        GROUP BY rec.REGISTER_ID
        ORDER BY rec.REC_TRE_TIME DESC
    </select>



    <select id="getRecordByPatIdAndAdm2" parameterType="java.lang.String"
            resultType="com.jiuzhekan.cbkj.beans.business.record.TRecordAll">
        select REC_ID as recId from `t_record` as t join t_register as r on r.REGISTER_ID=t.REGISTER_ID
      <where>
          <if test="dataScope == 1">
              and t.DOC_ID = #{userId} and t.PATIENT_ID = #{patientId}
          </if>
          <if test="dataScope == 0">
              and t.PATIENT_ID = #{patientId}
          </if>
      </where>
        GROUP BY t.REGISTER_ID ORDER BY t.REC_TRE_TIME DESC
    </select>
    <!-- 就诊记录详情 -->
    <select id="getRecordByToken" resultMap="BaseResultMap2" parameterType="String">
        SELECT rec.`REC_ID`,
               rec.APP_ID,
               rec.INS_CODE,
               rec.DOC_ID,
               rec.PATIENT_CONTENT,
               rec.NOW_DESC,
               rec.PAST_DESC,
               rec.FOUR_DIAGNOSIS,
               rec.PHYSICAL,
               rec.AUXILIARY_EXAM,
               rec.ALLERGY_DESC,
               rec.PRESONAL_DESC,
               pat.`PATIENT_NAME`,
               pat.`PATIENT_GENDER`,
               rec.`REC_AGE1`,
               rec.`REC_AGEUNIT1`,
               pat.`PATIENT_CERTIFICATE_TYPE`,
               pat.`PATIENT_CERTIFICATE`,
               rec.`DEPT_NAME`,
               rec.`DIS_NAME`,
               rec.`DIS_ID`,
               rec.`SYM_NAME`,
               rec.`SYM_ID`,
               rec.`WESTERN_DISEASE`,
               rec.`THE_NAMES`,
               rec.TREATMENT_ADVICE,
               pre.`PRE_ID`,
               pre.`PRE_TYPE`,
               pre.`PRE_NUM`,
               pre.PRE_DESCRIPTION_ID,
               pre.`PRE_DESCRIPTION`,
               pre.PRE_FREQUENCY_ID,
               pre.`PRE_FREQUENCY`,
               pre.PRE_USETIME_ID,
               pre.`PRE_USETIME_DES`,
               pre.PRE_SMOKE_TYPE_ID,
               pre.PRE_SMOKE_TYPE,
               pre.`PRE_DOCTORNAME`,
               pre.`PRE_TIME`,
               pre.PRE_N_BAG,
               pre.PRE_N_ML,
               pre.PRE_N_ML_NAME,
               pre.PRE_SMOKE_INSTRUMENT_ID,
               pre.PRE_SMOKE_INSTRUMENT,
               pre.PRE_DECOCT_NUM,
               pre.ACU_TYPE_ID,
               pre.ACU_TYPE,
               pre.ACU_PROJECT_ID,
               pre.ACU_PROJECT,
               pre.ACU_OPERATION,
               pre.DECOCT_TYPE,
               pre.IS_PRODUCTION,
               pre.IS_SPECIAL_DIS,
               pre.STORE_ID,
               pre.DC_TYPE,
               pre.DC_ID,
               pre.PRE_MAT_TYPE,
               pre.PRE_ADVICE,
               pre.PRE_ORIGIN,
               pre.PRE_ORIGIN_ID,
               pre.PRE_ORIGIN_NAME,
               pre.PRODUCTION_TYPE,
               pre.PRODUCTION_TYPE_ID,
               pre.BEGAN_TIME,
               pre.END_TIME,
               item.*,
               acu.*
        FROM (select * from `t_record` where `REGISTER_ID` = #{registerId}) rec
                 LEFT JOIN `t_patients` pat ON rec.`PATIENT_ID` = pat.`PATIENT_ID`
                 LEFT JOIN `t_prescription` pre ON rec.`REC_ID` = pre.`REC_ID`
                 LEFT JOIN `t_prescription_item` item ON pre.`PRE_ID` = item.`PRE_ID`
                 LEFT JOIN `t_prescription_acu_item` acu ON acu.`PRE_ID` = pre.`PRE_ID`
        WHERE pre.`IS_PAY` = '1'
          AND pre.`REC_EXT_TYPE` >= 50
          AND pre.`REC_EXT_TYPE` != 110
          AND pre.is_del = '0'

        ORDER BY pre.`PRE_TIME`,
                 item.MAT_SEQN,
                 acu.ACU_SEQN
    </select>

    <!--接口 -->
    <select id="getBaseInfo" parameterType="java.lang.String"
            resultType="com.jiuzhekan.cbkj.beans.interaction.RecordInfo">
        SELECT rec.app_id          appId,
               rec.ins_code        insCode,
               rec.REC_NAME        patientName,
               rec.DEPT_NAME       dept,
               rec.DEPT_ID         deptId,
               rec.WARD_NAME       ward,
               rec.WARD_ID         wardId,
               rec.DIS_NAME        disName,
               rec.DIS_ID          disCode,
               rec.SYM_NAME        symName,
               rec.SYM_ID          symCode,
               rec.THE_NAMES       theNames,
               rec.WESTERN_DISEASE westernDisease,
               rec.PATIENT_CONTENT patientContent,
               rec.NOW_DESC        nowDesc,
               rec.PAST_DESC       pastDesc,
               rec.FOUR_DIAGNOSIS  fourDiagnosis,
               rec.PHYSICAL        physical,
               rec.AUXILIARY_EXAM  auxiliaryExam,
               rec.REC_TRE_TYPE    preMzZy,
               reg.VISIT_NO        visitNo,
               rec.CHECK_ADVISE    extendedTxt,
               rec.REC_AGE1        recAge,
               rec.REC_AGEUNIT1    recAgeUnit,
               rec.REC_GENDER      sex,
               rec.HOSPITAL_NO     hospitalNo,
               rec.BED_NO          bedNo,
               pat.PATIENT_MOBILE  phone
        FROM t_record rec
                 LEFT JOIN t_register reg
                           ON rec.REGISTER_ID = reg.REGISTER_ID
                 left join t_patients pat
                           on rec.patient_id = pat.patient_id
        WHERE rec.IS_DEL = '0'
          AND rec.REC_ID = #{recId}
        group by rec.register_id
    </select>

    <select id="getCheckRecord" parameterType="com.jiuzhekan.cbkj.beans.business.record.preCheckVO.CheckRecVO"
            resultType="CheckRecVO">
        SELECT
        r.REC_ID recId,
        r.APP_ID appId,
        r.INS_CODE insCode,
        p.PRE_DOCTORNAME doctorName,
        r.REC_NAME patientName,
        r.DIS_NAME disName,
        r.SYM_NAME symName,
        r.THE_NAMES theNames,
        date_format(p.PRE_TIME, '%Y-%m-%d %H:%i:%s') recTime
        FROM t_record r JOIN t_prescription p ON r.REC_ID = p.REC_ID AND p.IS_DEL = '0'
        WHERE r.is_del = '0'
        AND p.IS_CHECK is not null
        <if test="appId != null and appId !=''">
            AND r.APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode !=''">
            AND r.INS_CODE = #{insCode}
        </if>
        <if test="patientName != null and patientName !=''">
            AND r.REC_NAME like concat(#{patientName}, '%')
        </if>
        <if test="doctorName != null and doctorName !=''">
            AND p.PRE_DOCTORNAME like concat(#{doctorName}, '%')
        </if>
        <if test="startDate != null and startDate !=''">
            and p.PRE_TIME &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate !=''">
            and p.PRE_TIME &lt;= concat(#{endDate},' 23:59:59')
        </if>
        <if test="isCheck != null and isCheck !=''">
            AND p.IS_CHECK = #{isCheck}
        </if>
        <if test="checkType != null and checkType !=''">
            AND p.CHECK_TYPE = #{checkType}
        </if>
        GROUP BY r.REC_ID
        ORDER BY p.PRE_TIME DESC
    </select>

    <select id="getCheckPrescription" parameterType="com.jiuzhekan.cbkj.beans.business.record.preCheckVO.CheckRecVO"
            resultType="CheckPreVO">
        SELECT
        p.PRE_ID preId,
        p.PRE_NO preNo,
        p.CHECK_ADVISE checkAdvise,
        p.PRE_TYPE preType,
        date_format(p.PRE_TIME, '%Y-%m-%d %H:%i:%s') preTime,
        p.IS_CHECK isCheck,
        p.CHECK_TYPE checkType
        FROM t_record r JOIN t_prescription p ON r.REC_ID = p.REC_ID AND p.IS_DEL = '0'
        WHERE r.is_del = '0' and p.is_check is not null
        <if test="recId != null and recId !=''">
            AND r.REC_ID = #{recId}
        </if>
        <if test="doctorName != null and doctorName !=''">
            AND p.PRE_DOCTORNAME like concat(#{doctorName}, '%')
        </if>
        <if test="startDate != null and startDate !=''">
            and p.PRE_TIME &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate !=''">
            and p.PRE_TIME &lt;= concat(#{endDate},' 23:59:59')
        </if>
        <if test="isCheck != null and isCheck !=''">
            AND p.IS_CHECK = #{isCheck}
        </if>
        <if test="checkType != null and checkType !=''">
            AND p.CHECK_TYPE = #{checkType}
        </if>
        ORDER BY p.PRE_TIME DESC
    </select>

    <select id="newCheckPres" resultType="Integer" parameterType="com.jiuzhekan.cbkj.beans.business.CheckPreTIme" >
        select count(*)
        from t_prescription
        where is_del = '0'
          and IS_CHECK = '0'
          and pre_time &gt;= #{checkTime}
    </select>

    <select id="getTableHistoryNames" resultType="String" parameterType="String">
        SELECT TABLE_NAME tableName
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA like 'cbkj_web_api%' and TABLE_NAME like CONCAT(#{tableName}, '%') and TABLE_COMMENT = #{tableComment}
    </select>

    <select id="getRecordByPatientId" resultMap="BaseResultMap" parameterType="String">
        SELECT
        tr.PATIENT_CONTENT,tr.NOW_DESC,tr.PAST_DESC,tr.FOUR_DIAGNOSIS,tr.PHYSICAL,tr.AUXILIARY_EXAM
        FROM `t_record` tr
        WHERE tr.PATIENT_ID=  #{patientId}
        ORDER BY tr.REC_TRE_TIME DESC
        LIMIT 1
    </select>
</mapper>
