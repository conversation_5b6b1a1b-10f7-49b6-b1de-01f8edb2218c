<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.record.TRegisterMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.record.TRegister">
        <id column="REGISTER_ID" jdbcType="VARCHAR"  property="registerId" />
        <result column="APP_ID" jdbcType="VARCHAR" property="appId" />
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode" />
        <result column="PATIENT_ID" jdbcType="VARCHAR" property="patientId" />
        <result column="PATIENT_NAME" jdbcType="VARCHAR" property="patientName" />
        <result column="DEPT_ID" jdbcType="VARCHAR" property="deptId" />
        <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName" />
        <result column="DOCTOR_ID" jdbcType="VARCHAR" property="doctorId" />
        <result column="DOCTOR_NAME" jdbcType="VARCHAR" property="doctorName" />
        <result column="REGISTER_TIME" jdbcType="TIMESTAMP" property="registerTime" />
        <result column="REGISTER_NUM" jdbcType="INTEGER" property="registerNum" />
        <result column="REGISTER_TIME_ARANGE" jdbcType="VARCHAR" property="registerTimeArange" />
        <result column="REGISTER_DIAGNOSIS_STATUS" jdbcType="TINYINT" property="registerDiagnosisStatus" />
        <result column="CLINIC_TYPE_ID" jdbcType="INTEGER" property="clinicTypeId" />
        <result column="CLINIC_TYPE_NAME" jdbcType="VARCHAR" property="clinicTypeName" />
        <result column="CLINIC_TYPE_NO" jdbcType="VARCHAR" property="clinicTypeNo" />
        <result column="CLINIC_TYPE_MONEY" jdbcType="DECIMAL" property="clinicTypeMoney" />
        <result column="REC_OPEN_TIME" jdbcType="TIMESTAMP" property="recOpenTime" />
        <result column="VISIT_NO" jdbcType="VARCHAR" property="visitNo" />
        <result column="GRAVIDITY" jdbcType="VARCHAR" property="gravidity" />
        <result column="IS_INTERNET" jdbcType="INTEGER" property="isInternet"/>
    </resultMap>


    <sql id="Base_Column_List">
    REGISTER_ID,APP_ID,INS_CODE,PATIENT_ID,PATIENT_NAME,DEPT_ID,DEPT_NAME,DOCTOR_ID,DOCTOR_NAME,REGISTER_TIME,REGISTER_NUM,REGISTER_TIME_ARANGE,REGISTER_DIAGNOSIS_STATUS,CLINIC_TYPE_ID,CLINIC_TYPE_NAME,CLINIC_TYPE_NO,CLINIC_TYPE_MONEY,REC_OPEN_TIME,VISIT_NO,GRAVIDITY,IS_INTERNET
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRegister">
        delete from t_register where REGISTER_ID = #{ registerId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_register where REGISTER_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TRegister">
        insert into t_register (REGISTER_ID,APP_ID,INS_CODE,PATIENT_ID,PATIENT_NAME,DEPT_ID,DEPT_NAME,DOCTOR_ID,DOCTOR_NAME,REGISTER_TIME,REGISTER_NUM,REGISTER_TIME_ARANGE,REGISTER_DIAGNOSIS_STATUS,CLINIC_TYPE_ID,CLINIC_TYPE_NAME,CLINIC_TYPE_NO,CLINIC_TYPE_MONEY,REC_OPEN_TIME,VISIT_NO,GRAVIDITY,IS_INTERNET) values
        (#{registerId},#{appId},#{insCode},#{patientId},#{patientName},#{deptId},#{deptName},#{doctorId},#{doctorName},#{registerTime},#{registerNum},#{registerTimeArange},#{registerDiagnosisStatus},#{clinicTypeId},#{clinicTypeName},#{clinicTypeNo},#{clinicTypeMoney},#{recOpenTime},#{visitNo},#{gravidity},#{isInternet})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_register (REGISTER_ID,APP_ID,INS_CODE,PATIENT_ID,PATIENT_NAME,DEPT_ID,DEPT_NAME,DOCTOR_ID,DOCTOR_NAME,REGISTER_TIME,REGISTER_NUM,REGISTER_TIME_ARANGE,REGISTER_DIAGNOSIS_STATUS,CLINIC_TYPE_ID,CLINIC_TYPE_NAME,CLINIC_TYPE_NO,CLINIC_TYPE_MONEY,REC_OPEN_TIME,VISIT_NO,GRAVIDITY,IS_INTERNET) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.registerId},#{item.appId},#{item.insCode},#{item.patientId},#{item.patientName},#{item.deptId},#{item.deptName},#{item.doctorId},#{item.doctorName},#{item.registerTime},#{item.registerNum},#{item.registerTimeArange},#{item.registerDiagnosisStatus},#{item.clinicTypeId},#{item.clinicTypeName},#{item.clinicTypeNo},#{item.clinicTypeMoney},#{item.recOpenTime},#{item.visitNo},#{item.gravidity},#{item.isInternet})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRegister">
        update t_register
        <set>
             <if test="appId != null">
                APP_ID = #{ appId },
             </if>
             <if test="insCode != null">
                INS_CODE = #{ insCode },
             </if>
             <if test="patientId != null">
                PATIENT_ID = #{ patientId },
             </if>
             <if test="patientName != null">
                PATIENT_NAME = #{ patientName },
             </if>
             <if test="deptId != null">
                DEPT_ID = #{ deptId },
             </if>
             <if test="deptName != null">
                DEPT_NAME = #{ deptName },
             </if>
             <if test="doctorId != null">
                DOCTOR_ID = #{ doctorId },
             </if>
             <if test="doctorName != null">
                DOCTOR_NAME = #{ doctorName },
             </if>
             <if test="registerTime != null">
                REGISTER_TIME = #{ registerTime },
             </if>
             <if test="registerNum != null">
                REGISTER_NUM = #{ registerNum },
             </if>
             <if test="registerTimeArange != null">
                REGISTER_TIME_ARANGE = #{ registerTimeArange },
             </if>
             <if test="registerDiagnosisStatus != null">
                REGISTER_DIAGNOSIS_STATUS = #{ registerDiagnosisStatus },
             </if>
             <if test="clinicTypeId != null">
                CLINIC_TYPE_ID = #{ clinicTypeId },
             </if>
             <if test="clinicTypeName != null">
                CLINIC_TYPE_NAME = #{ clinicTypeName },
             </if>
             <if test="clinicTypeNo != null">
                CLINIC_TYPE_NO = #{ clinicTypeNo },
             </if>
             <if test="clinicTypeMoney != null">
                CLINIC_TYPE_MONEY = #{ clinicTypeMoney },
             </if>
             <if test="recOpenTime != null">
                REC_OPEN_TIME = #{ recOpenTime },
             </if>
             <if test="visitNo != null">
                 VISIT_NO = #{ visitNo },
             </if>
             <if test="gravidity != null">
                 GRAVIDITY = #{ gravidity },
             </if>
             <if test="isInternet != null">
                 IS_INTERNET = #{ isInternet },
             </if>
        </set>
        where REGISTER_ID = #{ registerId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_register where REGISTER_ID = #{id}
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TRegister" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from t_register
        where 1=1
        <!--条件-->
        <!--<if test="name != null and name!='' ">-->
        <!--and name like CONCAT('%',trim(#{name}),'%')-->
        <!--</if>-->
    </select>

    <select id="getExistRegByReg" parameterType="TRegister" resultType="TRegister">
        select
            DOCTOR_NAME AS doctorName,
            REGISTER_ID AS registerId,
            APP_ID AS appId,
            INS_CODE AS insCode,
            PATIENT_ID AS patientId,
            PATIENT_NAME AS patientName,
            REGISTER_DIAGNOSIS_STATUS AS registerDiagnosisStatus,
            DOCTOR_ID doctorId
        from t_register
        <where>
            DATE_FORMAT(REGISTER_TIME,'%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d')
            <if test="appId != null and appId != ''">
                AND APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode != ''">
                AND INS_CODE = #{insCode}
            </if>
            <if test="doctorId != null and doctorId != ''">
                AND DOCTOR_ID = #{doctorId}
            </if>
            <if test="patientId != null and patientId != ''">
                AND PATIENT_ID = #{patientId}
            </if>
            <if test="registerDiagnosisStatus != null ">
                AND REGISTER_DIAGNOSIS_STATUS &lt;= #{registerDiagnosisStatus}
            </if>
        </where>
        limit 1
    </select>


    <!-- 插入挂号表 -->
    <insert id="addRegister" parameterType="com.jiuzhekan.cbkj.beans.business.record.TRegister">
        INSERT INTO t_register (
            REGISTER_ID,	APP_ID,	INS_CODE,	PATIENT_ID,	PATIENT_NAME,	DEPT_ID,	DEPT_NAME,	DOCTOR_ID,
            DOCTOR_NAME,	REGISTER_TIME,	REGISTER_NUM,	REGISTER_TIME_ARANGE,	REGISTER_DIAGNOSIS_STATUS,
            CLINIC_TYPE_ID,	CLINIC_TYPE_NAME,	CLINIC_TYPE_MONEY,	REC_OPEN_TIME, GRAVIDITY
        ) VALUES
        (#{registerId},#{appId},	#{insCode},#{patientId},	#{patientName},#{deptId},	#{deptName},
            #{doctorId},#{doctorName},#{registerTime},	#{registerNum},#{registerTimeArange},
            #{registerDiagnosisStatus},	#{clinicTypeId}, #{clinicTypeName}, #{clinicTypeMoney}, #{recOpenTime}, #{gravidity})
    </insert>

    <select id="getSMFParams" parameterType="String" resultType="com.jiuzhekan.cbkj.beans.http.SMFParams">
        select tr.PATIENT_NAME as patientName , tr.DOCTOR_NAME as doctorName , tr.DEPT_NAME as deptName ,
        (
            case
                when tp.PATIENT_GENDER = 'm' then '男'
                when tp.PATIENT_GENDER = 'f' then '女'
            end
        ) as patientGender , tp.PATIENT_BIRTHDAY as patientBirthday , tp.PATIENT_CERTIFICATE as patientCertificate
        from t_register tr
        left join t_patients tp on tr.PATIENT_ID = tp.PATIENT_ID
        where tr.REGISTER_ID = #{registerId}
    </select>

    <select id="getRegisterByRegId" parameterType="String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from t_register where REGISTER_ID = #{registerId}
    </select>

</mapper>