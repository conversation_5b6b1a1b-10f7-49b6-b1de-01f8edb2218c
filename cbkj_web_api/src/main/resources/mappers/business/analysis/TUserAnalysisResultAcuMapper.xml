<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.analysis.TUserAnalysisResultAcuMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.analysis.TUserAnalysisResultAcu">
        <id column="analy_id" jdbcType="VARCHAR"  property="analyId" />
        <id column="acu_id" jdbcType="VARCHAR" property="acuId" />
        <result column="acu_code" jdbcType="VARCHAR" property="acuCode" />
        <result column="acu_name" jdbcType="VARCHAR" property="acuName" />
        <result column="acu_img" jdbcType="VARCHAR" property="acuImg" />
        <result column="acu_position" jdbcType="VARCHAR" property="acuPosition" />
    </resultMap>


    <sql id="Base_Column_List">
    analy_id,acu_id,acu_code,acu_name,acu_img,acu_position
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TUserAnalysisResultAcu">
        delete from t_user_analysis_result_acu where analy_id = #{ analyId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_user_analysis_result_acu where analy_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TUserAnalysisResultAcu">
        insert into t_user_analysis_result_acu (<include refid="Base_Column_List" />) values
        (#{analyId},#{acuId},#{acuCode},#{acuName},#{acuImg},#{acuPosition})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_user_analysis_result_acu (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.analyId},#{item.acuId},#{item.acuCode},#{item.acuName},#{item.acuImg},#{item.acuPosition})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TUserAnalysisResultAcu">
        update t_user_analysis_result_acu
        <set>
             <if test="acuId != null">
                acu_id = #{ acuId },
             </if>
             <if test="acuCode != null">
                acu_code = #{ acuCode },
             </if>
             <if test="acuName != null">
                acu_name = #{ acuName },
             </if>
             <if test="acuImg != null">
                acu_img = #{ acuImg },
             </if>
             <if test="acuPosition != null">
                 acu_position = #{ acuPosition },
             </if>
        </set>
        where analy_id = #{ analyId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_user_analysis_result_acu where analy_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TUserAnalysisResultAcu" resultMap="BaseResultMap">
        SELECT analy_id,acu_id,acu_code,acu_name,acu_img,acu_position
        from t_user_analysis_result_acu
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>


    <delete id="deleteByAnalyId" parameterType="String">
        delete from t_user_analysis_result_acu where analy_id = #{ analyId }
    </delete>

    <select id="getAcuListByAnalyId" parameterType="String" resultMap="BaseResultMap">
        SELECT analy_id,acu_id,acu_code,acu_name,acu_img,acu_position
        from t_user_analysis_result_acu
        where analy_id = #{id}
    </select>

</mapper>