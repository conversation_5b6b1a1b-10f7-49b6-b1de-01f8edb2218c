<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.doctor.TDoctorBookMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.doctor.TDoctorBook">
        <id column="ID" jdbcType="INTEGER"  property="id" />
        <result column="DOCTOR_ID" jdbcType="VARCHAR" property="doctorId" />
        <result column="DOCTOR_NAME" jdbcType="VARCHAR" property="doctorName" />
        <result column="BOOK_ID" jdbcType="VARCHAR" property="bookId" />
        <result column="BOOK_NAME" jdbcType="VARCHAR" property="bookName" />
        <result column="AUTHOR" jdbcType="VARCHAR" property="author" />
        <result column="CHAPTER_ID" jdbcType="VARCHAR" property="chapterId" />
        <result column="PROGRESS" jdbcType="VARCHAR" property="progress" />
        <result column="RATE" jdbcType="DECIMAL" property="rate" />
        <result column="SORT" jdbcType="INTEGER" property="sort" />
        <result column="SHELF" jdbcType="INTEGER" property="shelf" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="readTime" jdbcType="VARCHAR" property="readTime" />
    </resultMap>


    <sql id="Base_Column_List">
        ID,DOCTOR_ID,DOCTOR_NAME,BOOK_ID,BOOK_NAME,AUTHOR,CHAPTER_ID,PROGRESS,RATE,SORT,SHELF,DATE_FORMAT(UPDATE_DATE,'%Y-%m-%d %H:%i:%s') readTime
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDoctorBook">
        delete from t_doctor_book where ID = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_doctor_book where ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TDoctorBook">
        insert into t_doctor_book (DOCTOR_ID,DOCTOR_NAME,BOOK_ID,BOOK_NAME,AUTHOR,CHAPTER_ID,PROGRESS,RATE,SORT,SHELF,CREATE_DATE) values
        (#{doctorId},#{doctorName},#{bookId},#{bookName},#{author},#{chapterId},#{progress},#{rate},#{sort},#{shelf},#{createDate})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_doctor_book (DOCTOR_ID,DOCTOR_NAME,BOOK_ID,BOOK_NAME,AUTHOR,CHAPTER_ID,PROGRESS,RATE,SORT,SHELF,CREATE_DATE) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.doctorId},#{item.doctorName},#{item.bookId},#{item.bookName},#{item.author},#{item.chapterId},#{item.progress},#{item.rate},#{item.sort},#{item.shelf},#{item.createDate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDoctorBook">
        update t_doctor_book
        <set>
             <if test="doctorId != null">
                DOCTOR_ID = #{ doctorId },
             </if>
             <if test="doctorName != null">
                DOCTOR_NAME = #{ doctorName },
             </if>
             <if test="bookId != null">
                BOOK_ID = #{ bookId },
             </if>
             <if test="bookName != null">
                BOOK_NAME = #{ bookName },
             </if>
             <if test="author != null">
                AUTHOR = #{ author },
             </if>
             <if test="chapterId != null">
                CHAPTER_ID = #{ chapterId },
             </if>
             <if test="progress != null">
                PROGRESS = #{ progress },
             </if>
             <if test="rate != null">
                RATE = #{ rate },
             </if>
             <if test="sort != null">
                SORT = #{ sort },
             </if>
             <if test="shelf != null">
                 SHELF = #{ shelf },
             </if>
             <if test="createDate != null">
                CREATE_DATE = #{ createDate },
             </if>
             <if test="updateDate != null">
                UPDATE_DATE = #{ updateDate },
             </if>
        </set>
        where ID = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_doctor_book where ID = #{id}
    </select>

    <select id="readRecord" parameterType="TDoctorBook" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from t_doctor_book
        <where>
            PROGRESS is not null
            <if test=" doctorId != null and doctorId!='' ">
                and DOCTOR_ID = #{doctorId}
            </if>
            <if test=" bookId != null and bookId!='' ">
                and BOOK_ID = #{bookId}
            </if>
        </where>
        order by UPDATE_DATE desc, CREATE_DATE desc
    </select>

    <select id="recentReading" parameterType="TDoctorBook" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from t_doctor_book
        <where>
            PROGRESS is not null
            <if test=" doctorId != null and doctorId!='' ">
                and DOCTOR_ID = #{doctorId}
            </if>
        </where>
        order by UPDATE_DATE desc, CREATE_DATE desc
        limit 1
    </select>

    <select id="shelf" parameterType="TDoctorBook" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from t_doctor_book
        <where>
            shelf = 1
            <if test=" doctorId != null and doctorId!='' ">
                and DOCTOR_ID = #{doctorId}
            </if>
            <if test=" bookId != null and bookId!='' ">
                and BOOK_ID = #{bookId}
            </if>
        </where>
        order by SORT, UPDATE_DATE desc, CREATE_DATE desc
    </select>

    <select id="readDetail" parameterType="TDoctorBook" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from t_doctor_book
        <where>
            <if test=" doctorId != null and doctorId!='' ">
                and DOCTOR_ID = #{doctorId}
            </if>
            <if test=" bookId != null and bookId!='' ">
                and BOOK_ID = #{bookId}
            </if>
        </where>
        limit 1
    </select>

    <select id="readBookNum" parameterType="string" resultType="int">
        SELECT count(1) from t_doctor_book where BOOK_ID = #{bookId}
    </select>
</mapper>