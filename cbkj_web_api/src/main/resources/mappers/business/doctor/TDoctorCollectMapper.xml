<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.doctor.TDoctorCollectMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.doctor.TDoctorCollect">
        <id column="COLLECT_ID" jdbcType="VARCHAR"  property="collectId" />
        <result column="COLLECT_MODULE" jdbcType="INTEGER" property="collectModule" />
        <result column="CONNECT_ID" jdbcType="VARCHAR" property="connectId" />
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    </resultMap>


    <sql id="Base_Column_List">
    COLLECT_ID,COLLECT_MODULE,CONNECT_ID,CREATE_USER,CREATE_DATE,UPDATE_DATE
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDoctorCollect">
        delete from t_doctor_collect where COLLECT_ID = #{ collectId }
    </delete>

    <delete id="deleteByObj" parameterType="TDoctorCollect">
        delete from t_doctor_collect
        <where>
            <choose>
                <when test="collectId != null and collectId != ''">
                    COLLECT_ID = #{ collectId }
                </when>
                <when test="connectId != null and connectId != '' and createUser != null and createUser != ''">
                    CONNECT_ID = #{ connectId } and CREATE_USER = #{createUser}
                </when>
                <otherwise>
                    COLLECT_ID = ''
                </otherwise>
            </choose>
        </where>
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_doctor_collect where COLLECT_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="COLLECT_ID"  parameterType="TDoctorCollect">
        insert into t_doctor_collect (COLLECT_ID,COLLECT_MODULE,CONNECT_ID,CREATE_USER,CREATE_DATE,UPDATE_DATE) values
        (#{collectId},#{collectModule},#{connectId},#{createUser},#{createDate},#{updateDate})
    </insert>

    <insert id="insertTDoctor"  useGeneratedKeys="true" keyProperty="COLLECT_ID"  parameterType="TDoctorCollect">
        insert into t_doctor_collect (COLLECT_ID,COLLECT_MODULE,CONNECT_ID,CREATE_USER,CREATE_DATE,UPDATE_DATE) values
        (#{collectId},#{collectModule},#{connectId},#{createUser},#{createDate},#{updateDate})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_doctor_collect (COLLECT_ID,COLLECT_MODULE,CONNECT_ID,CREATE_USER,CREATE_DATE,UPDATE_DATE) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.collectId},#{item.collectModule},#{item.connectId},#{item.createUser},#{item.createDate},#{item.updateDate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDoctorCollect">
        update t_doctor_collect
        <set>
             <if test="collectModule != null">
                COLLECT_MODULE = #{ collectModule },
             </if>
             <if test="connectId != null">
                CONNECT_ID = #{ connectId },
             </if>
             <if test="createUser != null">
                CREATE_USER = #{ createUser },
             </if>
             <if test="createDate != null">
                CREATE_DATE = #{ createDate },
             </if>
             <if test="updateDate != null">
                UPDATE_DATE = #{ updateDate },
             </if>
        </set>
        where COLLECT_ID = #{ collectId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_doctor_collect where COLLECT_ID = #{id}
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TDoctorCollect" resultMap="BaseResultMap">
        SELECT c.COLLECT_ID,c.COLLECT_MODULE,c.CONNECT_ID,c.CREATE_USER,
        c.CREATE_DATE,c.UPDATE_DATE
        FROM t_doctor_collect c

        <where>
            <if test="collectModule != null ">
                and COLLECT_MODULE = #{collectModule}
            </if>
            <if test="createUser != null and createUser !=''">
                and CREATE_USER = #{createUser}
            </if>
            <if test="connectId != null and connectId !=''">
                and CONNECT_ID = #{connectId}
            </if>
        </where>
        order by c.CREATE_DATE desc
    </select>

    <select id="getCollectByObj" parameterType="TDoctorCollect" resultMap="BaseResultMap">
        select COLLECT_ID,COLLECT_MODULE,CONNECT_ID,CREATE_USER,CREATE_DATE
        from t_doctor_collect
        <where>
            <if test="collectId != null and collectId != ''">
                COLLECT_ID = #{collectId}
            </if>
            <if test="connectId != null and connectId != ''">
                and CONNECT_ID = #{connectId}
            </if>
            <if test="createUser != null and createUser != ''">
                and CREATE_USER = #{createUser}
            </if>
        </where>
        limit 1
    </select>

    <select id="getExistCollectByConnectIds" parameterType="TDoctorCollect" resultType="String">
        select CONNECT_ID
        from t_doctor_collect
        where CREATE_USER = #{createUser}
        and CONNECT_ID in
        <foreach collection="connectId.split(',')" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        group by CONNECT_ID
    </select>
    <select id="getGuideExistCollectByConnectIds" parameterType="TDoctorCollect" resultType="String">
        select CONNECT_ID
        from t_doctor_collect
        where CREATE_USER = #{createUser}
        and CONNECT_ID REGEXP #{connectId}
        group by CONNECT_ID
    </select>
    <select id="getListJoinTKnowledgeShare" resultType="java.util.Map" parameterType="java.util.Map">
        select a.knowledge_share_id as id,a.share_title as name ,
               a.share_file_path as shareFilePath
        from t_knowledge_share as a join t_doctor_collect as b on(b.CONNECT_ID = a.knowledge_share_id)
        where a.share_type = #{shareType} and b.CREATE_USER = #{createUser}
    </select>

</mapper>