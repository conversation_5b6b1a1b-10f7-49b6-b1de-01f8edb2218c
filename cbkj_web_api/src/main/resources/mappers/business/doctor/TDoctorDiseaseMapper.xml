<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.doctor.TDoctorDiseaseMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.doctor.TDoctorDisease">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="DOC_ID" jdbcType="VARCHAR" property="docId"/>
        <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId"/>
        <result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName"/>
        <result column="DIS_ID" jdbcType="VARCHAR" property="disId"/>
        <result column="DIS_NAME" jdbcType="VARCHAR" property="disName"/>
        <result column="DIS_NUM" jdbcType="VARCHAR" property="disNum"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="GENDER" jdbcType="VARCHAR" property="gender"/>
    </resultMap>


    <sql id="Base_Column_List">
    ID,GROUP_ID,GROUP_NAME,DOC_ID,DIS_ID,DIS_NAME,DIS_NUM,CREATE_TIME,GENDER
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDoctorDisease">
        delete from t_doctor_disease where ID = #{ id }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_doctor_disease where ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" parameterType="TDoctorDisease">
        insert into t_doctor_disease (ID,GROUP_ID,GROUP_NAME,DOC_ID,DIS_ID,DIS_NAME,DIS_NUM,CREATE_TIME,GENDER) values
        (#{id},#{groupId},#{groupName},#{docId},#{disId},#{disName},#{disNum},#{createTime},#{gender})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_doctor_disease (ID,GROUP_ID,GROUP_NAME,DOC_ID,DIS_ID,DIS_NAME,DIS_NUM,CREATE_TIME,GENDER) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.groupId},#{item.groupName},#{item.docId},#{item.disId},#{item.disName},#{item.disNum},#{item.createTime},#{item.gender})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDoctorDisease">
        update t_doctor_disease
        <set>
            <if test="docId != null">
                DOC_ID = #{ docId },
            </if>
            <if test="groupId != null">
                GROUP_ID = #{ groupId },
            </if>
            <if test="groupName != null">
                GROUP_NAME = #{ groupName },
            </if>
            <if test="disId != null">
                DIS_ID = #{ disId },
            </if>
            <if test="disName != null">
                DIS_NAME = #{ disName },
            </if>
            <if test="disNum != null">
                DIS_NUM = #{ disNum },
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{ createTime },
            </if>
            <if test="gender != null">
                GENDER = #{ gender },
            </if>
        </set>
        where ID = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_doctor_disease where ID = #{id}
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TDoctorDisease" resultMap="BaseResultMap">
        SELECT ID,GROUP_ID,GROUP_NAME,DOC_ID,DIS_ID,DIS_NAME,DIS_NUM,CREATE_TIME
        from t_doctor_disease
        <where>
            <if test=" docId != null and docId != '' ">
                and DOC_ID = #{docId}
            </if>
        </where>
    </select>

    <!--某医生30天内的常用疾病-->
    <select id="getDoctorDiseaseInThirtyDay" parameterType="TDoctorDisease" resultMap="BaseResultMap">
        select a.* from (SELECT ID,GROUP_ID,GROUP_NAME,DOC_ID,DIS_ID,DIS_NAME,DIS_NUM,CREATE_TIME,GENDER
        FROM t_doctor_disease
        WHERE DOC_ID = #{docId}
        AND CREATE_TIME = (SELECT MAX(CREATE_TIME) FROM t_doctor_disease WHERE DOC_ID = #{docId})
        order by DIS_NUM desc) as a group by a.DIS_ID
    </select>

<!--     <select id="statisticDoctorDiseaseFromRecord" resultMap="BaseResultMap">-->
<!--       SELECT tr.doc_id, tr.dis_id, tr.dis_name, COUNT(*) as dis_num,bm.GENDER-->
<!--        FROM `t_record` tr-->
<!--        inner join-->
<!--        b_mapping_disease bm-->
<!--        on tr.DIS_ID = bm.DIS_ID_SYS-->
<!--        WHERE is_del = '0' AND doc_id IS NOT NULL AND dis_id IS NOT NULL AND dis_name IS NOT NULL-->
<!--            AND DATE_SUB(CURDATE(), INTERVAL 30 DAY) &lt;= REC_TRE_TIME-->
<!--        GROUP BY doc_id, dis_id-->
<!--        ORDER BY doc_id, COUNT(*) DESC-->
<!--    </select>-->

    <select id="statisticDoctorDiseaseFromGroup" parameterType="Integer" resultMap="BaseResultMap">
        SELECT doctor_id AS doc_id, group_id, group_name, dis_id, dis_name, COUNT(*) AS dis_num
        FROM t_record_syndrome_group
        WHERE DATE_SUB(CURDATE(), INTERVAL IFNULL(#{days}, 0) DAY) &lt;= CREATE_DATE
        GROUP BY doctor_id, group_id
        ORDER BY doctor_id, COUNT(*) DESC
    </select>
</mapper>