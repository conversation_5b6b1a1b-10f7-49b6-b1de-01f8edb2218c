<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.doctor.TDoctorNoteMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.doctor.TDoctorNote">
        <id column="NOTE_ID" jdbcType="VARCHAR"  property="noteId" />
        <result column="NOTE_MODULE" jdbcType="INTEGER" property="noteModule" />
        <result column="CONNECT_ID" jdbcType="VARCHAR" property="connectId" />
        <result column="NOTE_TYPE" jdbcType="INTEGER" property="noteType" />
        <result column="NOTE_DESC" jdbcType="VARCHAR" property="noteDesc" />
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="NOTE_USERNAME" jdbcType="VARCHAR" property="noteUsername" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="isSelf" jdbcType="INTEGER" property="isSelf" />
    </resultMap>


    <sql id="Base_Column_List">
    NOTE_ID,NOTE_MODULE,CONNECT_ID,NOTE_TYPE,NOTE_DESC,CREATE_USER,CREATE_DATE,UPDATE_DATE
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDoctorNote">
        delete from t_doctor_note where NOTE_ID = #{ noteId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_doctor_note where NOTE_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="NOTE_ID"  parameterType="TDoctorNote">
        insert into t_doctor_note (NOTE_ID,NOTE_MODULE,CONNECT_ID,NOTE_TYPE,NOTE_DESC,CREATE_USER,CREATE_DATE,UPDATE_DATE) values
        (#{noteId},#{noteModule},#{connectId},#{noteType},#{noteDesc},#{createUser},#{createDate},#{updateDate})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_doctor_note (NOTE_ID,NOTE_MODULE,CONNECT_ID,NOTE_TYPE,NOTE_DESC,CREATE_USER,CREATE_DATE,UPDATE_DATE) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.noteId},#{item.noteModule},#{item.connectId},#{item.noteType},#{item.noteDesc}
            ,#{item.createUser},#{item.createDate},#{item.updateDate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDoctorNote">
        update t_doctor_note
        <set>
             <if test="noteModule != null">
                NOTE_MODULE = #{ noteModule },
             </if>
             <if test="connectId != null">
                CONNECT_ID = #{ connectId },
             </if>
             <if test="noteType != null">
                NOTE_TYPE = #{ noteType },
             </if>
             <if test="noteDesc != null">
                NOTE_DESC = #{ noteDesc },
             </if>
             <if test="createUser != null">
                CREATE_USER = #{ createUser },
             </if>
             <if test="createDate != null">
                CREATE_DATE = #{ createDate },
             </if>
             <if test="updateDate != null">
                UPDATE_DATE = #{ updateDate },
             </if>
        </set>
        where NOTE_ID = #{ noteId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_doctor_note <where> NOTE_ID = #{id}</where>
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TDoctorNote" resultMap="BaseResultMap">
        SELECT NOTE_ID,NOTE_MODULE,CONNECT_ID,NOTE_TYPE,NOTE_DESC,CREATE_USER,CREATE_DATE,UPDATE_DATE, (create_user = #{createUser}) isSelf
        from t_doctor_note
        <where>
            <if test=" noteModeule != null">
                and NOTE_MODULE =  #{noteModeule}
            </if>
            <if test=" noteType != null">
                and NOTE_TYPE =  #{noteType}
            </if>
            <if test="connectId != null and connectId != ''">
                and connect_id = #{connectId}
            </if>
            <if test="createUser != null and createUser != ''">
                and !(create_user != #{createUser} and note_type =1)
            </if>
        </where>
        order by isSelf desc, CREATE_DATE desc
    </select>

    <select id="getAllNotes"  parameterType="TDoctorNote" resultMap="BaseResultMap">
        SELECT note.NOTE_ID,note.NOTE_MODULE,note.CONNECT_ID,note.NOTE_TYPE,note.NOTE_DESC,
        note.CREATE_USER,note.CREATE_DATE
<!--        , (note.create_user = #{createUser}) isSelf-->
        FROM  t_doctor_note AS note
        <where>
              <if test="noteModule != null">
                and  note.note_module = #{noteModule}
              </if>
              <if test="connectId != null and connectId != ''">
                 and note.connect_id = #{connectId}
              </if>
              <if test="createUser != null and createUser != ''">
                  and !(note.create_user != #{createUser} and note.note_type =1)
              </if>
          </where>
           ORDER BY
<!--        isSelf desc, -->
        note.create_date desc
    </select>

    <select id="getObjectByNote" resultMap="BaseResultMap" parameterType="String">
        select NOTE_ID,NOTE_MODULE,CONNECT_ID,NOTE_TYPE,NOTE_DESC,CREATE_USER,CREATE_DATE
        from t_doctor_note
        <where>
            <if test="noteId != null and noteId != ''">
                NOTE_ID = #{noteId}
            </if>
            <if test="connectId != null and connectId != ''">
                and CONNECT_ID = #{connectId}
            </if>
            <if test="createUser != null and createUser != ''">
                and CREATE_USER = #{createUser}
            </if>
        </where>
        limit 1
    </select>
</mapper>