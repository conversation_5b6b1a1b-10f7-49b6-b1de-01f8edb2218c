<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.his.THisRecordMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.his.THisRecord">
        <id column="TOKEN" jdbcType="VARCHAR" property="token"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="TIMESTAMP" jdbcType="INTEGER" property="timestamp"/>
        <result column="PATIENT_CONTENT" jdbcType="VARCHAR" property="patientContent"/>
        <result column="NOW_DESC" jdbcType="VARCHAR" property="nowDesc"/>
        <result column="PAST_DESC" jdbcType="VARCHAR" property="pastDesc"/>
        <result column="FOUR_DIAGNOSIS" jdbcType="VARCHAR" property="fourDiagnosis"/>
        <result column="PHYSICAL" jdbcType="VARCHAR" property="physical"/>
        <result column="AUXILIARY_EXAM" jdbcType="VARCHAR" property="auxiliaryExam"/>
        <result column="ADMIN_INFO_ID" jdbcType="VARCHAR" property="adminInfoId"/>
        <result column="PATIENTS_ID" jdbcType="VARCHAR" property="patientsId"/>
        <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode"/>
        <result column="DIS_NAME" jdbcType="VARCHAR" property="disName"/>
        <result column="SYM_COED" jdbcType="VARCHAR" property="symCode"/>
        <result column="SYM_NAME" jdbcType="VARCHAR" property="symName"/>
        <result column="WESTERN_DISEASE" jdbcType="VARCHAR" property="westernDisease"/>
        <result column="WESTERN_CODE" jdbcType="VARCHAR" property="westernCode"/>
        <result column="PRE_MZ_ZY" jdbcType="VARCHAR" property="preMzZy"/>
        <result column="HOSPITAL_NO" jdbcType="VARCHAR" property="hospitalNo"/>
        <result column="DEPT_ID" jdbcType="VARCHAR" property="deptId"/>
        <result column="DEPT" jdbcType="VARCHAR" property="dept"/>
        <result column="WARD_ID" jdbcType="VARCHAR" property="wardId"/>
        <result column="WARD" jdbcType="VARCHAR" property="ward"/>
        <result column="BED_NO" jdbcType="VARCHAR" property="bedNo"/>
        <result column="EXTENDED_TXT" jdbcType="VARCHAR" property="extendedTxt"/>
        <result column="VISIT_NO" jdbcType="VARCHAR" property="visitNo"/>
        <result column="IS_OWN_EXP" jdbcType="VARCHAR" property="isOwnExp"/>
        <result column="IS_SPECIAL_DIS" jdbcType="VARCHAR" property="isSpecialDis"/>
        <result column="SPECIAL_DIS_CODE" jdbcType="VARCHAR" property="specialDisCode"/>
        <result column="PRE_DECOCTION_FEE_CODE" jdbcType="VARCHAR" property="preDecoctionFeeCode"/>
        <result column="PRE_PRODUCTION_FEE_CODE" jdbcType="VARCHAR" property="preProductionFeeCode"/>
        <result column="PRE_EXPRESS_FEE_CODE" jdbcType="VARCHAR" property="preExpressFeeCode"/>
        <result column="PRE_SMO_MONEY_CODE" jdbcType="VARCHAR" property="preSmoMoneyCode"/>
        <result column="ADMISSION_TIME" jdbcType="DATE" property="admissionTime"/>
        <result column="IS_INTERNET" jdbcType="INTEGER" property="isInternet"/>
        <result column="XDFXH" jdbcType="VARCHAR" property="xdfxh"/>
        <result column="patient_weight" jdbcType="VARCHAR" property="patientWeight"/>
        <result column="patient_height" jdbcType="VARCHAR" property="patientHeight"/>
        <result column="treatment_advice" jdbcType="VARCHAR" property="treatmentAdvice"/>
        <result column="gms" jdbcType="VARCHAR" property="gms"/>
        <result column="grs" jdbcType="VARCHAR" property="grs"/>

    </resultMap>

    <resultMap id="MappingDiseaseMap" type="com.jiuzhekan.cbkj.beans.business.his.BMappingDisease">
        <id column="DIS_ID_SYS" jdbcType="VARCHAR" property="disIdSys"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="DIS_ID_HIS" jdbcType="VARCHAR" property="disIdHis"/>
        <result column="DIS_CODE_HIS" jdbcType="VARCHAR" property="disCodeHis"/>
        <result column="DIS_CODE_SYS" jdbcType="VARCHAR" property="disCodeSys"/>
        <result column="DIS_NAME_HIS" jdbcType="VARCHAR" property="disNameHis"/>
        <result column="DIS_NAME_SYS" jdbcType="VARCHAR" property="disNameSys"/>
    </resultMap>

    <resultMap id="MappingSymptomMap" type="com.jiuzhekan.cbkj.beans.business.his.BMappingSymptom">
        <id column="SYM_ID_SYS" jdbcType="VARCHAR" property="symIdSys"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="SYM_ID_HIS" jdbcType="VARCHAR" property="symIdHis"/>
        <result column="SYM_CODE_HIS" jdbcType="VARCHAR" property="symCodeHis"/>
        <result column="SYM_CODE_SYS" jdbcType="VARCHAR" property="symCodeSys"/>
        <result column="SYM_NAME_HIS" jdbcType="VARCHAR" property="symNameHis"/>
        <result column="SYM_NAME_SYS" jdbcType="VARCHAR" property="symNameSys"/>
    </resultMap>


    <sql id="Base_Column_List">
    TOKEN,APP_ID,INS_CODE,TIMESTAMP,PATIENT_CONTENT,NOW_DESC,PAST_DESC,FOUR_DIAGNOSIS,PHYSICAL,AUXILIARY_EXAM,ADMIN_INFO_ID,PATIENTS_ID,DIS_CODE,
    DIS_NAME,SYM_COED,SYM_NAME,WESTERN_DISEASE,WESTERN_CODE,PRE_MZ_ZY,HOSPITAL_NO,DEPT_ID,DEPT,WARD_ID,WARD,BED_NO,EXTENDED_TXT,VISIT_NO,IS_OWN_EXP,
    IS_SPECIAL_DIS,SPECIAL_DIS_CODE,PRE_DECOCTION_FEE_CODE,PRE_PRODUCTION_FEE_CODE,PRE_EXPRESS_FEE_CODE,PRE_SMO_MONEY_CODE,ADMISSION_TIME,IS_INTERNET,XDFXH,
    patient_height,patient_weight,treatment_advice,gms,grs
    </sql>

    <select id="getHisRecordByToken" parameterType="String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_his_record
        where token = #{token}
    </select>
    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.business.prescription.THisXdfResult2">
        <id column="xh" jdbcType="VARCHAR"  property="xh" />
        <result column="preName" jdbcType="VARCHAR" property="preName" />
        <result column="persPreId" jdbcType="VARCHAR" property="persPreId" />
        <result column="preType" jdbcType="VARCHAR" property="preType" />
    </resultMap>

    <select id="getHisXHByToken" parameterType="com.jiuzhekan.cbkj.beans.business.his.XDFQuery" resultMap="BaseResultMap2">
        SELECT
        c.PRE_TYPE as preType,
        c.PRE_NAME AS preName,
        c.PERS_PRE_ID AS persPreId,
        b.xh AS xh
        FROM t_his_xdf AS a
        JOIN t_his_xdf_mapping AS b ON (a.XH=b.XH)
        JOIN t_personal_prescription AS c ON (c.PERS_PRE_ID=b.PERS_PRE_ID)
        <where>
            b.XH in
            <foreach collection="xh" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
<!--            and c.PRE_TYPE=#{preType}-->
        </where>



    </select>

    <select id="getHisRecordByVisitNo" parameterType="String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_his_record
        where APP_ID = #{appId} and INS_CODE = #{insCode} and VISIT_NO = #{visitNo}
        order by TIMESTAMP desc
        limit 1
    </select>

    <select id="getSysDisease" parameterType="com.jiuzhekan.cbkj.beans.business.his.THisRecord" resultMap="MappingDiseaseMap">
        select * from b_mapping_disease where app_id = #{appId} and DIS_CODE_HIS = #{disCode} and DIS_NAME_HIS = #{disName}
    </select>
    <select id="getHisDisease" parameterType="com.jiuzhekan.cbkj.controller.business.treatment.vo.SDKQuery" resultMap="MappingDiseaseMap">
        select * from b_mapping_disease where app_id = #{appId} and DIS_ID_SYS = #{disId} limit 1
    </select>

    <select id="getSysSymptom" parameterType="com.jiuzhekan.cbkj.beans.business.his.THisRecord" resultMap="MappingSymptomMap">
        select * from b_mapping_symptom where app_id = #{appId} and SYM_CODE_HIS = #{symCode} and SYM_NAME_HIS = #{symName}
    </select>
    <select id="getHisSymptom" parameterType="com.jiuzhekan.cbkj.controller.business.treatment.vo.SDKQuery" resultMap="MappingSymptomMap">
        select * from b_mapping_symptom where app_id = #{appId} and SYM_ID_SYS = #{symId} limit 1
    </select>

</mapper>