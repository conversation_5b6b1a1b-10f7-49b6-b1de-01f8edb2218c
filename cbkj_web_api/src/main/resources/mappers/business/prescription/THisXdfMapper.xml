<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.THisXdfMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.THisXdf">
        <id column="XH" jdbcType="VARCHAR"  property="xh" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="SYFW" jdbcType="VARCHAR" property="syfw" />
    </resultMap>


    <sql id="Base_Column_List">
    XH,NAME,SYFW
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.business.prescription.THisXdf">
        delete from t_his_xdf where XH = #{ xh }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_his_xdf where XH in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>
    <delete id="deleteMapping" parameterType="com.jiuzhekan.cbkj.beans.business.prescription.THisXdfMapping">
        delete from t_his_xdf_mapping
        <where>
            <if test="xh != null and xh != ''">
                and XH =#{xh}
            </if>
            <if test="persPreId != null and persPreId != ''">
                and PERS_PRE_ID=#{persPreId}
            </if>
        </where>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.cbkj.beans.business.prescription.THisXdf">
        insert into t_his_xdf (<include refid="Base_Column_List" />) values
        (#{xh},#{name},#{syfw})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_his_xdf (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.xh},#{item.name},#{item.syfw})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.business.prescription.THisXdf">
        update t_his_xdf
        <set>
             <if test="name != null">
                NAME = #{ name },
             </if>
             <if test="syfw != null">
                SYFW = #{ syfw },
             </if>
        </set>
        where XH = #{ xh }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_his_xdf where XH = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.cbkj.beans.business.prescription.THisXdf" resultMap="BaseResultMap">
        SELECT XH,NAME,SYFW
        from t_his_xdf
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getList" resultMap="BaseResultMap" parameterType="com.jiuzhekan.cbkj.beans.business.prescription.THisXdfMapping">
        SELECT t2.XH,t2.NAME
        FROM t_his_xdf t2
        WHERE NOT EXISTS (
        SELECT t.xh FROM
( SELECT XH,PERS_PRE_ID FROM `t_his_xdf_mapping` )     t  WHERE t.xh=t2.xh
<if test="persPreId != null and persPreId !=''">
    AND t.PERS_PRE_ID != #{persPreId}
</if>
        )
        <if test="searchStr != null and searchStr != ''">
            and ( XH = #{searchStr} or NAME like CONCAT('%',trim(#{searchStr}),'%'))
        </if>
    </select>
    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.business.prescription.THisXdfResult2">
        <id column="xh" jdbcType="VARCHAR"  property="xh" />
        <result column="preName" jdbcType="VARCHAR" property="preName" />
        <result column="persPreId" jdbcType="VARCHAR" property="persPreId" />
    </resultMap>

    <select id="getOneByXH" resultMap="BaseResultMap2" parameterType="com.jiuzhekan.cbkj.beans.business.prescription.THisXdf">
        SELECT
        c.PRE_NAME AS preName,
        c.PERS_PRE_ID AS persPreId,
        b.xh AS xh
        FROM t_his_xdf AS a
        JOIN t_his_xdf_mapping AS b ON (a.XH=b.XH)
        JOIN t_personal_prescription AS c ON (c.PERS_PRE_ID=b.PERS_PRE_ID)
        <where>
            b.XH=#{XH}
        </where>
        limit 1
    </select>

</mapper>