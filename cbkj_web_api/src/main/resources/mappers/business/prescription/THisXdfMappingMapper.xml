<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.THisXdfMappingMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.THisXdfMapping">
        <id column="XH" jdbcType="VARCHAR"  property="xh" />
        <result column="PERS_PRE_ID" jdbcType="VARCHAR" property="persPreId" />
    </resultMap>


    <sql id="Base_Column_List">
    XH,PERS_PRE_ID
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.business.prescription.THisXdfMapping">
        delete from t_his_xdf_mapping where XH = #{ xh } and PERS_PRE_ID={persPreId}
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_his_xdf_mapping where PERS_PRE_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.cbkj.beans.business.prescription.THisXdfMapping">
        insert into t_his_xdf_mapping (<include refid="Base_Column_List" />) values
        (#{xh},#{persPreId})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_his_xdf_mapping (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.xh},#{item.persPreId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.business.prescription.THisXdfMapping">
        update t_his_xdf_mapping
        <set>
             <if test="persPreId != null">
                PERS_PRE_ID = #{ persPreId },
             </if>
        </set>
        where XH = #{ xh }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_his_xdf_mapping where XH = #{id} and PERS_PRE_ID={persPreId}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.cbkj.beans.business.prescription.THisXdfMapping" resultMap="BaseResultMap">
        SELECT XH,PERS_PRE_ID
        from t_his_xdf_mapping
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.business.prescription.THisXdfResult">
        <id column="xh" jdbcType="VARCHAR"  property="xh" />
        <result column="xhName" jdbcType="VARCHAR" property="xhName" />
    </resultMap>

    <select id="getByxhAndPresId" resultMap="BaseResultMap2" parameterType="com.jiuzhekan.cbkj.beans.business.prescription.THisXdfMapping">
        SELECT
        a.XH AS xh,
        b.NAME AS xhName
        FROM t_his_xdf_mapping AS a JOIN t_his_xdf AS b ON a.XH = b.XH
        WHERE a.PERS_PRE_ID= #{persPreId} LIMIT 1
    </select>

</mapper>