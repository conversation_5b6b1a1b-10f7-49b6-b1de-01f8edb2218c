<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.TPersonalPrescriptionDisMappingMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.TPersonalPrescriptionDisMapping">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="PERS_PRE_ID" jdbcType="VARCHAR" property="persPreId"/>
        <result column="DIS_ID" jdbcType="VARCHAR" property="disId"/>
        <result column="DIS_NAME" jdbcType="VARCHAR" property="disName"/>
        <result column="SYM_ID" jdbcType="VARCHAR" property="symId"/>
        <result column="SYM_NAME" jdbcType="VARCHAR" property="symName"/>
        <result column="THE_CODE" jdbcType="VARCHAR" property="theCode"/>
        <result column="THE_NAMES" jdbcType="VARCHAR" property="theNames"/>
    </resultMap>


    <sql id="Base_Column_List">

    id,PERS_PRE_ID,DIS_ID,DIS_NAME,SYM_ID,SYM_NAME,THE_CODE,THE_NAMES

    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPersonalPrescriptionDisMapping">

    delete from t_personal_prescription_dis_mapping where id = #{ id }

    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_personal_prescription_dis_mapping where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteBypresId" parameterType="String">
        delete from t_personal_prescription_dis_mapping where PERS_PRE_ID = #{persPreId}
    </delete>

    <insert id="insert" parameterType="TPersonalPrescriptionDisMapping">

    insert into t_personal_prescription_dis_mapping (PERS_PRE_ID,DIS_ID,DIS_NAME,SYM_ID,SYM_NAME,THE_CODE,THE_NAMES) values


    (#{persPreId},#{disId},#{disName},#{symId},#{symName},#{theCode},#{theNames})

    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_personal_prescription_dis_mapping (PERS_PRE_ID,DIS_ID,DIS_NAME,SYM_ID,SYM_NAME,THE_CODE,THE_NAMES)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.persPreId},#{item.disId},#{item.disName},#{item.symId},#{item.symName},#{item.theCode},#{item.theNames})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPersonalPrescriptionDisMapping">
        update t_personal_prescription_dis_mapping
        <set>
            <if test="persPreId != null">
                PERS_PRE_ID = #{ persPreId },
            </if>
            <if test="disId != null">
                DIS_ID = #{ disId },
            </if>
            <if test="disName != null">
                DIS_NAME = #{ disName },
            </if>
            <if test="symId != null">
                SYM_ID = #{ symId },
            </if>
            <if test="symName != null">
                SYM_NAME = #{ symName },
            </if>
            <if test="theCode != null">
                THE_CODE = #{ theCode },
            </if>
            <if test="theNames != null">
                THE_NAMES = #{ theNames },
            </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_personal_prescription_dis_mapping where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TPersonalPrescriptionDisMapping" resultMap="BaseResultMap">
        SELECT id,PERS_PRE_ID,DIS_ID,DIS_NAME,SYM_ID,SYM_NAME,THE_CODE,THE_NAMES
        from t_personal_prescription_dis_mapping
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getListByPersPreId"
            resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_personal_prescription_dis_mapping where PERS_PRE_ID = #{persPreId}
    </select>

</mapper>