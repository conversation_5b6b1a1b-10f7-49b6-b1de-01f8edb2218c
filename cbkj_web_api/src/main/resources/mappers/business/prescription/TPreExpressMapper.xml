<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.TPreExpressMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.TPreExpress">
        <id column="pre_id" jdbcType="VARCHAR"  property="preId" />
        <result column="dic_id" jdbcType="VARCHAR" property="dicId" />
        <result column="dic_code" jdbcType="VARCHAR" property="dicCode" />
        <result column="dic_name" jdbcType="VARCHAR" property="dicName" />
        <result column="charge_code" jdbcType="VARCHAR" property="chargeCode" />
        <result column="charge_name" jdbcType="VARCHAR" property="chargeName" />
        <result column="price" jdbcType="DECIMAL" property="price" />
    </resultMap>


    <sql id="Base_Column_List">
    pre_id,dic_id,dic_code,dic_name,charge_code,charge_name,price
    </sql>

    <delete id="physicalDeleteByPreId" parameterType="String">
        delete from t_pre_express where PRE_ID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TPreExpress">
        insert into t_pre_express (<include refid="Base_Column_List" />) values
        (#{preId},#{dicId},#{dicCode},#{dicName},#{chargeCode},#{chargeName},#{price})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_pre_express (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.preId},#{item.dicId},#{item.dicCode},#{item.dicName},#{item.chargeCode},#{item.chargeName},#{item.price})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPreExpress">
        update t_pre_express
        <set>
             <if test="dicId != null">
                dic_id = #{ dicId },
             </if>
             <if test="dicCode != null">
                dic_code = #{ dicCode },
             </if>
             <if test="dicName != null">
                dic_name = #{ dicName },
             </if>
             <if test="chargeCode != null">
                charge_code = #{ chargeCode },
             </if>
             <if test="chargeName != null">
                charge_name = #{ chargeName },
             </if>
             <if test="price != null">
                price = #{ price },
             </if>
        </set>
        where pre_id = #{ preId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_pre_express where pre_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TPreExpress" resultMap="BaseResultMap">
        SELECT pre_id,dic_id,dic_code,dic_name,charge_code,charge_name,price
        from t_pre_express
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>