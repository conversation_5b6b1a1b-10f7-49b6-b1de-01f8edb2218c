<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.TPrescriptionTechnicalAcuMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.TPrescriptionTechnicalAcu">
        <id column="technical_id" jdbcType="VARCHAR"  property="technicalId" />
        <result column="acu_id" jdbcType="VARCHAR" property="acuId" />
        <result column="acu_code" jdbcType="VARCHAR" property="acuCode" />
        <result column="acu_name" jdbcType="VARCHAR" property="acuName" />
        <result column="acu_num" jdbcType="VARCHAR" property="acuNum" />
        <result column="acu_seqn" jdbcType="SMALLINT" property="acuSeqn" />
    </resultMap>


    <sql id="Base_Column_List">
    technical_id,acu_id,acu_code,acu_name,acu_num,acu_seqn
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPrescriptionTechnicalAcu">
        delete from t_prescription_technical_acu where technical_id = #{ technicalId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_prescription_technical_acu where technical_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TPrescriptionTechnicalAcu">
        insert into t_prescription_technical_acu (<include refid="Base_Column_List" />) values
        (#{technicalId},#{acuId},#{acuCode},#{acuName},#{acuNum},#{acuSeqn})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_prescription_technical_acu (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.technicalId},#{item.acuId},#{item.acuCode},#{item.acuName},#{item.acuNum},#{item.acuSeqn})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPrescriptionTechnicalAcu">
        update t_prescription_technical_acu
        <set>
             <if test="acuId != null">
                acu_id = #{ acuId },
             </if>
             <if test="acuCode != null">
                acu_code = #{ acuCode },
             </if>
             <if test="acuName != null">
                acu_name = #{ acuName },
             </if>
             <if test="acuNum != null">
                acu_num = #{ acuNum },
             </if>
             <if test="acuSeqn != null">
                acu_seqn = #{ acuSeqn },
             </if>
        </set>
        where technical_id = #{ technicalId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_prescription_technical_acu where technical_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TPrescriptionTechnicalAcu" resultMap="BaseResultMap">
        SELECT technical_id,acu_id,acu_code,acu_name,acu_num,acu_seqn
        from t_prescription_technical_acu
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>