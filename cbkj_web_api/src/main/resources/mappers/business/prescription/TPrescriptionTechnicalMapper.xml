<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.TPrescriptionTechnicalMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.TPrescriptionTechnical">
        <id column="technical_id" jdbcType="VARCHAR"  property="technicalId" />
        <result column="pre_id" jdbcType="VARCHAR" property="preId" />
        <result column="project_acu_num" jdbcType="SMALLINT" property="projectAcuNum" />
        <result column="project_day_num" jdbcType="SMALLINT" property="projectDayNum" />
        <result column="project_type_id" jdbcType="VARCHAR" property="projectTypeId" />
        <result column="project_type_name" jdbcType="VARCHAR" property="projectTypeName" />
        <result column="project_free_id" jdbcType="VARCHAR" property="projectFreeId" />
        <result column="project_free_name" jdbcType="VARCHAR" property="projectFreeName" />
        <result column="project_free_code" jdbcType="VARCHAR" property="projectFreeCode" />
        <result column="project_fee_price" jdbcType="DECIMAL" property="projectFeePrice" />
        <result column="project_fee_unit" jdbcType="VARCHAR" property="projectFeeUnit" />
        <result column="fedical_insurance" jdbcType="VARCHAR" property="fedicalInsurance" />
        <result column="project_connotation" jdbcType="VARCHAR" property="projectConnotation" />
        <result column="limit_payment" jdbcType="VARCHAR" property="limitPayment" />
        <result column="is_acupoint" jdbcType="VARCHAR" property="isAcupoint" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="acu_senq" jdbcType="INTEGER" property="acuSenq" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>


    <sql id="Base_Column_List">
    technical_id,pre_id,project_acu_num,project_day_num,project_type_id,project_type_name,project_free_id,project_free_name,project_free_code,project_fee_price,project_fee_unit,fedical_insurance,project_connotation,limit_payment,is_acupoint,remark,acu_senq,create_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPrescriptionTechnical">
        delete from t_prescription_technical where technical_id = #{ technicalId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_prescription_technical where technical_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TPrescriptionTechnical">
        insert into t_prescription_technical (<include refid="Base_Column_List" />) values
        (#{technicalId},#{preId},#{projectAcuNum},#{projectDayNum},#{projectTypeId},#{projectTypeName},#{projectFreeId},#{projectFreeName},#{projectFreeCode},#{projectFeePrice},#{projectFeeUnit},#{fedicalInsurance},#{projectConnotation},#{limitPayment},#{isAcupoint},#{remark},#{acuSenq},#{createTime})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_prescription_technical (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.technicalId},#{item.preId},#{item.projectAcuNum},#{item.projectDayNum},#{item.projectTypeId},#{item.projectTypeName},#{item.projectFreeId},#{item.projectFreeName},#{item.projectFreeCode},#{item.projectFeePrice},#{item.projectFeeUnit},#{item.fedicalInsurance},#{item.projectConnotation},#{item.limitPayment},#{item.isAcupoint},#{item.remark},#{item.acuSenq},#{item.createTime})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPrescriptionTechnical">
        update t_prescription_technical
        <set>
             <if test="preId != null">
                pre_id = #{ preId },
             </if>
             <if test="projectAcuNum != null">
                project_acu_num = #{ projectAcuNum },
             </if>
             <if test="projectDayNum != null">
                project_day_num = #{ projectDayNum },
             </if>
             <if test="projectTypeId != null">
                project_type_id = #{ projectTypeId },
             </if>
             <if test="projectTypeName != null">
                project_type_name = #{ projectTypeName },
             </if>
             <if test="projectFreeId != null">
                project_free_id = #{ projectFreeId },
             </if>
             <if test="projectFreeName != null">
                project_free_name = #{ projectFreeName },
             </if>
             <if test="projectFreeCode != null">
                project_free_code = #{ projectFreeCode },
             </if>
             <if test="projectFeePrice != null">
                project_fee_price = #{ projectFeePrice },
             </if>
             <if test="projectFeeUnit != null">
                project_fee_unit = #{ projectFeeUnit },
             </if>
             <if test="fedicalInsurance != null">
                fedical_insurance = #{ fedicalInsurance },
             </if>
             <if test="projectConnotation != null">
                project_connotation = #{ projectConnotation },
             </if>
             <if test="limitPayment != null">
                limit_payment = #{ limitPayment },
             </if>
             <if test="isAcupoint != null">
                is_acupoint = #{ isAcupoint },
             </if>
             <if test="remark != null">
                remark = #{ remark },
             </if>
             <if test="acuSenq != null">
                acu_senq = #{ acuSenq },
             </if>
             <if test="createTime != null">
                create_time = #{ createTime },
             </if>
        </set>
        where technical_id = #{ technicalId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_prescription_technical where technical_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TPrescriptionTechnical" resultMap="BaseResultMap">
        SELECT technical_id,pre_id,project_acu_num,project_day_num,project_type_id,project_type_name,project_free_id,project_free_name,project_free_code,project_fee_price,project_fee_unit,fedical_insurance,project_connotation,limit_payment,is_acupoint,remark,acu_senq,create_time
        from t_prescription_technical
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>