<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.TRecord2AskMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.TRecord2Ask">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="rec_id" jdbcType="VARCHAR" property="recId" />
        <result column="tag_no" jdbcType="VARCHAR" property="tagNo" />
        <result column="wzysykwdm" jdbcType="VARCHAR" property="wzysykwdm" />
        <result column="wzysykwqt" jdbcType="VARCHAR" property="wzysykwqt" />
        <result column="wzysykw" jdbcType="VARCHAR" property="wzysykw" />
        <result column="wzsmdm" jdbcType="VARCHAR" property="wzsmdm" />
        <result column="wzsmqt" jdbcType="VARCHAR" property="wzsmqt" />
        <result column="wzsm" jdbcType="VARCHAR" property="wzsm" />
        <result column="wzdbdm" jdbcType="VARCHAR" property="wzdbdm" />
        <result column="wzdbqt" jdbcType="VARCHAR" property="wzdbqt" />
        <result column="wzdb" jdbcType="VARCHAR" property="wzdb" />
        <result column="wzhr" jdbcType="VARCHAR" property="wzhr" />
        <result column="wzhrqt" jdbcType="VARCHAR" property="wzhrqt" />
        <result column="wzch" jdbcType="VARCHAR" property="wzch" />
        <result column="wzchqt" jdbcType="VARCHAR" property="wzchqt" />
        <result column="wzts" jdbcType="VARCHAR" property="wzts" />
        <result column="wztsqt" jdbcType="VARCHAR" property="wztsqt" />
        <result column="wzxxwf" jdbcType="VARCHAR" property="wzxxwf" />
        <result column="wzxxwfqt" jdbcType="VARCHAR" property="wzxxwfqt" />
        <result column="wzem" jdbcType="VARCHAR" property="wzem" />
        <result column="wzemqt" jdbcType="VARCHAR" property="wzemqt" />
        <result column="wzxb" jdbcType="VARCHAR" property="wzxb" />
        <result column="wzxbqt" jdbcType="VARCHAR" property="wzxbqt" />
        <result column="wzfn" jdbcType="VARCHAR" property="wzfn" />
        <result column="wzfnqt" jdbcType="VARCHAR" property="wzfnqt" />
        <result column="wzxe" jdbcType="VARCHAR" property="wzxe" />
        <result column="wzxeqt" jdbcType="VARCHAR" property="wzxeqt" />
        <result column="wzqt3" jdbcType="VARCHAR" property="wzqt3" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />

        <result column="wzhrdm" jdbcType="VARCHAR" property="wzhrdm" />
        <result column="wzchdm" jdbcType="VARCHAR" property="wzchdm" />
        <result column="wztsdm" jdbcType="VARCHAR" property="wztsdm" />
        <result column="wzxxwfdm" jdbcType="VARCHAR" property="wzxxwfdm" />
        <result column="wzemdm" jdbcType="VARCHAR" property="wzemdm" />
        <result column="wzxbdm" jdbcType="VARCHAR" property="wzxbdm" />
        <result column="wzfndm" jdbcType="VARCHAR" property="wzfndm" />
        <result column="wzxedm" jdbcType="VARCHAR" property="wzxedm" />
        <result column="equipment_patient_id" jdbcType="INTEGER" property="equipmentPatientId" />
    </resultMap>


    <sql id="Base_Column_List">
    id,rec_id,tag_no,wzysykwdm,wzysykwqt,wzysykw,wzsmdm,wzsmqt,wzsm,wzdbdm,wzdbqt,wzdb,
        wzhr,wzhrqt,wzch,wzchqt,wzts,wztsqt,wzxxwf,wzxxwfqt,wzem,wzemqt,wzxb,wzxbqt,
        wzfn,wzfnqt,wzxe,wzxeqt,wzqt3,status,create_date,create_user,update_date,
        update_user,
        wzhrdm ,wzchdm,wztsdm,wzxxwfdm,wzemdm,wzxbdm,wzfndm,wzxedm,equipment_patient_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRecord2Ask">
        delete from t_record2_ask where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record2_ask where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>
    <delete id="deleteByTagNolist" parameterType="ArrayList">
    delete from t_record2_ask where tag_no in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </delete>

    <insert id="insert"  parameterType="TRecord2Ask">
        replace into t_record2_ask (<include refid="Base_Column_List" />) values
        (#{id},#{recId},#{tagNo},#{wzysykwdm},#{wzysykwqt},#{wzysykw},#{wzsmdm},#{wzsmqt},#{wzsm},#{wzdbdm},#{wzdbqt},
         #{wzdb},#{wzhr},#{wzhrqt},#{wzch},#{wzchqt},#{wzts},#{wztsqt},#{wzxxwf},#{wzxxwfqt},#{wzem},#{wzemqt},#{wzxb},
         #{wzxbqt},#{wzfn},#{wzfnqt},#{wzxe},#{wzxeqt},#{wzqt3},#{status},#{createDate},#{createUser},#{updateDate},
         #{updateUser},
        #{wzhrdm},#{wzchdm},#{wztsdm},#{wzxxwfdm},#{wzemdm},#{wzxbdm},#{wzfndm},#{wzxedm},#{equipmentPatientId}
         )
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_record2_ask (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.recId},#{item.tagNo},#{item.wzysykwdm},#{item.wzysykwqt},#{item.wzysykw},#{item.wzsmdm},#{item.wzsmqt},#{item.wzsm},#{item.wzdbdm},#{item.wzdbqt},#{item.wzdb},#{item.wzhr},#{item.wzhrqt},#{item.wzch},#{item.wzchqt},#{item.wzts},#{item.wztsqt},#{item.wzxxwf},#{item.wzxxwfqt},#{item.wzem},#{item.wzemqt},#{item.wzxb},#{item.wzxbqt},#{item.wzfn},#{item.wzfnqt},#{item.wzxe},#{item.wzxeqt},#{item.wzqt3},#{item.status},#{item.createDate},#{item.createUser},#{item.updateDate},#{item.updateUser},
            #{item.wzhrdm},#{item.wzchdm},#{item.wztsdm},#{item.wzxxwfdm},#{item.wzemdm},#{item.wzxbdm},#{item.wzfndm},#{item.wzxedm},#{item.equipmentPatientId}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRecord2Ask">
        update t_record2_ask
        <set>
             <if test="recId != null">
                rec_id = #{ recId },
             </if>
             <if test="tagNo != null">
                tag_no = #{ tagNo },
             </if>
             <if test="wzysykwdm != null">
                wzysykwdm = #{ wzysykwdm },
             </if>
             <if test="wzysykwqt != null">
                wzysykwqt = #{ wzysykwqt },
             </if>
             <if test="wzysykw != null">
                wzysykw = #{ wzysykw },
             </if>
             <if test="wzsmdm != null">
                wzsmdm = #{ wzsmdm },
             </if>
             <if test="wzsmqt != null">
                wzsmqt = #{ wzsmqt },
             </if>
             <if test="wzsm != null">
                wzsm = #{ wzsm },
             </if>
             <if test="wzdbdm != null">
                wzdbdm = #{ wzdbdm },
             </if>
             <if test="wzdbqt != null">
                wzdbqt = #{ wzdbqt },
             </if>
             <if test="wzdb != null">
                wzdb = #{ wzdb },
             </if>
             <if test="wzhr != null">
                wzhr = #{ wzhr },
             </if>
             <if test="wzhrqt != null">
                wzhrqt = #{ wzhrqt },
             </if>
             <if test="wzch != null">
                wzch = #{ wzch },
             </if>
             <if test="wzchqt != null">
                wzchqt = #{ wzchqt },
             </if>
             <if test="wzts != null">
                wzts = #{ wzts },
             </if>
             <if test="wztsqt != null">
                wztsqt = #{ wztsqt },
             </if>
             <if test="wzxxwf != null">
                wzxxwf = #{ wzxxwf },
             </if>
             <if test="wzxxwfqt != null">
                wzxxwfqt = #{ wzxxwfqt },
             </if>
             <if test="wzem != null">
                wzem = #{ wzem },
             </if>
             <if test="wzemqt != null">
                wzemqt = #{ wzemqt },
             </if>
             <if test="wzxb != null">
                wzxb = #{ wzxb },
             </if>
             <if test="wzxbqt != null">
                wzxbqt = #{ wzxbqt },
             </if>
             <if test="wzfn != null">
                wzfn = #{ wzfn },
             </if>
             <if test="wzfnqt != null">
                wzfnqt = #{ wzfnqt },
             </if>
             <if test="wzxe != null">
                wzxe = #{ wzxe },
             </if>
             <if test="wzxeqt != null">
                wzxeqt = #{ wzxeqt },
             </if>
             <if test="wzqt3 != null">
                wzqt3 = #{ wzqt3 },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="updateDate != null">
                update_date = #{ updateDate },
             </if>
             <if test="updateUser != null">
                update_user = #{ updateUser },
            </if>
            <if test="wzhrdm != null">
                wzhrdm = #{wzhrdm},
             </if>
            <if test="wzchdm != null">
                wzchdm = #{wzchdm},
            </if>
            <if test="wztsdm != null">
                wztsdm = #{wztsdm},
            </if>
            <if test="wzxxwfdm != null">
                wzxxwfdm = #{wzxxwfdm},
            </if>
            <if test="wzemdm != null">
                wzemdm = #{wzemdm},
            </if>
            <if test="wzxbdm != null">
                wzxbdm = #{wzxbdm},
            </if>
            <if test="wzfndm != null">
                wzfndm = #{wzfndm},
            </if>
            <if test="wzxedm != null">
                wzxedm = #{wzxedm},
            </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record2_ask where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TRecord2Ask" resultMap="BaseResultMap">
        SELECT id,rec_id,tag_no,wzysykwdm,wzysykwqt,wzysykw,wzsmdm,wzsmqt,wzsm,wzdbdm,wzdbqt,wzdb,wzhr,wzhrqt,wzch,wzchqt,wzts,wztsqt,wzxxwf,wzxxwfqt,wzem,wzemqt,wzxb,wzxbqt,wzfn,wzfnqt,wzxe,wzxeqt,wzqt3,status,create_date,create_user,update_date,update_user,wzchdm,wztsdm,wzxxwfdm,wzemdm,wzxbdm,wzfndm,wzxedm
        from t_record2_ask
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>


    <select id="findByRecId" parameterType="String" resultMap="BaseResultMap">
        SELECT
        id,rec_id,wzysykwdm,wzysykwqt,wzysykw,wzsmdm,wzsmqt,wzsm,wzdbdm,wzdbqt,wzdb,wzhr,wzhrqt,wzch,wzchqt,wzts,wztsqt,wzxxwf,wzxxwfqt,wzem,wzemqt,wzxb,wzxbqt,wzfn,wzfnqt,wzxe,wzxeqt,wzqt3,status,create_date,create_user,update_date,update_user,wzhrdm,wzchdm,wztsdm,wzxxwfdm,wzemdm,wzxbdm,wzfndm,wzxedm
        from t_record2_ask
        where status='0'
        <!--条件-->
        <if test="recId != null and recId!='' ">
            and rec_id = #{recId}
        </if>
    </select>
    <select id="findByEquipmentPatientId" parameterType="Integer" resultMap="BaseResultMap">
        SELECT
        id,rec_id,wzysykwdm,wzysykwqt,wzysykw,wzsmdm,wzsmqt,wzsm,wzdbdm,wzdbqt,wzdb,wzhr,wzhrqt,wzch,wzchqt,wzts,wztsqt,wzxxwf,wzxxwfqt,wzem,wzemqt,wzxb,wzxbqt,wzfn,wzfnqt,wzxe,wzxeqt,wzqt3,status,create_date,create_user,update_date,update_user,wzhrdm,wzchdm,wztsdm,wzxxwfdm,wzemdm,wzxbdm,wzfndm,wzxedm
        from t_record2_ask
        where status='0'
        <!--条件-->
        <if test="equipmentPatientId != null and equipmentPatientId!='' ">
            and equipment_patient_id = #{equipmentPatientId}
        </if>
    </select>

    <select id="findByTagNo" parameterType="String" resultMap="BaseResultMap">
        SELECT
        id,tag_no,wzysykwdm,wzysykwqt,wzysykw,wzsmdm,wzsmqt,wzsm,wzdbdm,wzdbqt,wzdb,wzhr,wzhrqt,wzch,wzchqt,wzts,wztsqt,wzxxwf,wzxxwfqt,wzem,wzemqt,wzxb,wzxbqt,wzfn,wzfnqt,wzxe,wzxeqt,wzqt3,status,create_date,create_user,update_date,update_user,wzhrdm ,wzchdm,wztsdm,wzxxwfdm,wzemdm,wzxbdm,wzfndm,wzxedm
        from t_record2_ask
        where status='0'
        <!--条件-->
        <if test="tagNo != null and tagNo!='' ">
            and tag_no = #{tagNo}
        </if>
    </select>

</mapper>