<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.TRecord2CutMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.TRecord2Cut">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="rec_id" jdbcType="VARCHAR" property="recId" />
        <result column="tag_no" jdbcType="VARCHAR" property="tagNo" />
        <result column="qzmz" jdbcType="VARCHAR" property="qzmz" />
        <result column="qzmzqt" jdbcType="VARCHAR" property="qzmzqt" />
        <result column="qzmzdm" jdbcType="VARCHAR" property="qzmzdm" />
        <result column="qzaz" jdbcType="VARCHAR" property="qzaz" />
        <result column="qzazdm" jdbcType="VARCHAR" property="qzazdm" />
        <result column="qzazqt" jdbcType="VARCHAR" property="qzazqt" />
        <result column="qzqt" jdbcType="VARCHAR" property="qzqt" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="equipment_patient_id" jdbcType="INTEGER" property="equipmentPatientId" />
    </resultMap>


    <sql id="Base_Column_List">
    id,rec_id,tag_no,qzmz,qzmzqt,qzmzdm,qzaz,qzazqt,qzqt,status,create_date,create_user,update_date,update_user,qzazdm,equipment_patient_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRecord2Cut">
        delete from t_record2_cut where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record2_cut where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <delete id="deleteByTagNolist" parameterType="ArrayList">
        delete from t_record2_cut where tag_no in
            <foreach collection="array" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </delete>

    <insert id="insert"  parameterType="TRecord2Cut">
        replace into t_record2_cut (<include refid="Base_Column_List" />) values
        (#{id},#{recId},#{tagNo},#{qzmz},#{qzmzqt},#{qzmzdm},#{qzaz},#{qzazqt},#{qzqt},#{status},#{createDate},#{createUser},#{updateDate},#{updateUser},#{qzazdm},#{equipmentPatientId})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_record2_cut (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.recId},#{item.tagNo},#{item.qzmz},#{item.qzmzqt},#{item.qzmzdm},#{item.qzaz},#{item.qzazqt},#{item.qzqt},#{item.status},#{item.createDate},
            #{item.createUser},#{item.updateDate},#{item.updateUser},#{item.qzazdm},#{item.equipmentPatientId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRecord2Cut">
        update t_record2_cut
        <set>
             <if test="recId != null">
                rec_id = #{ recId },
             </if>
             <if test="tagNo != null">
                tag_no = #{ tagNo },
             </if>
             <if test="qzmz != null">
                qzmz = #{ qzmz },
             </if>
             <if test="qzmzqt != null">
                qzmzqt = #{ qzmzqt },
             </if>
             <if test="qzmzdm != null">
                qzmzdm = #{ qzmzdm },
             </if>
             <if test="qzaz != null">
                qzaz = #{ qzaz },
             </if>
             <if test="qzazqt != null">
                qzazqt = #{ qzazqt },
             </if>
             <if test="qzqt != null">
                qzqt = #{ qzqt },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="updateDate != null">
                update_date = #{ updateDate },
             </if>
             <if test="updateUser != null">
                update_user = #{ updateUser },
            </if>
            <if test="qzazdm != null">
                qzazdm = #{qzazdm},
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record2_cut where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TRecord2Cut" resultMap="BaseResultMap">
        SELECT id,rec_id,tag_no,qzmz,qzmzqt,qzmzdm,qzaz,qzazqt,qzqt,status,create_date,create_user,update_date,update_user,qzazdm
        from t_record2_cut
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>


    <select id="findByRecId" parameterType="String" resultMap="BaseResultMap">
        SELECT id,rec_id,qzmz,qzmzqt,qzmzdm,qzaz,qzazqt,qzqt,status,create_date,create_user,update_date,update_user,qzazdm
        from t_record2_cut
        where status='0'
        <!--条件-->
        <if test="recId != null and recId!='' ">
            and rec_id = #{recId}
        </if>
    </select>
    <select id="findByEquipmentPatientId" parameterType="Integer" resultMap="BaseResultMap">
        SELECT id,rec_id,qzmz,qzmzqt,qzmzdm,qzaz,qzazqt,qzqt,status,create_date,create_user,update_date,update_user,qzazdm
        from t_record2_cut
        where status='0'
        <!--条件-->
        <if test="equipmentPatientId != null and equipmentPatientId!='' ">
            and equipment_patient_id = #{equipmentPatientId}
        </if>
    </select>

    <select id="findByTagNo" parameterType="String" resultMap="BaseResultMap">
        SELECT id,tag_no,qzmz,qzmzqt,qzmzdm,qzaz,qzazqt,qzqt,status,create_date,create_user,update_date,update_user,qzazdm
        from t_record2_cut
        where status='0'
        <!--条件-->
        <if test="tagNo != null and tagNo!='' ">
            and tag_no = #{tagNo}
        </if>
    </select>
</mapper>