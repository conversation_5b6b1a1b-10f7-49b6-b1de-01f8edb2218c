<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.TRecord2DicMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.TRecord2Dic">
        <id column="dic_id" jdbcType="BIGINT"  property="dicId" />
        <result column="dic_code" jdbcType="VARCHAR" property="dicCode" />
        <result column="dic_name" jdbcType="VARCHAR" property="dicName" />
        <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
        <result column="mutex_str" jdbcType="VARCHAR" property="mutexStr" />
        <result column="is_must" jdbcType="VARCHAR" property="isMust" />
        <result column="dic_desc" jdbcType="VARCHAR" property="dicDesc" />
        <result column="sort" jdbcType="VARCHAR" property="sort" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="status" jdbcType="VARCHAR" property="status" />
    </resultMap>


    <sql id="Base_Column_List">
    dic_id,dic_code,dic_name,parent_id,mutex_str,is_must,dic_desc,sort,type,create_date,create_user,create_user_name,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRecord2Dic">
        delete from t_record2_dic where dic_id = #{ dicId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record2_dic where dic_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TRecord2Dic">
        insert into t_record2_dic (<include refid="Base_Column_List" />) values
        (#{dicId},#{dicCode},#{dicName},#{parentId},#{mutexStr},#{isMust},#{dicDesc},#{sort},#{type},#{createDate},#{createUser},#{createUserName},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_record2_dic (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.dicId},#{item.dicCode},#{item.dicName},#{item.parentId},#{item.mutexStr},#{item.isMust},#{item.dicDesc},#{item.sort},#{item.type},#{item.createDate},#{item.createUser},#{item.createUserName},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRecord2Dic">
        update t_record2_dic
        <set>
             <if test="dicCode != null">
                dic_code = #{ dicCode },
             </if>
             <if test="dicName != null">
                dic_name = #{ dicName },
             </if>
             <if test="parentId != null">
                parent_id = #{ parentId },
             </if>
             <if test="mutexStr != null">
                mutex_str = #{ mutexStr },
             </if>
             <if test="isMust != null">
                is_must = #{ isMust },
             </if>
             <if test="dicDesc != null">
                dic_desc = #{ dicDesc },
             </if>
             <if test="sort != null">
                sort = #{ sort },
             </if>
             <if test="type != null">
                type = #{ type },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
        </set>
        where dic_id = #{ dicId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record2_dic where dic_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TRecord2Dic" resultMap="BaseResultMap">
        SELECT dic_id,dic_code,dic_name,parent_id,mutex_str,is_must,dic_desc,sort,type,create_date,create_user,create_user_name,status
        from t_record2_dic
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>