<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.TRecord2LookMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.TRecord2Look">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="rec_id" jdbcType="VARCHAR" property="recId" />
        <result column="tag_no" jdbcType="VARCHAR" property="tagNo" />
        <result column="wzsdm" jdbcType="VARCHAR" property="wzsdm" />
        <result column="wzsqt" jdbcType="VARCHAR" property="wzsqt" />
        <result column="wzs" jdbcType="VARCHAR" property="wzs" />
        <result column="wztdm" jdbcType="VARCHAR" property="wztdm" />
        <result column="wztqt" jdbcType="VARCHAR" property="wztqt" />
        <result column="wzt" jdbcType="VARCHAR" property="wzt" />
        <result column="wzws" jdbcType="VARCHAR" property="wzws" />
        <result column="wzwsqt" jdbcType="VARCHAR" property="wzwsqt" />
        <result column="wzwms" jdbcType="VARCHAR" property="wzwms" />
        <result column="wzwmsqt" jdbcType="VARCHAR" property="wzwmsqt" />
        <result column="wzwxt" jdbcType="VARCHAR" property="wzwxt" />
        <result column="wzwxtqt" jdbcType="VARCHAR" property="wzwxtqt" />
        <result column="wzwtlwgjq" jdbcType="VARCHAR" property="wzwtlwgjq" />
        <result column="wzwtlwgjqqt" jdbcType="VARCHAR" property="wzwtlwgjqqt" />
        <result column="wzwpf" jdbcType="VARCHAR" property="wzwpf" />
        <result column="wzwpfqt" jdbcType="VARCHAR" property="wzwpfqt" />
        <result column="wzwlm" jdbcType="VARCHAR" property="wzwlm" />
        <result column="wzwlmqt" jdbcType="VARCHAR" property="wzwlmqt" />
        <result column="wzwpxwyfmw" jdbcType="VARCHAR" property="wzwpxwyfmw" />
        <result column="wzwpxwyfmwqt" jdbcType="VARCHAR" property="wzwpxwyfmwqt" />
        <result column="wzqt1" jdbcType="VARCHAR" property="wzqt1" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="wzwxexf" jdbcType="VARCHAR" property="wzwxexf" />
        <result column="wzwxexfdm" jdbcType="VARCHAR" property="wzwxexfdm" />

        <result column="wzwxexfqt" jdbcType="VARCHAR" property="wzwxexfqt" />
        <result column="wzwpxwyfmwdm" jdbcType="VARCHAR" property="wzwpxwyfmwdm" />
        <result column="wzwlmdm" jdbcType="VARCHAR" property="wzwlmdm" />
        <result column="wzwpfdm" jdbcType="VARCHAR" property="wzwpfdm" />
        <result column="wzwtlwgjqdm" jdbcType="VARCHAR" property="wzwtlwgjqdm" />
        <result column="wzwxtdm" jdbcType="VARCHAR" property="wzwxtdm" />
        <result column="wzwmsdm" jdbcType="VARCHAR" property="wzwmsdm" />
        <result column="wzwsdm" jdbcType="VARCHAR" property="wzwsdm" />
        <result column="equipment_patient_id" jdbcType="INTEGER" property="equipmentPatientId" />
    </resultMap>


    <sql id="Base_Column_List">
    id,rec_id,tag_no,wzsdm,wzsqt,wzs,wztdm,wztqt,wzt,wzws,wzwsqt,wzwms,wzwmsqt,
        wzwxt,wzwxtqt,wzwtlwgjq,wzwtlwgjqqt,wzwpf,wzwpfqt,wzwlm,wzwlmqt,wzwpxwyfmw,
        wzwpxwyfmwqt,wzqt1,status,create_date,create_user,update_date,update_user,
        wzwxexf,wzwxexfdm,
        wzwsdm,wzwmsdm,wzwxtdm,wzwtlwgjqdm,wzwpfdm,wzwlmdm,wzwpxwyfmwdm,wzwxexfqt,equipment_patient_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRecord2Look">
        delete from t_record2_look where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record2_look where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>
    <delete id="deleteByTagNolist" parameterType="ArrayList">
        delete from t_record2_look where tag_no in
             <foreach collection="array" item="item" open="(" separator="," close=")">
                 #{item}
             </foreach>
    </delete>

    <insert id="insert"  parameterType="TRecord2Look">
        replace into t_record2_look (<include refid="Base_Column_List" />) values
        (#{id},#{recId},#{tagNo},#{wzsdm},#{wzsqt},#{wzs},#{wztdm},#{wztqt},#{wzt},#{wzws},#{wzwsqt},
         #{wzwms},#{wzwmsqt},#{wzwxt},#{wzwxtqt},#{wzwtlwgjq},#{wzwtlwgjqqt},#{wzwpf},#{wzwpfqt},#{wzwlm},#{wzwlmqt},#{wzwpxwyfmw},
         #{wzwpxwyfmwqt},#{wzqt1},#{status},#{createDate},
         #{createUser},#{updateDate},#{updateUser},#{wzwxexf},#{wzwxexfdm},
        #{wzwsdm},#{wzwmsdm},#{wzwxtdm},#{wzwtlwgjqdm},#{wzwpfdm},#{wzwlmdm},
        #{wzwpxwyfmwdm},#{wzwxexfqt},#{equipmentPatientId}
         )
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_record2_look (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.recId},#{item.tagNo},#{item.wzsdm},#{item.wzsqt},#{item.wzs},#{item.wztdm},#{item.wztqt},#{item.wzt},
             #{item.wzws},#{item.wzwsqt},#{item.wzwms},#{item.wzwmsqt},#{item.wzwxt},#{item.wzwxtqt},#{item.wzwtlwgjq},#{item.wzwtlwgjqqt},
             #{item.wzwpf},#{item.wzwpfqt},#{item.wzwlm},#{item.wzwlmqt},#{item.wzwpxwyfmw},#{item.wzwpxwyfmwqt},#{item.wzqt1},#{item.status},
             #{item.createDate},#{item.createUser},#{item.updateDate},#{item.updateUser},#{item.wzwxexf},#{item.wzwxexfdm},
            #{item.wzwsdm},#{item.wzwmsdm},#{item.wzwxtdm},#{item.wzwtlwgjqdm},#{item.wzwpfdm},#{item.wzwlmdm},
            #{item.wzwpxwyfmwdm},#{item.wzwxexfqt},#{item.equipmentPatientId}
             )
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRecord2Look">
        update t_record2_look
        <set>
             <if test="recId != null">
                rec_id = #{ recId },
             </if>
             <if test="tagNo != null">
                tag_no = #{ tagNo },
             </if>
             <if test="wzsdm != null">
                wzsdm = #{ wzsdm },
             </if>
             <if test="wzsqt != null">
                wzsqt = #{ wzsqt },
             </if>
             <if test="wzs != null">
                wzs = #{ wzs },
             </if>
             <if test="wztdm != null">
                wztdm = #{ wztdm },
             </if>
             <if test="wztqt != null">
                wztqt = #{ wztqt },
             </if>
             <if test="wzt != null">
                wzt = #{ wzt },
             </if>
             <if test="wzws != null">
                wzws = #{ wzws },
             </if>
             <if test="wzwsqt != null">
                wzwsqt = #{ wzwsqt },
             </if>
             <if test="wzwms != null">
                wzwms = #{ wzwms },
             </if>
             <if test="wzwmsqt != null">
                wzwmsqt = #{ wzwmsqt },
             </if>
             <if test="wzwxt != null">
                wzwxt = #{ wzwxt },
             </if>
             <if test="wzwxtqt != null">
                wzwxtqt = #{ wzwxtqt },
             </if>
             <if test="wzwtlwgjq != null">
                wzwtlwgjq = #{ wzwtlwgjq },
             </if>
             <if test="wzwtlwgjqqt != null">
                wzwtlwgjqqt = #{ wzwtlwgjqqt },
             </if>
             <if test="wzwpf != null">
                wzwpf = #{ wzwpf },
             </if>
             <if test="wzwpfqt != null">
                wzwpfqt = #{ wzwpfqt },
             </if>
             <if test="wzwlm != null">
                wzwlm = #{ wzwlm },
             </if>
             <if test="wzwlmqt != null">
                wzwlmqt = #{ wzwlmqt },
             </if>
             <if test="wzwpxwyfmw != null">
                wzwpxwyfmw = #{ wzwpxwyfmw },
             </if>
             <if test="wzwpxwyfmwqt != null">
                wzwpxwyfmwqt = #{ wzwpxwyfmwqt },
             </if>
             <if test="wzqt1 != null">
                wzqt1 = #{ wzqt1 },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="updateDate != null">
                update_date = #{ updateDate },
             </if>
             <if test="updateUser != null">
                update_user = #{ updateUser },
             </if>
            <if test="wzwxexf != null">
                wzwxexf = #{wzwxexf},
             </if>
            <if test="wzwxexfdm != null">
                wzwxexfdm = #{wzwxexfdm},
             </if>

            <if test="wzwsdm != null">
                wzwsdm = #{wzwsdm},
            </if>
            <if test="wzwmsdm != null">
                wzwmsdm = #{wzwmsdm},
            </if>
            <if test="wzwxtdm != null">
                wzwxtdm = #{wzwxtdm},
            </if>
            <if test="wzwtlwgjqdm != null">
                wzwtlwgjqdm = #{wzwtlwgjqdm},
            </if>
            <if test="wzwpfdm != null">
                wzwpfdm = #{wzwpfdm},
            </if>
            <if test="wzwlmdm != null">
                wzwlmdm = #{wzwlmdm},
            </if>
            <if test="wzwpxwyfmwdm != null">
                wzwpxwyfmwdm = #{wzwpxwyfmwdm},
            </if>
            <if test="wzwxexfqt != null">
                wzwxexfqt = #{wzwxexfqt},
            </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record2_look where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TRecord2Look" resultMap="BaseResultMap">
        SELECT id,rec_id,tag_no,wzsdm,wzsqt,wzs,wztdm,wztqt,wzt,wzws,wzwsqt,wzwms,wzwmsqt,wzwxt,wzwxtqt,wzwtlwgjq,wzwtlwgjqqt,wzwpf,wzwpfqt,wzwlm,wzwlmqt,wzwpxwyfmw,wzwpxwyfmwqt,wzqt1,status,create_date,create_user,update_date,update_user,wzwxexf,wzwxexfdm, wzwsdm,wzwmsdm,wzwxtdm,wzwtlwgjqdm,wzwpfdm,wzwlmdm,wzwpxwyfmwdm,wzwxexfqt
        from t_record2_look
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>



    <select id="findByRecId" parameterType="String" resultMap="BaseResultMap">
        SELECT id,rec_id,wzsdm,wzsqt,wzs,wztdm,wztqt,wzt,wzws,wzwsqt,wzwms,wzwmsqt,wzwxt,wzwxtqt,wzwtlwgjq,wzwtlwgjqqt,wzwpf,wzwpfqt,wzwlm,wzwlmqt,wzwpxwyfmw,wzwpxwyfmwqt,wzqt1,status,create_date,create_user,update_date,update_user,wzwxexf,wzwxexfdm,wzwsdm,wzwmsdm,wzwxtdm,wzwtlwgjqdm,wzwpfdm,wzwlmdm,wzwpxwyfmwdm,wzwxexfqt
        from t_record2_look
        where status='0'
        <!--条件-->
        <if test="recId != null and recId!='' ">
            and rec_id = #{recId}
        </if>
    </select>
    <select id="findByEquipmentPatientId" parameterType="Integer" resultMap="BaseResultMap">
        SELECT id,rec_id,wzsdm,wzsqt,wzs,wztdm,wztqt,wzt,wzws,wzwsqt,wzwms,wzwmsqt,wzwxt,wzwxtqt,wzwtlwgjq,wzwtlwgjqqt,wzwpf,wzwpfqt,wzwlm,wzwlmqt,wzwpxwyfmw,wzwpxwyfmwqt,wzqt1,status,create_date,create_user,update_date,update_user,wzwxexf,wzwxexfdm,wzwsdm,wzwmsdm,wzwxtdm,wzwtlwgjqdm,wzwpfdm,wzwlmdm,wzwpxwyfmwdm,wzwxexfqt
        from t_record2_look
        where status='0'
        <!--条件-->
        <if test="equipmentPatientId != null and equipmentPatientId!='' ">
            and equipment_patient_id = #{equipmentPatientId}
        </if>
    </select>

    <select id="findByTagNo" parameterType="String" resultMap="BaseResultMap">
        SELECT id,tag_no,wzsdm,wzsqt,wzs,wztdm,wztqt,wzt,wzws,wzwsqt,wzwms,wzwmsqt,wzwxt,wzwxtqt,wzwtlwgjq,wzwtlwgjqqt,wzwpf,
               wzwpfqt,wzwlm,wzwlmqt,wzwpxwyfmw,wzwpxwyfmwqt,wzqt1,status,create_date,create_user,update_date,update_user,wzwxexf,
               wzwxexfdm,wzwsdm,wzwmsdm,wzwxtdm,wzwtlwgjqdm,wzwpfdm,wzwlmdm,wzwpxwyfmwdm,wzwxexfqt
        from t_record2_look
        where status='0'
        <!--条件-->
        <if test="tagNo != null and tagNo!='' ">
            and tag_no = #{tagNo}
        </if>
    </select>

</mapper>