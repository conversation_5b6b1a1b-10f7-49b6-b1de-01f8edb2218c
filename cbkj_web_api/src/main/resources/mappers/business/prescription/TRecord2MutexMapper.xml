<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.TRecord2MutexMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.TRecord2Mutex">
        <id column="id" jdbcType="BIGINT"  property="id" />
        <result column="dis_id" jdbcType="VARCHAR" property="disId" />
        <result column="dis_name" jdbcType="VARCHAR" property="disName" />
        <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
        <result column="mutex_name" jdbcType="VARCHAR" property="mutexName" />
        <result column="child_code" jdbcType="VARCHAR" property="childCode" />
        <result column="mutex_sub_data" jdbcType="VARCHAR" property="mutexSubData" />
        <result column="status" jdbcType="VARCHAR" property="status" />
    </resultMap>


    <sql id="Base_Column_List">
    id,dis_id,dis_name,parent_id,mutex_name,child_code,mutex_sub_data,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRecord2Mutex">
        delete from t_record2_mutex where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record2_mutex where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TRecord2Mutex">
        insert into t_record2_mutex (<include refid="Base_Column_List" />) values
        (#{id},#{disId},#{disName},#{parentId},#{mutexName},#{childCode},#{mutexSubData},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_record2_mutex (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.disId},#{item.disName},#{item.parentId},#{item.mutexName},#{item.childCode},#{item.mutexSubData},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRecord2Mutex">
        update t_record2_mutex
        <set>
             <if test="disId != null">
                dis_id = #{ disId },
             </if>
             <if test="disName != null">
                dis_name = #{ disName },
             </if>
             <if test="parentId != null">
                parent_id = #{ parentId },
             </if>
             <if test="mutexName != null">
                mutex_name = #{ mutexName },
             </if>
             <if test="childCode != null">
                child_code = #{ childCode },
             </if>
             <if test="mutexSubData != null">
                mutex_sub_data = #{ mutexSubData },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record2_mutex where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TRecord2Mutex" resultMap="BaseResultMap">
        SELECT id,dis_id,dis_name,parent_id,mutex_name,child_code,mutex_sub_data,status
        from t_record2_mutex
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>


    <select id="getListByDisId" parameterType="String" resultMap="BaseResultMap">
        SELECT id,dis_id,dis_name,parent_id,mutex_name,child_code,mutex_sub_data,status
        from t_record2_mutex
        where status='0'
        <!--条件-->
        <if test="disId != null and disId!='' ">
            and dis_id = #{disId}
        </if>

    </select>

</mapper>