<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.TRecord2SmellMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.TRecord2Smell">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="rec_id" jdbcType="VARCHAR" property="recId" />
        <result column="tag_no" jdbcType="VARCHAR" property="tagNo" />
        <result column="wztsy" jdbcType="VARCHAR" property="wztsy" />
        <result column="wztsydm" jdbcType="VARCHAR" property="wztsydm" />
        <result column="wztsyqt" jdbcType="VARCHAR" property="wztsyqt" />
        <result column="wzxqw" jdbcType="VARCHAR" property="wzxqw" />
        <result column="wzxqwdm" jdbcType="VARCHAR" property="wzxqwdm" />
        <result column="wzxqwqt" jdbcType="VARCHAR" property="wzxqwqt" />
        <result column="wzqt2" jdbcType="VARCHAR" property="wzqt2" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="equipment_patient_id" jdbcType="INTEGER" property="equipmentPatientId" />
    </resultMap>


    <sql id="Base_Column_List">
    id,rec_id,tag_no,wztsy,wztsyqt,wzxqw,wzxqwqt,wzqt2,status,create_date,
        create_user,update_date,update_user,wztsydm,wzxqwdm,equipment_patient_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRecord2Smell">
        delete from t_record2_smell where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record2_smell where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>
    <delete id="deleteByTagNolist"  parameterType="ArrayList" >
      delete from t_record2_smell where tag_no in
            <foreach collection="array" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>

    </delete>

    <insert id="insert"  parameterType="TRecord2Smell">
        replace into t_record2_smell (<include refid="Base_Column_List" />) values
        (#{id},#{recId},#{tagNo},#{wztsy},#{wztsyqt},#{wzxqw},#{wzxqwqt},
         #{wzqt2},#{status},#{createDate},#{createUser},#{updateDate},#{updateUser},
         #{wztsydm},#{wzxqwdm},#{equipmentPatientId}

         )
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_record2_smell (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.recId},#{item.tagNo},#{item.wztsy},#{item.wztsyqt},
             #{item.wzxqw},#{item.wzxqwqt},#{item.wzqt2},#{item.status},#{item.createDate},#{item.createUser},#{item.updateDate},#{item.updateUser},
            #{item.wztsydm},#{item.wzxqwdm},#{item.equipmentPatientId}

             )
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRecord2Smell">
        update t_record2_smell
        <set>
             <if test="recId != null">
                rec_id = #{ recId },
             </if>
             <if test="tagNo != null">
                tag_no = #{ tagNo },
             </if>
             <if test="wztsy != null">
                wztsy = #{ wztsy },
             </if>
             <if test="wztsyqt != null">
                wztsyqt = #{ wztsyqt },
             </if>
             <if test="wzxqw != null">
                wzxqw = #{ wzxqw },
             </if>
             <if test="wzxqwqt != null">
                wzxqwqt = #{ wzxqwqt },
             </if>
             <if test="wzqt2 != null">
                wzqt2 = #{ wzqt2 },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="updateDate != null">
                update_date = #{ updateDate },
             </if>
             <if test="updateUser != null">
                update_user = #{ updateUser },
             </if>
            <if test="wztsydm != null">
                wztsydm = #{wztsydm},
            </if>
            <if test="wzxqwdm != null">
                wzxqwdm = #{wzxqwdm},
            </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record2_smell where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TRecord2Smell" resultMap="BaseResultMap">
        SELECT id,rec_id,tag_no,wztsy,wztsyqt,wzxqw,wzxqwqt,wzqt2,status,create_date,create_user,update_date,update_user,wztsydm,wzxqwdm
        from t_record2_smell
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>


    <select id="findByRecId" parameterType="String" resultMap="BaseResultMap">
        SELECT id,rec_id,wztsy,wztsyqt,wzxqw,wzxqwqt,wzqt2,status,create_date,create_user,update_date,update_user,wztsydm,wzxqwdm
        from t_record2_smell
        where status='0'
        <!--条件-->
        <if test="recId != null and recId!='' ">
            and rec_id = #{recId}
        </if>
    </select>
    <select id="findByEquipmentPatientId" parameterType="Integer" resultMap="BaseResultMap">
        SELECT id,rec_id,wztsy,wztsyqt,wzxqw,wzxqwqt,wzqt2,status,create_date,create_user,update_date,update_user,wztsydm,wzxqwdm
        from t_record2_smell
        where status='0'
        <!--条件-->
        <if test="equipmentPatientId != null and equipmentPatientId!='' ">
            and equipment_patient_id = #{equipmentPatientId}
        </if>
    </select>

    <select id="findByTagNo" parameterType="String" resultMap="BaseResultMap">
        SELECT id,tag_no,wztsy,wztsyqt,wzxqw,wzxqwqt,wzqt2,status,create_date,create_user,update_date,update_user,wztsydm,wzxqwdm
        from t_record2_smell
        where status='0'
        <!--条件-->
        <if test="tagNo != null and tagNo!='' ">
            and tag_no = #{tagNo}
        </if>
    </select>

</mapper>