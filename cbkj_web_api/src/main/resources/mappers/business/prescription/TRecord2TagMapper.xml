<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.prescription.TRecord2TagMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.prescription.TRecord2Tag">
        <id column="tag_no" jdbcType="VARCHAR"  property="tagNo" />
        <result column="tag_name" jdbcType="VARCHAR" property="tagName" />
        <result column="tag_py" jdbcType="VARCHAR" property="tagPy" />
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId" />
        <result column="doctor_name" jdbcType="VARCHAR" property="doctorName" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_id" jdbcType="VARCHAR" property="insId" />
        <result column="patient_content" jdbcType="VARCHAR" property="patientContent"/>
        <result column="now_desc" jdbcType="VARCHAR" property="nowDesc"/>
        <result column="past_desc" jdbcType="VARCHAR" property="pastDesc"/>
        <result column="physical" jdbcType="VARCHAR" property="physical"/>
        <result column="auxiliary_exam" jdbcType="VARCHAR" property="auxiliaryExam"/>
        <result column="patient_height" jdbcType="VARCHAR" property="patientHeight"/>
        <result column="patient_weight" jdbcType="VARCHAR" property="patientWeight"/>
        <result column="xyzdbzbm" jdbcType="VARCHAR" property="xyzdbzbm" />
        <result column="xyzdbzmc" jdbcType="VARCHAR" property="xyzdbzmc" />
        <result column="zybmdm" jdbcType="VARCHAR" property="zybmdm" />
        <result column="zybmmc" jdbcType="VARCHAR" property="zybmmc" />
        <result column="zyzhdm" jdbcType="VARCHAR" property="zyzhdm" />
        <result column="zyzhmc" jdbcType="VARCHAR" property="zyzhmc" />
        <result column="zzzfdm" jdbcType="VARCHAR" property="zzzfdm" />
        <result column="zzzfmc" jdbcType="VARCHAR" property="zzzfmc" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="tag_type" jdbcType="VARCHAR" property="tagType" />
        <result column="version" jdbcType="VARCHAR" property="version" />
        <result column="treatment_advice" jdbcType="VARCHAR" property="treatmentAdvice"/>
    </resultMap>


    <sql id="Base_Column_List">
    tag_no,tag_name,tag_py,doctor_id,doctor_name,app_id,ins_id,patient_content,now_desc,past_desc,physical,auxiliary_exam,patient_height,patient_weight,xyzdbzbm,xyzdbzmc,zybmdm,zybmmc,zyzhdm,zyzhmc,zzzfdm,zzzfmc,status,create_date,create_user,update_date,update_user,tag_type,version,treatment_advice
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRecord2Tag">
        delete from t_record2_tag where tag_no = #{ tagNo }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record2_tag where tag_no in
            <foreach collection="array" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>

    </delete>

    <insert id="insert"  parameterType="TRecord2Tag">
        insert into t_record2_tag (<include refid="Base_Column_List" />) values
        (#{tagNo},#{tagName},#{tagPy},#{doctorId},#{doctorName},#{appId},#{insId},
         #{patientContent},#{nowDesc},#{pastDesc},#{physical},#{auxiliaryExam},#{patientHeight},#{patientWeight},
         #{xyzdbzbm},#{xyzdbzmc},#{zybmdm},#{zybmmc},#{zyzhdm},#{zyzhmc},#{zzzfdm},#{zzzfmc},#{status},#{createDate},#{createUser},#{updateDate},#{updateUser}
         ,#{tagType},#{version},#{treatmentAdvice})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_record2_tag (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.tagNo},#{item.tagName},#{item.tagPy},#{item.doctorId},#{item.doctorName},#{item.appId},#{item.insId},#{item.xyzdbzbm},#{item.xyzdbzmc},#{item.zybmdm},#{item.zybmmc},#{item.zyzhdm},#{item.zyzhmc},#{item.zzzfdm},#{item.zzzfmc},#{item.status},#{item.createDate},#{item.createUser},#{item.updateDate},#{item.updateUser},#{item.tagType}
            ,#{item.version},#{item.treatmentAdvice}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRecord2Tag">
        update t_record2_tag
        <set>
             <if test="tagName != null">
                tag_name = #{ tagName },
             </if>
             <if test="tagPy != null">
                tag_py = #{ tagPy },
             </if>
             <if test="doctorId != null">
                doctor_id = #{ doctorId },
             </if>
             <if test="doctorName != null">
                doctor_name = #{ doctorName },
             </if>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="insId != null">
                ins_id = #{ insId },
             </if>
             <if test="xyzdbzbm != null">
                xyzdbzbm = #{ xyzdbzbm },
             </if>
             <if test="xyzdbzmc != null">
                xyzdbzmc = #{ xyzdbzmc },
             </if>
             <if test="zybmdm != null">
                zybmdm = #{ zybmdm },
             </if>
             <if test="zybmmc != null">
                zybmmc = #{ zybmmc },
             </if>
             <if test="zyzhdm != null">
                zyzhdm = #{ zyzhdm },
             </if>
             <if test="zyzhmc != null">
                zyzhmc = #{ zyzhmc },
             </if>
             <if test="zzzfdm != null">
                zzzfdm = #{ zzzfdm },
             </if>
             <if test="zzzfmc != null">
                zzzfmc = #{ zzzfmc },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="updateDate != null">
                update_date = #{ updateDate },
             </if>
             <if test="updateUser != null">
                update_user = #{ updateUser },
             </if>
            <if test="tagType != null">
                tag_type = #{tagType},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
        </set>
        where tag_no = #{ tagNo }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record2_tag where tag_no = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TRecord2Tag" resultMap="BaseResultMap">
        SELECT tag_no,tag_name,tag_py,doctor_id,doctor_name,app_id,ins_id,xyzdbzbm,xyzdbzmc,zybmdm,zybmmc,zyzhdm,zyzhmc,zzzfdm,zzzfmc,status,create_date,create_user,update_date,update_user,version,tag_type
        from t_record2_tag
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getListByDoctorIdAndInsId" parameterType="String"
            resultMap="BaseResultMap">
        SELECT tag_no,tag_name,doctor_id,doctor_name,app_id,ins_id,version,tag_type
        from t_record2_tag
        where status='0'
        <!--条件-->
        <if test="insId != null and insId!='' ">
            and ins_id = #{insId}
        </if>
        <if test="doctorId != null and doctorId!='' ">
            and doctor_id = #{doctorId}
        </if>
        <if test="appId != null and appId != ''">
            and app_id  = #{appId}
        </if>
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getListByRequest" parameterType="com.jiuzhekan.cbkj.controller.request.TagQueryRequest"
            resultMap="BaseResultMap">
        SELECT tag_no,tag_name,doctor_id,doctor_name,app_id,ins_id,tag_type,version,treatment_advice
        from t_record2_tag
        where status='0'
        <!--条件-->
        <if test="appId != null and appId != ''">
            and app_id  = #{appId}
        </if>
        <if test="insId != null and insId!='' ">
            and ins_id = #{insId}
        </if>
        <if test="doctorId != null and doctorId!='' ">
            and doctor_id = #{doctorId}
        </if>
        <if test="keyWord != null and keyWord!='' ">
            and (tag_name like CONCAT('%',trim(#{keyWord}),'%')  or tag_py  like concat('%',trim(#{keyWord}),'%'))
        </if>
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="findByTagNo" parameterType="String" resultMap="BaseResultMap">
        SELECT tag_no,tag_name,doctor_id,doctor_name,app_id,ins_id,patient_content,now_desc,past_desc,physical,auxiliary_exam,treatment_advice,patient_height,patient_weight,xyzdbzbm,xyzdbzmc,zybmdm,zybmmc,zyzhdm,zyzhmc,zzzfdm,zzzfmc,status,create_date,create_user,update_date,update_user,`version`
        from t_record2_tag
        where status='0'
        <!--条件-->
        <if test="tagNo != null and tagNo!='' ">
            and tag_no = #{tagNo}
        </if>

    </select>
</mapper>