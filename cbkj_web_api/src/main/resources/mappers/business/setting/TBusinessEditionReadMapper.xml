<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.setting.TBusinessEditionReadMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.setting.TBusinessEditionRead">
        <id column="read_id" jdbcType="INTEGER"  property="readId" />
        <result column="edition_id" jdbcType="VARCHAR" property="editionId" />
        <result column="edition_num" jdbcType="VARCHAR" property="editionNum" />
        <result column="admin_id" jdbcType="VARCHAR" property="adminId" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    </resultMap>


    <sql id="Base_Column_List">
        read_id,edition_id,edition_num,admin_id,create_date,create_user,update_date,update_user
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TBusinessEditionRead">
        delete from t_business_edition_read where read_id = #{ readId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_business_edition_read where read_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TBusinessEditionRead">
        insert into t_business_edition_read (edition_id,edition_num,admin_id,create_date,create_user) values
        (#{editionId},#{editionNum},#{adminId},#{createDate},#{createUser})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_business_edition_read (edition_id,edition_num,admin_id,create_date,create_user) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.editionId},#{item.editionNum},#{item.adminId},#{item.createDate},#{item.createUser})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TBusinessEditionRead">
        update t_business_edition_read
        <set>
             <if test="editionId != null">
                edition_id = #{ editionId },
             </if>
             <if test="editionNum != null">
                edition_num = #{ editionNum },
             </if>
             <if test="adminId != null">
                admin_id = #{ adminId },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="updateDate != null">
                update_date = #{ updateDate },
             </if>
             <if test="updateUser != null">
                update_user = #{ updateUser },
             </if>
        </set>
        where read_id = #{ readId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_business_edition_read where read_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TBusinessEditionRead" resultMap="BaseResultMap">
        SELECT read_id,edition_id,edition_num,admin_id,create_date,create_user,update_date,update_user
        from t_business_edition_read
        <where>
        </where>
    </select>

</mapper>