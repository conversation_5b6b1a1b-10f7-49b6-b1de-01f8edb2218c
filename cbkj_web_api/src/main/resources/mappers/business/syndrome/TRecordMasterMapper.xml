<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.syndrome.TRecordMasterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.syndrome.TRecordMaster">
        <id column="register_id" property="registerId" />
        <result column="disease_code" property="diseaseCode" />
        <result column="dis_id" property="disId" />
        <result column="patient_content" property="patientContent" />
        <result column="now_desc" property="nowDesc" />
        <result column="past_desc" property="pastDesc" />
        <result column="four_diagnosis" property="fourDiagnosis" />
        <result column="physical" property="physical" />
        <result column="auxiliary_exam" property="auxiliaryExam" />
        <result column="json_str" property="jsonStr" />
        <result column="save_time" property="saveTime" />
        <result column="origin_type" property="originType" />
        <result column="origin_id" property="originId" />
        <result column="origin_url" property="originUrl" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        register_id, disease_code,dis_id,patient_content, now_desc, past_desc, four_diagnosis, physical, auxiliary_exam, json_str, save_time, origin_type, origin_id, origin_url
    </sql>

    <insert id="insertMasterResult" parameterType="com.jiuzhekan.cbkj.beans.business.syndrome.TRecordMaster">
        insert into `t_record_master`( <include refid="Base_Column_List"/> )
        values(#{registerId},#{diseaseCode},#{disId},#{patientContent},#{nowDesc},#{pastDesc},#{fourDiagnosis},#{physical},#{auxiliaryExam},#{jsonStr,jdbcType=VARCHAR},#{saveTime},#{originType,jdbcType=VARCHAR},#{originId,jdbcType=VARCHAR},#{originUrl,jdbcType=VARCHAR})
    </insert>
    <select id="getMasterRecord" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from   `t_record_master`  where register_id = #{registerId}
    </select>
   <delete id="deleteByRegisterId" parameterType="java.lang.String">
       delete from  t_record_master  where register_id = #{registerId}
   </delete>
</mapper>
