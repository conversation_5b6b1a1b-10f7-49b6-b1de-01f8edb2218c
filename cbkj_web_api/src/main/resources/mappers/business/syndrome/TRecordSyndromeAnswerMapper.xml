<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.syndrome.TRecordSyndromeAnswerMapper">
    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeAnswer">
        <id column="RS_ANSWER_ID" jdbcType="VARCHAR" property="rsAnswerId"/>
        <result column="RS_ASK_ID" jdbcType="VARCHAR" property="rsAskId"/>
        <result column="ANSWER_ID" jdbcType="VARCHAR" property="answerId"/>
        <result column="ANSWER_NAME" jdbcType="VARCHAR" property="answerName"/>
        <result column="ANSWER_SEQN" jdbcType="VARCHAR" property="answerSeqn"/>
        <result column="ITEM_ID" jdbcType="VARCHAR" property="itemId"/>
        <result column="TEMP_TYPE" jdbcType="VARCHAR" property="tempType"/>
        <result column="RECORD_TYPE_CODE" jdbcType="VARCHAR" property="recordTypeCode"/>
        <result column="RECORD_TYPE_NAME" jdbcType="VARCHAR" property="recordTypeName"/>
        <result column="RECORD_TYPE_OPTION_CODE" jdbcType="VARCHAR" property="recordTypeOptionCode"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
    </resultMap>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_record_syndrome_answer
        (RS_ANSWER_ID,RS_ASK_ID,ANSWER_ID,ANSWER_NAME,ANSWER_SEQN,ITEM_ID,TEMP_TYPE,
        RECORD_TYPE_CODE,RECORD_TYPE_NAME,RECORD_TYPE_OPTION_CODE,CREATE_DATE) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.rsAnswerId},#{item.rsAskId},#{item.answerId},#{item.answerName},#{item.answerSeqn},#{item.itemId},#{item.tempType},
            #{item.recordTypeCode},#{item.recordTypeName},#{item.recordTypeOptionCode},#{item.createDate})
        </foreach>
    </insert>
</mapper>