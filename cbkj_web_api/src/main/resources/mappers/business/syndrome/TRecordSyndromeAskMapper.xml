<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.syndrome.TRecordSyndromeAskMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeAsk">
        <id column="RS_ASK_ID" jdbcType="VARCHAR"  property="rsAskId" />
        <result column="RS_GROUP_ID" jdbcType="VARCHAR" property="rsGroupId" />
        <result column="ASK_ID" jdbcType="VARCHAR" property="askId" />
        <result column="ASK_NAME" jdbcType="VARCHAR" property="askName" />
        <result column="ASK_SEQN" jdbcType="VARCHAR" property="askSeqn" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    </resultMap>


    <sql id="Base_Column_List">
    RS_ASK_ID,RS_GROUP_ID,ASK_ID,ASK_NAME,ASK_SEQN,CREATE_DATE
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRecordSyndromeAsk">
        delete from t_record_syndrome_ask where RS_ASK_ID = #{ rsAskId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record_syndrome_ask where RS_ASK_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TRecordSyndromeAsk">
        insert into t_record_syndrome_ask (RS_ASK_ID,RS_GROUP_ID,ASK_ID,ASK_NAME,ASK_SEQN,CREATE_DATE) values
        (#{rsAskId},#{rsGroupId},#{askId},#{askName},#{askSeqn},#{createDate})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_record_syndrome_ask (RS_ASK_ID,RS_GROUP_ID,ASK_ID,ASK_NAME,ASK_SEQN,CREATE_DATE) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.rsAskId},#{item.rsGroupId},#{item.askId},#{item.askName},#{item.askSeqn},#{item.createDate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRecordSyndromeAsk">
        update t_record_syndrome_ask
        <set>
             <if test="rsGroupId != null">
                RS_GROUP_ID = #{ rsGroupId },
             </if>
             <if test="askId != null">
                ASK_ID = #{ askId },
             </if>
             <if test="askName != null">
                ASK_NAME = #{ askName },
             </if>
             <if test="askSeqn != null">
                ASK_SEQN = #{ askSeqn },
             </if>
            <if test="createDate != null">
                CREATE_DATE = #{ createDate },
            </if>
        </set>
        where RS_ASK_ID = #{ rsAskId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record_syndrome_ask where RS_ASK_ID = #{id}
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TRecordSyndromeAsk" resultMap="BaseResultMap">
        SELECT RS_ASK_ID,RS_GROUP_ID,ASK_ID,ASK_NAME,ASK_SEQN,CREATE_DATE
        from t_record_syndrome_ask
        <where>
            <if test=" rsGroupId != null and rsGroupId!='' ">
                and RS_GROUP_ID = #{rsGroupId}
            </if>
        </where>
    </select>

</mapper>