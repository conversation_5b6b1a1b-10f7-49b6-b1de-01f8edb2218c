<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.syndrome.TRecordSyndromeFileMapper">

    <insert id="insertCheckResult" parameterType="List">
        insert into t_record_syndrome_file
        (
            register_id,
            file_type,
            file_url,
            smf_id
        )
        values
        <foreach collection="list" separator="," item="item">
            (
            #{item.registerId,jdbcType=VARCHAR},
            #{item.fileType,jdbcType=VARCHAR},
            #{item.fileUrl,jdbcType=VARCHAR},
            #{item.smfId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="deleteCheckResult" parameterType="List">
        <foreach collection="list" separator=";" item="item">
            update t_record_syndrome_file set is_del = '1' ,
            delete_date = #{item.deleteDate} , delete_user = #{deleteUser} where is_del = '0'
            <if test="null != item.registerId and item.registerId != ''">
                and register_id = #{item.registerId}
            </if>
<!--            <if test="null != item.fileType and item.fileType != ''">-->
<!--                and file_type = #{item.fileType}-->
<!--            </if>-->
        </foreach>
    </update>

    <select id="getSyndromeFile" parameterType="com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeFile"
            resultType="com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeFile">
        select register_id as registerId , file_type as fileType , file_url as fileUrl from t_record_syndrome_file
        where is_del = '0'
        <if test="null != fileType and fileType != ''">
            and file_type = #{fileType}
        </if>
        <if test="null != registerId and registerId != ''">
            and register_id = #{registerId}
        </if>
        <if test="null != smfId and smfId != ''">
            and smf_id like CONCAT( trim(#{smfId}) , '%')
        </if>
    </select>

    <select id="getSyndromeFileBySMFId" parameterType="String" resultType="com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeFile">
        select register_id as registerId , file_type as fileType , file_url as fileUrl , smf_id as smfId from t_record_syndrome_file
        where is_del = '0'
        <if test="null != smfId and smfId != ''">
            and smf_id like CONCAT(trim(#{smfId}) , '%')
        </if>
        order by smf_id desc
    </select>

</mapper>
