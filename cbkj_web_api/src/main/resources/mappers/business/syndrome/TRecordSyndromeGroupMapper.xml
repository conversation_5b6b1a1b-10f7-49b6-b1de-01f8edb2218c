<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.syndrome.TRecordSyndromeGroupMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeGroup">
        <id column="RS_GROUP_ID" jdbcType="VARCHAR" property="rsGroupId"/>
        <result column="REGISTER_ID" jdbcType="VARCHAR" property="registerId"/>
        <result column="DOCTOR_ID" jdbcType="VARCHAR" property="doctorId"/>
        <result column="PATIENT_ID" jdbcType="VARCHAR" property="patientId"/>
        <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId"/>
        <result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName"/>
        <result column="DIS_ID" jdbcType="VARCHAR" property="disId"/>
        <result column="DIS_NAME" jdbcType="VARCHAR" property="disName"/>
        <result column="CLASS_ID" jdbcType="VARCHAR" property="classId"/>
        <result column="CLASS_NAME" jdbcType="VARCHAR" property="className"/>
        <result column="HAS_SAVE" jdbcType="VARCHAR" property="hasSave"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeGroup">
        <id column="RS_GROUP_ID" jdbcType="VARCHAR" property="rsGroupId"/>
        <result column="REGISTER_ID" jdbcType="VARCHAR" property="registerId"/>
        <result column="DOCTOR_ID" jdbcType="VARCHAR" property="doctorId"/>
        <result column="PATIENT_ID" jdbcType="VARCHAR" property="patientId"/>
        <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId"/>
        <result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName"/>
        <result column="DIS_ID" jdbcType="VARCHAR" property="disId"/>
        <result column="DIS_NAME" jdbcType="VARCHAR" property="disName"/>
        <result column="CLASS_ID" jdbcType="VARCHAR" property="classId"/>
        <result column="CLASS_NAME" jdbcType="VARCHAR" property="className"/>
        <result column="HAS_SAVE" jdbcType="VARCHAR" property="hasSave"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <collection property="askList" ofType="com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeAsk">
            <id column="RS_ASK_ID" jdbcType="VARCHAR" property="rsAskId"/>
            <result column="ASK_ID" jdbcType="VARCHAR" property="askId"/>
            <result column="ASK_NAME" jdbcType="VARCHAR" property="askName"/>
            <result column="ASK_SEQN" jdbcType="VARCHAR" property="askSeqn"/>

            <collection property="answerList" ofType="com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeAnswer">
                <id column="RS_ANSWER_ID" jdbcType="VARCHAR" property="rsAnswerId"/>
                <result column="ANSWER_ID" jdbcType="VARCHAR" property="answerId"/>
                <result column="ANSWER_NAME" jdbcType="VARCHAR" property="answerName"/>
                <result column="ANSWER_SEQN" jdbcType="VARCHAR" property="answerSeqn"/>
                <result column="ITEM_ID" jdbcType="VARCHAR" property="itemId"/>
                <result column="TEMP_TYPE" jdbcType="VARCHAR" property="tempType"/>
                <result column="RECORD_TYPE_CODE" jdbcType="VARCHAR" property="recordTypeCode"/>
                <result column="RECORD_TYPE_NAME" jdbcType="VARCHAR" property="recordTypeName"/>
                <result column="RECORD_TYPE_OPTION_CODE" jdbcType="VARCHAR" property="recordTypeOptionCode"/>
            </collection>
        </collection>
    </resultMap>


    <sql id="Base_Column_List">
    RS_GROUP_ID,REGISTER_ID,DOCTOR_ID,PATIENT_ID,GROUP_ID,GROUP_NAME,DIS_ID,DIS_NAME,CLASS_ID,CLASS_NAME,HAS_SAVE,CREATE_DATE
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRecordSyndromeGroup">
        delete from t_record_syndrome_group where RS_GROUP_ID = #{ rsGroupId }
    </delete>

    <delete id="deleteByRegisterId" parameterType="String">
        DELETE FROM t_record_syndrome_answer WHERE RS_ASK_ID IN 
            (SELECT RS_ASK_ID FROM t_record_syndrome_ask WHERE RS_GROUP_ID IN 
	            (SELECT RS_GROUP_ID FROM t_record_syndrome_group WHERE REGISTER_ID = #{ registerId }));
    </delete>

    <delete id="deleteByRegisterIdV2" parameterType="String">
        DELETE FROM t_record_syndrome_ask WHERE RS_GROUP_ID IN
        (SELECT RS_GROUP_ID FROM t_record_syndrome_group WHERE REGISTER_ID = #{ registerId });

    </delete>

    <delete id="deleteByRegisterIdV3" parameterType="String">
        DELETE FROM t_record_syndrome_answer WHERE RS_ASK_ID IN
        (SELECT RS_ASK_ID FROM t_record_syndrome_ask WHERE RS_GROUP_ID IN
        (SELECT RS_GROUP_ID FROM t_record_syndrome_group WHERE REGISTER_ID = #{ registerId }));
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record_syndrome_group where RS_GROUP_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" parameterType="TRecordSyndromeGroup">
        insert into t_record_syndrome_group (RS_GROUP_ID,REGISTER_ID,DOCTOR_ID,PATIENT_ID,GROUP_ID,GROUP_NAME,DIS_ID,DIS_NAME,CLASS_ID,CLASS_NAME,HAS_SAVE,CREATE_DATE) values
        (#{rsGroupId},#{registerId},#{doctorId},#{patientId},#{groupId},#{groupName},#{disId},#{disName},#{classId},#{className},#{hasSave},#{createDate})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_record_syndrome_group
        (RS_GROUP_ID,REGISTER_ID,DOCTOR_ID,PATIENT_ID,GROUP_ID,GROUP_NAME,DIS_ID,DIS_NAME,CLASS_ID,CLASS_NAME,HAS_SAVE,CREATE_DATE) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.rsGroupId},#{item.registerId},#{item.doctorId},#{item.patientId},#{item.groupId},#{item.groupName},#{item.disId},#{item.disName},#{item.classId},#{item.className},#{item.hasSave},#{item.createDate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRecordSyndromeGroup">
        update t_record_syndrome_group
        <set>
            <if test="registerId != null">
                REGISTER_ID = #{ registerId },
            </if>
            <if test="doctorId != null">
                DOCTOR_ID = #{ doctorId },
            </if>
            <if test="patientId != null">
                PATIENT_ID = #{ patientId },
            </if>
            <if test="groupId != null">
                GROUP_ID = #{ groupId },
            </if>
            <if test="groupName != null">
                GROUP_NAME = #{ groupName },
            </if>
            <if test="disId != null">
                DIS_ID = #{ disId },
            </if>
            <if test="disName != null">
                DIS_NAME = #{ disName },
            </if>
            <if test="classId != null">
                CLASS_ID = #{ classId },
            </if>
            <if test="className != null">
                CLASS_NAME = #{ className },
            </if>
            <if test="hasSave != null">
                HAS_SAVE = #{ hasSave },
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{ createDate },
            </if>
        </set>
        where RS_GROUP_ID = #{ rsGroupId }
    </update>

    <update id="hasSaveRecordByRegisterId" parameterType="String">
        update t_record_syndrome_group set HAS_SAVE = '1' where REGISTER_ID = #{ registerId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_record_syndrome_group where RS_GROUP_ID = #{id}
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TRecordSyndromeGroup" resultMap="BaseResultMap">
        SELECT RS_GROUP_ID,REGISTER_ID,DOCTOR_ID,PATIENT_ID,GROUP_ID,GROUP_NAME,DIS_ID,DIS_NAME,CLASS_ID,CLASS_NAME,HAS_SAVE,CREATE_DATE
        from t_record_syndrome_group
        <where>
            <if test=" registerId != null and registerId!='' ">
                and REGISTER_ID = #{registerId}
            </if>
            <if test=" hasSave != null and hasSave!='' ">
                and gro.HAS_SAVE = #{hasSave}
            </if>
        </where>
    </select>

    <!--获取分组、问题、答案记录-->
    <select id="getSyndromeRecord" parameterType="TRecordSyndromeGroup" resultMap="BaseResultMap2">
        SELECT gro.*, ask.*, ans.*
        from t_record_syndrome_group gro
        join t_record_syndrome_ask ask on ask.RS_GROUP_ID = gro.RS_GROUP_ID
        join t_record_syndrome_answer ans on ans.RS_ASK_ID = ask.RS_ASK_ID
        <where>
            <if test=" registerId != null and registerId!='' ">
                and gro.REGISTER_ID = #{registerId}
            </if>
            <if test=" hasSave != null and hasSave!='' ">
                and gro.HAS_SAVE = #{hasSave}
            </if>
        </where>
    </select>


</mapper>