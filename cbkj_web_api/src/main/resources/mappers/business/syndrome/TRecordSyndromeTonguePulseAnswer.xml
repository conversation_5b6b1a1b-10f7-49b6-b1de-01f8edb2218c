<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.syndrome.TRecordSyndromeTonguePulseAnswerMapper">
    <insert id="insertList" parameterType="java.util.ArrayList">
        insert into t_record_syndrome_tongue_pulse_answer(rs_group_id,code,name,type) values
        <foreach collection="list" item="item" separator=",">
            (#{item.rsGroupId},#{item.code},#{item.name},#{item.type})
        </foreach>
    </insert>
</mapper>