<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.statistics.mapper.function.TStatisticsFunction2Mapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.statistics.beans.function.TStatisticsFunction2">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="classify" jdbcType="VARCHAR" property="classify" />
        <result column="function" jdbcType="VARCHAR" property="function" />
        <result column="usage_times" jdbcType="INTEGER" property="usageTimes" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="insert_date" jdbcType="TIMESTAMP" property="insertDate" />
    </resultMap>


    <sql id="Base_Column_List">
    app_id,app_name,ins_code,ins_name,dept_id,dept_name,user_id,user_name,classify,`function`,usage_times,create_date,insert_date
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.function.TStatisticsFunction2">
        delete from t_statistics_function2 where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_statistics_function2 where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.statistics.beans.function.TStatisticsFunction2">
        insert into t_statistics_function2 (<include refid="Base_Column_List" />) values
        (#{appId},#{appName},#{insCode},#{insName},#{deptId},#{deptName},#{userId},
        #{userName},#{classify},#{function},#{usageTimes},#{createDate},#{insertDate})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_statistics_function2 (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.deptId},#{item.deptName},#{item.userId},
            #{item.userName},#{item.classify},#{item.function},#{item.usageTimes},#{item.createDate},#{item.insertDate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.function.TStatisticsFunction2">
        update t_statistics_function2
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="deptName != null">
                dept_name = #{ deptName },
             </if>
             <if test="userId != null">
                user_id = #{ userId },
             </if>
             <if test="userName != null">
                user_name = #{ userName },
             </if>
             <if test="classify != null">
                classify = #{ classify },
             </if>
             <if test="function != null">
                `function` = #{ function },
             </if>
             <if test="usageTimes != null">
                usage_times = #{ usageTimes },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="insertDate != null">
                 insert_date = #{ insertDate },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_statistics_function2 where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.statistics.beans.function.TStatisticsFunction2" resultMap="BaseResultMap">
        SELECT id,app_id,app_name,ins_code,ins_name,dept_id,dept_name,user_id,user_name,classify,`function`,usage_times,create_date
        from t_statistics_function2
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="sumUsageTimes" parameterType="com.jiuzhekan.statistics.beans.function.TStatisticsFunction2" resultType="int">
        select sum(usage_times)
        from t_statistics_function2
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id = #{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code = #{insCode}
            </if>
            <if test=" classify != null and classify!='' ">
                and classify = #{classify}
            </if>
            <if test=" function != null and function!='' ">
                and `function` = #{function}
            </if>
            <if test=" beginDate != null and beginDate!='' ">
                and create_date &gt;= #{beginDate}
            </if>
            <if test=" endDate != null and endDate!='' ">
                and create_date &lt;= #{endDate}
            </if>
        </where>

    </select>
    <select id="sumUsageTimes2" resultMap="BaseResultMap" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam">

        select sum(usage_times) usage_times ,app_id,ins_code,`function`
        from t_statistics_function2
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test=" beginDate != null and beginDate!='' ">
                and create_date &gt;= #{beginDate}
            </if>
            <if test=" endDate != null and endDate!='' ">
                and create_date &lt;= #{endDate}
            </if>
        </where>
        group by app_id,ins_code,`function` ORDER BY `function`
    </select>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.statistics.beans.function.SimpleP">
        <result column="function" jdbcType="VARCHAR" property="function" />
        <result column="usage_times" jdbcType="INTEGER" property="usageTimes" />
    </resultMap>
    <select id="sumUsageTimesAll2" resultMap="BaseResultMap2" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam">

        select sum(usage_times) usage_times,`function`
        from t_statistics_function2
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test=" beginDate != null and beginDate!='' ">
                and create_date &gt;= #{beginDate}
            </if>
            <if test=" endDate != null and endDate!='' ">
                and create_date &lt;= #{endDate}
            </if>
        </where>
        group by `function` ORDER BY `function`
    </select>

    <select id="getByDetail" resultMap="BaseResultMap" parameterType="com.jiuzhekan.statistics.beans.function.TStatisticsFunction2">

        select <include refid="Base_Column_List" />
        from t_statistics_function2
        where app_id = #{appId}
        and ins_code = #{insCode}
        and dept_id = #{deptId}
        and user_id = #{userId}
        and classify = #{classify}
        and `function` = #{function}
        and create_date = #{createDate}
        limit 1
    </select>
    <select id="sumUsageTimes3" resultMap="BaseResultMap" parameterType="com.jiuzhekan.statistics.beans.register.QueryParam">
        select sum(usage_times) usage_times ,app_id,ins_code,`function`
        from t_statistics_function2
        <where>
            (
            <foreach collection="queryParamList" item="item" index="index" separator=" or ">
                (app_id=#{item.appId} and ins_code=#{item.insCode})
            </foreach>
            )
            <if test=" beginDate != null and beginDate!='' ">
                and create_date &gt;= #{beginDate}
            </if>
            <if test=" endDate != null and endDate!='' ">
                and create_date &lt;= #{endDate}
            </if>
        </where>
        group by app_id,`function` ORDER BY `function`
    </select>

    <select id="sumStatistics" resultType="java.lang.Integer">
        select
        SUM(usage_times) as number
        from t_statistics_function2
        <where>
            `function` in('智能辩证','智能推导','参考医案','中药查询','方剂查询','经络穴位查询','经方查询','疾病查询','中成药','临床诊疗指南','舌诊','脉诊','古书籍')
            <if test=" appId != null and appId!='' ">
                and app_id = #{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code = #{insCode}
            </if>
            <if test=" numType != null">
                and num_type = #{numType}
            </if>
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>

        </where>
    </select>

    <update id="updateUsageTimesInsertDate" parameterType="com.jiuzhekan.statistics.beans.function.TStatisticsFunction2">
        update t_statistics_function2
        <set>
            <if test="usageTimes != null">
                usage_times = #{ usageTimes },
            </if>
            <if test="insertDate != null">
                insert_date = #{ insertDate },
            </if>
        </set>
        where id = #{ id }
    </update>

</mapper>