<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.healthpres.mapper.TUserQuestionnaireCommonDisMapper">
    <resultMap id="BaseResultMap" type="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonDis">
        <id column="common_dis_id" jdbcType="VARCHAR"  property="commonDisId" />
        <result column="common_dis_name" jdbcType="VARCHAR" property="commonDisName" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="sort" jdbcType="INTEGER" property="sort" />
        <result column="common_dis_code" jdbcType="VARCHAR" property="commonDisCode" />
    </resultMap>


    <sql id="Base_Column_List">
    common_dis_id,common_dis_name,status,sort,common_dis_code
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonDis">
        delete from t_user_questionnaire_common_dis where common_dis_id = #{ commonDisId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_user_questionnaire_common_dis where common_dis_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonDis">
        insert into t_user_questionnaire_common_dis (<include refid="Base_Column_List" />) values
        (#{commonDisId},#{commonDisName},#{status},#{sort},#{commonDisCode})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_user_questionnaire_common_dis (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.commonDisId},#{item.commonDisName},#{item.status},#{item.sort},#{item.commonDisCode})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonDis">
        update t_user_questionnaire_common_dis
        <set>
             <if test="commonDisName != null">
                common_dis_name = #{ commonDisName },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="sort != null">
                sort = #{ sort },
             </if>
             <if test="commonDisCode != null">
                common_dis_code = #{ commonDisCode },
             </if>
        </set>
        where common_dis_id = #{ commonDisId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_user_questionnaire_common_dis where common_dis_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonDis" resultMap="BaseResultMap">
        SELECT common_dis_id,common_dis_name,status,sort,common_dis_code
        from t_user_questionnaire_common_dis
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getAllList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from t_user_questionnaire_common_dis where status = 0 order by sort asc
    </select>

</mapper>