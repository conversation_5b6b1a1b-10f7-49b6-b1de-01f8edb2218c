<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.healthpres.mapper.TUserQuestionnaireCommonMappingMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonMapping">
        <id column="common_dis_id" jdbcType="VARCHAR"  property="commonDisId" />
        <result column="common_west_id" jdbcType="VARCHAR" property="commonWestId" />
        <result column="type" jdbcType="INTEGER" property="type" />
    </resultMap>


    <sql id="Base_Column_List">
    common_west_id,common_dis_id,type
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonMapping">
        delete from t_user_questionnaire_common_mapping where common_dis_id = #{ commonDisId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_user_questionnaire_common_mapping where common_dis_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonMapping">
        insert into t_user_questionnaire_common_mapping (<include refid="Base_Column_List" />) values
        (#{commonWestId},#{commonDisId},#{type})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_user_questionnaire_common_mapping (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.commonWestId},#{item.commonDisId},#{item.type})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonMapping">
        update t_user_questionnaire_common_mapping
        <set>
             <if test="commonWestId != null">
                common_west_id = #{ commonWestId },
             </if>
             <if test="type != null">
                type = #{ type },
             </if>
        </set>
        where common_dis_id = #{ commonDisId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_user_questionnaire_common_mapping where common_dis_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonMapping" resultMap="BaseResultMap">
        SELECT common_west_id,common_dis_id,type
        from t_user_questionnaire_common_mapping
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>