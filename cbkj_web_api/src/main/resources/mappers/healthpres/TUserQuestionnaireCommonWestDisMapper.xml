<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.healthpres.mapper.TUserQuestionnaireCommonWestDisMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonWestDis">
        <id column="common_west_id" jdbcType="VARCHAR" property="commonWestId"/>
        <result column="common_west_name" jdbcType="VARCHAR" property="commonWestName"/>
        <result column="common_west_code" jdbcType="VARCHAR" property="commonWestCode"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="class_type" jdbcType="VARCHAR" property="classType"/>
        <result column="class_type_name" jdbcType="VARCHAR" property="classTypeName"/>
    </resultMap>


    <sql id="Base_Column_List">
        common_west_id,common_west_name,common_west_code,status,sort
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonWestDis">
        delete
        from t_user_questionnaire_common_west_dis
        where common_west_id = #{ commonWestId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_user_questionnaire_common_west_dis where common_west_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonWestDis">
        insert into t_user_questionnaire_common_west_dis (<include refid="Base_Column_List"/>) values
        (#{commonWestId},#{commonWestName},#{commonWestCode},#{status},#{sort})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_user_questionnaire_common_west_dis (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.commonWestId},#{item.commonWestName},#{item.commonWestCode},#{item.status},#{item.sort})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonWestDis">
        update t_user_questionnaire_common_west_dis
        <set>
            <if test="commonWestName != null">
                common_west_name = #{ commonWestName },
            </if>
            <if test="commonWestCode != null">
                common_west_code = #{ commonWestCode },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="sort != null">
                sort = #{ sort },
            </if>
        </set>
        where common_west_id = #{ commonWestId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_user_questionnaire_common_west_dis where common_west_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonWestDis"
            resultMap="BaseResultMap">
        SELECT common_west_id,common_west_name,common_west_code,status,sort
        from t_user_questionnaire_common_west_dis
        <where>
            stauts = 0
            <if test=" commonWestName != null and commonWestName!='' ">
                and common_west_name like CONCAT('%',trim(#{commonWestName}),'%')
            </if>
        </where>
        order by sort asc
    </select>
    <select id="getRecommendByPatient" resultType="com.jiuzhekan.healthpres.beans.GetRecommendByPatient"
            parameterType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.RecommendSearchQuery">
       select a.* from ( SELECT a.`class_type` as classType,a.class_type_name as classTypeName
        FROM `t_user_questionnaire_common_dis` AS a
        WHERE EXISTS (SELECT 1
        FROM t_record AS b
        WHERE
        (b.DIS_ID = a.common_dis_id )
        AND b.PATIENT_ID = #{patientId}
        )
        union
        SELECT c.`class_type` as classType,c.class_type_name as classTypeName
        from `t_user_questionnaire_common_west_dis` as c
        WHERE EXISTS (SELECT 1
        FROM t_record AS b
        WHERE
        (c.`common_west_code` = b.WESTERN_DISEASE_ID)
        AND b.PATIENT_ID = #{patientId}
        )
        ) as a
        GROUP BY a.classType

    </select>
<select id="getBianZhengRecommendByPatient" resultType="com.jiuzhekan.healthpres.beans.GetRecommendByPatient"
        parameterType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.RecommendSearchQuery">
    <!-- SELECT c.`class_type` as classType, c.class_type_name as classTypeName
    FROM `t_user_questionnaire_common_dis` AS a
    JOIN `t_user_questionnaire_common_mapping` AS b ON (a.`common_dis_id` = b.`common_dis_id`)
    JOIN `t_user_questionnaire_common_west_dis` AS c ON (c.`common_west_id` = b.`common_west_id`)
    WHERE EXISTS (
        SELECT 1
        FROM t_record AS r
        WHERE r.PATIENT_ID = #{patientId}
        AND (r.DIS_ID = a.common_dis_id OR r.WESTERN_DISEASE_ID = c.common_west_code)
    )
        <if test="westDisId != null  and westDisId != '' and  (chineseDisId == null ) ">
            AND c.common_west_code = #{westDisId}
        </if>
        <if test="westDisId == null and chineseDisId != '' and chineseDisId != null">
            AND a.common_dis_id = #{chineseDisId}
        </if>
        <if test="westDisId != null and westDisId != '' and chineseDisId != null and chineseDisId != '' ">
            AND (c.common_west_code = #{westDisId} OR a.common_dis_id = #{chineseDisId})
        </if>

    GROUP BY c.class_type

    -->

    select a.* from ( SELECT a.`class_type` as classType,a.class_type_name as classTypeName
    FROM `t_user_questionnaire_common_dis` AS a
    WHERE EXISTS (SELECT 1
    FROM t_record AS b
    WHERE
    (b.DIS_ID = a.common_dis_id )
    AND b.PATIENT_ID = #{patientId}
    )

    <if test="chineseDisId != '' and chineseDisId != null">
        AND a.common_dis_id = #{chineseDisId}
    </if>

    union
    SELECT c.`class_type` as classType,c.class_type_name as classTypeName
    from `t_user_questionnaire_common_west_dis` as c
    WHERE EXISTS (SELECT 1
    FROM t_record AS b
    WHERE
    (c.`common_west_code` = b.WESTERN_DISEASE_ID)
    AND b.PATIENT_ID = #{patientId}
    )
    <if test="westDisId != null  and westDisId != '' ">
        AND c.common_west_code = #{westDisId}
    </if>
    ) as a
    GROUP BY a.classType

</select>
<select id="getBianZhengRecommendByPatientNew" resultType="com.jiuzhekan.healthpres.beans.GetRecommendByPatient"
        parameterType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.RecommendSearchQuery">


    select a.* from ( SELECT a.`class_type` as classType,a.class_type_name as classTypeName
    FROM `t_user_questionnaire_common_dis` AS a
    <where>

    <if test="chineseDisId != '' and chineseDisId != null">
          a.common_dis_id = #{chineseDisId}
    </if>
    </where>
    union
    SELECT c.`class_type` as classType,c.class_type_name as classTypeName
    from `t_user_questionnaire_common_west_dis` as c
    <where>
    <if test="westDisId != null  and westDisId != '' ">
         c.common_west_code = #{westDisId}
    </if>
    </where>
    ) as a
    GROUP BY a.classType

</select>


</mapper>