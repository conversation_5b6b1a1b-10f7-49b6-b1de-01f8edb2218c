<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.healthpres.mapper.TUserQuestionnaireMainItemMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.healthpres.beans.TUserQuestionnaireMainItem">
        <id column="item_id" jdbcType="INTEGER"  property="itemId" />
        <result column="item_type" jdbcType="INTEGER" property="itemType" />
        <result column="item_title" jdbcType="VARCHAR" property="itemTitle" />
        <result column="item_content" jdbcType="VARCHAR" property="itemContent" />
        <result column="item_select_default_id" jdbcType="VARCHAR" property="itemSelectDefaultId" />
        <result column="questionnaire_main_id" jdbcType="VARCHAR" property="questionnaireMainId" />
        <result column="item_sort" jdbcType="INTEGER" property="itemSort" />
        <result column="show_type" jdbcType="INTEGER" property="showType" />
    </resultMap>


    <sql id="Base_Column_List">
    item_id,item_type,item_title,item_content,item_select_default_id,questionnaire_main_id,item_sort,show_type
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireMainItem">
        delete from t_user_questionnaire_main_item where item_id = #{ itemId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_user_questionnaire_main_item where item_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireMainItem">
        insert into t_user_questionnaire_main_item (<include refid="Base_Column_List" />) values
        (#{itemId},#{itemType},#{itemTitle},#{itemContent},#{itemSelectDefaultId},#{questionnaireMainId},#{itemSort},#{showType})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_user_questionnaire_main_item (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.itemId},#{item.itemType},#{item.itemTitle},#{item.itemContent},#{item.itemSelectDefaultId},#{item.questionnaireMainId},#{item.itemSort},#{item.showType})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireMainItem">
        update t_user_questionnaire_main_item
        <set>
             <if test="itemType != null">
                item_type = #{ itemType },
             </if>
             <if test="itemTitle != null">
                item_title = #{ itemTitle },
             </if>
             <if test="itemContent != null">
                item_content = #{ itemContent },
             </if>
             <if test="itemSelectDefaultId != null">
                item_select_default_id = #{ itemSelectDefaultId },
             </if>
             <if test="questionnaireMainId != null">
                questionnaire_main_id = #{ questionnaireMainId },
             </if>
             <if test="itemSort != null">
                item_sort = #{ itemSort },
             </if>
             <if test="showType != null">
                show_type = #{ showType },
             </if>
        </set>
        where item_id = #{ itemId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_user_questionnaire_main_item where item_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireMainItem" resultMap="BaseResultMap">
        SELECT item_id,item_type,item_title,item_content,item_select_default_id,questionnaire_main_id,item_sort,show_type
        from t_user_questionnaire_main_item
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>