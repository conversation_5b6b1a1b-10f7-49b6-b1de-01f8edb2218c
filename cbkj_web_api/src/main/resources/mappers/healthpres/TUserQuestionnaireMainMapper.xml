<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.healthpres.mapper.TUserQuestionnaireMainMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.healthpres.beans.TUserQuestionnaireMain">
        <id column="questionnaire_main_id" jdbcType="VARCHAR" property="questionnaireMainId"/>
        <result column="questionnaire_main_title" jdbcType="VARCHAR" property="questionnaireMainTitle"/>
        <result column="questionnaire_main_first_line" jdbcType="VARCHAR" property="questionnaireMainFirstLine"/>
        <result column="questionnaire_main_tips" jdbcType="VARCHAR" property="questionnaireMainTips"/>
        <result column="questionnaire_main_type" jdbcType="INTEGER" property="questionnaireMainType"/>
        <result column="questionnaire_main_second_type_code" jdbcType="VARCHAR"
                property="questionnaireMainSecondTypeCode"/>
        <result column="questionnaire_main_second_type_name" jdbcType="VARCHAR"
                property="questionnaireMainSecondTypeName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>


    <sql id="Base_Column_List">
        questionnaire_main_id,questionnaire_main_title,questionnaire_main_first_line,questionnaire_main_tips,questionnaire_main_type,questionnaire_main_second_type_code,questionnaire_main_second_type_name,create_time,create_user_name,create_user_id,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireMain">
        delete
        from t_user_questionnaire_main
        where questionnaire_main_id = #{ questionnaireMainId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_user_questionnaire_main where questionnaire_main_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireMain">
        insert into t_user_questionnaire_main (<include refid="Base_Column_List"/>) values
        (#{questionnaireMainId},#{questionnaireMainTitle},#{questionnaireMainFirstLine},#{questionnaireMainTips},#{questionnaireMainType},#{questionnaireMainSecondTypeCode},#{questionnaireMainSecondTypeName},#{createTime},#{createUserName},#{createUserId},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_user_questionnaire_main (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.questionnaireMainId},#{item.questionnaireMainTitle},#{item.questionnaireMainFirstLine},#{item.questionnaireMainTips},#{item.questionnaireMainType},#{item.questionnaireMainSecondTypeCode},#{item.questionnaireMainSecondTypeName},#{item.createTime},#{item.createUserName},#{item.createUserId},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireMain">
        update t_user_questionnaire_main
        <set>
            <if test="questionnaireMainTitle != null">
                questionnaire_main_title = #{ questionnaireMainTitle },
            </if>
            <if test="questionnaireMainFirstLine != null">
                questionnaire_main_first_line = #{ questionnaireMainFirstLine },
            </if>
            <if test="questionnaireMainTips != null">
                questionnaire_main_tips = #{ questionnaireMainTips },
            </if>
            <if test="questionnaireMainType != null">
                questionnaire_main_type = #{ questionnaireMainType },
            </if>
            <if test="questionnaireMainSecondTypeCode != null">
                questionnaire_main_second_type_code = #{ questionnaireMainSecondTypeCode },
            </if>
            <if test="questionnaireMainSecondTypeName != null">
                questionnaire_main_second_type_name = #{ questionnaireMainSecondTypeName },
            </if>
            <if test="createTime != null">
                create_time = #{ createTime },
            </if>
            <if test="createUserName != null">
                create_user_name = #{ createUserName },
            </if>
            <if test="createUserId != null">
                create_user_id = #{ createUserId },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
        </set>
        where questionnaire_main_id = #{ questionnaireMainId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_user_questionnaire_main where questionnaire_main_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireMain"
            resultMap="BaseResultMap">
        SELECT
        questionnaire_main_id,questionnaire_main_title,questionnaire_main_first_line,questionnaire_main_tips,questionnaire_main_type,questionnaire_main_second_type_code,questionnaire_main_second_type_name,create_time,create_user_name,create_user_id,status
        from t_user_questionnaire_main
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <resultMap id="BaseResultMapRes"
               type="com.jiuzhekan.healthpres.beans.heathPresControllerVo.HealthPreModelMainResponse">
        <id column="questionnaire_main_id" jdbcType="VARCHAR" property="questionnaireMainId"/>
        <result column="questionnaire_main_title" jdbcType="VARCHAR" property="questionnaireMainTitle"/>
        <result column="questionnaire_main_first_line" jdbcType="VARCHAR" property="questionnaireMainFirstLine"/>
        <result column="questionnaire_main_tips" jdbcType="VARCHAR" property="questionnaireMainTips"/>
        <result column="questionnaire_main_type" jdbcType="INTEGER" property="questionnaireMainType"/>
        <result column="questionnaire_main_second_type_code" jdbcType="VARCHAR" property="questionnaireMainSecondTypeCode"/>
        <result column="questionnaire_main_second_type_name" jdbcType="VARCHAR" property="questionnaireMainSecondTypeName"/>
        <collection property="questionnaireMainItemList" ofType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.HealthPreModelMainItemResponse">
            <result column="item_id" jdbcType="INTEGER" property="itemId"/>
            <result column="item_type" jdbcType="INTEGER" property="itemType"/>
            <result column="item_title" jdbcType="VARCHAR"  property="itemTitle"/>
            <result column="item_content" jdbcType="VARCHAR" property="itemContent"/>
            <result column="item_select_default_id" jdbcType="VARCHAR" property="itemSelectDefaultId"/>
            <result column="show_type" jdbcType="INTEGER" property="showType"/>
        </collection>
    </resultMap>

    <select id="getHealthPreModel"
            resultMap="BaseResultMapRes"
            parameterType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.GetHealthPreModel">
        SELECT
        a.`questionnaire_main_id`,
        a.`questionnaire_main_second_type_code`,
        a.`questionnaire_main_second_type_name`,
        a.`questionnaire_main_tips`,
        a.questionnaire_main_first_line,
        a.`questionnaire_main_title`,
        a.`questionnaire_main_type`,
        b.`item_content`,
        b.`item_id`,
        b.`item_select_default_id`,
        b.`item_title`,
        b.`item_type`,
        b.`show_type`
        FROM `t_user_questionnaire_main` AS a , `t_user_questionnaire_main_item` AS b
        WHERE a.`questionnaire_main_id` = b.`questionnaire_main_id`
        <if test="questionnaireMainType != null">
            AND a.`questionnaire_main_type` = #{questionnaireMainType}
        </if>
        <if test="questionnaireMainSecondTypeCode != null">
            AND a.`questionnaire_main_second_type_code` = #{questionnaireMainSecondTypeCode}
        </if>
        order by b.item_sort asc
    </select>
    <select id="getHealthPreModelAllList"
            resultMap="BaseResultMapRes"
            parameterType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.GetHealthPreModel">
        SELECT a.`questionnaire_main_id`,
               a.`questionnaire_main_second_type_code`,
               a.`questionnaire_main_second_type_name`,
               a.`questionnaire_main_tips`,
               a.`questionnaire_main_title`,
               a.`questionnaire_main_type`,
               b.`item_content`,
               b.`item_id`,
               b.`item_select_default_id`,
               b.`item_title`,
               b.`item_type`,
               b.`show_type`,
               a.questionnaire_main_first_line
        FROM `t_user_questionnaire_main` AS a,
             `t_user_questionnaire_main_item` AS b
        WHERE a.`questionnaire_main_id` = b.`questionnaire_main_id`
        order by b.item_sort asc
    </select>
    <select id="getCommonDicList" resultType="java.util.Map">
        SELECT common_dic_name AS commonDicName, common_dic_code AS commonDicCode
        FROM t_user_questionnaire_common_dic
        ORDER BY sort ASC
    </select>


</mapper>