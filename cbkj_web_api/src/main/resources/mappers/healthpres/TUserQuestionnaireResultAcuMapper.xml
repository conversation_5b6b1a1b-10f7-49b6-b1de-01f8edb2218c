<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.healthpres.mapper.TUserQuestionnaireResultAcuMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResultAcu">
        <id column="result_acu_id" jdbcType="VARCHAR"  property="resultAcuId" />
        <result column="acu_id" jdbcType="VARCHAR" property="acuId" />
        <result column="result_id" jdbcType="VARCHAR" property="resultId" />
        <result column="result_item_id" jdbcType="VARCHAR" property="resultItemId" />
        <result column="acu_code" jdbcType="VARCHAR" property="acuCode" />
        <result column="acu_name" jdbcType="VARCHAR" property="acuName" />
        <result column="acu_img" jdbcType="VARCHAR" property="acuImg" />
        <result column="acu_position" jdbcType="VARCHAR" property="acuPosition" />
    </resultMap>


    <sql id="Base_Column_List">
    result_acu_id,acu_id,result_id,result_item_id,acu_code,acu_name,acu_img,acu_position
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResultAcu">
        delete from t_user_questionnaire_result_acu where result_acu_id = #{ resultAcuId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_user_questionnaire_result_acu where result_acu_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>
    <delete id="deleteByRersultId" parameterType="String">
        delete from t_user_questionnaire_result_acu where result_id = #{resultId}
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResultAcu">
        insert into t_user_questionnaire_result_acu (<include refid="Base_Column_List" />) values
        (#{resultAcuId},#{acuId},#{resultId},#{resultItemId},#{acuCode},#{acuName},#{acuImg},#{acuPosition})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_user_questionnaire_result_acu (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.resultAcuId},#{item.acuId},#{item.resultId},#{item.resultItemId},#{item.acuCode},#{item.acuName},#{item.acuImg},#{item.acuPosition})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResultAcu">
        update t_user_questionnaire_result_acu
        <set>
             <if test="acuId != null">
                acu_id = #{ acuId },
             </if>
             <if test="resultId != null">
                result_id = #{ resultId },
             </if>
             <if test="resultItemId != null">
                result_item_id = #{ resultItemId },
             </if>
             <if test="acuCode != null">
                acu_code = #{ acuCode },
             </if>
             <if test="acuName != null">
                acu_name = #{ acuName },
             </if>
             <if test="acuImg != null">
                acu_img = #{ acuImg },
             </if>
             <if test="acuPosition != null">
                acu_position = #{ acuPosition },
             </if>
        </set>
        where result_acu_id = #{ resultAcuId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_user_questionnaire_result_acu where result_acu_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResultAcu" resultMap="BaseResultMap">
        SELECT result_acu_id,acu_id,result_id,result_item_id,acu_code,acu_name,acu_img,acu_position
        from t_user_questionnaire_result_acu
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>