<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.healthpres.mapper.TUserQuestionnaireResultItemMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResultItem">
        <id column="result_item_id" jdbcType="VARCHAR"  property="resultItemId" />
        <result column="result_item_title" jdbcType="VARCHAR" property="resultItemTitle" />
        <result column="result_item_content" jdbcType="VARCHAR" property="resultItemContent" />
        <result column="result_item_type" jdbcType="INTEGER" property="resultItemType" />
        <result column="result_item_select_id" jdbcType="VARCHAR" property="resultItemSelectId" />
        <result column="result_item_sort" jdbcType="INTEGER" property="resultItemSort" />
        <result column="result_id" jdbcType="VARCHAR" property="resultId" />
        <result column="show_type" jdbcType="VARCHAR" property="showType" />
    </resultMap>


    <sql id="Base_Column_List">
    result_item_id,result_item_title,result_item_content,result_item_type,result_item_select_id,result_item_sort,result_id,show_type
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResultItem">
        delete from t_user_questionnaire_result_item where result_item_id = #{ resultItemId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_user_questionnaire_result_item where result_item_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>
    <delete id="deleteByMainResultId" parameterType="String">
        delete from t_user_questionnaire_result_item where result_id = #{resultId}
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResultItem">
        insert into t_user_questionnaire_result_item (<include refid="Base_Column_List" />) values
        (#{resultItemId},#{resultItemTitle},#{resultItemContent},#{resultItemType},#{resultItemSelectId},#{resultItemSort},#{resultId},#{showType})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_user_questionnaire_result_item (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.resultItemId},#{item.resultItemTitle},#{item.resultItemContent},#{item.resultItemType},#{item.resultItemSelectId},#{item.resultItemSort},
             #{item.resultId},#{item.showType})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResultItem">
        update t_user_questionnaire_result_item
        <set>
             <if test="resultItemTitle != null">
                result_item_title = #{ resultItemTitle },
             </if>
             <if test="resultItemContent != null">
                result_item_content = #{ resultItemContent },
             </if>
             <if test="resultItemType != null">
                result_item_type = #{ resultItemType },
             </if>
             <if test="resultItemSelectId != null">
                result_item_select_id = #{ resultItemSelectId },
             </if>
             <if test="resultItemSort != null">
                result_item_sort = #{ resultItemSort },
             </if>
             <if test="resultId != null">
                result_id = #{ resultId },
             </if>
        </set>
        where result_item_id = #{ resultItemId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_user_questionnaire_result_item where result_item_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResultItem" resultMap="BaseResultMap">
        SELECT result_item_id,result_item_title,result_item_content,result_item_type,result_item_select_id,result_item_sort,result_id,show_type
        from t_user_questionnaire_result_item
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>