<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.healthpres.mapper.TUserQuestionnaireResultMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResult">
        <id column="result_id" jdbcType="VARCHAR" property="resultId"/>
        <result column="patient_id" jdbcType="VARCHAR" property="patientId"/>
        <result column="patient_name" jdbcType="VARCHAR" property="patientName"/>
        <result column="result_report_name" jdbcType="VARCHAR" property="resultReportName"/>
        <result column="result_report_time" jdbcType="TIMESTAMP" property="resultReportTime"/>
        <result column="insert_time" jdbcType="TIMESTAMP" property="insertTime"/>
        <result column="result_report_type_info" jdbcType="VARCHAR" property="resultReportTypeInfo"/>
        <result column="create_doctor_id" jdbcType="VARCHAR" property="createDoctorId"/>
        <result column="create_doctor_name" jdbcType="VARCHAR" property="createDoctorName"/>
        <result column="last_update_doctor_id" jdbcType="VARCHAR" property="lastUpdateDoctorId"/>
        <result column="last_update_doctor_name" jdbcType="VARCHAR" property="lastUpdateDoctorName"/>
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="del_doctor_id" jdbcType="VARCHAR" property="delDoctorId"/>
        <result column="del_doctor_name" jdbcType="VARCHAR" property="delDoctorName"/>
        <result column="del_time" jdbcType="TIMESTAMP" property="delTime"/>
        <result column="questionnaire_main_title" jdbcType="VARCHAR" property="questionnaireMainTitle"/>
        <result column="questionnaire_main_first_line" jdbcType="VARCHAR" property="questionnaireMainFirstLine"/>
        <result column="questionnaire_main_tips" jdbcType="VARCHAR" property="questionnaireMainTips"/>
        <result column="questionnaire_main_type" jdbcType="INTEGER" property="questionnaireMainType"/>
        <result column="questionnaire_main_second_type_code" jdbcType="VARCHAR"
                property="questionnaireMainSecondTypeCode"/>
        <result column="questionnaire_main_second_type_name" jdbcType="VARCHAR"
                property="questionnaireMainSecondTypeName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="word_path" jdbcType="VARCHAR" property="wordPath"/>
        <result column="pdf_path" jdbcType="VARCHAR" property="pdfPath"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="result_code" jdbcType="VARCHAR" property="resultCode"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="ins_name" jdbcType="VARCHAR" property="insName"/>
        <result column="app_name" jdbcType="VARCHAR" property="appName"/>
    </resultMap>


    <sql id="Base_Column_List">
        result_id,patient_id,patient_name,result_report_name,result_report_time,insert_time,result_report_type_info,create_doctor_id,create_doctor_name,last_update_doctor_id,
        last_update_doctor_name,last_update_time,del_doctor_id,del_doctor_name,del_time,questionnaire_main_title,questionnaire_main_first_line,questionnaire_main_tips,
        questionnaire_main_type,questionnaire_main_second_type_code,questionnaire_main_second_type_name,
        status,word_path,pdf_path,
        app_id,ins_code,dept_id,result_code,dept_name,ins_name,app_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResult">
        delete
        from t_user_questionnaire_result
        where result_id = #{ resultId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_user_questionnaire_result where result_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResult">
        insert into t_user_questionnaire_result (<include refid="Base_Column_List"/>) values
        (#{resultId},#{patientId},#{patientName},#{resultReportName},#{resultReportTime},#{insertTime},#{resultReportTypeInfo},
         #{createDoctorId},#{createDoctorName},#{lastUpdateDoctorId},#{lastUpdateDoctorName},#{lastUpdateTime},#{delDoctorId},
         #{delDoctorName},#{delTime},#{questionnaireMainTitle},#{questionnaireMainFirstLine},#{questionnaireMainTips},#{questionnaireMainType},
         #{questionnaireMainSecondTypeCode},#{questionnaireMainSecondTypeName},#{status},#{wordPath},#{pdfPath},
         #{appId},#{insCode},#{deptId},#{resultCode},
         #{deptName},#{insName},#{appName})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_user_questionnaire_result (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.resultId},#{item.patientId},#{item.patientName},#{item.resultReportName},#{item.resultReportTime},#{item.insertTime},
             #{item.resultReportTypeInfo},#{item.createDoctorId},#{item.createDoctorName},#{item.lastUpdateDoctorId},#{item.lastUpdateDoctorName},
             #{item.lastUpdateTime},#{item.delDoctorId},#{item.delDoctorName},#{item.delTime},#{item.questionnaireMainTitle},
             #{item.questionnaireMainFirstLine},#{item.questionnaireMainTips},#{item.questionnaireMainType},#{item.questionnaireMainSecondTypeCode},
             #{item.questionnaireMainSecondTypeName},#{item.status},#{item.wordPath},#{item.pdfPath},
            #{item.appId},#{item.insCode},#{item.deptId},#{item.resultCode},
            #{item.deptName},#{item.insName},#{item.appName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResult">
        update t_user_questionnaire_result
        <set>
            <if test="patientId != null">
                patient_id = #{ patientId },
            </if>
            <if test="patientName != null">
                patient_name = #{ patientName },
            </if>
            <if test="resultReportName != null">
                result_report_name = #{ resultReportName },
            </if>
            <if test="resultReportTime != null">
                result_report_time = #{ resultReportTime },
            </if>
            <if test="insertTime != null">
                insert_time = #{ insertTime },
            </if>
            <if test="resultReportTypeInfo != null">
                result_report_type_info = #{ resultReportTypeInfo },
            </if>
            <if test="createDoctorId != null">
                create_doctor_id = #{ createDoctorId },
            </if>
            <if test="createDoctorName != null">
                create_doctor_name = #{ createDoctorName },
            </if>
            <if test="lastUpdateDoctorId != null">
                last_update_doctor_id = #{ lastUpdateDoctorId },
            </if>
            <if test="lastUpdateDoctorName != null">
                last_update_doctor_name = #{ lastUpdateDoctorName },
            </if>
            <if test="lastUpdateTime != null">
                last_update_time = #{ lastUpdateTime },
            </if>
            <if test="delDoctorId != null">
                del_doctor_id = #{ delDoctorId },
            </if>
            <if test="delDoctorName != null">
                del_doctor_name = #{ delDoctorName },
            </if>
            <if test="delTime != null">
                del_time = #{ delTime },
            </if>
            <if test="questionnaireMainTitle != null">
                questionnaire_main_title = #{ questionnaireMainTitle },
            </if>
            <if test="questionnaireMainFirstLine != null">
                questionnaire_main_first_line = #{ questionnaireMainFirstLine },
            </if>
            <if test="questionnaireMainTips != null">
                questionnaire_main_tips = #{ questionnaireMainTips },
            </if>
            <if test="questionnaireMainType != null">
                questionnaire_main_type = #{ questionnaireMainType },
            </if>
            <if test="questionnaireMainSecondTypeCode != null">
                questionnaire_main_second_type_code = #{ questionnaireMainSecondTypeCode },
            </if>
            <if test="questionnaireMainSecondTypeName != null">
                questionnaire_main_second_type_name = #{ questionnaireMainSecondTypeName },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="wordPath != null">
                word_path = #{ wordPath },
            </if>
            <if test="pdfPath != null">
                pdf_path = #{ pdfPath },
            </if>
        </set>
        where result_id = #{ resultId }
    </update>
    <update id="updateWordPdf" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResult">
        update t_user_questionnaire_result
        set word_path = #{ wordPath },
            pdf_path  = #{ pdfPath }
        where result_id = #{resultId}
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_user_questionnaire_result where result_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.healthpres.beans.TUserQuestionnaireResult"
            resultMap="BaseResultMap">
        SELECT
        result_id,patient_id,patient_name,result_report_name,
        result_report_time,insert_time,result_report_type_info,create_doctor_id,create_doctor_name,last_update_doctor_id,
        last_update_doctor_name,last_update_time,del_doctor_id,del_doctor_name,del_time,questionnaire_main_title,questionnaire_main_first_line,
        questionnaire_main_tips,questionnaire_main_type,questionnaire_main_second_type_code,questionnaire_main_second_type_name,status,word_path,pdf_path,
        app_id,ins_code,dept_id,result_code,dept_name,ins_name,app_name
        from t_user_questionnaire_result
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>


    <select id="getChildIndexPageList"
            parameterType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.ChildIndexPageListVo"
            resultType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.GetChildIndexPageList"
    >select a.* from
        (SELECT a.`patient_id` as patientId,
        a.`patient_name` as patientName,
        a.`result_report_name` as resultReportName,
        a.questionnaire_main_type as questionnaireMainType,
        max(a.`result_report_time`) as resultReportTime,
        a.`questionnaire_main_second_type_name` as questionnaireMainSecondTypeName,
        a.`questionnaire_main_second_type_code` as questionnaireMainSecondTypeCode,
        a.result_report_type_info as resultReportTypeInfo,
        b.PATIENT_BIRTHDAY as patientBirthDay,
        b.PATIENT_GENDER as patientSex,
        a.create_doctor_name as createDoctorName,
        create_doctor_id as createDoctorId
        FROM `t_user_questionnaire_result` AS a ,t_patients as b where a.patient_id = b.patient_id and a.status = 0
        and questionnaire_main_type = #{questionnaireMainType}
        <if test="patientName != null">
            and a.patient_name like CONCAT('%',trim(#{patientName}),'%')
        </if>
        group by a.`patient_id`) as a order by a.resultReportTime desc
    </select>
    <select id="getDetailPageList"
            parameterType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.GetDetailPageListRequest"
            resultType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.GetDetailPageListResponse"
    >SELECT a.`patient_id`                          as patientId,
                  a.`patient_name`                        as patientName,
                  a.`result_report_name`                  as resultReportName,
                  (a.`result_report_time`)             as resultReportTime,
                  a.`questionnaire_main_second_type_name` as questionnaireMainSecondTypeName,
                  a.`questionnaire_main_second_type_code` as questionnaireMainSecondTypeCode,
               b.PATIENT_BIRTHDAY as patientBirthDay,
               b.PATIENT_GENDER as patientSex,
               a.create_doctor_id as createDoctorId,
               a.create_doctor_name as createDoctorName,
               a.result_id as resultId,
               a.result_report_type_info as resultReportTypeInfo
           FROM `t_user_questionnaire_result` AS a,
                t_patients as b
           where a.patient_id = b.patient_id and a.status = 0
             and a.patient_id = #{patientId}
             <if test="questionnaireMainType != null">
                 and a.questionnaire_main_type = #{questionnaireMainType}
             </if>
     order by a.result_report_time desc
    </select>
    <select id="getReportList"
            resultType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.GetDetailPageListResponse"
            parameterType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.GetReportListRequestVo">


        SELECT a.`patient_id` as patientId,
        a.`patient_name` as patientName,
        a.`result_report_name` as resultReportName,
        a.`result_report_time` as resultReportTime,
        a.`questionnaire_main_second_type_name` as questionnaireMainSecondTypeName,
        a.`questionnaire_main_second_type_code` as questionnaireMainSecondTypeCode,
        b.PATIENT_BIRTHDAY as patientBirthDay,
        b.PATIENT_GENDER as patientSex,
        a.result_id as resultId,
        a.create_doctor_name as createDoctorName,
        a.create_doctor_id as createDoctorId,
        a.result_report_type_info as resultReportTypeInfo

        FROM `t_user_questionnaire_result` AS a ,t_patients as b where a.patient_id = b.patient_id
and a.status = 0
<!--        <if test="patientId != null">-->
<!--            and a.patient_id = #{patientId}-->
<!--        </if>-->
        <if test="appId != null">
            and a.app_id = #{appId}
        </if>
        <if test="insCode != null">
            and a.ins_code = #{insCode}
        </if>
        <if test="deptId != null">
            and a.dept_id = #{deptId}
        </if>
        <if test="beginTime != null">
            and a.result_report_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and a.result_report_time &lt; #{endTime}
        </if>

        <if test="doctorName != null">
            and a.create_doctor_name like CONCAT('%',trim(#{doctorName}),'%')
        </if>
        <if test="patientName != null">
            and a.patient_name like CONCAT('%',trim(#{patientName}),'%')
        </if>

        <if test="type != null">
            and a.questionnaire_main_second_type_code = #{type}
        </if>
        order by a.result_report_time desc

    </select>
    <resultMap id="lookHealthPreMap" type="com.jiuzhekan.healthpres.beans.heathPresControllerVo.LookHealthPreMainResponse">
        <id property="resultId" column="result_id"/>
        <result property="patientId" column="patient_id"/>
        <result property="patientName" column="patient_name"/>
        <result property="resultReportName" column="result_report_name"/>
        <result property="resultReportTime" column="result_report_time"/>
        <result property="insertTime" column="insert_time"/>
        <result property="resultReportTypeInfo" column="result_report_type_info"/>
        <result property="questionnaireMainTitle" column="questionnaire_main_title"/>
        <result property="questionnaireMainFirstLine" column="questionnaire_main_first_line"/>
        <result property="questionnaireMainTips" column="questionnaire_main_tips"/>
        <result property="questionnaireMainType" column="questionnaire_main_type"/>
        <result property="questionnaireMainSecondTypeCode" column="questionnaire_main_second_type_code"/>
        <result property="questionnaireMainSecondTypeName" column="questionnaire_main_second_type_name"/>
        <result property="wordPath" column="word_path"/>
        <result property="pdfPath" column="pdf_path"/>
        <result property="resultCode" column="resultCode"/>
        <result property="appName" column="appName"/>
        <result property="deptName" column="deptName"/>
        <result property="insName" column="insName"/>
        <result property="createDoctorName" column="createDoctorName"/>
        <collection property="questionnaireMainItemList" ofType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.LookHealthPreMainItemResponse">
            <result property="resultItemId" column="result_item_id"/>
            <result property="itemTitle" column="result_item_title"/>
            <result property="itemContent" column="result_item_content"/>
            <result property="itemType" column="result_item_type"/>
            <result property="itemSelectId" column="result_item_select_id"/>
            <result property="itemSort" column="result_item_sort"/>
            <result property="showType" column="show_type"/>
            <collection property="lookHealthPreMainItemAcuResponseList" ofType="com.jiuzhekan.healthpres.beans.heathPresControllerVo.LookHealthPreMainItemAcuResponse">
                <result property="resultAcuId" column="result_acu_id"/>
                <result property="acuId" column="acu_id"/>
                <result property="acuCode" column="acu_code"/>
                <result property="acuName" column="acu_name"/>
                <result property="acuImg" column="acu_img"/>
                <result property="acuPosition" column="acu_position"/>
                <result property="resultId" column="result_id_c"/>
                <result property="resultItemId" column="result_item_id_c"/>
            </collection>
        </collection>
    </resultMap>
    <select id="lookHealthPre"
            parameterType="String"
            resultMap="lookHealthPreMap">
        SELECT
            a.result_id,
            a.patient_id,
            a.patient_name,
            a.result_report_name,
            a.result_report_type_info,
            a.questionnaire_main_title,
            a.questionnaire_main_first_line,
            a.questionnaire_main_tips,
            a.questionnaire_main_type,
            a.questionnaire_main_second_type_code,
            a.questionnaire_main_second_type_name,
            a.word_path,
            a.pdf_path,
            b.`result_item_content`,
            b.`result_item_id`,
            b.`result_item_title`,
            b.`result_item_type`,
            b.`result_item_select_id`,
            c.result_acu_id,
            c.`acu_id`,
            c.`acu_code`,
            c.`acu_name`,
            c.`acu_img`,
            c.`acu_position`,
            c.`result_id` as result_id_c,
            c.`result_item_id` as result_item_id_c,
            a.result_code as resultCode,
            a.app_name as appName,
            a.dept_name as deptName,
            a.ins_name as insName,
            a.create_doctor_name as createDoctorName,
            b.show_type
        FROM
            `t_user_questionnaire_result` AS a join
            `t_user_questionnaire_result_item` AS b on (a.result_id = b.result_id and a.result_id = #{resultId})
            left join `t_user_questionnaire_result_acu` AS c on ( c.result_item_id = b.result_item_id)

        order by b.result_item_sort  asc
    </select>

</mapper>