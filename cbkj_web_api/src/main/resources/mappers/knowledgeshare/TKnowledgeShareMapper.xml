<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.knowledgeshare.mapper.TKnowledgeShareMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.knowledgeshare.beans.TKnowledgeShare">
        <id column="knowledge_share_id" jdbcType="VARCHAR"  property="knowledgeShareId" />
        <result column="share_type" jdbcType="VARCHAR" property="shareType" />
        <result column="share_user_name" jdbcType="VARCHAR" property="shareUserName" />
        <result column="share_user_id" jdbcType="VARCHAR" property="shareUserId" />
        <result column="share_time" jdbcType="TIMESTAMP" property="shareTime" />
        <result column="share_sort" jdbcType="INTEGER" property="shareSort" />
        <result column="share_title" jdbcType="VARCHAR" property="shareTitle" />
        <result column="share_info" jdbcType="VARCHAR" property="shareInfo" />
        <result column="del_user_id" jdbcType="VARCHAR" property="delUserId" />
        <result column="del_user_name" jdbcType="VARCHAR" property="delUserName" />
        <result column="del_time" jdbcType="TIMESTAMP" property="delTime" />
        <result column="update_user_time" jdbcType="TIMESTAMP" property="updateUserTime" />
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
        <result column="share_status" jdbcType="INTEGER" property="shareStatus" />
        <result column="share_file_path" jdbcType="VARCHAR" property="shareFilePath" />
    </resultMap>


    <sql id="Base_Column_List">
    knowledge_share_id,share_type,share_user_name,share_user_id,share_time,share_sort,share_title,
    share_info,del_user_id,del_user_name,del_time,update_user_id,update_user_name,share_status,share_file_path,update_user_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.knowledgeshare.beans.TKnowledgeShare">
        delete from t_knowledge_share where knowledge_share_id = #{ knowledgeShareId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_knowledge_share where knowledge_share_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.knowledgeshare.beans.TKnowledgeShare">
        insert into t_knowledge_share (<include refid="Base_Column_List" />) values
        (#{knowledgeShareId},#{shareType},#{shareUserName},#{shareUserId},#{shareTime},#{shareSort},
         #{shareTitle},#{shareInfo},#{delUserId},#{delUserName},#{delTime},#{updateUserId},#{updateUserName},#{shareStatus},#{shareFilePath},
         #{updateUserTime})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_knowledge_share (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.knowledgeShareId},#{item.shareType},#{item.shareUserName},#{item.shareUserId},#{item.shareTime},
             #{item.shareSort},#{item.shareTitle},#{item.shareInfo},#{item.delUserId},#{item.delUserName},#{item.delTime},
             #{item.updateUserId},#{item.updateUserName},#{item.shareStatus},#{item.shareFilePath},#{item.updateUserTime})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.knowledgeshare.beans.TKnowledgeShare">
        update t_knowledge_share
        <set>
             <if test="shareType != null">
                share_type = #{ shareType },
             </if>
             <if test="shareUserName != null and shareUserName != ''">
                share_user_name = #{ shareUserName },
             </if>
             <if test="shareUserId != null and shareUserId != ''">
                share_user_id = #{ shareUserId },
             </if>
             <if test="shareTime != null">
                share_time = #{ shareTime },
             </if>
             <if test="shareSort != null">
                share_sort = #{ shareSort },
             </if>
             <if test="shareTitle != null and shareTitle != ''">
                share_title = #{ shareTitle },
             </if>
             <if test="shareInfo != null and shareInfo != ''">
                share_info = #{ shareInfo },
             </if>
             <if test="delUserId != null and delUserId != ''">
                del_user_id = #{ delUserId },
             </if>
             <if test="delUserName != null and delUserName != ''">
                del_user_name = #{ delUserName },
             </if>
             <if test="delTime != null">
                del_time = #{ delTime },
             </if>
             <if test="updateUserTime != null">
                 update_user_time = #{updateUserTime},
             </if>
             <if test="updateUserId != null and updateUserId != ''">
                update_user_id = #{ updateUserId },
             </if>
             <if test="updateUserName != null and updateUserName != ''">
                update_user_name = #{ updateUserName },
             </if>
             <if test="shareStatus != null">
                share_status = #{ shareStatus },
             </if>
             <if test="shareFilePath != null and shareFilePath != ''">
                share_file_path = #{ shareFilePath },
             </if>
        </set>
        where knowledge_share_id = #{ knowledgeShareId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_knowledge_share where knowledge_share_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.knowledgeshare.beans.TKnowledgeShare" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
            from t_knowledge_share
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getShareList" resultMap="BaseResultMap" parameterType="com.jiuzhekan.knowledgeshare.beans.ShareListReq">
        SELECT
        <include refid="Base_Column_List" />
              from t_knowledge_share
        <where>
            <if test=" shareType != null ">
                and share_type = #{shareType}
            </if>
            <if test="shareTitle != null ">
                and share_title like CONCAT('%',trim(#{shareTitle}),'%')
            </if>
            <if test="shareUserName != null ">
                and share_user_name like CONCAT('%',trim(#{shareUserName}),'%')
            </if>
            <if test="shareStatus != null ">
                and share_status = #{shareStatus}
            </if>
            <if test="shareStatus == null ">
                and share_status in(0,2)
            </if>

        </where>
        order by share_type,share_sort asc
    </select>
    <select id="getChineseMedicineList" resultMap="BaseResultMap" parameterType="com.jiuzhekan.knowledgeshare.beans.ChineseMedicineListReq">
        SELECT
        <include refid="Base_Column_List" />
        from t_knowledge_share
        <where>
             share_type = 1
            <if test="bookName != null ">
                and share_title like CONCAT('%',trim(#{bookName}),'%')
            </if>
            and share_status = 0
        </where> order by share_sort asc
    </select>
    <select id="getSharedLiteratureList" resultMap="BaseResultMap" parameterType="com.jiuzhekan.knowledgeshare.beans.ChineseMedicineListReq">
        SELECT
        <include refid="Base_Column_List" />
        from t_knowledge_share
        <where>
            share_type = 2
            <if test="bookName != null ">
                and share_title like CONCAT('%',trim(#{bookName}),'%')
            </if>
            and share_status = 0
        </where>
        order by share_sort asc
    </select>

</mapper>