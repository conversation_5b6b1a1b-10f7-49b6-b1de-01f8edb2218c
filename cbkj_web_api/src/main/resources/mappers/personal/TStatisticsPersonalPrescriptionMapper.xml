<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.statistics.mapper.personal.TStatisticsPersonalPrescriptionMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.statistics.beans.personal.TStatisticsPersonalPrescription">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="total_num" jdbcType="INTEGER" property="totalNum" />
        <result column="self_num" jdbcType="INTEGER" property="selfNum" />
        <result column="dept_num" jdbcType="INTEGER" property="deptNum" />
        <result column="ins_num" jdbcType="INTEGER" property="insNum" />
        <result column="app_num" jdbcType="INTEGER" property="appNum" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="insert_date" jdbcType="DATE" property="insertDate" />
    </resultMap>


    <sql id="Base_Column_List">
    id,app_id,app_name,ins_code,ins_name,dept_id,dept_name,user_id,user_name,total_num,self_num,dept_num,ins_num,app_num,create_date
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.personal.TStatisticsPersonalPrescription">
        delete from t_statistics_personal_prescription where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_statistics_personal_prescription where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.statistics.beans.personal.TStatisticsPersonalPrescription">
        insert into t_statistics_personal_prescription (<include refid="Base_Column_List" />) values
        (#{id},#{appId},#{appName},#{insCode},#{insName},#{deptId},#{deptName},#{userId},#{userName},#{totalNum},#{selfNum},#{deptNum},#{insNum},#{appNum},#{createDate})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_statistics_personal_prescription (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.deptId},#{item.deptName},#{item.userId},#{item.userName},#{item.totalNum},#{item.selfNum},#{item.deptNum},#{item.insNum},#{item.appNum},#{item.createDate})
        </foreach>
    </insert>
    <insert id="insertStatistics">
        insert into t_statistics_personal_prescription (
        app_id,ins_code,user_id,user_name,total_num,self_num,dept_num,ins_num,app_num,create_date,insert_date
        ) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.appId},#{item.insCode},#{item.userId},#{item.userName},#{item.totalNum},#{item.selfNum},#{item.deptNum},#{item.insNum},#{item.appNum},#{item.dataDate},now())
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.personal.TStatisticsPersonalPrescription">
        update t_statistics_personal_prescription
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="deptName != null">
                dept_name = #{ deptName },
             </if>
             <if test="userId != null">
                user_id = #{ userId },
             </if>
             <if test="userName != null">
                user_name = #{ userName },
             </if>
             <if test="totalNum != null">
                total_num = #{ totalNum },
             </if>
             <if test="selfNum != null">
                self_num = #{ selfNum },
             </if>
             <if test="deptNum != null">
                dept_num = #{ deptNum },
             </if>
             <if test="insNum != null">
                ins_num = #{ insNum },
             </if>
             <if test="appNum != null">
                app_num = #{ appNum },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_statistics_personal_prescription where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.statistics.beans.personal.TStatisticsPersonalPrescription" resultMap="BaseResultMap">
        SELECT id,app_id,app_name,ins_code,ins_name,dept_id,dept_name,user_id,user_name,total_num,self_num,dept_num,ins_num,app_num,create_date
        from t_statistics_personal_prescription
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <resultMap id="BaseResultMap2" type="com.jiuzhekan.statistics.beans.personal.StatisticsPersonalPrescription">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
    </resultMap>
    <select id="selectPersonalPrescriptionResult" parameterType="Map" resultMap="BaseResultMap2">
        select
            b.`id`,a.`APP_ID`,a.`INS_CODE`,a.`DEPT_ID`,a.`PRE_OWNER` userId,
            CAST(a.`INSERT_DATE` AS DATE) dataDate,
            COUNT(a.PERS_PRE_ID) number,
            a.`PRE_OWNER_NAME` userName,
            ifnull(b.`total_num`,0) totalNum,
            ifnull(b.`self_num`,0) selfNum,
            ifnull(b.`dept_num`,0) deptNum,
            ifnull(b.`ins_num`,0) insNum,
            ifnull(b.`app_num`,0) appNum
        from `t_personal_prescription` as a
                 left join `t_statistics_personal_prescription` as b on a.`APP_ID`=b.`app_id` and a.`INS_CODE`=b.`ins_code` and a.`PRE_OWNER`=b.`user_id` and
                                                                        b.`create_date`= CAST(IFNULL(a.`INSERT_DATE`,'1900-01-01 00:00:00') AS DATE)
        where
                TIMESTAMPDIFF(DAY,a.INSERT_DATE, DATE_ADD(#{curDate},INTERVAL 1 DAY))&lt;= #{timeDiff} and
            a.`IS_SHARE`=#{shareType}
        GROUP BY a.`APP_ID`,a.`INS_CODE`,a.PRE_OWNER,dataDate
    </select>
    <select id="getLastData" resultMap="BaseResultMap">
        select * from t_statistics_personal_prescription order by insert_date limit 0,1
    </select>

</mapper>