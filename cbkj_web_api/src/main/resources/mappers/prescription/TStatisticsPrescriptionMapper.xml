<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.statistics.mapper.prescription.TStatisticsPrescriptionMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.statistics.beans.prescription.TStatisticsPrescription">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="total_num" jdbcType="INTEGER" property="totalNum" />
        <result column="inner_num" jdbcType="INTEGER" property="innerNum" />
        <result column="exter_num" jdbcType="INTEGER" property="exterNum" />
        <result column="acu_num" jdbcType="INTEGER" property="acuNum" />
        <result column="patent_num" jdbcType="INTEGER" property="patentNum" />
        <result column="prepare_num" jdbcType="INTEGER" property="prepareNum" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="insert_date" jdbcType="DATE" property="insertDate" />
    </resultMap>


    <sql id="Base_Column_List">
    id,app_id,app_name,ins_code,ins_name,dept_id,dept_name,user_id,user_name,total_num,inner_num,exter_num,acu_num,patent_num,prepare_num,create_date
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.prescription.TStatisticsPrescription">
        delete from t_statistics_prescription where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_statistics_prescription where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.statistics.beans.prescription.TStatisticsPrescription">
        insert into t_statistics_prescription (<include refid="Base_Column_List" />) values
        (#{id},#{appId},#{appName},#{insCode},#{insName},#{deptId},#{deptName},#{userId},#{userName},#{totalNum},#{innerNum},#{exterNum},#{acuNum},#{patentNum},#{prepareNum},#{createDate})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_statistics_prescription (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.deptId},#{item.deptName},#{item.userId},#{item.userName},#{item.totalNum},#{item.innerNum},#{item.exterNum},#{item.acuNum},#{item.patentNum},#{item.prepareNum},#{item.createDate})
        </foreach>
    </insert>
    <insert id="insertStatisticsPrescription" parameterType="List">
        insert into t_statistics_prescription (
        app_id,ins_code,user_id,user_name,total_num,inner_num,exter_num,acu_num,patent_num,prepare_num,create_date,insert_date
        ) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.appId},#{item.insCode},#{item.userId},#{item.userName},#{item.totalNum},#{item.innerNum},#{item.exterNum},#{item.acuNum},#{item.patentNum},#{item.prepareNum},#{item.dataDate},now())
        </foreach>
    </insert>

    <insert id="insertStatisticsPrescriptionCDSS" parameterType="List">
        insert into t_statistics_prescription_cdss (
        app_id,ins_code,user_id,user_name,total_num,inner_num,exter_num,acu_num,patent_num,prepare_num,create_date,insert_date
        ) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.appId},#{item.insCode},#{item.userId},#{item.userName},#{item.totalNum},#{item.innerNum},#{item.exterNum},#{item.acuNum},#{item.patentNum},#{item.prepareNum},#{item.dataDate},now())
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.prescription.TStatisticsPrescription">
        update t_statistics_prescription
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="deptName != null">
                dept_name = #{ deptName },
             </if>
             <if test="userId != null">
                user_id = #{ userId },
             </if>
             <if test="userName != null">
                user_name = #{ userName },
             </if>
             <if test="totalNum != null">
                total_num = #{ totalNum },
             </if>
             <if test="innerNum != null">
                inner_num = #{ innerNum },
             </if>
             <if test="exterNum != null">
                exter_num = #{ exterNum },
             </if>
             <if test="acuNum != null">
                acu_num = #{ acuNum },
             </if>
             <if test="patentNum != null">
                patent_num = #{ patentNum },
             </if>
             <if test="prepareNum != null">
                prepare_num = #{ prepareNum },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
        </set>
        where id = #{ id }
    </update>


    <update id="updateByPrimaryKeyCDSS" parameterType="com.jiuzhekan.statistics.beans.prescription.TStatisticsPrescription">
        update t_statistics_prescription_cdss
        <set>
            <if test="appId != null">
                app_id = #{ appId },
            </if>
            <if test="appName != null">
                app_name = #{ appName },
            </if>
            <if test="insCode != null">
                ins_code = #{ insCode },
            </if>
            <if test="insName != null">
                ins_name = #{ insName },
            </if>
            <if test="deptId != null">
                dept_id = #{ deptId },
            </if>
            <if test="deptName != null">
                dept_name = #{ deptName },
            </if>
            <if test="userId != null">
                user_id = #{ userId },
            </if>
            <if test="userName != null">
                user_name = #{ userName },
            </if>
            <if test="totalNum != null">
                total_num = #{ totalNum },
            </if>
            <if test="innerNum != null">
                inner_num = #{ innerNum },
            </if>
            <if test="exterNum != null">
                exter_num = #{ exterNum },
            </if>
            <if test="acuNum != null">
                acu_num = #{ acuNum },
            </if>
            <if test="patentNum != null">
                patent_num = #{ patentNum },
            </if>
            <if test="prepareNum != null">
                prepare_num = #{ prepareNum },
            </if>
            <if test="createDate != null">
                create_date = #{ createDate },
            </if>
        </set>
        where id = #{ id }
    </update>



    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_statistics_prescription where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.statistics.beans.prescription.TStatisticsPrescription" resultMap="BaseResultMap">
        SELECT id,app_id,app_name,ins_code,ins_name,dept_id,dept_name,user_id,user_name,total_num,inner_num,exter_num,acu_num,patent_num,prepare_num,create_date
        from t_statistics_prescription
        <where>
            <if test="appId != null">
                and app_id = #{ appId }
            </if>
            <if test="insCode != null">
                and ins_code = #{insCode}
            </if>
            <if test="userId != null">
                and user_id = #{ userId }
            </if>
            <if test="createDate != null">
                and create_date = #{ createDate }
            </if>
        </where>
    </select>
    <resultMap id="BaseResultMap2" type="com.jiuzhekan.statistics.beans.prescription.StatisticsPrescription">
    <id column="id" jdbcType="INTEGER"  property="id" />
    </resultMap>

    <select id="selectStatisticsPrescriptionResult"
            resultMap="BaseResultMap2" parameterType="Map">
        SELECT b.id,a.`APP_ID` as appId,a.`INS_CODE` insCode,a.`DEPT_ID` deptId,CAST(a.`PRE_TIME` AS DATE) dataDate ,a.`PRE_DOCTOR` userId,a.PRE_DOCTORNAME as userName
             ,COUNT(a.PRE_ID) number,
    ifnull(b.`total_num`,0) totalNum,
               ifnull(b.`inner_num`,0) innerNum,
               ifnull(b.`exter_num`,0) exterNum,
               ifnull(b.`acu_num`,0) acuNum,
               ifnull(b.`patent_num`,0) patentNum,
               ifnull(b.`prepare_num`,0) prepareNum
        FROM t_prescription AS a LEFT JOIN t_statistics_prescription AS b ON(
                    a.`APP_ID`=b.`app_id` AND a.`INS_CODE`=b.`ins_code` AND a.`PRE_DOCTOR`=b.`user_id` AND b.create_date=CAST(IFNULL(a.PRE_TIME,'1900-01-01 00:00:00') AS DATE)
            )
        WHERE a.`PRE_TYPE`=#{preType}
        and TIMESTAMPDIFF(DAY,a.PRE_TIME, DATE_ADD(#{curDate},INTERVAL 1 DAY))&lt;= #{timeDiff} AND a.app_id IS NOT NULL
         <!-- AND a.REC_EXT_TYPE >= 50 AND a.REC_EXT_TYPE != 110 AND a.IS_DEL='0' -->
        and a.IS_PAY = '1'
        GROUP BY a.`APP_ID`,a.`INS_CODE`,a.`PRE_DOCTOR`,dataDate

    </select>

    <select id="selectStatisticsPrescriptionResultCDSS"
            resultMap="BaseResultMap2" parameterType="Map">
        SELECT b.id,a.`APP_ID` as appId,a.`INS_CODE` insCode,a.`DEPT_ID` deptId,CAST(a.`PRE_TIME` AS DATE) dataDate ,a.`PRE_DOCTOR` userId,a.PRE_DOCTORNAME userName,
        COUNT(a.PRE_ID) number,
        ifnull(b.`total_num`,0) totalNum,
        ifnull(b.`inner_num`,0) innerNum,
        ifnull(b.`exter_num`,0) exterNum,
        ifnull(b.`acu_num`,0) acuNum,
        ifnull(b.`patent_num`,0) patentNum,
        ifnull(b.`prepare_num`,0) prepareNum
        FROM t_prescription AS a LEFT JOIN t_statistics_prescription_cdss AS b ON(
        a.`APP_ID`=b.`app_id` AND a.`INS_CODE`=b.`ins_code` AND a.`PRE_DOCTOR`=b.`user_id` AND b.create_date=CAST(IFNULL(a.PRE_TIME,'1900-01-01 00:00:00') AS DATE)
        )
        WHERE a.`PRE_TYPE`=#{preType}
        and TIMESTAMPDIFF(DAY,a.PRE_TIME, DATE_ADD(#{curDate},INTERVAL 1 DAY))&lt;= #{timeDiff} AND a.app_id IS NOT NULL
        <!-- AND a.REC_EXT_TYPE >= 50 AND a.REC_EXT_TYPE != 110 AND a.IS_DEL='0' -->
<!--        and a.IS_PAY = '1' -->
        GROUP BY a.`APP_ID`,a.`INS_CODE`,a.`PRE_DOCTOR`,dataDate

    </select>

    <select id="selectStatisticsRegisterSplit" resultMap="BaseResultMap2" parameterType="Map">
        SELECT
            b.`APP_ID` AS appId,b.`INS_CODE` insCode,b.`DEPT_ID` deptId,b.`PRE_DOCTOR` userId,CAST(b.`PRE_TIME` AS DATE) dataDate,
            b.PRE_TYPE as preType,count(*) as number
        FROM
            (SELECT distinct a.PRE_ID FROM
                `t_order_status` AS a join t_prescription as b on(a.`PRE_ID`=b.`PRE_ID` and  CAST(b.PRE_TIME  AS DATE) <![CDATA[ <= ]]> (DATE_ADD(CURDATE(),INTERVAL -2 DAY) ) )
             WHERE TIMESTAMPDIFF(DAY,a.`OPERATION_TIME`, DATE_ADD(CURDATE(),INTERVAL 1 DAY)) =#{timeDiff} AND a.`OPERATION_TYPE` IN(110,111)
            ) AS a JOIN `t_prescription` AS b WHERE a.PRE_ID=b.PRE_ID GROUP BY b.`APP_ID` ,b.`INS_CODE` ,b.`PRE_DOCTOR`,dataDate,preType
    </select>
    <select id="getLastData" resultMap="BaseResultMap">
        select * from t_statistics_prescription order by insert_date desc limit 0,1
    </select>
    <select id="getInnerAndExterData" resultType="java.lang.Integer">
        select
        SUM(inner_num+exter_num) AS number
        from t_statistics_prescription
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id = #{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code = #{insCode}
            </if>
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
        </where>
    </select>

</mapper>