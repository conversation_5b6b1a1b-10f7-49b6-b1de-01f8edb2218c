<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.recordquality.mapper.TRecordQualityRuleItemsMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItems">
        <id column="quality_rule_items_id" jdbcType="VARCHAR"  property="qualityRuleItemsId" />
        <result column="record_quality_rule_id" jdbcType="VARCHAR" property="recordQualityRuleId" />
        <result column="quality_rule_items_class" jdbcType="VARCHAR" property="qualityRuleItemsClass" />
        <result column="sort" jdbcType="INTEGER" property="sort" />
    </resultMap>


    <sql id="Base_Column_List">
    quality_rule_items_id,record_quality_rule_id,quality_rule_items_class,sort
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItems">
        delete from t_record_quality_rule_items where quality_rule_items_id = #{ qualityRuleItemsId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record_quality_rule_items where quality_rule_items_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>
    <delete id="deleteByRecordQualityRuleId" parameterType="String">
        delete from t_record_quality_rule_items where record_quality_rule_id = #{recordQualityRuleId}
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItems">
        insert into t_record_quality_rule_items (<include refid="Base_Column_List" />) values
        (#{qualityRuleItemsId},#{recordQualityRuleId},#{qualityRuleItemsClass},#{ruleInfoId},#{ruleInfoName},#{sort})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_record_quality_rule_items (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.qualityRuleItemsId},#{item.recordQualityRuleId},#{item.qualityRuleItemsClass},#{item.sort})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItems">
        update t_record_quality_rule_items
        <set>
             <if test="recordQualityRuleId != null">
                record_quality_rule_id = #{ recordQualityRuleId },
             </if>
             <if test="qualityRuleItemsClass != null">
                quality_rule_items_class = #{ qualityRuleItemsClass },
             </if>

             <if test="sort != null">
                sort = #{ sort },
             </if>
        </set>
        where quality_rule_items_id = #{ qualityRuleItemsId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record_quality_rule_items where quality_rule_items_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItems" resultMap="BaseResultMap">
        SELECT quality_rule_items_id,record_quality_rule_id,quality_rule_items_class,sort
        from t_record_quality_rule_items
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>