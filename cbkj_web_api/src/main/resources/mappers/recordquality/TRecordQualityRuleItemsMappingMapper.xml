<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.recordquality.mapper.TRecordQualityRuleItemsMappingMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItemsMapping">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="id_1" jdbcType="VARCHAR" property="id1"/>
        <result column="name_1" jdbcType="VARCHAR" property="name1"/>
        <result column="id_2" jdbcType="VARCHAR" property="id2"/>
        <result column="name_2" jdbcType="VARCHAR" property="name2"/>
        <result column="qualityRuleItemsClass" jdbcType="VARCHAR" property="qualityRuleItemsClass"/>
        <result column="quality_rule_items_id" jdbcType="VARCHAR" property="qualityRuleItemsId"/>
    </resultMap>


    <sql id="Base_Column_List">
        id,id_1,name_1,id_2,name_2,quality_rule_items_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItemsMapping">
        delete
        from t_record_quality_rule_items_mapping
        where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record_quality_rule_items_mapping where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByQualityRuleItemsId" parameterType="String">
        delete
        from t_record_quality_rule_items_mapping
        where quality_rule_items_id = #{ qualityRuleItemsId }
    </delete>

    <insert id="insert" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItemsMapping">
        insert into t_record_quality_rule_items_mapping (<include refid="Base_Column_List"/>) values
        (#{id},#{id1},#{name1},#{id2},#{name2},#{qualityRuleItemsId})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_record_quality_rule_items_mapping (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.id1},#{item.name1},#{item.id2},#{item.name2},#{item.qualityRuleItemsId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItemsMapping">
        update t_record_quality_rule_items_mapping
        <set>
            <if test="id1 != null">
                id_1 = #{ id1 },
            </if>
            <if test="name1 != null">
                name_1 = #{ name1 },
            </if>
            <if test="id2 != null">
                id_2 = #{ id2 },
            </if>
            <if test="name2 != null">
                name_2 = #{ name2 },
            </if>
            <if test="qualityRuleItemsId != null">
                quality_rule_items_id = #{ qualityRuleItemsId },
            </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_record_quality_rule_items_mapping where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItemsMapping"
            resultMap="BaseResultMap">
        SELECT id,id_1,name_1,id_2,name_2,quality_rule_items_id
        from t_record_quality_rule_items_mapping

    </select>
    <select id="getPageListByObj2" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItemsMapping"
            resultMap="BaseResultMap">
        SELECT a.id,a.id_1,a.name_1,a.id_2,a.name_2,a.quality_rule_items_id
        from t_record_quality_rule_items_mapping as a
        join t_record_quality_rule_items as b on a.quality_rule_items_id = b.quality_rule_items_id
        join t_record_quality_rule_main as c on b.record_quality_rule_id = c.record_quality_rule_id and c.record_quality_rule_status = 0
        where b.quality_rule_items_class in('3-3')

    </select>
    <select id="getHuChiList" resultMap="BaseResultMap">

        SELECT c.*,b.quality_rule_items_class as qualityRuleItemsClass,a.record_quality_rule_name as recordQualityRuleName
        FROM t_record_quality_rule_main AS a
        JOIN t_record_quality_rule_items AS b
        ON a.record_quality_rule_id = b.record_quality_rule_id
                and b.quality_rule_items_class in ('1-3','2-3')
        AND a.record_quality_rule_status = 0
        JOIN t_record_quality_rule_items_mapping AS c
        ON c.quality_rule_items_id = b.quality_rule_items_id
        AND c.id_1 in
      (
        <foreach collection="huChiListOne" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )


        AND c.id_2 IN (
        <foreach collection="huChiListTwo" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )

    </select>
    <select id="jugmentExist" resultMap="BaseResultMap">

        SELECT c.*,b.quality_rule_items_class as qualityRuleItemsClass,a.record_quality_rule_name as recordQualityRuleName
        FROM t_record_quality_rule_main AS a
        JOIN t_record_quality_rule_items AS b
        ON a.record_quality_rule_id = b.record_quality_rule_id
                and b.quality_rule_items_class in ('1-3','2-3','3-3')
        AND a.record_quality_rule_status = 0
        JOIN t_record_quality_rule_items_mapping AS c
        ON c.quality_rule_items_id = b.quality_rule_items_id
        AND c.id_1 in
      (
        <foreach collection="huChiListOne" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )


        AND c.id_2 IN (
        <foreach collection="huChiListTwo" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
        <if test="recordQualityRuleId != null and recordQualityRuleId != ''">
            and a.record_quality_rule_id != #{recordQualityRuleId}
        </if>

    </select>

    <select id="getHuChiListF" resultMap="BaseResultMap">

        SELECT c.*,b.quality_rule_items_class as qualityRuleItemsClass,a.record_quality_rule_name as recordQualityRuleName
        FROM t_record_quality_rule_main AS a
        JOIN t_record_quality_rule_items AS b
        ON a.record_quality_rule_id = b.record_quality_rule_id and b.quality_rule_items_class  in ('1-4','2-4')
        AND a.record_quality_rule_status = 0
        JOIN t_record_quality_rule_items_mapping AS c
        ON c.quality_rule_items_id = b.quality_rule_items_id
        AND
        (
        <foreach collection="huChiListOne" item="item" index="index" separator="or">
            #{item} LIKE CONCAT('%', c.id_1, '%')
        </foreach>
        )
        AND (
        <foreach collection="huChiListTwo" item="item" index="index" separator="or">
            #{item} LIKE CONCAT('%', c.id_2, '%')
        </foreach>
        )
        AND c.id_1
        &lt; c.id_2

    </select>

</mapper>