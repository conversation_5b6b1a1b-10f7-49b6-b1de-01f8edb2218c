<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.recordquality.mapper.TRecordQualityRuleMainMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.recordquality.beans.TRecordQualityRuleMain">
        <id column="record_quality_rule_id" jdbcType="VARCHAR"  property="recordQualityRuleId" />
        <result column="record_quality_rule_calss_value" jdbcType="VARCHAR" property="recordQualityRuleCalssValue" />
        <result column="record_quality_rule_calss_name" jdbcType="VARCHAR" property="recordQualityRuleCalssName" />
        <result column="record_quality_rule_name" jdbcType="VARCHAR" property="recordQualityRuleName" />
        <result column="record_quality_rule_status" jdbcType="INTEGER" property="recordQualityRuleStatus" />
        <result column="record_quality_rule_desc" jdbcType="VARCHAR" property="recordQualityRuleDesc" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_user_name" jdbcType="VARCHAR" property="delUserName" />
        <result column="del_user_id" jdbcType="VARCHAR" property="delUserId" />
        <result column="del_time" jdbcType="TIMESTAMP" property="delTime" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="record_quality_rule_condition" jdbcType="VARCHAR" property="recordQualityRuleCondition" />
    </resultMap>


    <sql id="Base_Column_List">
    record_quality_rule_id,record_quality_rule_calss_value,record_quality_rule_calss_name,record_quality_rule_name
    ,record_quality_rule_status,record_quality_rule_desc,create_time,create_user_name,create_user_id,update_user_name,update_user_id,
    update_time,del_user_name,del_user_id,del_time,app_id,app_name,ins_code,ins_name,dept_id,dept_name,record_quality_rule_condition
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleMain">
        delete from t_record_quality_rule_main where record_quality_rule_id = #{ recordQualityRuleId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record_quality_rule_main where record_quality_rule_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleMain">
        insert into t_record_quality_rule_main (<include refid="Base_Column_List" />) values
        (#{recordQualityRuleId},#{recordQualityRuleCalssValue},#{recordQualityRuleCalssName},#{recordQualityRuleName},#{recordQualityRuleStatus},#{recordQualityRuleDesc},#{createTime},#{createUserName},#{createUserId},#{updateUserName},#{updateUserId},#{updateTime},#{delUserName},#{delUserId},#{delTime},#{appId},#{appName},#{insCode},#{insName},#{deptId},#{deptName},#{recordQualityRuleCondition})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_record_quality_rule_main (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.recordQualityRuleId},#{item.recordQualityRuleCalssValue},#{item.recordQualityRuleCalssName},#{item.recordQualityRuleName},#{item.recordQualityRuleStatus},#{item.recordQualityRuleDesc},#{item.createTime},#{item.createUserName},#{item.createUserId},#{item.updateUserName},#{item.updateUserId},#{item.updateTime},#{item.delUserName},#{item.delUserId},#{item.delTime},#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.deptId},#{item.deptName},#{item.recordQualityRuleCondition})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleMain">
        update t_record_quality_rule_main
        <set>
             <if test="recordQualityRuleCalssValue != null">
                record_quality_rule_calss_value = #{ recordQualityRuleCalssValue },
             </if>
             <if test="recordQualityRuleCalssName != null">
                record_quality_rule_calss_name = #{ recordQualityRuleCalssName },
             </if>
             <if test="recordQualityRuleName != null">
                record_quality_rule_name = #{ recordQualityRuleName },
             </if>
             <if test="recordQualityRuleStatus != null">
                record_quality_rule_status = #{ recordQualityRuleStatus },
             </if>
             <if test="recordQualityRuleDesc != null">
                record_quality_rule_desc = #{ recordQualityRuleDesc },
             </if>
             <if test="createTime != null">
                create_time = #{ createTime },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
             <if test="createUserId != null">
                create_user_id = #{ createUserId },
             </if>
             <if test="updateUserName != null">
                update_user_name = #{ updateUserName },
             </if>
             <if test="updateUserId != null">
                update_user_id = #{ updateUserId },
             </if>
             <if test="updateTime != null">
                update_time = #{ updateTime },
             </if>
             <if test="delUserName != null">
                del_user_name = #{ delUserName },
             </if>
             <if test="delUserId != null">
                del_user_id = #{ delUserId },
             </if>
             <if test="delTime != null">
                del_time = #{ delTime },
             </if>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="deptName != null">
                dept_name = #{ deptName },
             </if>
             <if test="recordQualityRuleCondition != null">
                record_quality_rule_condition = #{recordQualityRuleCondition},
             </if>
        </set>
        where record_quality_rule_id = #{ recordQualityRuleId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record_quality_rule_main where record_quality_rule_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleMain" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
            from t_record_quality_rule_main
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <select id="list" resultMap="BaseResultMap" parameterType="com.jiuzhekan.recordquality.beans.RecordQualityListReq">
        select
        <include refid="Base_Column_List"/>
        from t_record_quality_rule_main
        <where>

            <if test=" recordQualityRuleCalssValue != null and recordQualityRuleCalssValue!='' ">
                and record_quality_rule_calss_value like CONCAT('%',trim(#{recordQualityRuleCalssValue}),'%')
            </if>
            <if test=" recordQualityRuleName != null and recordQualityRuleName!='' ">
                and record_quality_rule_name like CONCAT('%',trim(#{recordQualityRuleName}),'%')
            </if>
            <if test=" recordQualityRuleStatus == null  ">
                and record_quality_rule_status in (0,2)
            </if>
            <if test=" recordQualityRuleStatus != null  ">
                and record_quality_rule_status = #{recordQualityRuleStatus}
            </if>
        </where>
order by create_time desc
    </select>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.recordquality.beans.TRecordQualityRuleMain">

        <id column="record_quality_rule_id" jdbcType="VARCHAR"  property="recordQualityRuleId" />
        <result column="record_quality_rule_calss_value" jdbcType="VARCHAR" property="recordQualityRuleCalssValue" />
        <result column="record_quality_rule_calss_name" jdbcType="VARCHAR" property="recordQualityRuleCalssName" />
        <result column="record_quality_rule_name" jdbcType="VARCHAR" property="recordQualityRuleName" />
        <result column="record_quality_rule_status" jdbcType="INTEGER" property="recordQualityRuleStatus" />
        <result column="record_quality_rule_desc" jdbcType="VARCHAR" property="recordQualityRuleDesc" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_user_name" jdbcType="VARCHAR" property="delUserName" />
        <result column="del_user_id" jdbcType="VARCHAR" property="delUserId" />
        <result column="del_time" jdbcType="TIMESTAMP" property="delTime" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="record_quality_rule_condition" jdbcType="VARCHAR" property="recordQualityRuleCondition" />
        <collection property="ruleInfoList" ofType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItems">
            <result column="quality_rule_items_id" jdbcType="VARCHAR"  property="qualityRuleItemsId" />
            <result column="recordQualityRuleId2" jdbcType="VARCHAR" property="recordQualityRuleId" />
            <result column="quality_rule_items_class" jdbcType="VARCHAR" property="qualityRuleItemsClass" />
            <result column="sort" jdbcType="INTEGER" property="sort" />
            <collection property="ruleInfoMapping" ofType="com.jiuzhekan.recordquality.beans.TRecordQualityRuleItemsMapping" >
                <result column="id" jdbcType="INTEGER"  property="id" />
                <result column="id_1" jdbcType="VARCHAR" property="id1" />
                <result column="name_1" jdbcType="VARCHAR" property="name1" />
                <result column="id_2" jdbcType="VARCHAR" property="id2" />
                <result column="name_2" jdbcType="VARCHAR" property="name2" />
            </collection>
        </collection>

    </resultMap>

    <select id="getObjectMapping" resultMap="BaseResultMap2" parameterType="String">
        SELECT a.*,
               b.`quality_rule_items_id`,
               b.`record_quality_rule_id` as recordQualityRuleId2,
               b.`quality_rule_items_class`,
               b.`sort`,
               c.`id`,
               c.`id_1`,
               c.`name_1`,
               c.`id_2`,
               c.`name_2`
        FROM t_record_quality_rule_main a
                 join `t_record_quality_rule_items` AS b on (a.record_quality_rule_id = b.record_quality_rule_id)
                 JOIN `t_record_quality_rule_items_mapping` AS c
                      ON (b.`quality_rule_items_id` = c.`quality_rule_items_id`) where a.record_quality_rule_id = #{recordQualityRuleId}
        order by b.`sort`, c.`id`


    </select>
    <select id="getQualitySymptom" resultType="java.util.HashMap" parameterType="String">
            select id ,name from t_record_quality_symptom where type is not null  and type != 2
                                                          <if test="name != null and name != ''">
                                                              and name like CONCAT('%',trim(#{name}),'%')
                                                          </if>
                                                          order by id
    </select>

</mapper>