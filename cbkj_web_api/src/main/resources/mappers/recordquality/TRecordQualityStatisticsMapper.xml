<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.recordquality.mapper.TRecordQualityStatisticsMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.recordquality.beans.TRecordQualityStatistics">
        <id column="record_quality_statistics_id" jdbcType="INTEGER"  property="recordQualityStatisticsId" />
        <result column="quality_goal_code" jdbcType="INTEGER" property="qualityGoalCode" />
        <result column="quality_goal_name" jdbcType="VARCHAR" property="qualityGoalName" />
        <result column="quality_time_point_code" jdbcType="INTEGER" property="qualityTimePointCode" />
        <result column="quality_time_point_name" jdbcType="VARCHAR" property="qualityTimePointName" />
        <result column="quality_key_ponit_code" jdbcType="INTEGER" property="qualityKeyPonitCode" />
        <result column="quality_key_ponit_name" jdbcType="VARCHAR" property="qualityKeyPonitName" />
        <result column="quality_doctor_id" jdbcType="VARCHAR" property="qualityDoctorId" />
        <result column="quality_doctor_name" jdbcType="VARCHAR" property="qualityDoctorName" />
        <result column="quality_time" jdbcType="TIMESTAMP" property="qualityTime" />
        <result column="quality_key_element" jdbcType="VARCHAR" property="qualityKeyElement" />
        <result column="quality_score" jdbcType="DOUBLE" property="qualityScore" />
        <result column="quality_score_time" jdbcType="TIMESTAMP" property="qualityScoreTime" />
        <result column="quality_score_user_id" jdbcType="VARCHAR" property="qualityScoreUserId" />
        <result column="quality_score_user_name" jdbcType="VARCHAR" property="qualityScoreUserName" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    </resultMap>


    <sql id="Base_Column_List">
    record_quality_statistics_id,quality_goal_code,quality_goal_name,quality_time_point_code,quality_time_point_name,quality_key_ponit_code,quality_key_ponit_name,quality_doctor_id,quality_doctor_name,quality_time,quality_key_element,quality_score,quality_score_time,quality_score_user_id,quality_score_user_name,app_id,app_name,ins_code,ins_name,dept_id,dept_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityStatistics">
        delete from t_record_quality_statistics where record_quality_statistics_id = #{ recordQualityStatisticsId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record_quality_statistics where record_quality_statistics_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityStatistics">
        insert into t_record_quality_statistics (<include refid="Base_Column_List" />) values
        (#{recordQualityStatisticsId},#{qualityGoalCode},#{qualityGoalName},#{qualityTimePointCode},#{qualityTimePointName},#{qualityKeyPonitCode},#{qualityKeyPonitName},#{qualityDoctorId},#{qualityDoctorName},#{qualityTime},#{qualityKeyElement},#{qualityScore},#{qualityScoreTime},#{qualityScoreUserId},#{qualityScoreUserName},#{appId},#{appName},#{insCode},#{insName},#{deptId},#{deptName})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_record_quality_statistics (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.recordQualityStatisticsId},#{item.qualityGoalCode},#{item.qualityGoalName},#{item.qualityTimePointCode},#{item.qualityTimePointName},#{item.qualityKeyPonitCode},#{item.qualityKeyPonitName},#{item.qualityDoctorId},#{item.qualityDoctorName},#{item.qualityTime},#{item.qualityKeyElement},#{item.qualityScore},#{item.qualityScoreTime},#{item.qualityScoreUserId},#{item.qualityScoreUserName},#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.deptId},#{item.deptName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityStatistics">
        update t_record_quality_statistics
        <set>
             <if test="qualityGoalCode != null">
                quality_goal_code = #{ qualityGoalCode },
             </if>
             <if test="qualityGoalName != null">
                quality_goal_name = #{ qualityGoalName },
             </if>
             <if test="qualityTimePointCode != null">
                quality_time_point_code = #{ qualityTimePointCode },
             </if>
             <if test="qualityTimePointName != null">
                quality_time_point_name = #{ qualityTimePointName },
             </if>
             <if test="qualityKeyPonitCode != null">
                quality_key_ponit_code = #{ qualityKeyPonitCode },
             </if>
             <if test="qualityKeyPonitName != null">
                quality_key_ponit_name = #{ qualityKeyPonitName },
             </if>
             <if test="qualityDoctorId != null">
                quality_doctor_id = #{ qualityDoctorId },
             </if>
             <if test="qualityDoctorName != null">
                quality_doctor_name = #{ qualityDoctorName },
             </if>
             <if test="qualityTime != null">
                quality_time = #{ qualityTime },
             </if>
             <if test="qualityKeyElement != null">
                quality_key_element = #{ qualityKeyElement },
             </if>
             <if test="qualityScore != null">
                quality_score = #{ qualityScore },
             </if>
             <if test="qualityScoreTime != null">
                quality_score_time = #{ qualityScoreTime },
             </if>
             <if test="qualityScoreUserId != null">
                quality_score_user_id = #{ qualityScoreUserId },
             </if>
             <if test="qualityScoreUserName != null">
                quality_score_user_name = #{ qualityScoreUserName },
             </if>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="deptName != null">
                dept_name = #{ deptName },
             </if>
        </set>
        where record_quality_statistics_id = #{ recordQualityStatisticsId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record_quality_statistics where record_quality_statistics_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.recordquality.beans.TRecordQualityStatistics" resultMap="BaseResultMap">
        SELECT record_quality_statistics_id,quality_goal_code,quality_goal_name,quality_time_point_code,quality_time_point_name,quality_key_ponit_code,quality_key_ponit_name,quality_doctor_id,quality_doctor_name,quality_time,quality_key_element,quality_score,quality_score_time,quality_score_user_id,quality_score_user_name,app_id,app_name,ins_code,ins_name,dept_id,dept_name
        from t_record_quality_statistics
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getPageListByMyObj" resultMap="BaseResultMap" parameterType="com.jiuzhekan.recordquality.beans.RecordQualityStaticsListReq">
        SELECT
        <include refid="Base_Column_List"/>
        from t_record_quality_statistics
        <where>
            <if test="startDate != null">
                quality_time >= #{startDate}
            </if>
            <if test="endDate != null">
                and quality_time &lt;= #{endDate}
            </if>
            <if test="qualityGoalCode != null">
                and quality_goal_code = #{qualityGoalCode}
            </if>
            <if test="doctorName != null">
                and quality_doctor_name like CONCAT('%',trim(#{doctorName}),'%')
            </if>
        </where>
        order by quality_time desc
    </select>

</mapper>