<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.statistics.mapper.register.TStatisticsRegisterMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.statistics.beans.register.TStatisticsRegister">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="register_times" jdbcType="INTEGER" property="registerTimes" />
        <result column="electronic_record_num" jdbcType="INTEGER" property="electronicRecordNum" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="insert_date" jdbcType="TIMESTAMP" property="insertDate" />
    </resultMap>


    <sql id="Base_Column_List">
    id,app_id,app_name,ins_code,ins_name,dept_id,dept_name,user_id,user_name,register_times,electronic_record_num,create_date,insert_date
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.register.TStatisticsRegister">
        delete from t_statistics_register where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_statistics_register where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.statistics.beans.register.TStatisticsRegister">
        insert into t_statistics_register (<include refid="Base_Column_List" />) values
        (#{id},#{appId},#{appName},#{insCode},#{insName},#{deptId},#{deptName},#{userId},#{userName},#{registerTimes},#{electronicRecordNum},#{createDate},#{insertDate})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_statistics_register (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.deptId},#{item.deptName},#{item.userId},#{item.userName},#{item.registerTimes},#{item.electronicRecordNum},#{item.createDate},#{item.insertDate})
        </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.register.TStatisticsRegister">
        update t_statistics_register
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="deptName != null">
                dept_name = #{ deptName },
             </if>
             <if test="userId != null">
                user_id = #{ userId },
             </if>
             <if test="userName != null">
                user_name = #{ userName },
             </if>
             <if test="registerTimes != null">
                register_times = #{ registerTimes },
             </if>
             <if test="electronicRecordNum != null">
                electronic_record_num = #{ electronicRecordNum },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="insertDate != null">
                insert_date = #{ insertDate },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_statistics_register where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.statistics.beans.register.TStatisticsRegister" resultMap="BaseResultMap">
        SELECT id,app_id,app_name,ins_code,ins_name,dept_id,dept_name,user_id,user_name,register_times,electronic_record_num,create_date,insert_date
        from t_statistics_register
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <resultMap id="BaseResultMap2" type="com.jiuzhekan.statistics.beans.register.StatisticsRegister">
            <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="appId" jdbcType="VARCHAR" property="appId" />
        <result column="insCode" jdbcType="VARCHAR" property="insCode" />
    </resultMap>

    <select id="selectStatisticsRegisterResult" resultMap="BaseResultMap2" parameterType="Map">
        SELECT
            b.`id`,a.`APP_ID` appId,a.`INS_CODE` insCode ,a.`DEPT_ID` deptId,a.`DOCTOR_ID` userId,
            CAST(a.`REGISTER_TIME` AS DATE) dataDate ,COUNT(a.`REGISTER_ID`) number ,a.`DOCTOR_NAME` userName,
            ifnull(b.`register_times`,0) registerTimes ,ifnull(b.`electronic_record_num`,0) electronicRecordNum
        FROM `t_register` AS a LEFT JOIN t_statistics_register AS b ON a.`APP_ID`=b.`app_id` AND a.`INS_CODE`=b.`ins_code` AND a.`DOCTOR_ID`=b.`user_id`
            AND  b.`create_date`= CAST(IFNULL(a.`REGISTER_TIME`,'1900-01-01 00:00:00') AS DATE)
        WHERE a.`REGISTER_DIAGNOSIS_STATUS` IN (4,5,6,8)
          AND TIMESTAMPDIFF(DAY,a.REGISTER_TIME, DATE_ADD(#{curDate},INTERVAL 1 DAY))&lt;= #{timeDiff} AND a.app_id IS NOT NULL
        GROUP BY a.`APP_ID`,a.`INS_CODE`,a.`DOCTOR_ID`,dataDate
    </select>

    <select id="selectStatisticsRegisterEle" resultMap="BaseResultMap2" parameterType="Map">
        SELECT
            b.`id`,a.`APP_ID` appId,a.`INS_CODE` insCode,a.`DEPT_ID` deptId,a.`DOC_ID` userId,
            CAST(a.`REC_TRE_TIME` AS DATE) dataDate ,COUNT(a.`REC_ID`) number ,a.`DOC_NAME` userName,
            ifnull(b.`register_times`,0) registerTimes ,ifnull(b.`electronic_record_num`,0) electronicRecordNum
        FROM t_record AS a LEFT JOIN t_statistics_register AS b ON a.`APP_ID`=b.`app_id` AND a.`INS_CODE`=b.`ins_code` AND a.`DOC_ID`=b.`user_id`
            AND b.`create_date`= CAST(IFNULL(a.`REC_TRE_TIME`,'1900-01-01 00:00:00') AS DATE)
        WHERE TIMESTAMPDIFF(DAY,a.REC_TRE_TIME, DATE_ADD(#{curDate},INTERVAL 1 DAY))&lt;= #{timeDiff} AND a.app_id IS NOT NULL
        GROUP BY a.`APP_ID`,a.`INS_CODE`,a.`DOC_ID`,dataDate

    </select>
    <select id="selectLastDate" resultMap="BaseResultMap">
        select * from t_statistics_register order by insert_date desc limit 0,1
    </select>

    <insert id="insertStatisticsRegister" parameterType="List">
        insert into t_statistics_register (
        app_id,ins_code,user_id,user_name,create_date,insert_date,register_times,electronic_record_num
   ) values
        <foreach collection="list" item="item" index="index" separator="," >
            (
             #{item.appId},#{item.insCode},#{item.userId},#{item.userName},#{item.dataDate},now()
           ,#{item.registerTimes},#{item.electronicRecordNum}
)
        </foreach>
    </insert>
    <insert id="replaceinsertStatisticsRegister" parameterType="List">
        replace into t_statistics_register (
        id,app_id,ins_code,user_id,user_name,create_date,insert_date,register_times,electronic_record_num
        ) values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.id},#{item.appId},#{item.insCode},#{item.userId},#{item.userName},#{item.dataDate},now()
            ,#{item.registerTimes},#{item.electronicRecordNum}
            )
        </foreach>
    </insert>
</mapper>