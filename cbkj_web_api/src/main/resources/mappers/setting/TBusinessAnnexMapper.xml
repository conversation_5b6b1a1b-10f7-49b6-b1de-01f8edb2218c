<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.setting.TBusinessAnnexMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.setting.TBusinessAnnex">
        <id column="id" jdbcType="VARCHAR"  property="id" />
        <result column="annex_foreign_id" jdbcType="VARCHAR" property="annexForeignId" />
        <result column="annex_name" jdbcType="VARCHAR" property="annexName" />
        <result column="annex_Original_name" jdbcType="VARCHAR" property="annexOriginalName" />
        <result column="annex_suffix_name" jdbcType="VARCHAR" property="annexSuffixName" />
        <result column="annex_size" jdbcType="DOUBLE" property="annexSize" />
        <result column="annex_path" jdbcType="VARCHAR" property="annexPath" />
        <result column="manual_title" jdbcType="VARCHAR" property="manualTitle" />
        <result column="annex_type" jdbcType="TINYINT" property="annexType" />
        <result column="is_del" jdbcType="TINYINT" property="isDel" />
    </resultMap>


    <sql id="Base_Column_List">
    id,annex_foreign_id,annex_name,annex_Original_name,annex_suffix_name,annex_size,annex_path,annex_type,is_del
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TBusinessAnnex">
        delete from t_business_annex where id = #{ id }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_business_annex where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TBusinessAnnex">
        insert into t_business_annex (id,annex_foreign_id,annex_name,annex_Original_name,annex_suffix_name,annex_size,annex_path,annex_type,is_del) values
        (#{id},#{annexForeignId},#{annexName},#{annexOriginalName},#{annexSuffixName},#{annexSize},#{annexPath},#{annexType},#{isDel})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_business_annex (id,annex_foreign_id,annex_name,annex_Original_name,annex_suffix_name,annex_size,annex_path,annex_type,is_del) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.annexForeignId},#{item.annexName},#{item.annexOriginalName},#{item.annexSuffixName},#{item.annexSize},#{item.annexPath},#{item.annexType},#{item.isDel})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TBusinessAnnex">
        update t_business_annex
        <set>
             <if test="annexForeignId != null">
                annex_foreign_id = #{ annexForeignId },
             </if>
             <if test="annexName != null">
                annex_name = #{ annexName },
             </if>
             <if test="annexOriginalName != null">
                annex_Original_name = #{ annexOriginalName },
             </if>
             <if test="annexSuffixName != null">
                annex_suffix_name = #{ annexSuffixName },
             </if>
             <if test="annexSize != null">
                annex_size = #{ annexSize },
             </if>
             <if test="annexPath != null">
                annex_path = #{ annexPath },
             </if>
             <if test="annexType != null">
                annex_type = #{ annexType },
             </if>
             <if test="isDel != null">
                is_del = #{ isDel },
             </if>
        </set>
        where id = #{ id }
    </update>

    <update id="updateByAnnex" parameterType="TBusinessAnnex">
        update t_business_annex
        <set>
             <if test="annexForeignId != null">
                annex_foreign_id = #{ annexForeignId },
             </if>
             <if test="annexName != null">
                annex_name = #{ annexName },
             </if>
             <if test="annexOriginalName != null">
                annex_Original_name = #{ annexOriginalName },
             </if>
             <if test="annexSuffixName != null">
                annex_suffix_name = #{ annexSuffixName },
             </if>
             <if test="annexSize != null">
                annex_size = #{ annexSize },
             </if>
             <if test="annexPath != null">
                annex_path = #{ annexPath },
             </if>
             <if test="annexType != null">
                annex_type = #{ annexType },
             </if>
             <if test="isDel != null">
                is_del = #{ isDel },
             </if>
        </set>
        <where>
            <if test="id != null and id != ''">
                id = #{ id }
            </if>
            <if test="annexForeignId != null and annexForeignId != ''">
               and annex_foreign_id = #{ annexForeignId }
            </if>
        </where>
    </update>


    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_business_annex where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_business_annex where id = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_business_annex where id = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TBusinessAnnex" resultType="Map">
        SELECT id,annex_foreign_id,annex_name,annex_Original_name,annex_suffix_name,annex_size,annex_path,annex_type,is_del  from t_business_annex
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TBusinessAnnex" resultMap="BaseResultMap">
        SELECT id,annex_foreign_id,annex_name,annex_Original_name,annex_suffix_name,annex_size,annex_path,annex_type,is_del
        from t_business_annex
        <where>
            is_del = '0'
            <if test=" id != null and id!='' ">
                and id = #{id}
            </if>
            <if test=" annexForeignId != null and annexForeignId!='' ">
                and annex_foreign_id = #{annexForeignId}
            </if>
        </where>
    </select>

    <insert id="insertUpdateAnnex" parameterType="list">
        replace into t_business_annex
        (id,annex_foreign_id,annex_name,annex_Original_name,annex_suffix_name,annex_size,annex_path,annex_type,is_del) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.annexForeignId},#{item.annexName},#{item.annexOriginalName},#{item.annexSuffixName}
            ,#{item.annexSize},#{item.annexPath},#{item.annexType},#{item.isDel})
        </foreach>
    </insert>

    <update id="deleteByForeignIds" parameterType="list">
        update t_business_annex set is_del = '1'
        WHERE annex_foreign_id  in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")" >
           #{item}
        </foreach>
    </update>

    <select id="getMyProposalS" parameterType="list" resultMap="BaseResultMap">
        SELECT id,annex_foreign_id,annex_name,annex_Original_name,annex_suffix_name,annex_size,annex_path,annex_type
        from t_business_annex
        <where>
            is_del = '0'
            and annex_foreign_id  in
            <foreach collection="list" item="item" index="index" separator="," open="(" close=")" >
               #{item.id}
             </foreach>
        </where>
    </select>

    <select id="getManualAnnexS" parameterType="TBusinessManual" resultMap="BaseResultMap">
        SELECT
            a.id,a.annex_foreign_id,a.annex_name,a.annex_Original_name,a.annex_suffix_name,
            a.annex_size,a.annex_path,a.annex_type,m.manual_title
        from t_business_manual m join t_business_annex a on m.id = a.annex_foreign_id and a.is_del = '0'
        where m.manual_state = '0' AND m.is_del = '0'
        ORDER BY m.create_time desc
    </select>

    <select id="getBusinessAnnexByUrl" parameterType="TBusinessManual" resultMap="BaseResultMap">
        SELECT *
        from t_business_annex
        where is_del = '0' and annex_path = #{ annexPath }
        limit 1
    </select>
</mapper>