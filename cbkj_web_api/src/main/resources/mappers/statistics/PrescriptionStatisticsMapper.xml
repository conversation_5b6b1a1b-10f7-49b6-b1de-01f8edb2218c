<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.statistics.PrescriptionStatisticsMapper">
    <resultMap id="prescriptionStatisticsMap" type="com.jiuzhekan.cbkj.controller.statistics.vo.PrescriptionStatisticsVO">
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="INS_NAME" jdbcType="VARCHAR" property="insName"/>
        <result column="DEPT_ID" jdbcType="VARCHAR" property="deptId"/>
        <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName"/>
        <result column="PRE_DOCTOR" jdbcType="VARCHAR" property="doctorId"/>
        <result column="PRE_DOCTORNAME" jdbcType="VARCHAR" property="doctorName"/>
        <result column="PATIENT_ID" jdbcType="VARCHAR" property="patientId"/>
        <result column="REC_NAME" jdbcType="VARCHAR" property="patientName"/>
        <result column="REC_GENDER" jdbcType="VARCHAR" property="patientGender"/>
        <result column="REC_AGE" jdbcType="VARCHAR" property="patientAge"/>
        <result column="PRE_TIME" jdbcType="VARCHAR" property="preTime"/>
        <result column="REC_ID" jdbcType="VARCHAR" property="recId"/>
        <result column="PRE_ID" jdbcType="VARCHAR" property="preId"/>
        <result column="PRE_NO" jdbcType="VARCHAR" property="preNo"/>
        <result column="DIS_ID" jdbcType="VARCHAR" property="disId"/>
        <result column="DIS_NAME" jdbcType="VARCHAR" property="disName"/>
        <result column="SYM_ID" jdbcType="VARCHAR" property="symId"/>
        <result column="SYM_NAME" jdbcType="VARCHAR" property="symName"/>
        <result column="THE_NAMES" jdbcType="VARCHAR" property="theNames"/>
        <result column="WESTERN_DISEASE_ID" jdbcType="VARCHAR" property="westernDiseaseId"/>
        <result column="WESTERN_DISEASE" jdbcType="VARCHAR" property="westernDisease"/>
        <result column="PRE_ORIGIN" jdbcType="VARCHAR" property="preOrigin"/>
        <result column="PRE_TYPE" jdbcType="VARCHAR" property="preType"/>
        <result column="PRE_MAT_TYPE" jdbcType="VARCHAR" property="preMatType"/>
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId"/>
        <result column="STORE_NAME" jdbcType="VARCHAR" property="storeName"/>
        <result column="REC_TRE_TYPE" jdbcType="VARCHAR" property="recTreType"/>
        <result column="PRE_DESCRIPTION" jdbcType="VARCHAR" property="preDescription"/>
        <result column="PRE_NUM" jdbcType="INTEGER" property="preNum"/>
        <result column="MAT_TOL_MONEY" jdbcType="DECIMAL" property="matTolMoney"/>
        <result column="PRE_TOL_MONEY" jdbcType="DECIMAL" property="preTolMoney"/>
        <result column="mats" jdbcType="VARCHAR" property="mats"/>
        <result column="IS_SCIENTIFIC_PREPARATION" jdbcType="INTEGER" property="isScientificPreparation"/>
        <result column="INFO_SCIENTIFIC_PREPARATION" jdbcType="VARCHAR" property="infoScientificPreparation"/>
    </resultMap>


    <select id="detailedCount" parameterType="com.jiuzhekan.cbkj.controller.statistics.vo.PrescriptionStatisticsVO"
            resultType="integer">
        select count(*) from
        (select p.PRE_ID
        from t_prescription p
        left join t_record r on r.REC_ID = p.REC_ID
        <if test="mats != null and mats != ''">
            left join t_prescription_item i on i.PRE_ID = p.PRE_ID
        </if>
        <include refid="detailedWhere"/>
        group by p.PRE_ID ) e
    </select>
    <select id="detailedScienceCount" parameterType="com.jiuzhekan.cbkj.controller.statistics.vo.PrescriptionStatisticsVO"
            resultType="integer">
        select count(*) from
        (select p.PRE_ID
        from t_prescription p
        left join t_record r on r.REC_ID = p.REC_ID
        <if test="mats != null and mats != ''">
            left join t_prescription_item i on i.PRE_ID = p.PRE_ID
        </if>
        <include refid="detailedWhere2"/>
        group by p.PRE_NO ) e
    </select>

    <select id="detailedList" parameterType="com.jiuzhekan.cbkj.controller.statistics.vo.PrescriptionStatisticsVO"
            resultMap="prescriptionStatisticsMap">
        select
        r.APP_ID,
        r.INS_CODE,
        r.DEPT_ID,
        r.DEPT_NAME,
        p.PRE_DOCTOR,
        p.PRE_DOCTORNAME,
        r.PATIENT_ID,
        r.REC_NAME,
        case r.REC_GENDER when 'F' then '女' when 'M' then '男' end REC_GENDER,
        CONCAT_WS('', r.REC_AGE1, r.REC_AGEUNIT1, r.REC_AGE2, r.REC_AGEUNIT2) REC_AGE,
        date_format(p.PRE_TIME, '%Y-%m-%d %H:%i:%s') PRE_TIME,
        p.REC_ID,
        p.PRE_ID,
        p.PRE_NO,
        r.DIS_ID,
        r.DIS_NAME,
        r.SYM_ID,
        r.SYM_NAME,
        r.THE_NAMES,
        r.WESTERN_DISEASE_ID,
        r.WESTERN_DISEASE,
        case p.PRE_ORIGIN
            when '0' then '智能开方'
            when '1' then '智能辩证'
            when '2' then '智能开方'
            when '3' then '方剂搜索'
            when '4' then '协定方'
            when '5' then '专家经验共享'
            when '6' then '我的验案'
            when '7' then '名家验案'
            when '8' then '历史病历'
            when '9' then '国医大师'
            when '10' then '配方'
            when '11' then '传承经方'
            end PRE_ORIGIN,
        case p.PRE_TYPE
            when '1' then '内服中药方'
            when '2' then '外用中药方'
            when '3' then '中成药方'
            when '4' then '适宜技术方'
            when '5' then '制剂'
            end PRE_TYPE,
        p.PRE_MAT_TYPE,
        p.STORE_ID,
        p.STORE_NAME,
        case r.REC_TRE_TYPE when '1' then '门诊' when '2' then '住院' end REC_TRE_TYPE,
        CASE p.PRE_TYPE
            WHEN '1' THEN CONCAT_WS('，', p.PRE_DESCRIPTION, PRE_FREQUENCY, CONCAT('每次', PRE_N_ML_NAME))
            WHEN '2' THEN CONCAT_WS('，', p.PRE_SMOKE_TYPE, PRE_FREQUENCY, CONCAT('每袋', PRE_N_ML_NAME))
        END PRE_DESCRIPTION,
        p.PRE_NUM,
        CONVERT(p.MAT_TOL_MONEY, DECIMAL(10,2)) MAT_TOL_MONEY,
        CONVERT(p.PRE_TOL_MONEY, DECIMAL(10,2)) PRE_TOL_MONEY,
        i.YPMC_HIS,
        i.MAT_DOSE,
        i.BZDW_HIS,
        i.YFMC_HIS,
        GROUP_CONCAT(CONCAT_WS('', i.YPMC_HIS, 0+CAST(i.MAT_DOSE AS CHAR(10)), i.BZDW_HIS, CONCAT('(',i.YFMC_HIS,')'))) mats

        from t_prescription p
        left join t_record r on r.REC_ID = p.REC_ID
        left join t_prescription_item i on i.PRE_ID = p.PRE_ID
        <include refid="detailedWhere"/>
        GROUP BY p.PRE_ID
        order by p.pre_time
        <if test="limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>
    <select id="detailedListScience" parameterType="com.jiuzhekan.cbkj.controller.statistics.vo.PrescriptionStatisticsVO"
            resultMap="prescriptionStatisticsMap">
        select
        r.APP_ID,
        r.INS_CODE,
        r.DEPT_ID,
        r.DEPT_NAME,
        p.PRE_DOCTOR,
        p.PRE_DOCTORNAME,
        r.PATIENT_ID,
        r.REC_NAME,
        case r.REC_GENDER when 'F' then '女' when 'M' then '男' end REC_GENDER,
        CONCAT_WS('', r.REC_AGE1, r.REC_AGEUNIT1, r.REC_AGE2, r.REC_AGEUNIT2) REC_AGE,
        date_format(p.PRE_TIME, '%Y-%m-%d %H:%i:%s') PRE_TIME,
        p.REC_ID,
        p.PRE_ID,
        p.PRE_NO,
        r.DIS_ID,
        r.DIS_NAME,
        r.SYM_ID,
        r.SYM_NAME,
        r.THE_NAMES,
        r.WESTERN_DISEASE_ID,
        r.WESTERN_DISEASE,
        '科研处方' as PRE_ORIGIN,
        case p.PRE_TYPE
            when '1' then '内服中药方'
            when '2' then '外用中药方'
            when '3' then '中成药方'
            when '4' then '适宜技术方'
            when '5' then '制剂'
            end PRE_TYPE,
        p.PRE_MAT_TYPE,
        p.STORE_ID,
        p.STORE_NAME,
        case r.REC_TRE_TYPE when '1' then '门诊' when '2' then '住院' end REC_TRE_TYPE,
        CASE p.PRE_TYPE
            WHEN '1' THEN CONCAT_WS('，', p.PRE_DESCRIPTION, p.PRE_FREQUENCY, CONCAT('每次', PRE_N_ML_NAME))
            WHEN '2' THEN CONCAT_WS('，', p.PRE_SMOKE_TYPE, p.PRE_FREQUENCY, CONCAT('每袋', PRE_N_ML_NAME))
        END PRE_DESCRIPTION,
        p.PRE_NUM,
        CONVERT(p.MAT_TOL_MONEY, DECIMAL(10,2)) MAT_TOL_MONEY,
        CONVERT(p.PRE_TOL_MONEY, DECIMAL(10,2)) PRE_TOL_MONEY,
        i.YPMC_HIS,
        i.MAT_DOSE,
        i.BZDW_HIS,
        i.YFMC_HIS,
        GROUP_CONCAT(CONCAT_WS('', i.YPMC_HIS, 0+CAST(i.MAT_DOSE AS CHAR(10)), i.BZDW_HIS, CONCAT('(',i.YFMC_HIS,')'))) mats,
        p.INFO_SCIENTIFIC_PREPARATION,
        p.IS_SCIENTIFIC_PREPARATION

        from t_prescription p

        left join t_record r on r.REC_ID = p.REC_ID
        left join t_prescription_item i on i.PRE_ID = p.PRE_ID
        <include refid="detailedWhere2"/>
  <!--      GROUP BY p.PRE_ID
        order by p.pre_time
        -->
        GROUP BY p.PRE_NO
        ORDER BY p.PRE_NO
        <if test="limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <sql id="detailedWhere">
        <where>
            p.is_del = '0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110
            <if test="appId != null and appId != ''">
                and r.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode != ''">
                and r.INS_CODE = #{insCode}
            </if>
            <if test="deptId != null and deptId != ''">
                and r.DEPT_ID = #{deptId}
            </if>
            <if test="doctorName != null and doctorName != ''">
                and p.PRE_DOCTORNAME like CONCAT('%', #{doctorName}, '%')
            </if>
            <if test="patientName != null and patientName != ''">
                and r.REC_NAME like CONCAT('%', #{patientName}, '%')
            </if>
            <if test="patientGender != null and patientGender != ''">
                and r.REC_GENDER = #{patientGender}
            </if>
            <if test="patientAgeStart != null and patientAgeStart != ''">
                and r.REC_AGE1 &gt;= #{patientAgeStart}
            </if>
            <if test="patientAgeEnd != null and patientAgeEnd != ''">
                and r.REC_AGE1 &lt;= #{patientAgeEnd}
            </if>
            <if test="preTimeStart != null and preTimeStart != ''">
                and p.PRE_TIME &gt;= #{preTimeStart}
            </if>
            <if test="preTimeEnd != null and preTimeEnd != ''">
                and p.PRE_TIME &lt;= concat(#{preTimeEnd}, ' 23:59:59')
            </if>
            <if test="preNo != null and preNo != ''">
                and p.PRE_NO = #{preNo}
            </if>
            <if test="disId != null and disId != ''">
                and r.DIS_ID = #{disId}
            </if>
            <if test="disName != null and disName != ''">
                and r.DIS_NAME like CONCAT('%', #{disName}, '%')
            </if>
            <if test="symId != null and symId != ''">
                and r.SYM_ID = #{symId}
            </if>
            <if test="symName != null and symName != ''">
                and r.SYM_NAME like CONCAT('%', #{symName}, '%')
            </if>
            <if test="theNames != null and theNames != ''">
                and r.THE_NAMES like CONCAT('%', #{theNames}, '%')
            </if>
            <if test="westernDiseaseId != null and westernDiseaseId != ''">
                and r.WESTERN_DISEASEID = #{westernDiseaseId}
            </if>
            <if test="westernDisease != null and westernDisease != ''">
                and r.WESTERN_DISEASE like CONCAT('%', #{westernDisease}, '%')
            </if>
            <if test="preOrigin != null and preOrigin != ''">
                and p.PRE_ORIGIN in
                <foreach collection="preOrigin.split(',')" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="recTreType != null and recTreType != ''">
                and r.REC_TRE_TYPE = #{recTreType}
            </if>
            <if test="storeId != null and storeId != ''">
                and p.STORE_ID = #{storeId}
            </if>
            <if test="preNumStart != null and preNumStart != ''">
                and p.PRE_NUM &gt;= #{preNumStart}
            </if>
            <if test="preNumEnd != null and preNumEnd != ''">
                and p.PRE_NUM &lt;= #{preNumEnd}
            </if>
            <if test="matTolMoneyStart != null and matTolMoneyStart != ''">
                and p.MAT_TOL_MONEY &gt;= #{matTolMoneyStart}
            </if>
            <if test="matTolMoneyEnd != null and matTolMoneyEnd != ''">
                and p.MAT_TOL_MONEY &lt;= #{matTolMoneyEnd}
            </if>
            <if test="preTolMoneyStart != null and preTolMoneyStart != ''">
                and p.PRE_TOL_MONEY &gt;= #{preTolMoneyStart}
            </if>
            <if test="preTolMoneyEnd != null and preTolMoneyEnd != ''">
                and p.PRE_TOL_MONEY &lt;= #{preTolMoneyEnd}
            </if>
            <if test="mats != null and mats != ''">
                and (i.YPMC_HIS like CONCAT('%', #{mats}, '%')
                or concat(0+CAST(i.MAT_DOSE AS CHAR), i.BZDW_HIS) = #{mats}
                or i.YFMC_HIS = #{mats}
                )
            </if>
            <choose>
                <when test="preType != null and preType == '1'.toString()">
                    and p.PRE_TYPE = '1'
                    <if test="preDescription != null and preDescription != ''">
                        and (p.PRE_DESCRIPTION like CONCAT('%', #{preDescription}, '%')
                        or p.PRE_FREQUENCY like CONCAT('%', #{preDescription}, '%'))
                    </if>
                </when>
                <when test="preType != null and preType == '2'.toString()">
                    and p.PRE_TYPE = '2'
                    <if test="preDescription != null and preDescription != ''">
                        and (p.PRE_SMOKE_TYPE like CONCAT('%', #{preDescription}, '%')
                        or p.PRE_FREQUENCY like CONCAT('%', #{preDescription}, '%'))
                    </if>
                </when>
                <otherwise>
                    and locate(p.PRE_TYPE, #{preType}) > 0
                    <if test="preDescription != null and preDescription != ''">
                        and (p.PRE_DESCRIPTION like CONCAT('%', #{preDescription}, '%')
                        or p.PRE_SMOKE_TYPE like CONCAT('%', #{preDescription}, '%')
                        or p.PRE_FREQUENCY like CONCAT('%', #{preDescription}, '%'))
                    </if>
                </otherwise>
            </choose>
        </where>
    </sql>
    <sql id="detailedWhere2">
        <where>
            p.is_del = '0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110 and  p.IS_SCIENTIFIC_PREPARATION = 1
            <if test="appId != null and appId != ''">
                and r.APP_ID = #{appId}
            </if>
            <if test="infoScientificPreparation != null and infoScientificPreparation != ''">
                and p.INFO_SCIENTIFIC_PREPARATION = #{infoScientificPreparation}
            </if>
            <if test="insCode != null and insCode != ''">
                and r.INS_CODE = #{insCode}
            </if>
            <if test="deptId != null and deptId != ''">
                and r.DEPT_ID = #{deptId}
            </if>
            <if test="doctorName != null and doctorName != ''">
                and p.PRE_DOCTORNAME like CONCAT('%', #{doctorName}, '%')
            </if>
            <if test="patientName != null and patientName != ''">
                and r.REC_NAME like CONCAT('%', #{patientName}, '%')
            </if>
            <if test="patientGender != null and patientGender != ''">
                and r.REC_GENDER = #{patientGender}
            </if>
            <if test="patientAgeStart != null and patientAgeStart != ''">
                and r.REC_AGE1 &gt;= #{patientAgeStart}
            </if>
            <if test="patientAgeEnd != null and patientAgeEnd != ''">
                and r.REC_AGE1 &lt;= #{patientAgeEnd}
            </if>
            <if test="preTimeStart != null and preTimeStart != ''">
                and p.PRE_TIME &gt;= #{preTimeStart}
            </if>
            <if test="preTimeEnd != null and preTimeEnd != ''">
                and p.PRE_TIME &lt;= concat(#{preTimeEnd}, ' 23:59:59')
            </if>
            <if test="preNo != null and preNo != ''">
                and p.PRE_NO = #{preNo}
            </if>
            <if test="disId != null and disId != ''">
                and r.DIS_ID = #{disId}
            </if>
            <if test="disName != null and disName != ''">
                and r.DIS_NAME like CONCAT('%', #{disName}, '%')
            </if>
            <if test="symId != null and symId != ''">
                and r.SYM_ID = #{symId}
            </if>
            <if test="symName != null and symName != ''">
                and r.SYM_NAME like CONCAT('%', #{symName}, '%')
            </if>
            <if test="theNames != null and theNames != ''">
                and r.THE_NAMES like CONCAT('%', #{theNames}, '%')
            </if>
            <if test="westernDiseaseId != null and westernDiseaseId != ''">
                and r.WESTERN_DISEASEID = #{westernDiseaseId}
            </if>
            <if test="westernDisease != null and westernDisease != ''">
                and r.WESTERN_DISEASE like CONCAT('%', #{westernDisease}, '%')
            </if>
            <if test="preOrigin != null and preOrigin != ''">
                and p.PRE_ORIGIN in
                <foreach collection="preOrigin.split(',')" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="recTreType != null and recTreType != ''">
                and r.REC_TRE_TYPE = #{recTreType}
            </if>
            <if test="storeId != null and storeId != ''">
                and p.STORE_ID = #{storeId}
            </if>
            <if test="preNumStart != null and preNumStart != ''">
                and p.PRE_NUM &gt;= #{preNumStart}
            </if>
            <if test="preNumEnd != null and preNumEnd != ''">
                and p.PRE_NUM &lt;= #{preNumEnd}
            </if>
            <if test="matTolMoneyStart != null and matTolMoneyStart != ''">
                and p.MAT_TOL_MONEY &gt;= #{matTolMoneyStart}
            </if>
            <if test="matTolMoneyEnd != null and matTolMoneyEnd != ''">
                and p.MAT_TOL_MONEY &lt;= #{matTolMoneyEnd}
            </if>
            <if test="preTolMoneyStart != null and preTolMoneyStart != ''">
                and p.PRE_TOL_MONEY &gt;= #{preTolMoneyStart}
            </if>
            <if test="preTolMoneyEnd != null and preTolMoneyEnd != ''">
                and p.PRE_TOL_MONEY &lt;= #{preTolMoneyEnd}
            </if>
            <if test="mats != null and mats != ''">
                and (i.YPMC_HIS like CONCAT('%', #{mats}, '%')
                or concat(0+CAST(i.MAT_DOSE AS CHAR), i.BZDW_HIS) = #{mats}
                or i.YFMC_HIS = #{mats}
                )
            </if>
            <choose>
                <when test="preType != null and preType == '1'.toString()">
                    and p.PRE_TYPE = '1'
                    <if test="preDescription != null and preDescription != ''">
                        and (p.PRE_DESCRIPTION like CONCAT('%', #{preDescription}, '%')
                        or p.PRE_FREQUENCY like CONCAT('%', #{preDescription}, '%'))
                    </if>
                </when>
                <when test="preType != null and preType == '2'.toString()">
                    and p.PRE_TYPE = '2'
                    <if test="preDescription != null and preDescription != ''">
                        and (p.PRE_SMOKE_TYPE like CONCAT('%', #{preDescription}, '%')
                        or p.PRE_FREQUENCY like CONCAT('%', #{preDescription}, '%'))
                    </if>
                </when>
                <otherwise>
                    and locate(p.PRE_TYPE, #{preType}) > 0
                    <if test="preDescription != null and preDescription != ''">
                        and (p.PRE_DESCRIPTION like CONCAT('%', #{preDescription}, '%')
                        or p.PRE_SMOKE_TYPE like CONCAT('%', #{preDescription}, '%')
                        or p.PRE_FREQUENCY like CONCAT('%', #{preDescription}, '%'))
                    </if>
                </otherwise>
            </choose>
        </where>
    </sql>
</mapper>