<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.statistics.StatisticsInstitutionWorkloadMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.statistics.StatisticsInstitutionWorkload">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="reg_count" jdbcType="VARCHAR" property="regCount" />
        <result column="nf_count" jdbcType="VARCHAR" property="nfCount" />
        <result column="wy_count" jdbcType="VARCHAR" property="wyCount" />
        <result column="zc_count" jdbcType="VARCHAR" property="zcCount" />
        <result column="sy_count" jdbcType="VARCHAR" property="syCount" />
        <result column="pCount" jdbcType="VARCHAR" property="pcount" />
        <result column="qreg_count" jdbcType="VARCHAR" property="qregCount" />
        <result column="qnf_count" jdbcType="VARCHAR" property="qnfCount" />
        <result column="qwy_count" jdbcType="VARCHAR" property="qwyCount" />
        <result column="qzc_count" jdbcType="VARCHAR" property="qzcCount" />
        <result column="qsy_count" jdbcType="VARCHAR" property="qsyCount" />
        <result column="qp_count" jdbcType="VARCHAR" property="qpCount" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="insert_date" jdbcType="TIMESTAMP" property="insertDate" />
    </resultMap>


    <sql id="Base_Column_List">
    id,app_id,app_name,ins_code,ins_name,reg_count,nf_count,wy_count,zc_count,sy_count,pCount,qreg_count,qnf_count,qwy_count,qzc_count,qsy_count,qp_count,create_date,insert_date
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="StatisticsInstitutionWorkload">
        delete from statistics_institution_workload where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from statistics_institution_workload where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="StatisticsInstitutionWorkload">
        insert into statistics_institution_workload (<include refid="Base_Column_List" />) values
        (#{id},#{appId},#{appName},#{insCode},#{insName},#{regCount},#{nfCount},#{wyCount},#{zcCount},#{syCount},#{pcount},#{qregCount},#{qnfCount},#{qwyCount},#{qzcCount},#{qsyCount},#{qpCount},#{createDate},#{insertDate})
    </insert>

    <insert id="insertList" parameterType="List">
        REPLACE INTO statistics_institution_workload (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.regCount},#{item.nfCount},#{item.wyCount},#{item.zcCount},#{item.syCount},#{item.pcount},#{item.qregCount},#{item.qnfCount},#{item.qwyCount},#{item.qzcCount},#{item.qsyCount},#{item.qpCount},#{item.createDate},#{item.insertDate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="StatisticsInstitutionWorkload">
        update statistics_institution_workload
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="regCount != null">
                reg_count = #{ regCount },
             </if>
             <if test="nfCount != null">
                nf_count = #{ nfCount },
             </if>
             <if test="wyCount != null">
                wy_count = #{ wyCount },
             </if>
             <if test="zcCount != null">
                zc_count = #{ zcCount },
             </if>
             <if test="syCount != null">
                sy_count = #{ syCount },
             </if>
             <if test="pcount != null">
                pCount = #{ pcount },
             </if>
             <if test="qregCount != null">
                qreg_count = #{ qregCount },
             </if>
             <if test="qnfCount != null">
                qnf_count = #{ qnfCount },
             </if>
             <if test="qwyCount != null">
                qwy_count = #{ qwyCount },
             </if>
             <if test="qzcCount != null">
                qzc_count = #{ qzcCount },
             </if>
             <if test="qsyCount != null">
                qsy_count = #{ qsyCount },
             </if>
             <if test="qpCount != null">
                qp_count = #{ qpCount },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="insertDate != null">
                insert_date = #{ insertDate },
             </if>
        </set>
        where id = #{ id }
    </update>
    <update id="updateList">
        <foreach collection="list" item="item" index="index" separator=";" >

        UPDATE statistics_institution_workload
            <set>

            <if test="item.qregCount != null">
                qreg_count = #{item.qregCount},
            </if>
            <if test="item.qnfCount != null">
                qnf_count = #{item.qnfCount},
            </if>
            <if test="item.qwyCount != null">
                qwy_count = #{item.qwyCount},
            </if>
            <if test="item.qzcCount != null">
                qzc_count = #{item.qzcCount},
            </if>
            <if test="item.qsyCount != null">
                qsy_count = #{item.qsyCount},
            </if>
            <if test="item.qpCount != null">
                qp_count = #{item.qpCount},
            </if>
            </set>

            WHERE  app_id = #{item.appId} and ins_code = #{item.insCode}
<!--            and create_date like  CONCAT('%',  DATE_FORMAT( #{ item.createDate}, '%Y') , '%')-->
            and DATE_FORMAT( create_date, '%Y') = #{ item.createDate}
        </foreach>
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from statistics_institution_workload where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="StatisticsInstitutionWorkload" resultMap="BaseResultMap">
        SELECT id,app_id,app_name,ins_code,ins_name,reg_count,nf_count,wy_count,zc_count,sy_count,pCount,qreg_count,qnf_count,qwy_count,qzc_count,qsy_count,qp_count,create_date,insert_date
        from statistics_institution_workload
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getInstitutionWorkload"
            resultType="com.jiuzhekan.cbkj.beans.statistics.StatisticsInstitutionWorkload">

        select
        app_name as   appName,
        ins_code as   insCode,
        ins_name as   insName,
        reg_count as  regCount,
        nf_count as   nfCount,
        wy_count as   wyCount,
        zc_count as   zcCount,
        sy_count as   syCount,
        pCount as     pCount,
        qreg_count as qregCount,
        qnf_count as  qnfCount,
        qwy_count as  qwyCount,
        qzc_count as  qzcCount,
        qsy_count as  qsyCount,
        qp_count as   qpCount,
        create_date as createDate
        from statistics_institution_workload
        where    DATE_FORMAT(create_date, '%Y-%m')= #{month}
        <if test="appId != null  and appId != ''">
            AND  app_id = #{appId}
        </if>
        <if test="insCodeList != null  and insCodeList.size() > 0">
            AND  ins_code in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


    </select>

    <select id="getInstitutionWorkloadByYear"
            resultType="com.jiuzhekan.cbkj.beans.statistics.StatisticsInstitutionWorkload">

        select
        app_name as   appName,
        ins_code as   insCode,
        ins_name as   insName,

        0 as  regCount,
        0 as   nfCount,
        0 as   wyCount,
        0 as   zcCount,
        0 as   syCount,
        0 as     pCount,

        qreg_count as qregCount,
        qnf_count as  qnfCount,
        qwy_count as  qwyCount,
        qzc_count as  qzcCount,
        qsy_count as  qsyCount,
        qp_count as   qpCount,
        create_date as createDate
        from statistics_institution_workload
        where    DATE_FORMAT(create_date, '%Y')= #{year}
        <if test="appId != null  and appId != ''">
            AND  app_id = #{appId}
        </if>
                <if test="insCodeList != null  and insCodeList.size() > 0">
                    AND  ins_code in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

        GROUP BY  ins_code,DATE_FORMAT(create_date, '%Y')
    </select>

    <select id="getInstitutionWorkloadTotal" resultType="com.jiuzhekan.cbkj.beans.statistics.StatisticsInstitutionWorkload">
        SELECT
        '所有医院合计' AS insName,
        SUM(reg_count) regCount ,
        SUM(nf_count) nfCount ,
        SUM(wy_count) wyCount ,
        SUM(zc_count) zcCount ,
        SUM(sy_count) syCount ,
        SUM(pCount) pCount ,
        SUM(qreg_count) qregCount ,
        SUM(qnf_count) qnfCount ,
        SUM(qwy_count) qwyCount ,
        SUM(qzc_count) qzcCount ,
        SUM(qsy_count) qsyCount ,
        SUM(qp_count) qpCount
        FROM
        statistics_institution_workload
        WHERE    DATE_FORMAT(create_date, '%Y-%m')  = #{month}
        <if test="appId != null  and appId != ''">
            AND  app_id = #{appId}
        </if>
        <if test="insCodeList != null  and insCodeList.size() > 0">
            AND  ins_code in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY create_date
    </select>

    <select id="getInstitutionWorkloadTotalByYear" resultType="com.jiuzhekan.cbkj.beans.statistics.StatisticsInstitutionWorkload">
        SELECT
        '所有医院合计' AS insName,
        0 regCount ,
        0 nfCount ,
        0 wyCount ,
        0 zcCount ,
        0 syCount ,
        0 pCount ,
        SUM(qreg_count) qregCount ,
        SUM(qnf_count) qnfCount ,
        SUM(qwy_count) qwyCount ,
        SUM(qzc_count) qzcCount ,
        SUM(qsy_count) qsyCount ,
        SUM(qp_count) qpCount
        FROM
        statistics_institution_workload
        WHERE    DATE_FORMAT(create_date, '%Y')  = #{year}
        <if test="appId != null  and appId != ''">
            AND  app_id = #{appId}
        </if>
                <if test="insCodeList != null  and insCodeList.size() > 0">
                    AND  ins_code in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
        GROUP BY create_date
        LIMIT 1

    </select>
</mapper>