<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.statistics.TAnalysisNationalIndexMapper">


<!--    <select id="getTAnalysisNationalIndexVo" parameterType="com.jiuzhekan.cbkj.beans.statistics.TAnalysisNationalIndexVo"-->
<!--            resultType="com.jiuzhekan.cbkj.beans.statistics.TAnalysisNationalIndexVo">-->
<!--        select sdm.DEP_ID as deptId , sdm.DEP_NAME as deptName ,-->
<!--        COUNT((tp.PRE_MAT_TYPE != '3' and tp.PRE_MZ_ZY = '1') or NULL) as PrescriptionNum ,-->
<!--        COUNT(((tp.PRE_MAT_TYPE = '1' or tp.PRE_MAT_TYPE = '4') and tp.PRE_MZ_ZY = '1') or NULL) as PreSZOrXBZNum ,-->
<!--        COUNT((tp.REC_ID != '' and (tp.PRE_MAT_TYPE = '1' or tp.PRE_MAT_TYPE = '4'-->
<!--        or tp.PRE_MAT_TYPE = '2'or tp.PRE_MAT_TYPE = '5') and tp.PRE_MZ_ZY = '1') or NULL) as PeopleUseNum,-->
<!--        COUNT((tp.REC_ID != '' and tp.PRE_TYPE = '4' and tp.PRE_MZ_ZY = '1') or NULL) as PeopleNonUseNum ,-->
<!--        SUM(-->
<!--	        case-->
<!--		        when tp.PRE_MAT_TYPE != '3' then tp.MAT_TOL_MONEY-->
<!--		        else 0-->
<!--	        end-->
<!--        ) as CMedicineIncome ,-->
<!--        SUM(-->
<!--	        case-->
<!--		        when tp.PRE_MAT_TYPE = '1' then tp.MAT_TOL_MONEY-->
<!--		        when tp.PRE_MAT_TYPE = '2' then tp.MAT_TOL_MONEY-->
<!--		        when tp.PRE_MAT_TYPE = '4' then tp.MAT_TOL_MONEY-->
<!--		        when tp.PRE_MAT_TYPE = '5' then tp.MAT_TOL_MONEY-->
<!--		        else 0-->
<!--	        end-->
<!--        ) as CMedicinePiecesIncome-->
<!--        from sys_department sdm-->
<!--        left join t_prescription tp on tp.DEPT_ID = sdm.DEP_ID-->
<!--        and tp.is_del = '0' and tp.is_pay = '1'-->
<!--        and tp.pre_time &gt;= #{startTimeStr} and tp.pre_time &lt;= #{endTimeStr}-->
<!--        <if test="null != insCode and insCode != ''">-->
<!--            and tp.INS_CODE = #{insCode}-->
<!--        </if>-->
<!--        <if test="null != appId and appId != ''">-->
<!--            and tp.APP_ID = #{appId}-->
<!--        </if>-->
<!--        left join t_record tr on tr.REC_ID = tp.REC_ID-->
<!--        and tr.is_del = '0'-->
<!--        and tr.REC_TRE_TIME &gt;= #{startTimeStr} and tr.REC_TRE_TIME &lt;= #{endTimeStr}-->
<!--        <if test="null != insCode and insCode != ''">-->
<!--            and tr.INS_CODE = #{insCode}-->
<!--        </if>-->
<!--        <if test="null != appId and appId != ''">-->
<!--            and tr.APP_ID = #{appId}-->
<!--        </if>-->
<!--        where sdm.is_del = '0' and sdm.NATIONAL_SEQN != ''-->
<!--        <if test="null != insCode and insCode != ''">-->
<!--            and sdm.INS_CODE = #{insCode}-->
<!--        </if>-->
<!--        <if test="null != appId and appId != ''">-->
<!--            and sdm.APP_ID = #{appId}-->
<!--        </if>-->
<!--        group by sdm.DEP_ID-->
<!--        ORDER BY sdm.NATIONAL_SEQN-->
<!--    </select>-->

<!--    <insert id="insertNationalGoal" parameterType="com.jiuzhekan.cbkj.beans.statistics.TAnalysisNationalIndexVo">-->
<!--        <foreach collection="list" item="item" separator=";">-->
<!--            insert into t_national_ananlysis_goal(app_id , ins_code , dep_id , dep_name , prescription_proportion ,-->
<!--            preszorxbz_proportion , people_use_proportion , people_nonuse_proportion , cmedicine_income_proportion ,-->
<!--            cmedicinepieces_income_proportion , create_date , create_user) values (#{item.appId} , #{item.insCode} ,-->
<!--            #{item.deptId} ,#{item.deptName} , #{item.prescriptionProportion} , #{item.preSZOrXBZProportion} ,-->
<!--            #{item.peopleUseProportion} ,#{item.peopleNonUseProportion} , #{item.CMedicineIncomeProportion} ,-->
<!--            #{item.CMedicinePiecesIncomeProportion} , #{item.createDate} , #{item.createUser})-->
<!--        </foreach>-->
<!--    </insert>-->

<!--    <delete id="deleteNationalGoal" parameterType="String">-->
<!--        delete from t_national_ananlysis_goal where app_id = #{appId} and ins_code = #{insCode}-->
<!--        <if test="null != depId and depId != ''">-->
<!--            and dep_id = #{depId}-->
<!--        </if>-->
<!--    </delete>-->


<!--    <select id="getDeptGoal" parameterType="com.jiuzhekan.cbkj.beans.statistics.TAnalysisNationalIndexVo"-->
<!--            resultType="com.jiuzhekan.cbkj.beans.statistics.TAnalysisNationalIndexVo">-->
<!--        select app_id as appId , ins_code as insCode, dep_id as deptId, dep_name as deptName,-->
<!--        ifnull(prescription_proportion, 0) as prescriptionProportion, ifnull(preszorxbz_proportion , 0) as preSZOrXBZProportion,-->
<!--        ifnull(people_use_proportion , 0) as peopleUseProportion, ifnull(people_nonuse_proportion , 0) as peopleNonUseProportion,-->
<!--        ifnull(cmedicine_income_proportion , 0) as CMedicineIncomeProportion,-->
<!--        ifnull(cmedicinepieces_income_proportion , 0) as CMedicinePiecesIncomeProportion from t_national_ananlysis_goal-->
<!--        where 1=1-->
<!--        <if test="null != appId and appId != ''">-->
<!--            and app_id = #{appId}-->
<!--        </if>-->
<!--        <if test="null != insCode and insCode != ''">-->
<!--            and ins_code = #{insCode}-->
<!--        </if>-->
<!--        <if test="null != deptId and deptId != ''">-->
<!--            and dep_id = #{deptId}-->
<!--        </if>-->
<!--        <if test="null != deptName and deptName != ''">-->
<!--            and dep_name = #{deptName}-->
<!--        </if>-->
<!--    </select>-->

<!--    <select id="getDeps" parameterType="com.jiuzhekan.cbkj.beans.statistics.TAnalysisNationalIndexVo"-->
<!--            resultType="com.jiuzhekan.cbkj.beans.statistics.TAnalysisNationalIndexVo">-->
<!--        select APP_ID as appId, INS_CODE as insCode, DEP_ID as deptId, DEP_NAME as deptName,-->
<!--        NATIONAL_SEQN as nationalSeqn from sys_department-->
<!--        where NATIONAL_SEQN is not null-->
<!--        <if test="null != appId and appId != ''">-->
<!--            and APP_ID = #{appId}-->
<!--        </if>-->
<!--        <if test="null != insCode and insCode != ''">-->
<!--            and INS_CODE = #{insCode}-->
<!--        </if>-->
<!--        order by NATIONAL_SEQN-->
<!--    </select>-->

<!--&lt;!&ndash;        该sql语句为按照医生统计sql，不是完整版，需要补充科室查询中的条件&ndash;&gt;-->
<!--    <select id="getDoctor" parameterType="com.jiuzhekan.cbkj.beans.statistics.TAnalysisNationalIndexVo"-->
<!--            resultType="com.jiuzhekan.cbkj.beans.statistics.TAnalysisNationalIndexVo">-->
<!--        select sdm.DEP_ID as deptId , sdm.DEP_NAME as deptName , sai.name_zh as doctorName,sai.id as doctorId,-->
<!--        COUNT((tp.PRE_MAT_TYPE != '3' and tp.PRE_MZ_ZY = '1') or NULL) as PrescriptionNum ,-->
<!--        COUNT(((tp.PRE_MAT_TYPE = '1' or tp.PRE_MAT_TYPE = '4') and tp.PRE_MZ_ZY = '1') or NULL) as PreSZOrXBZNum ,-->
<!--        COUNT((tp.REC_ID != '' and (tp.PRE_MAT_TYPE = '1' or tp.PRE_MAT_TYPE = '4'-->
<!--        or tp.PRE_MAT_TYPE = '2'or tp.PRE_MAT_TYPE = '5') and tp.PRE_MZ_ZY = '1') or NULL) as PeopleUseNum,-->
<!--        COUNT((tp.REC_ID != '' and tp.PRE_TYPE = '4' and tp.PRE_MZ_ZY = '1') or NULL) as PeopleNonUseNum ,-->
<!--        SUM(-->
<!--            case-->
<!--                when tp.PRE_MAT_TYPE != '3' then tp.MAT_TOL_MONEY-->
<!--                else 0-->
<!--            end-->
<!--        ) as CMedicineIncome ,-->
<!--        SUM(-->
<!--            case-->
<!--                when tp.PRE_MAT_TYPE = '1' then tp.MAT_TOL_MONEY-->
<!--                when tp.PRE_MAT_TYPE = '2' then tp.MAT_TOL_MONEY-->
<!--                when tp.PRE_MAT_TYPE = '4' then tp.MAT_TOL_MONEY-->
<!--                when tp.PRE_MAT_TYPE = '5' then tp.MAT_TOL_MONEY-->
<!--                else 0-->
<!--            end-->
<!--        ) as CMedicinePiecesIncome-->
<!--        from sys_department sdm-->
<!--        left join sys_admin_practice sap on sap.dep_id = sdm.DEP_ID-->
<!--        <if test="null != insCode and insCode != ''">-->
<!--            and sdm.INS_CODE = #{insCode}-->
<!--        </if>-->
<!--        <if test="null != appId and appId != ''">-->
<!--            and sdm.APP_ID = #{appId}-->
<!--        </if>-->
<!--        left join sys_admin_info sai on sai.id = sap.admin_id-->
<!--        left join t_prescription tp on tp.PRE_DOCTOR = sai.id-->
<!--        and tp.is_del = '0' and tp.is_pay = '1'-->
<!--        and tp.pre_time &gt;= #{startTimeStr} and tp.pre_time &lt;= #{endTimeStr}-->
<!--        left join t_record tr on tr.REC_ID = tp.REC_ID-->
<!--        <if test="null != insCode and insCode != ''">-->
<!--            and tp.INS_CODE = #{insCode}-->
<!--        </if>-->
<!--        <if test="null != appId and appId != ''">-->
<!--            and tp.APP_ID = #{appId}-->
<!--        </if>-->
<!--        where sdm.is_del = '0' and sdm.NATIONAL_SEQN != ''-->
<!--        <if test="null != insCode and insCode != ''">-->
<!--            and sdm.INS_CODE = #{insCode}-->
<!--        </if>-->
<!--        <if test="null != appId and appId != ''">-->
<!--            and sdm.APP_ID = #{appId}-->
<!--        </if>-->
<!--        group by sap.practice_id-->
<!--        ORDER BY sdm.DEP_ID-->
<!--    </select>-->

</mapper>
