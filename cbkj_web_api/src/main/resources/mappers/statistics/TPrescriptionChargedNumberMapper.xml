<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.statistics.TPrescriptionChargedNumberMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.statistics.TPrescriptionChargedNumber">
        <id column="ID" property="id"/>
        <result column="PRE_TIME" property="preTime"/>
        <result column="INS_NAME" property="insName"/>
        <result column="PRE_MZ_ZY" property="preMzZy"/>
        <result column="DEPT_NAME" property="deptName"/>
        <result column="PRE_DOCTORNAME" property="preDoctorname"/>
        <result column="PRE_CHARGED_NUMBER" property="preChargedNumber"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PRE_TIME, INS_NAME, PRE_MZ_ZY, DEPT_NAME, PRE_DOCTORNAME, PRE_CHARGED_NUMBER
    </sql>

    <select id="getPrescriptionChargedNumber" parameterType="Map"
            resultType="com.jiuzhekan.cbkj.beans.statistics.TPrescriptionChargedNumber">
        SELECT REPLACE(MD5(UUID()), '-', '') AS id,
               t.`PAY_TIME`                  AS preTime,
               r.APP_ID                      as appId,
               r.INS_CODE                    as insCode,
               t.`PRE_MZ_ZY`                 AS preMzZy,
               r.`DEPT_NAME`                 AS deptName,
               t.pre_doctorname              AS preDoctorname,
               COUNT(*)                      AS preChargedNumber
        FROM `t_prescription` t
                 inner JOIN `t_record` r ON t.rec_id = r.rec_id
        WHERE t.rec_ext_type >= 50
          and t.REC_EXT_TYPE != 110
          AND t.is_del = '0'
          AND t.`IS_PAY` = '1' and TIMESTAMPDIFF(DAY,#{yesDay},DATE_ADD(t.PAY_TIME,INTERVAL 1 DAY) )=1
        GROUP BY t.pre_doctorname, t.`PRE_MZ_ZY`, r.`DEPT_NAME`, DATE_FORMAT(t.PAY_TIME, '%Y-%m-%d')
    </select>
    <insert id="insertPrescriptionChargedNumber" parameterType="List">
        insert into t_prescription_charged_number(ID, PRE_TIME, INS_NAME, PRE_MZ_ZY, DEPT_NAME, PRE_DOCTORNAME,
        PRE_CHARGED_NUMBER)
        values
        <foreach collection="list" separator="," item="item" open="(" close=")" >

            #{item.id,jdbcType=VARCHAR},
            #{item.preTime},
            #{item.insName},
            #{item.preMzZy},
            #{item.deptName},
            #{item.preDoctorname},
            #{item.preChargedNumber}

        </foreach>
    </insert>


    <select id="getPreChargedNumByTime" parameterType="String"
            resultType="com.jiuzhekan.cbkj.beans.statistics.TPrescriptionChargedNumber">
        SELECT
        ID,
        PRE_TIME AS preTime,
        INS_NAME AS insName,
        CASE PRE_MZ_ZY WHEN 2 THEN '住院' ELSE '门诊' END AS preMzZy,
        DEPT_NAME AS deptName,
        PRE_DOCTORNAME AS preDoctorname,
        SUM(PRE_CHARGED_NUMBER) AS preChargedNumber
        FROM `t_prescription_charged_number`
        <where>
            INS_NAME = #{insName}
            <if test="preTimeBegin!=null and preTimeBegin!=''">
                AND PRE_TIME &gt;= DATE_FORMAT(#{preTimeBegin}, '%Y-%m-%d')
            </if>
            <if test="preTimeEnd!=null and preTimeEnd!=''">
                AND PRE_TIME &lt; DATE_SUB(DATE_FORMAT(#{preTimeEnd}, '%Y-%m-%d'),INTERVAL '-1' DAY)
            </if>
        </where>
        GROUP BY PRE_MZ_ZY,DEPT_NAME,PRE_DOCTORNAME
        ORDER BY INS_NAME,PRE_MZ_ZY, DEPT_NAME, preChargedNumber desc
    </select>
    <select id="getNumberByTimeAndIns" parameterType="String" resultType="integer">
        SELECT
        SUM(PRE_CHARGED_NUMBER)
        FROM t_prescription_charged_number
        <where>
            INS_NAME = #{insName}
            <if test="preTimeBegin!=null and preTimeBegin!=''">
                AND PRE_TIME &gt;= DATE_FORMAT(#{preTimeBegin}, '%Y-%m-%d')
            </if>
            <if test="preTimeEnd!=null and preTimeEnd!=''">
                AND PRE_TIME &lt; DATE_SUB(DATE_FORMAT(#{preTimeEnd}, '%Y-%m-%d'),INTERVAL '-1' DAY)
            </if>
        </where>
    </select>
    <select id="getCountByTime" parameterType="String" resultType="java.util.HashMap">
        SELECT
        INS_NAME AS insName,
        SUM(PRE_CHARGED_NUMBER) AS count
        FROM `t_prescription_charged_number`
        <where>
            <if test="preTimeBegin!=null and preTimeBegin!=''">
                AND PRE_TIME &gt;= DATE_FORMAT(#{preTimeBegin}, '%Y-%m-%d')
            </if>
            <if test="preTimeEnd!=null and preTimeEnd!=''">
                AND PRE_TIME &lt; DATE_SUB(DATE_FORMAT(#{preTimeEnd}, '%Y-%m-%d'),INTERVAL '-1' DAY)
            </if>
        </where>
        GROUP BY INS_NAME
    </select>

    <delete id="deletePrescriptionChargedNumber">
        DELETE
        FROM `t_prescription_charged_number`
    </delete>
    <select id="getBeginTime" resultType="java.lang.String">
        SELECT DATE_FORMAT(PRE_TIME, '%Y-%m-%d')
        FROM `t_prescription_charged_number`
        ORDER BY PRE_TIME ASC
        LIMIT 1
    </select>
    <select id="getEndTime" resultType="java.lang.String">
        SELECT DATE_FORMAT(PRE_TIME, '%Y-%m-%d')
        FROM `t_prescription_charged_number`
        ORDER BY PRE_TIME DESC
        LIMIT 1
    </select>
</mapper>
