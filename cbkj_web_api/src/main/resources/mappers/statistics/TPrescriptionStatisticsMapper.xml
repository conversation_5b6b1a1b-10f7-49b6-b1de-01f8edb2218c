<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.statistics.TPrescriptionStatisticsMapper">

    <select id="getCountGroupByPreTolMoney" parameterType="com.jiuzhekan.cbkj.beans.statistics.TPrescriptionVO" resultType="java.util.HashMap">
        SELECT SUM(if(  PRE_TOL_MONEY &lt; 50
        and IS_DEL='0'
        <if test="preTimeBegin != null and preTimeBegin != '' ">
            and PRE_TIME &gt;=  #{preTimeBegin}
        </if>
        <if test="preTimeEnd != null and preTimeEnd != '' ">
            and PRE_TIME &lt;=  #{preTimeEnd}
        </if>

        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        <if test="insCodeList != null and insCodeList.size() > 0">
            and INS_CODE in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        and REC_EXT_TYPE >= 50 and REC_EXT_TYPE != 110
        ,1,0)) as fiftybelow,
        SUM(if(  PRE_TOL_MONEY  &gt;= 50
        and PRE_TOL_MONEY &lt; 200
        and IS_DEL='0'
        <if test="preTimeBegin != null and preTimeBegin != '' ">
            and PRE_TIME &gt;=  #{preTimeBegin}
        </if>
        <if test="preTimeEnd != null and preTimeEnd != '' ">
            and PRE_TIME &lt;=  #{preTimeEnd}
        </if>

        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        <if test="insCodeList != null and insCodeList.size() > 0">
            and INS_CODE in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        and REC_EXT_TYPE >= 50 and REC_EXT_TYPE != 110

        ,1,0)) as fiftyToTwoHund ,
        SUM(if(    PRE_TOL_MONEY  &gt;= 200
        and PRE_TOL_MONEY &lt; 500
        and IS_DEL='0'
        <if test="preTimeBegin != null and preTimeBegin != '' ">
            and PRE_TIME &gt;=  #{preTimeBegin}
        </if>
        <if test="preTimeEnd != null and preTimeEnd != '' ">
            and PRE_TIME &lt;=  #{preTimeEnd}
        </if>

        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        <if test="insCodeList != null and insCodeList.size() > 0">
            and INS_CODE in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>

        and REC_EXT_TYPE >= 50 and REC_EXT_TYPE != 110
        ,1,0)) as twoHundToFiveHund,
        SUM(if(      PRE_TOL_MONEY  &gt;= 500
        and PRE_TOL_MONEY &lt; 1000
        and IS_DEL='0'
        <if test="preTimeBegin != null and preTimeBegin != '' ">
            and PRE_TIME &gt;=  #{preTimeBegin}
        </if>
        <if test="preTimeEnd != null and preTimeEnd != '' ">
            and PRE_TIME &lt;=  #{preTimeEnd}
        </if>

        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        <if test="insCodeList != null and insCodeList.size() > 0">
            and INS_CODE in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>

        and REC_EXT_TYPE >= 50 and REC_EXT_TYPE != 110
        ,1,0)) as fiveHundToThou,
        SUM(if(   PRE_TOL_MONEY  &gt; 1000
        and IS_DEL='0'
        <if test="preTimeBegin != null and preTimeBegin != '' ">
            and PRE_TIME &gt;=  #{preTimeBegin}
        </if>
        <if test="preTimeEnd != null and preTimeEnd != '' ">
            and PRE_TIME &lt;=  #{preTimeEnd}
        </if>

        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        <if test="insCodeList != null and insCodeList.size() > 0">
            and INS_CODE in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>


        and REC_EXT_TYPE >= 50 and REC_EXT_TYPE != 110
        ,1,0)) as thousandAbove

        from t_prescription
    </select>

    <select id="getCountByPreTolMoneyGroupByType" parameterType="com.jiuzhekan.cbkj.beans.statistics.TPrescriptionVO" resultType="java.util.HashMap">
        SELECT PRE_TYPE as preType,
        SUM(IF(
        IS_DEL='0'  and PRE_TOL_MONEY &lt; 50
        <if test="preTimeBegin != null and preTimeBegin != '' ">
            and PRE_TIME &gt;=  #{preTimeBegin}
        </if>
        <if test="preTimeEnd != null and preTimeEnd != '' ">
            and PRE_TIME &lt;=  #{preTimeEnd}
        </if>
        <if test="insCode != null and insCode != '' ">
            and INS_CODE = #{insCode}
        </if>
        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        and REC_EXT_TYPE >= 50 and REC_EXT_TYPE != 110
        ,1,0))as map50,

        SUM(IF(
        IS_DEL='0' and PRE_TOL_MONEY  &gt;= 50 and PRE_TOL_MONEY &lt; 200
        <if test="preTimeBegin != null and preTimeBegin != '' ">
            and PRE_TIME &gt;=  #{preTimeBegin}
        </if>
        <if test="preTimeEnd != null and preTimeEnd != '' ">
            and PRE_TIME &lt;=  #{preTimeEnd}
        </if>
        <if test="insCode != null and insCode != '' ">
            and INS_CODE = #{insCode}
        </if>
        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        and REC_EXT_TYPE >= 50 and REC_EXT_TYPE != 110
        ,1,0))as map50200,

        SUM(IF(
        IS_DEL='0' and PRE_TOL_MONEY  &gt;= 200 and PRE_TOL_MONEY &lt; 500
        <if test="preTimeBegin != null and preTimeBegin != '' ">
            and PRE_TIME &gt;=  #{preTimeBegin}
        </if>
        <if test="preTimeEnd != null and preTimeEnd != '' ">
            and PRE_TIME &lt;=  #{preTimeEnd}
        </if>
        <if test="insCode != null and insCode != '' ">
            and INS_CODE = #{insCode}
        </if>
        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        and REC_EXT_TYPE >= 50 and REC_EXT_TYPE != 110
        ,1,0))as map200500,

        SUM(IF(
        IS_DEL='0' and PRE_TOL_MONEY  &gt;= 500 and PRE_TOL_MONEY &lt; 1000
        <if test="preTimeBegin != null and preTimeBegin != '' ">
            and PRE_TIME &gt;=  #{preTimeBegin}
        </if>
        <if test="preTimeEnd != null and preTimeEnd != '' ">
            and PRE_TIME &lt;=  #{preTimeEnd}
        </if>
        <if test="insCode != null and insCode != '' ">
            and INS_CODE = #{insCode}
        </if>
        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        and REC_EXT_TYPE >= 50 and REC_EXT_TYPE != 110
        ,1,0))as map5001000,

        SUM(IF(
        IS_DEL='0' and PRE_TOL_MONEY  &gt; 1000
        <if test="preTimeBegin != null and preTimeBegin != '' ">
            and PRE_TIME &gt;=  #{preTimeBegin}
        </if>
        <if test="preTimeEnd != null and preTimeEnd != '' ">
            and PRE_TIME &lt;=  #{preTimeEnd}
        </if>
        <if test="insCode != null and insCode != '' ">
            and INS_CODE = #{insCode}
        </if>
        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        and REC_EXT_TYPE >= 50 and REC_EXT_TYPE != 110
        ,1,0))as map1000
        from t_prescription
        GROUP BY preType
    </select>

    <select id="getCountByMonthAndType" parameterType="java.util.HashMap"
            resultType="com.jiuzhekan.cbkj.beans.statistics.InstitutionStatsResponse">
        select MONTH(PRE_TIME) AS `time`,
        SUM(IF( p.PRE_TYPE = 1 AND p.IS_DEL = 0 AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110
        <if test="year != null and year != '' ">
            AND p.PRE_TIME BETWEEN #{startYear} AND #{endYear}
        </if>
        <if test="insCodeList != null and insCodeList.size() > 0">
            and INS_CODE in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ,1,0) ) AS `neifuList`,
        SUM(IF( p.PRE_TYPE = 2 AND p.IS_DEL = 0 AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110
        <if test="year != null and year != '' ">
            AND p.PRE_TIME BETWEEN #{startYear} AND #{endYear}
        </if>
        <if test="insCodeList != null and insCodeList.size() > 0">
            and INS_CODE in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ,1,0) ) AS `wyList`,
        SUM(IF( p.PRE_TYPE = 3 AND p.IS_DEL = 0 AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110
        <if test="year != null and year != '' ">
            AND p.PRE_TIME BETWEEN #{startYear} AND #{endYear}
        </if>
        <if test="insCodeList != null and insCodeList.size() > 0">
            and INS_CODE in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ,1,0) ) AS `zcList`,
        SUM(IF( p.PRE_TYPE = 4 AND p.IS_DEL = 0 AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110
        <if test="year != null and year != '' ">
            AND p.PRE_TIME BETWEEN #{startYear} AND #{endYear}
        </if>
        <if test="insCodeList != null and insCodeList.size() > 0">
            and INS_CODE in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ,1,0) ) AS `syList`
        FROM t_prescription p
        GROUP BY `time`
    </select>

    <select id="getCountByPreTime" parameterType="com.jiuzhekan.cbkj.beans.statistics.TPrescriptionVO" resultType="java.lang.Integer">
        SELECT
        COUNT(1) AS count
        from t_prescription  where IS_DEL='0' and REC_EXT_TYPE >= 50 and REC_EXT_TYPE != 110
        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode != '' ">
            and INS_CODE = #{insCode}
        </if>
        <if test="preTimeBegin != null and preTimeBegin != '' and preTimeEnd != null and preTimeEnd != ''">
            AND  PRE_TIME BETWEEN  #{preTimeBegin} AND #{preTimeEnd}
        </if>
    </select>

    <select id="getCountByPreTimeGroupMatName" parameterType="com.jiuzhekan.cbkj.beans.statistics.TPrescriptionVO" resultType="java.util.Map">
        SELECT
        i.MAT_NAME AS matName,
        COUNT(1) AS count
        FROM
        t_prescription t
        LEFT JOIN t_prescription_item i ON t.PRE_ID = i.PRE_ID
         where IS_DEL='0'
        <if test="preTimeBegin != null and preTimeBegin != '' and preTimeEnd != null and preTimeEnd != ''">
            AND  PRE_TIME BETWEEN  #{preTimeBegin} AND #{preTimeEnd}
        </if>
        and t.REC_EXT_TYPE >= 50 and t.REC_EXT_TYPE != 110
        and t.PRE_TYPE in ('1','2')
        <if test="appId != null and appId != '' ">
            and t.APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode != '' ">
            and t.INS_CODE = #{insCode}
        </if>
<!--        and i.MAT_NAME is not null-->
        GROUP BY
        i.MAT_NAME
        ORDER BY
        count DESC
        LIMIT 50
    </select>

    <select id="getCountByPreTimeGroupPreOrigin" parameterType="com.jiuzhekan.cbkj.beans.statistics.TPrescriptionVO" resultType="java.util.Map">
        SELECT
        COUNT(1) as count,PRE_ORIGIN as preOrigin
        FROM
        t_prescription t
        where IS_DEL='0' and t.REC_EXT_TYPE >= 50 and t.REC_EXT_TYPE != 110
        and t.PRE_TYPE in ('1','2')
        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode != '' ">
            and INS_CODE = #{insCode}
        </if>
        <if test="preTimeBegin != null and preTimeBegin != '' ">
            and DATE_FORMAT(PRE_TIME, '%Y-%m-%d') &gt;=  #{preTimeBegin}
        </if>
        <if test="preTimeEnd != null and preTimeEnd != '' ">
            and DATE_FORMAT(PRE_TIME, '%Y-%m-%d') &lt;= #{preTimeEnd}
        </if>
        GROUP BY PRE_ORIGIN ORDER BY count desc
    </select>



    <select id="getDocutorWorkload" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT a.id,a.`name`,IFNULL(b.regCount,0) as regCount,IFNULL(c.nfCount,0) as nfCount,IFNULL(d.wyCount,0) as wyCount,IFNULL(e.zcCount,0) as zcCount,IFNULL(f.syCount,0) as syCount,IFNULL(g.pCount,0) as pCount,
                                                IFNULL(qb.regCount,0) as qregCount,IFNULL(qc.nfCount,0) as qnfCount,IFNULL(qd.wyCount,0) as qwyCount,IFNULL(qe.zcCount,0) as qzcCount,IFNULL(qf.syCount,0) as qsyCount,IFNULL(qg.pCount,0) as qpCount
        from (
        SELECT s.id,s.name_zh as name from sys_admin_info s
            <where>
                <if test="insCodeList != null and insCodeList.size() > 0">
                    and s.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and s.APP_ID=#{appId}
                </if>
            </where>
        ) a LEFT JOIN (
        SELECT COUNT(1) as regCount,r.DOCTOR_ID from t_register r where DATE_FORMAT(r.REGISTER_TIME, '%Y-%m') =  #{month}
                <if test="insCodeList != null and insCodeList.size() > 0">
                    and r.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and r.APP_ID=#{appId}
                </if>
                GROUP BY r.DOCTOR_ID
        ) b ON a.id=b.DOCTOR_ID LEFT JOIN (
        SELECT COUNT(1) as nfCount,p.PRE_DOCTOR from t_prescription p
        where p.IS_DEL='0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110  and p.PRE_TYPE='1' and DATE_FORMAT(p.PRE_TIME, '%Y-%m') =  #{month}
                <if test="insCodeList != null and insCodeList.size() > 0">
                    and p.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and p.APP_ID=#{appId}
                </if>
                GROUP BY p.PRE_DOCTOR
        ) c ON a.id=c.PRE_DOCTOR LEFT JOIN (
        SELECT COUNT(1) as wyCount,p.PRE_DOCTOR from t_prescription p
        where p.IS_DEL='0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110 and p.PRE_TYPE='2' and DATE_FORMAT(p.PRE_TIME, '%Y-%m') =  #{month}
                <if test="insCodeList != null and insCodeList.size() > 0">
                    and p.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and p.APP_ID=#{appId}
                </if>
                GROUP BY p.PRE_DOCTOR
        ) d ON a.id=d.PRE_DOCTOR LEFT JOIN (
        SELECT COUNT(1) as zcCount,p.PRE_DOCTOR from t_prescription p
        where p.IS_DEL='0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110 and p.PRE_TYPE='3' and DATE_FORMAT(p.PRE_TIME, '%Y-%m') =  #{month}
                <if test="insCodeList != null and insCodeList.size() > 0">
                    and p.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and p.APP_ID=#{appId}
                </if>
                GROUP BY p.PRE_DOCTOR
        ) e ON a.id=e.PRE_DOCTOR LEFT JOIN (
        SELECT COUNT(1) as syCount,p.PRE_DOCTOR from t_prescription p
        where p.IS_DEL='0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110 and p.PRE_TYPE='4' and DATE_FORMAT(p.PRE_TIME, '%Y-%m') =  #{month}
                <if test="insCodeList != null and insCodeList.size() > 0">
                    and p.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and p.APP_ID=#{appId}
                </if>
                GROUP BY p.PRE_DOCTOR
        ) f ON a.id=f.PRE_DOCTOR LEFT JOIN (
        SELECT COUNT(1) as pCount,p.PRE_DOCTOR from t_prescription p
        where p.IS_DEL='0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110 and DATE_FORMAT(p.PRE_TIME, '%Y-%m') =  #{month}
                <if test="insCodeList != null and insCodeList.size() > 0">
                    and p.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and p.APP_ID=#{appId}
                </if>
                GROUP BY p.PRE_DOCTOR
        ) g ON a.id=g.PRE_DOCTOR

        LEFT JOIN (
        SELECT COUNT(1) as regCount,r.DOCTOR_ID from t_register r where DATE_FORMAT(r.REGISTER_TIME, '%Y') =  #{year}
                <if test="insCodeList != null and insCodeList.size() > 0">
                    and r.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and r.APP_ID=#{appId}
                </if>
                GROUP BY r.DOCTOR_ID
        ) qb ON a.id=qb.DOCTOR_ID LEFT JOIN (
        SELECT COUNT(1) as nfCount,p.PRE_DOCTOR from t_prescription p
        where p.IS_DEL='0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110 and p.PRE_TYPE='1' and DATE_FORMAT(p.PRE_TIME, '%Y') =  #{year}
                <if test="insCodeList != null  and insCodeList.size() > 0">
                    and p.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and p.APP_ID=#{appId}
                </if>
                GROUP BY p.PRE_DOCTOR
        ) qc ON a.id=qc.PRE_DOCTOR LEFT JOIN (
        SELECT COUNT(1) as wyCount,p.PRE_DOCTOR from t_prescription p
        where p.IS_DEL='0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110 and p.PRE_TYPE='2' and DATE_FORMAT(p.PRE_TIME, '%Y') =  #{year}
                <if test="insCodeList != null  and insCodeList.size() > 0">
                    and p.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and p.APP_ID=#{appId}
                </if>
                GROUP BY p.PRE_DOCTOR
        ) qd ON a.id=qd.PRE_DOCTOR LEFT JOIN (
        SELECT COUNT(1) as zcCount,p.PRE_DOCTOR from t_prescription p
        where p.IS_DEL='0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110 and p.PRE_TYPE='3' and DATE_FORMAT(p.PRE_TIME, '%Y') =  #{year}
                <if test="insCodeList != null  and insCodeList.size() > 0">
                    and p.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and p.APP_ID=#{appId}
                 </if>
                GROUP BY p.PRE_DOCTOR
        ) qe ON a.id=qe.PRE_DOCTOR LEFT JOIN (
        SELECT COUNT(1) as syCount,p.PRE_DOCTOR from t_prescription p
        where p.IS_DEL='0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110 and p.PRE_TYPE='4' and DATE_FORMAT(p.PRE_TIME, '%Y') =  #{year}
                <if test="insCodeList != null  and insCodeList.size() > 0">
                    and p.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and p.APP_ID=#{appId}
                </if>
                GROUP BY p.PRE_DOCTOR
        ) qf ON a.id=qf.PRE_DOCTOR LEFT JOIN (
        SELECT COUNT(1) as pCount,p.PRE_DOCTOR from t_prescription p
        where p.IS_DEL='0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110 and DATE_FORMAT(p.PRE_TIME, '%Y') =  #{year}
                <if test="insCodeList != null and insCodeList.size() > 0">
                    and p.INS_CODE in
                    <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="appId != null and appId != '' ">
                and p.APP_ID=#{appId}
                </if>
                GROUP BY p.PRE_DOCTOR
        ) qg ON a.id=qg.PRE_DOCTOR
    </select>


    <select id="savePreTimeGroupMatName" parameterType="com.jiuzhekan.cbkj.beans.statistics.TPrescriptionVO" >
        <!-- 删除临时表-->
        DROP TEMPORARY TABLE IF EXISTS temp_drug_report_statistics;

        <!-- 创建临时表-->
        CREATE TEMPORARY TABLE
        IF NOT EXISTS temp_drug_report_statistics (
        PRE_ID VARCHAR (32) NOT NULL,
        INSERT_TIME DATETIME NOT NULL,

        PRIMARY KEY (`PRE_ID`),
        KEY (`INSERT_TIME`)
        );


        <!--将结果插入临时表 -->
        INSERT INTO temp_drug_report_statistics (PRE_ID,INSERT_TIME)
        SELECT
        PRE_ID,PRE_TIME
        FROM
        t_prescription t
        WHERE
        IS_DEL = '0'
        <if test="appId != null and appId != '' ">
            and t.APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode != '' ">
            and t.INS_CODE = #{insCode}
        </if>
        <if test="preTimeBegin != null and preTimeBegin != '' and preTimeEnd != null and preTimeEnd != '' ">
            and t.PRE_TIME BETWEEN #{preTimeBegin} AND #{preTimeEnd}
        </if>
        AND t.REC_EXT_TYPE >= 50
        AND t.REC_EXT_TYPE != 110
        AND  t.PRE_TYPE IN ('1','2')


    </select>


    <select id="getInstitutionWorkloadTotal" parameterType="java.util.Map" resultType="java.util.Map">
                SELECT
            COUNT(1) AS totalCount,
            0 AS type
        FROM
            t_register r
        <where>
            <if test="month != null and month != '' ">
                r.REGISTER_TIME  BETWEEN #{startMonth} and  #{endMonth}
            </if>
            <if test="insCodeList != null and insCodeList.size() > 0">
                and INS_CODE in
                <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="appId != null and appId != '' ">
                and APP_ID=#{appId}
            </if>
        </where>
        UNION
            SELECT
                COUNT(1) AS totalCount,
                10 AS type
            FROM
                t_register r
        <where>
            <if test="year != null and year != '' ">
                and REGISTER_TIME  BETWEEN #{startYear} and  #{endYear}
            </if>
            <if test="insCodeList != null and insCodeList.size() > 0">
                and INS_CODE in
                <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="appId != null and appId != '' ">
                and APP_ID=#{appId}
            </if>
        </where>
            UNION
                SELECT
                    COUNT(1) AS totalCount,
                    p.PRE_TYPE + '10' AS type
                FROM
                    t_prescription p
                WHERE
                    p.IS_DEL = '0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110
                    <if test="year != null and year != '' ">
                        AND p.PRE_TIME BETWEEN  #{startYear}  AND  #{endYear}
                    </if>
                    <if test="insCodeList != null  and insCodeList.size() > 0">
                        and INS_CODE in
                        <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="appId != null and appId != '' ">
                        and APP_ID=#{appId}
                    </if>
                GROUP BY
                    type
                UNION
                    SELECT
                        COUNT(1) AS totalCount,
                        p.PRE_TYPE AS type
                    FROM
                        t_prescription p
                    WHERE
                        p.IS_DEL = '0' and p.REC_EXT_TYPE >= 50 and p.REC_EXT_TYPE != 110
                        <if test="month != null and month != '' ">
                            AND p.PRE_TIME BETWEEN  #{startMonth}  AND  #{endMonth}
                        </if>
                        <if test="insCodeList != null  and insCodeList.size() > 0">
                            and INS_CODE in
                            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="appId != null and appId != '' ">
                            and APP_ID=#{appId}
                        </if>
                    GROUP BY
                        p.PRE_TYPE
    </select>


    <select id="getInstitutionWorkloadList"  resultType="com.jiuzhekan.cbkj.beans.statistics.InstitutionStatsVO">

        <!--从临时表中查询数据-->
        SELECT
        INS_CODE as insCode,
        IFNULL(regCount,0) as regCount,
        IFNULL(nfCount,0) as nfCount,
        IFNULL(wyCount,0) as wyCount,
        IFNULL(zcCount,0) as zcCount,
        IFNULL(syCount,0) as syCount,
        IFNULL(pCount,0) as pCount,
        IFNULL(qregCount,0) as qregCount,
        IFNULL(qnfCount,0) as qnfCount,
        IFNULL(qwyCount,0) as qwyCount,
        IFNULL(qzcCount,0) as qzcCount,
        IFNULL(qsyCount,0) as qsyCount,
        IFNULL(qpCount,0) as qpCount
        FROM temp_institution_work

    </select>

    <select id="getInstitutionWorkload" parameterType="java.util.Map" resultType="com.jiuzhekan.cbkj.beans.statistics.InstitutionStatsVO">

        SELECT
        register.appId,
        register.insCode,
        register.regCount,
        prescription.nfCount,
        prescription.wyCount,
        prescription.zcCount,
        prescription.syCount,
        prescription.pCount,
        register.qregCount,
        prescription.qnfCount,
        prescription.qwyCount,
        prescription.qzcCount,
        prescription.qsyCount,
        prescription.qpCount

        FROM(
        SELECT
        app_id AS appId,
        ins_code as insCode,
        COUNT(1) AS qregCount,
        COUNT(IF( r.REGISTER_TIME BETWEEN  #{startMonth}  AND  #{endMonth}  ,TRUE,NULL)) AS regCount
        FROM t_register r WHERE

        r.REGISTER_TIME BETWEEN  #{startYear}  AND  #{endYear}

        GROUP BY app_id, ins_code
        )register , (

        SELECT
        app_id AS appId,
        ins_code as insCode,
        COUNT(IF( p.PRE_TYPE = '1' AND p.REC_EXT_TYPE >= 50
        AND p.REC_EXT_TYPE != 110 ,TRUE,NULL)) AS qnfCount,
        COUNT(IF( p.PRE_TYPE = '2'AND p.REC_EXT_TYPE >= 50
        AND p.REC_EXT_TYPE != 110 ,TRUE,NULL)) AS qwyCount,
        COUNT(IF( p.PRE_TYPE = '3'AND p.REC_EXT_TYPE >= 50
        AND p.REC_EXT_TYPE != 110 ,TRUE,NULL)) AS qzcCount,
        COUNT(IF( p.PRE_TYPE = '4' AND p.REC_EXT_TYPE >= 50
        AND p.REC_EXT_TYPE != 110 ,TRUE,NULL)) AS qsyCount,
        COUNT(IF( p.REC_EXT_TYPE >= 50
        AND p.REC_EXT_TYPE != 110 ,TRUE,NULL )) AS qpCount,

        COUNT(IF( p.PRE_TYPE = '1' AND p.REC_EXT_TYPE >= 50
        AND p.REC_EXT_TYPE != 110 AND DATE_FORMAT(p.PRE_TIME, '%Y-%m') = #{month},TRUE,NULL)) AS nfCount,
        COUNT(IF( p.PRE_TYPE = '2'AND p.REC_EXT_TYPE >= 50
        AND p.REC_EXT_TYPE != 110 AND DATE_FORMAT(p.PRE_TIME, '%Y-%m') = #{month},TRUE,NULL)) AS wyCount,
        COUNT(IF( p.PRE_TYPE = '3'AND p.REC_EXT_TYPE >= 50
        AND p.REC_EXT_TYPE != 110 AND DATE_FORMAT(p.PRE_TIME, '%Y-%m') = #{month} ,TRUE,NULL)) AS zcCount,
        COUNT(IF( p.PRE_TYPE = '4' AND p.REC_EXT_TYPE >= 50
        AND p.REC_EXT_TYPE != 110 AND DATE_FORMAT(p.PRE_TIME, '%Y-%m') = #{month},TRUE,NULL)) AS syCount,
        COUNT(IF( p.REC_EXT_TYPE >= 50
        AND p.REC_EXT_TYPE != 110 AND DATE_FORMAT(p.PRE_TIME, '%Y-%m') = #{month},TRUE,NULL )) AS pCount

        FROM t_prescription p WHERE
        p.IS_DEL = '0'
        AND DATE_FORMAT(p.PRE_TIME, '%Y')= #{year}
        GROUP BY app_id, ins_code

        )prescription


        WHERE prescription.appId=register.appId AND prescription.insCode=register.insCode

        <if test="insCodeList != null  and insCodeList.size() > 0">
            and register.insCode in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="getCountByPreTimeGroupMatNameList" resultType="com.jiuzhekan.cbkj.beans.statistics.TStatisticsPrescriptionItem" parameterType="string">

        SELECT
        t.PRE_TIME AS createDate,
        t.APP_ID AS appId,
        t.INS_CODE AS insCode,
        i.MAT_ID AS matId,
        i.MAT_NAME AS matName,
        i.YPDM_HIS AS hisMatId,
        i.YPMC_HIS AS hisMatName,
        COUNT(1) AS matNumber
        FROM
        t_prescription t
        LEFT JOIN t_prescription_item i ON t.PRE_ID = i.PRE_ID
        where IS_DEL='0'
        and t.REC_EXT_TYPE >= 50 and t.REC_EXT_TYPE != 110
        and t.PRE_TYPE in ('1','2')
        AND  t.PRE_TIME BETWEEN  #{startTime} AND #{endTime}
        and i.PRE_ID  is not null
        GROUP BY t.INS_CODE,i.YPMC_HIS
    </select>

    <delete id="removeTempDrugReportStatistics">
        <!--删除临时表-->
        DROP TEMPORARY TABLE IF EXISTS temp_drug_report_statistics
    </delete>
</mapper>