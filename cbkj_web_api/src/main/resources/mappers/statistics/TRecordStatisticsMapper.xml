<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.statistics.TRecordStatisticsMapper">

    <select id="getDisNameGroupDisNameAge" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT COUNT(DIS_NAME) count, DIS_NAME disName
        FROM t_record where IS_DEL='0'
        and DIS_NAME is not null
        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode != '' ">
            and INS_CODE = #{insCode}
        </if>
        <if test="recTreTimeBegin != null and recTreTimeBegin != '' ">
            and DATE_FORMAT(REC_TRE_TIME, '%Y-%m-%d') &gt;= #{recTreTimeBegin}
        </if>
        <if test="recTreTimeEnd != null and recTreTimeEnd != '' ">
            and DATE_FORMAT(REC_TRE_TIME, '%Y-%m-%d') &lt;= #{recTreTimeEnd}
        </if>
        <if test="recAgeBegin != null and recAgeBegin != '' ">
            and if(REC_AGEUNIT1 = '岁',REC_AGE1 >= #{recAgeBegin},1=0)
        </if>
        <if test="recAgeEnd != null and recAgeEnd != '' ">
            and IF(REC_AGEUNIT1 &lt;&gt; '岁',REC_AGE1 &lt; 32,REC_AGE1 &lt; #{recAgeEnd})
        </if>
        GROUP BY
        disName
        ORDER BY
        count DESC
        <if test="num != null and num != '' ">
            LIMIT #{num}
        </if>
    </select>


    <select id="getCountByGenderAndAge" parameterType="java.util.HashMap" resultType="java.lang.Integer">
        SELECT sum(IF(
        IS_DEL='0'
        <if test="appId != null and appId != '' ">
            and APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode != '' ">
            and INS_CODE = #{insCode}
        </if>
        <if test="recGender != null and recGender != '' ">
            and REC_GENDER = #{recGender}
        </if>
        <if test="recTreTimeBegin != null and recTreTimeBegin != '' ">
            and DATE_FORMAT(REC_TRE_TIME, '%Y-%m-%d') &gt;= #{recTreTimeBegin}
        </if>
        <if test="recTreTimeEnd != null and recTreTimeEnd != '' ">
            and DATE_FORMAT(REC_TRE_TIME, '%Y-%m-%d') &lt;= #{recTreTimeEnd}
        </if>
        <if test="recAgeBegin != null and recAgeBegin != '' ">
            and if(REC_AGEUNIT1 = '岁',REC_AGE1 >= #{recAgeBegin},1=0)
        </if>
        <if test="recAgeEnd != null and recAgeEnd != '' ">
            and IF(REC_AGEUNIT1 &lt;&gt; '岁',REC_AGE1 &lt; 32,REC_AGE1 &lt; #{recAgeEnd})
        </if>
        ,1,0)) count FROM t_record
        <!-- SELECT  COUNT(REC_GENDER) count
         FROM  t_record where IS_DEL='0'
             <if test="appId != null and appId != '' ">
                 and APP_ID = #{appId}
             </if>
             <if test="insCode != null and insCode != '' ">
                 and INS_CODE = #{insCode}
             </if>
             <if test="recGender != null and recGender != '' ">
                 and REC_GENDER = #{recGender}
             </if>
             <if test="recTreTimeBegin != null and recTreTimeBegin != '' ">
                 and DATE_FORMAT(REC_TRE_TIME, '%Y-%m-%d') &gt;= #{recTreTimeBegin}
             </if>
             <if test="recTreTimeEnd != null and recTreTimeEnd != '' ">
                 and DATE_FORMAT(REC_TRE_TIME, '%Y-%m-%d') &lt;= #{recTreTimeEnd}
             </if>
             <if test="recAgeBegin != null and recAgeBegin != '' ">
                 and if(REC_AGEUNIT1 = '岁',REC_AGE1 >= #{recAgeBegin},1=0)
             </if>
             <if test="recAgeEnd != null and recAgeEnd != '' ">
                 and IF(REC_AGEUNIT1 &lt;&gt; '岁',REC_AGE1 &lt; 32,REC_AGE1 &lt; #{recAgeEnd})
             </if>-->
    </select>

    <select id="getDisTop5ByRecTreTime" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        COUNT(*) as num,
        r.DIS_NAME
        FROM
        t_record r
        WHERE
        IS_DEL='0'
        and DIS_NAME is not null
        <if test="recTreTimeBegin != null and recTreTimeBegin != '' ">
            AND r.REC_TRE_TIME &gt;= #{recTreTimeBegin}
        </if>
        <if test="recTreTimeEnd != null and recTreTimeEnd != '' ">
            AND r.REC_TRE_TIME &lt;= #{recTreTimeEnd}
        </if>
        GROUP BY
        r.DIS_NAME
        ORDER BY num desc LIMIT 5
    </select>

    <select id="getSynTop5ByRecTreTime" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        COUNT(*) as num,
        r.SYM_NAME
        FROM
        t_record r
        WHERE
        IS_DEL='0'
        and r.SYM_NAME is not null
        <if test="recTreTimeBegin != null and recTreTimeBegin != '' ">
            AND r.REC_TRE_TIME &gt;= #{recTreTimeBegin}
        </if>
        <if test="recTreTimeEnd != null and recTreTimeEnd != '' ">
            AND r.REC_TRE_TIME &lt;= #{recTreTimeEnd}
        </if>
        GROUP BY
        r.SYM_NAME
        ORDER BY num desc LIMIT 10
    </select>

    <select id="getDisNameTop5ByGender" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        COUNT(*) as num,
        r.DIS_NAME,
        r.REC_GENDER
        FROM
        t_record r
        WHERE
        IS_DEL='0'
        and r.DIS_NAME is not null
        and r.REC_GENDER is not null
        <if test="recTreTimeBegin != null and recTreTimeBegin != '' ">
            AND r.REC_TRE_TIME &gt;= #{recTreTimeBegin}
        </if>
        <if test="recTreTimeEnd != null and recTreTimeEnd != '' ">
            AND r.REC_TRE_TIME &lt;= #{recTreTimeEnd}
        </if>
        GROUP BY
        r.REC_GENDER,
        r.DIS_NAME
        ORDER BY num desc LIMIT 5
    </select>

    <select id="getDisNameTop5ByAge" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        COUNT(*) as num,
        r.DIS_NAME,
        r.REC_GENDER
        FROM
        t_record r
        WHERE
        IS_DEL='0'
        and r.DIS_NAME is not null
        <if test="recAgeunit1 != null and recAgeunit1 != '' ">
            <if test="recAgeunit1 = '岁' ">
                AND r.REC_AGEUNIT1 = #{recAgeunit1}
            </if>
            <if test="recAgeunit1 != '岁' ">
                AND r.REC_AGEUNIT1 != #{recAgeunit1}
            </if>
        </if>
        <if test="recAge1Begin != null and recAge1Begin != '' ">
            <if test="recAgeunit1 = '岁' ">
                AND r.REC_AGE1 >= #{recAge1Begin}
            </if>
        </if>
        <if test="recAge1End != null and recAge1End != '' ">
            <if test="recAgeunit1 = '岁' ">
                AND r.REC_AGE1 &lt; #{recAge1End}
            </if>
            <if test="recAgeunit1 != '岁' ">
                AND r.REC_AGE1 &lt; 32
            </if>
        </if>

        <if test="recTreTimeBegin != null and recTreTimeBegin != '' ">
            AND r.REC_TRE_TIME &gt;= #{recTreTimeBegin}
        </if>
        <if test="recTreTimeEnd != null and recTreTimeEnd != '' ">
            AND r.REC_TRE_TIME &lt;= #{recTreTimeEnd}
        </if>
        GROUP BY
        r.DIS_NAME
        ORDER BY num desc LIMIT 5
    </select>

    <select id="getDisCountGrupByDisName" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        COUNT(1) AS 'COUNT',
        CHINESE_DISEASE
        FROM
        t_record r
        WHERE
        r.IS_DEL = '0'
        AND r.CHINESE_DISEASE IS NOT NULL
        <if test="recTreTimeBegin != null and recTreTimeBegin != '' ">
            AND r.REC_TRE_TIME &gt;= #{recTreTimeBegin}
        </if>
        <if test="recTreTimeEnd != null and recTreTimeEnd != '' ">
            AND r.REC_TRE_TIME &lt;= #{recTreTimeEnd}
        </if>
        GROUP BY
        r.CHINESE_DISEASE
    </select>
</mapper>