<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.statistics.TRegisterStatisticsMapper">

    <select id="getRegisterCountByMonth" parameterType="java.util.HashMap" resultType="com.jiuzhekan.cbkj.beans.statistics.InstitutionStatsResponse">
        select MONTH(REGISTER_TIME) AS `time`,
        SUM(IF(
        <if test="insCodeList != null and insCodeList.size() > 0">
             INS_CODE in
            <foreach collection="insCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ,1,0) ) AS `renciList`
        FROM
        t_register
        <where>
            <if test="year != null and year != '' ">
                AND REGISTER_TIME BETWEEN #{startYear} AND #{endYear}
            </if>
            <if test="appId != null and appId != '' ">
                and APP_ID = #{appId}
            </if>
        </where>
        GROUP BY `time`
    </select>
</mapper>