<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.statistics.TStatisticsFunctionMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.statistics.TStatisticsFunction">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="function_name" jdbcType="VARCHAR" property="functionName"/>
        <result column="function_source" jdbcType="VARCHAR" property="functionSource"/>
        <result column="usage_times" jdbcType="INTEGER" property="usageTimes"/>
        <result column="create_date" jdbcType="VARCHAR" property="createDate"/>
    </resultMap>


    <sql id="Base_Column_List">
    id,function_name,function_source,usage_times,create_date
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TStatisticsFunction">
        delete from t_statistics_function where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_statistics_function where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="TStatisticsFunction">
        insert into t_statistics_function (function_name,function_source,usage_times,create_date) values
        (#{functionName},#{functionSource},#{usageTimes},#{createDate})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_statistics_function (function_name,function_source,usage_times,create_date) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.functionName},#{item.functionSource},#{item.usageTimes},#{item.createDate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TStatisticsFunction">
        update t_statistics_function
        <set>
            <if test="functionName != null">
                function_name = #{ functionName },
            </if>
            <if test="functionSource != null">
                function_source = #{ functionSource },
            </if>
            <if test="usageTimes != null">
                usage_times = #{ usageTimes },
            </if>
            <if test="createDate != null">
                create_date = #{ createDate },
            </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_statistics_function where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TStatisticsFunction" resultMap="BaseResultMap">
        SELECT id,function_name,function_source,usage_times,create_date
        from t_statistics_function
        <where>
            <if test=" functionName != null and functionName!='' ">
                and function_name like CONCAT('%',trim(#{functionName}),'%')
            </if>
        </where>
    </select>

    <select id="sumUsageTimes" parameterType="TStatisticsFunction" resultType="int">
        select sum(usage_times)
        from t_statistics_function
        <where>
            <if test=" functionName != null and functionName!='' ">
                and function_name = #{functionName}
            </if>
            <if test=" functionSource != null and functionSource!='' ">
                and function_source = #{functionSource}
            </if>
            <if test=" beginDate != null and beginDate!='' ">
                and create_date &gt;= #{startTime}
            </if>
            <if test=" endDate != null and endDate!='' ">
                and create_date &lt;= #{endTime}
            </if>
        </where>
    </select>

    <select id="lastUsageTimes" parameterType="TStatisticsFunction" resultType="int">
        select usage_times
        from t_statistics_function
        <where>
            <if test=" functionName != null and functionName!='' ">
                and function_name = #{functionName}
            </if>
            <if test=" functionSource != null and functionSource!='' ">
                and function_source = #{functionSource}
            </if>
        </where>
        order by create_date desc limit 1
    </select>


<!--    <insert id="statisticWb">-->
<!--        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date) -->
<!--        SELECT '五笔使用人数','设置',COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM sys_admin_infoex-->
<!--        WHERE USER_EX_TYPE = '1' AND USER_EX_CONTENT = '2'-->
<!--    </insert>-->
<!--    <insert id="statisticPy">-->
<!--        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)-->
<!--        SELECT '拼音使用人数','设置',COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM sys_admin_infoex-->
<!--        WHERE USER_EX_TYPE = '1' AND USER_EX_CONTENT = '1'-->
<!--    </insert>-->
<!--    <insert id="statisticDept">-->
<!--        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)-->
<!--        SELECT '科室数', '', COUNT(*) - 9, DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM sys_department WHERE is_del = '0'-->
<!--    </insert>-->
    <insert id="statisticAnalysis">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '体质辨识数量','中医体质辨识',COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_user_analysis_result
        WHERE is_del = '0' AND analy_time >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticPreNum">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '总处方数','', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_prescription
        WHERE is_del = '0' and PRE_TIME >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticCheckPreNum">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '人工审核的处方数','处方审核', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_prescription
        WHERE is_del = '0' and CHECK_TYPE = '1' and PRE_TIME >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticInPreNum">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '内服处方数量','智能开方', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_prescription
        WHERE is_del = '0' and PRE_TYPE = '1' and PRE_TIME >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticInPreNumProduction">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '内服膏方数量','智能开方', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_prescription
        WHERE is_del = '0' and PRE_TYPE = '1' AND IS_PRODUCTION = '1' and PRE_TIME >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticInPreNumDecoction">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '内服代煎数量','智能开方', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_prescription
        WHERE is_del = '0' and PRE_TYPE = '1' AND DECOCT_TYPE = '1' and PRE_TIME >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticInPreNumExpress">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '内服配送数量','智能开方', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_prescription
        WHERE is_del = '0' and PRE_TYPE = '1' AND DC_TYPE = '1' and PRE_TIME >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticPersonalNum">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '协定方数量','协定方', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_personal_prescription
        WHERE is_del = '0' and INSERT_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticPersonalSelfNum">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '个人协定方数量','协定方', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_personal_prescription
        WHERE is_del = '0' AND IS_SHARE = '0' and INSERT_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticPersonalDeptNum">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '科室协定方数量','协定方', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_personal_prescription
        WHERE is_del = '0' AND IS_SHARE = '1' and INSERT_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticPersonalInsNum">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '全院协定方数量','协定方', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_personal_prescription
        WHERE is_del = '0' AND IS_SHARE = '2' and INSERT_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticRecordTemplate">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '电子病历模板数','电子病历模板配置', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_record_template
        WHERE is_del = '0' AND CREATE_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticAppExpertPreNum">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '专家经验共享条数','专家经验共享', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_personal_prescription
        WHERE is_del = '0' AND IS_SHARE = '3' and INSERT_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticAppExpertInPreNum">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '专家经验共享内服处方数','专家经验共享', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_personal_prescription
        WHERE is_del = '0' AND IS_SHARE = '3' and PRE_TYPE = '1' and INSERT_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticAppExpertExtPreNum">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '专家经验共享外用处方数','专家经验共享', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_personal_prescription
        WHERE is_del = '0' AND IS_SHARE = '3' and PRE_TYPE = '2' and INSERT_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticAppExpertNum">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '专家经验共享专家数','专家经验共享', COUNT(DISTINCT(PRE_EXPERT)), DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        FROM t_personal_prescription WHERE is_del = '0' AND IS_SHARE = '3'
    </insert>

    <insert id="statisticVerifyCollection">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '收藏次数','名医验案', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_doctor_collect
        WHERE COLLECT_MODULE = '1' AND CREATE_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticMatCollection">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '收藏次数','中药查询', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_doctor_collect
        WHERE COLLECT_MODULE = '2' AND CREATE_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticPreCollection">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '收藏次数','方剂查询', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_doctor_collect
        WHERE COLLECT_MODULE = '3' AND CREATE_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticAcuCollection">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '收藏次数','经络穴位查询', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_doctor_collect
        WHERE COLLECT_MODULE = '4' AND CREATE_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
    <insert id="statisticDisCollection">
        INSERT INTO t_statistics_function ( function_name,  function_source,  usage_times,  create_date)
        SELECT '收藏次数','疾病查询', COUNT(*), DATE_SUB(CURDATE(), INTERVAL 1 DAY) FROM t_doctor_collect
        WHERE COLLECT_MODULE = '5' AND CREATE_DATE >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </insert>
</mapper>