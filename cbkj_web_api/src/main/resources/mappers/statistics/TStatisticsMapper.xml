<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.statistics.TStatisticsMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.statistics.TStatistics">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="time_type" jdbcType="INTEGER" property="timeType"/>
        <result column="statis_type" jdbcType="INTEGER" property="statisType"/>
        <result column="dis_name" jdbcType="VARCHAR" property="disName"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime"/>
        <result column="delete_user_id" jdbcType="VARCHAR" property="deleteUserId"/>
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.statistics.TStatistics">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="time_type" jdbcType="INTEGER" property="timeType"/>
        <result column="statis_type" jdbcType="INTEGER" property="statisType"/>
        <result column="dis_name" jdbcType="VARCHAR" property="disName"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime"/>
        <result column="delete_user_id" jdbcType="VARCHAR" property="deleteUserId"/>
        <collection property="statisList" ofType="com.jiuzhekan.cbkj.beans.statistics.TStatistics">
            <id column="id2" jdbcType="VARCHAR" property="id"/>
            <result column="time_type2" jdbcType="INTEGER" property="timeType"/>
            <result column="statis_type2" jdbcType="INTEGER" property="statisType"/>
            <result column="dis_name2" jdbcType="VARCHAR" property="disName"/>
            <result column="number2" jdbcType="INTEGER" property="number"/>
            <result column="group_name2" jdbcType="VARCHAR" property="groupName"/>
            <result column="begin_time2" jdbcType="TIMESTAMP" property="beginTime"/>
            <result column="end_time2" jdbcType="TIMESTAMP" property="endTime"/>
            <result column="create_time2" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="create_user_id2" jdbcType="VARCHAR" property="createUserId"/>
            <result column="create_user_name2" jdbcType="VARCHAR" property="createUserName"/>
            <result column="update_time2" jdbcType="TIMESTAMP" property="updateTime"/>
            <result column="update_user_id2" jdbcType="VARCHAR" property="updateUserId"/>
            <result column="delete_time2" jdbcType="TIMESTAMP" property="deleteTime"/>
            <result column="delete_user_id2" jdbcType="VARCHAR" property="deleteUserId"/>
        </collection>
    </resultMap>

    <select id="getTop5DisName" parameterType="TStatistics" resultType="TStatistics">
        SELECT app_id as appId,ins_code as insCode,dis_name as disName
        <if test=" appId != null and insCode == null ">
            ,SUM(num) as number
        </if>
        <if test=" appId == null and insCode == null">
            ,SUM(num) as number
        </if>
        <if test=" appId != null and insCode != null">
            ,num as number
        </if>
        from t_statistics_dis_num
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id = #{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code = #{insCode}
            </if>
            <if test=" timeType != null and timeType!='' ">
                and time_type = #{timeType}
            </if>
            <if test=" nowDate != null and nowDate!='' ">
                and create_time = #{nowDate}
            </if>
            <if test=" recTreType != null and recTreType!='' ">
                and REC_TRE_TYPE = #{recTreType}
            </if>
        </where>
        <if test=" appId != null  and insCode == null">
            GROUP BY app_id,dis_name
        </if>
        <if test=" appId == null and insCode == null ">
            GROUP BY dis_name
        </if>
        order by number desc LIMIT 5
    </select>

    <select id="getTop5SymName" parameterType="TStatistics" resultType="TStatistics">
        SELECT sym_name as disName,num as number
        from t_statistics_sym_num
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id = #{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code = #{insCode}
            </if>
            <if test=" timeType != null and timeType!='' ">
                and time_type = #{timeType}
            </if>
            <if test=" nowDate != null and nowDate!='' ">
                and create_time = #{nowDate}
            </if>
        </where>
        order by num desc LIMIT 5
    </select>


    <!--    <select id="getTop5DisNameByAge" parameterType="TStatistics" resultType="TStatistics">
            SELECT age_range as groupName,dis_name as disName,num as number
            from t_statistics_dis_age t
            <where>
                <if test=" appId != null and appId!='' ">
                    and app_id = #{appId}
                </if>
                <if test=" insCode != null and insCode!='' ">
                    and ins_code = #{insCode}
                </if>
                <if test=" timeType != null and timeType!='' ">
                    and time_type = #{timeType}
                </if>
                <if test=" nowDate != null and nowDate!='' ">
                    and create_time = #{nowDate}
                </if>
            </where>
            order by age_range, num desc
        </select>-->

    <select id="getTop5DisNameByAge" parameterType="TStatistics" resultType="TStatistics">
        SELECT
        t1.age_range as groupName,t1.dis_name as disName,t1.num as number
        FROM
        t_statistics_dis_age t1
        where (SELECT count(*) + 1 FROM t_statistics_dis_age t2
        WHERE t2.age_range = t1.age_range AND t2.num > t1.num
        <if test=" appId != null and appId!='' ">
            and t2.app_id = #{appId}
        </if>
        <if test=" insCode != null and insCode!='' ">
            and t2.ins_code = #{insCode}
        </if>
        <if test=" timeType != null and timeType!='' ">
            and t2.time_type= #{timeType}
        </if>
        <if test=" nowDate != null and nowDate!='' ">
            and t2.create_time= #{nowDate}
        </if>
        ) &lt;= 5
        <if test=" appId != null and appId!='' ">
            and t1.app_id = #{appId}
        </if>
        <if test=" insCode != null and insCode!='' ">
            and t1.ins_code = #{insCode}
        </if>
        <if test=" timeType != null and timeType!='' ">
            and t1.time_type= #{timeType}
        </if>
        <if test=" nowDate != null and nowDate!='' ">
            and t1.create_time= #{nowDate}
        </if>
        ORDER BY t1.age_range,t1.num
    </select>

    <select id="getTop5DisNameByGoUp" parameterType="TStatistics" resultType="TStatistics">
        SELECT date_format(group_name, '%c.%e') as groupName, dis_name as disName, rate as number
        from t_statistics_dis_rate
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id = #{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code = #{insCode}
            </if>
            <if test=" timeType != null and timeType!='' ">
                and time_type = #{timeType}
            </if>
            <if test=" nowDate != null and nowDate!='' ">
                and create_time = #{nowDate}
            </if>
        </where>
        ORDER BY create_time, group_name, rate desc
    </select>

    <sql id="getTop5DisNameByGenderWhere">
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id = #{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code = #{insCode}
            </if>
            <if test=" timeType != null and timeType!='' ">
                and time_type = #{timeType}
            </if>
            <if test=" nowDate != null and nowDate!='' ">
                and create_time = #{nowDate}
            </if>
        </where>
    </sql>

    <select id="getTop5DisNameByGender" parameterType="TStatistics" resultType="TStatistics">
        SELECT a.dis_name disName,IFNULL(b.num,0) groupName,IFNULL(c.num,0) statisType from (
        SELECT * from (
        SELECT t.dis_name from t_statistics_dis_gender t
        <include refid="getTop5DisNameByGenderWhere"/>
        and t.gender='F' ORDER BY t.num desc LIMIT 5
        ) f
        union
        SELECT * from (
        SELECT t.dis_name from t_statistics_dis_gender t
        <include refid="getTop5DisNameByGenderWhere"/>
        and t.gender='M' ORDER BY t.num desc LIMIT 5
        ) m
        ) a
        LEFT JOIN (
        SELECT t.dis_name,t.num from t_statistics_dis_gender t
        <include refid="getTop5DisNameByGenderWhere"/>
        and t.gender='F' ORDER BY t.num desc LIMIT 5
        ) b on a.dis_name=b.dis_name
        LEFT JOIN (
        SELECT t.dis_name,t.num from t_statistics_dis_gender t
        <include refid="getTop5DisNameByGenderWhere"/>
        and t.gender='M' ORDER BY t.num desc LIMIT 5
        ) c ON a.dis_name=c.dis_name
    </select>

    <select id="getTermsName" resultType="TStatistics">
        SELECT * from (
        SELECT DISTINCT terms groupName, t.begin_time beginTime,t.end_time endTime
        FROM t_statistics_dis_terms t
        WHERE t.begin_time >='2020-01-01 00:00:00'
        AND t.end_time &lt; NOW() ORDER BY t.end_time desc LIMIT 24
        ) a ORDER BY a.endTime asc
    </select>

    <select id="getTop5DisNameByTermName" parameterType="TStatistics" resultType="TStatistics">
        SELECT dis_name disName,num number,begin_time beginTime,end_time endTime
        from t_statistics_dis_terms t
        where t.begin_time >='2020-01-01 00:00:00'
        and t.terms = #{groupName}
        and t.end_time &lt; NOW()
        ORDER BY t.num desc LIMIT 5
    </select>

    <select id="getTop5DisNameBy24" parameterType="TStatistics" resultMap="BaseResultMap2">
        SELECT a.group_name,b.group_name AS group_name2,b.dis_name AS dis_name2,b.number AS number2,
        b.begin_time AS begin_time2,b.end_time AS end_time2
        FROM ( SELECT DISTINCT t.terms group_name
        FROM t_statistics_dis_terms t
        WHERE t.begin_time >='2020-01-01 00:00:00' AND t.end_time &lt; NOW()
        ORDER BY t.end_time DESC
        ) a LEFT JOIN
        ( SELECT t.terms group_name,t.dis_name,t.num number,t.begin_time,t.end_time
        FROM t_statistics_dis_terms t
        WHERE t.begin_time >='2020-01-01 00:00:00' AND t.end_time &lt; NOW()
        ) b ON a.group_name=b.group_name
    </select>


    <sql id="statisticsTop5DisNameSql">
        (SELECT app_id,ins_code,dis_name, COUNT(*) num,REC_TRE_TYPE FROM t_record r
        WHERE EXISTS(SELECT 1 FROM t_prescription p WHERE p.REC_ID = r.REC_ID
        AND p.REC_EXT_TYPE >=50 AND p.REC_EXT_TYPE != 110 and r.dis_name IS NOT NULL
        AND TIMESTAMPDIFF(DAY, p.PRE_TIME, DATE_ADD(CURDATE(),INTERVAL 1 DAY)) &lt;= #{timeDiff} )
        GROUP BY app_id,ins_code,dis_name,REC_TRE_TYPE)
    </sql>

    <!--    <insert id="statisticsTop5DisName" parameterType="Map">
            INSERT INTO t_statistics_dis_num ( app_id, ins_code, time_type, dis_name, num, create_time)
            SELECT app_id, ins_code, #{timeType}, dis_name, num, NOW()
            FROM <include refid="statisticsTop5DisNameSql"/> a
            WHERE 5 > (SELECT COUNT(*) FROM <include refid="statisticsTop5DisNameSql"/>
                b WHERE a.app_id = b.app_id AND a.ins_code = b.ins_code AND b.num > a.num)
        </insert>-->

    <insert id="statisticsTop5DisName" parameterType="Map">
        INSERT INTO t_statistics_dis_num ( app_id, ins_code, time_type, dis_name, num, create_time,REC_TRE_TYPE)
        SELECT app_id, ins_code,#{timeType}, dis_name, num, NOW(),REC_TRE_TYPE
        FROM
        <include refid="statisticsTop5DisNameSql"/>
        a
        WHERE 5 > (SELECT COUNT(*) FROM
        <include refid="statisticsTop5DisNameSql"/>
        b WHERE a.app_id = b.app_id and a.ins_code = b.ins_code and a.REC_TRE_TYPE=b.REC_TRE_TYPE AND b.num > a.num)
    </insert>

    <sql id="statisticsTop5SymNameSql">
        (SELECT app_id,sym_name, COUNT(*) num FROM t_record r
        WHERE EXISTS(SELECT 1 FROM t_prescription p WHERE p.REC_ID = r.rec_id
        AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110 and r.sym_name IS NOT NULL
        AND TIMESTAMPDIFF(DAY, p.PRE_TIME, DATE_ADD(CURDATE(),INTERVAL 1 DAY)) &lt;= #{timeDiff} )
        GROUP BY app_id,sym_name)
    </sql>
    <insert id="statisticsTop5SymName" parameterType="Map">
        INSERT INTO t_statistics_sym_num ( app_id, time_type, sym_name, num, create_time)
        SELECT app_id, #{timeType}, sym_name, num, NOW() FROM
        <include refid="statisticsTop5SymNameSql"/>
        a
        WHERE 5 > (SELECT COUNT(*) FROM
        <include refid="statisticsTop5SymNameSql"/>
        b WHERE a.app_id = b.app_id AND b.num > a.num)
    </insert>

    <sql id="statisticsTop5DisNameByAgeSql">
        (SELECT app_id, '0-10岁' age_range, dis_name, COUNT(*) num
        FROM t_record r
        WHERE EXISTS(SELECT 1 FROM t_prescription p WHERE p.REC_ID = r.rec_id and r.dis_name IS NOT NULL
        AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110 AND TIMESTAMPDIFF(DAY, p.PRE_TIME, CURDATE()) &lt;=
        #{timeDiff} )
        AND r.REC_AGE1 >= 0 AND r.REC_AGE1 &lt; 10 and r.dis_name IS NOT NULL
        GROUP BY app_id, dis_name
        UNION
        SELECT app_id, '10-20岁' age_range, dis_name, COUNT(*) num
        FROM t_record r
        WHERE EXISTS(SELECT 1 FROM t_prescription p WHERE p.REC_ID = r.rec_id and r.dis_name IS NOT NULL
        AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110 AND TIMESTAMPDIFF(DAY, p.PRE_TIME, CURDATE()) &lt;=
        #{timeDiff} )
        AND r.REC_AGE1 >= 10 AND r.REC_AGE1 &lt; 20
        GROUP BY app_id, dis_name
        UNION
        SELECT app_id, '20-35岁' age_range, dis_name, COUNT(*) num
        FROM t_record r
        WHERE EXISTS(SELECT 1 FROM t_prescription p WHERE p.REC_ID = r.rec_id and r.dis_name IS NOT NULL
        AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110 AND TIMESTAMPDIFF(DAY, p.PRE_TIME, CURDATE()) &lt;=
        #{timeDiff} )
        AND r.REC_AGE1 >= 20 AND r.REC_AGE1 &lt; 35
        GROUP BY app_id, dis_name
        UNION
        SELECT app_id, '35-50岁' age_range, dis_name, COUNT(*) num
        FROM t_record r
        WHERE EXISTS(SELECT 1 FROM t_prescription p WHERE p.REC_ID = r.rec_id and r.dis_name IS NOT NULL
        AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110 AND TIMESTAMPDIFF(DAY, p.PRE_TIME, CURDATE()) &lt;=
        #{timeDiff} )
        AND r.REC_AGE1 >= 35 AND r.REC_AGE1 &lt; 50
        GROUP BY app_id, dis_name
        UNION
        SELECT app_id, '50岁以上' age_range, dis_name, COUNT(*) num
        FROM t_record r
        WHERE EXISTS(SELECT 1 FROM t_prescription p WHERE p.REC_ID = r.rec_id and r.dis_name IS NOT NULL
        AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110 AND TIMESTAMPDIFF(DAY, p.PRE_TIME, CURDATE()) &lt;=
        #{timeDiff} )
        AND r.REC_AGE1 >= 50
        GROUP BY app_id, dis_name)
    </sql>
    <insert id="statisticsTop5DisNameByAge" parameterType="Map">
        INSERT INTO t_statistics_dis_age ( app_id, time_type, age_range, dis_name, num, create_time)
        SELECT app_id, #{timeType}, age_range, dis_name, num, NOW()
        FROM
        <include refid="statisticsTop5DisNameByAgeSql"/>
        a
        WHERE 5 > (SELECT COUNT(*) FROM
        <include refid="statisticsTop5DisNameByAgeSql"/>
        b WHERE a.app_id = b.app_id AND a.age_range = b.age_range AND b.num > a.num)
    </insert>


    <select id="statisticsTop5DisNameByGoUp" parameterType="Map" resultType="Map">
        <foreach collection="timeSplit" item="index" separator=" UNION ">
            (SELECT #{appId} app_id, '' ins_code, #{timeType} time_type, a.dis_name,
            DATE_SUB(CURDATE(), INTERVAL #{timeDiff} * (#{index} - 1) DAY) group_name,
            (IFNULL(b.num,0) - a.num) / a.num * 100 AS rate

            FROM (
            SELECT app_id, ins_code, dis_name, COUNT(*) num FROM t_record r
            JOIN
            ( SELECT REC_ID FROM t_prescription p
            WHERE
            p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110
            AND p.PRE_TIME >= DATE_SUB(CURDATE(), INTERVAL #{timeDiff} * (#{index} + 1) DAY)
            AND p.PRE_TIME &lt; DATE_SUB(CURDATE(), INTERVAL #{timeDiff} * #{index} DAY)
            GROUP BY REC_ID
            )p
            ON p.REC_ID = r.rec_id
            WHERE app_id = #{appId}
            GROUP BY dis_name) a

            LEFT JOIN (
            SELECT rd.dis_name ,COUNT(*) num
            FROM t_record rd
            JOIN
            ( SELECT REC_ID
            FROM t_prescription p
            WHERE  p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110
            AND p.PRE_TIME >= DATE_SUB(CURDATE(), INTERVAL #{timeDiff} * #{index} DAY)
            AND p.PRE_TIME &lt; DATE_SUB(CURDATE(), INTERVAL #{timeDiff} * (#{index} - 1) DAY)
            GROUP BY REC_ID
            )p ON p.REC_ID = rd.rec_id
            WHERE rd.app_id = #{appId}
            GROUP BY rd.dis_name
            )  b ON a.dis_name = b.dis_name

             ORDER BY rate DESC LIMIT 5)
        </foreach>
    </select>


    <select id="top5DisNameCompensate" parameterType="Map" resultType="Map">
        <foreach collection="timeSplit" item="index" separator=" UNION ">
            (SELECT #{appId} app_id, '' ins_code, #{timeType} time_type, a.dis_name,
            DATE_SUB(#{statisticsDate}, INTERVAL #{timeDiff} * (#{index} - 1) DAY) group_name,
            (IFNULL(b.num,0)- a.num) / a.num * 100 AS rate

            FROM (
            SELECT app_id, ins_code, dis_name, COUNT(*) num FROM t_record r
            JOIN
            ( SELECT REC_ID FROM t_prescription p
            WHERE
            p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110
            AND p.PRE_TIME >= DATE_SUB(#{statisticsDate}, INTERVAL #{timeDiff} * (#{index} + 1) DAY)
            AND p.PRE_TIME &lt; DATE_SUB(#{statisticsDate}, INTERVAL #{timeDiff} * #{index} DAY)
            GROUP BY REC_ID
            )p
            ON p.REC_ID = r.rec_id
            WHERE app_id = #{appId}
            GROUP BY dis_name) a

            LEFT JOIN (
            SELECT rd.dis_name ,COUNT(*) num
            FROM t_record rd
            JOIN
            ( SELECT REC_ID
            FROM t_prescription p
            WHERE  p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110
            AND p.PRE_TIME >= DATE_SUB(#{statisticsDate}, INTERVAL #{timeDiff} * #{index} DAY)
            AND p.PRE_TIME &lt; DATE_SUB(#{statisticsDate}, INTERVAL #{timeDiff} * (#{index} - 1) DAY)
            GROUP BY REC_ID
            )p ON p.REC_ID = rd.rec_id
            WHERE rd.app_id = #{appId}
            GROUP BY rd.dis_name
            )  b ON a.dis_name = b.dis_name

            ORDER BY rate DESC LIMIT 5)

        </foreach>
    </select>
    <insert id="saveStatisticsTop5DisNameByGoUp" parameterType="list">
        REPLACE into t_statistics_dis_rate ( app_id, ins_code, time_type, group_name, dis_name, rate, create_time) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.app_id},#{item.ins_code},#{item.time_type},#{item.group_name},#{item.dis_name},#{item.rate}, now())
        </foreach>
    </insert>
    <insert id="saveTop5DisNameCompensate" parameterType="list">
        REPLACE into t_statistics_dis_rate ( app_id, ins_code, time_type, group_name, dis_name, rate, create_time) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.app_id},#{item.ins_code},#{item.time_type},#{item.group_name},#{item.dis_name},#{item.rate},#{createTime})
        </foreach>
    </insert>

    <sql id="statisticsTop5DisNameByGenderSql">
        (SELECT app_id, dis_name, 'M' gender, COUNT(*) num
        FROM t_record r
        WHERE EXISTS(SELECT 1 FROM t_prescription p WHERE p.REC_ID = r.rec_id
        AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110
        AND TIMESTAMPDIFF(DAY, p.PRE_TIME, DATE_ADD(CURDATE(),INTERVAL 1 DAY)) &lt;= #{timeDiff} )
        AND r.REC_GENDER = 'M' and r.dis_name IS NOT NULL
        GROUP BY app_id, dis_name
        UNION
        SELECT app_id, dis_name, 'F' gender, COUNT(*) num
        FROM t_record r
        WHERE EXISTS(SELECT 1 FROM t_prescription p WHERE p.REC_ID = r.rec_id
        AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110
        AND TIMESTAMPDIFF(DAY, p.PRE_TIME, DATE_ADD(CURDATE(),INTERVAL 1 DAY)) &lt;= #{timeDiff} )
        AND r.REC_GENDER = 'F' and r.dis_name IS NOT NULL
        GROUP BY app_id, dis_name)
    </sql>
    <insert id="statisticsTop5DisNameByGender" parameterType="Map">
        INSERT INTO t_statistics_dis_gender ( app_id, time_type, gender, dis_name, num, create_time)
        SELECT app_id, #{timeType}, gender, dis_name, num, NOW()
        FROM
        <include refid="statisticsTop5DisNameByGenderSql"/>
        a
        WHERE 5 > (SELECT COUNT(*) FROM
        <include refid="statisticsTop5DisNameByGenderSql"/>
        b WHERE a.app_id = b.app_id AND a.gender = b.gender AND b.num > a.num)
    </insert>

    <sql id="statisticsTop5DisNameByTermNameSql">
        (SELECT app_id,dis_name, COUNT(*) num FROM t_record r
        WHERE EXISTS(SELECT 1 FROM t_prescription p WHERE p.REC_ID = r.rec_id
        AND p.REC_EXT_TYPE >= 50 AND p.REC_EXT_TYPE != 110
        AND p.PRE_TIME >= #{beginTime} AND p.PRE_TIME &lt; #{endTime})
        GROUP BY app_id, dis_name)
    </sql>
    <insert id="statisticsTop5DisNameByTermName" parameterType="Map">
        INSERT INTO t_statistics_dis_terms ( app_id, terms, dis_name, num, begin_time, end_time, create_time)
        SELECT app_id, #{terms}, dis_name, num, #{beginTime}, #{endTime}, NOW()
        FROM
        <include refid="statisticsTop5DisNameByTermNameSql"/>
        a
        WHERE 5 > (SELECT COUNT(*) FROM
        <include refid="statisticsTop5DisNameByTermNameSql"/>
        b WHERE a.app_id = b.app_id AND b.num > a.num)
    </insert>


    <insert id="statisticsAvgAmount">
        INSERT INTO t_statistics_avg_amount (app_id, ins_code, doctor_id, doctor_name, avg_amount, start_date, end_date)
        SELECT * FROM
        (SELECT APP_ID, INS_CODE, PRE_DOCTOR, PRE_DOCTORNAME,
        SUM(MAT_TOL_MONEY) / SUM(PRE_NUM) avg_amount,
        CONCAT(DATE_FORMAT(CURDATE(), '%Y-%m'), '-01'), CURDATE()
        FROM t_prescription pre
        WHERE pre.is_del = '0' AND pre.PRE_TYPE IN ('1', '2')
        AND PRE_SINGLE_MONEY > 10 AND PRE_TIME > DATE_FORMAT(CURDATE(), '%Y-%m')
        AND IS_INSURANCE = '1' AND REC_EXT_TYPE >= 50 AND REC_EXT_TYPE != 110
        GROUP BY PRE_DOCTOR ORDER BY PRE_DOCTOR) a
    </insert>

    <select id="getDoctorMonthlyAvgAmount" parameterType="com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo"
            resultType="decimal">
        select avg_amount
        from t_statistics_avg_amount
        where app_id = #{appId} and ins_code = #{insCode} and doctor_id = #{id}
        and end_date = (SELECT MAX(end_date) FROM t_statistics_avg_amount
        WHERE app_id = #{appId} AND ins_code = #{insCode} AND doctor_id = #{id})
        order by id desc limit 1
    </select>

</mapper>
