<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.statistics.TStatisticsPrescriptionExpandMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.statistics.TStatisticsPrescriptionExpand">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="ins_id" jdbcType="VARCHAR" property="insId"/>
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId"/>
        <result column="pre_id" jdbcType="VARCHAR" property="preId"/>
        <result column="pre_orgin" jdbcType="VARCHAR" property="preOrgin"/>
        <result column="pre_num" jdbcType="INTEGER" property="preNum"/>
        <result column="insert_date" jdbcType="TIMESTAMP" property="insertDate"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
    </resultMap>

    <resultMap id="BaseDoctorResultMap" type="com.jiuzhekan.cbkj.beans.statistics.TStatisticsPrescriptionExpand">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId"/>
    </resultMap>

    <!--从处方列表中获取开方医生id-->
    <select id="getStatisticsPrescriptionDoctorList" resultMap="BaseDoctorResultMap">
        SELECT PRE_DOCTOR as doctor_id
            from t_prescription
        where PRE_ORIGIN = '4'
        GROUP BY PRE_DOCTOR
    </select>

    <!--统计数据-->
    <select id="getStatisticsPrescriptionListbyDoctorId" parameterType="String" resultMap="BaseResultMap">
        SELECT pre.app_id,pre.INS_CODE as ins_id,pre.PRE_DOCTOR as doctor_id,
            pre.PRE_ORIGIN_ID as pre_id,'1' as pre_orgin,
            count(pre.pre_id) as pre_num,now() as insert_date
            from t_prescription pre
        where pre.PRE_ORIGIN = '4'
         and pre.PRE_ORIGIN_ID is not null
         and pre.pre_doctor = #{doctorId} and pre.PRE_TIME>DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY pre.PRE_DOCTOR,pre.PRE_ORIGIN_ID
        order BY pre.PRE_DOCTOR,pre_num desc
        limit 50
    </select>

    <!--删除-->
    <delete id="delStatisticsPrescriptionByDoctorList" parameterType="java.util.List">
        delete from t_statistics_prescription_expand
        where doctor_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
    </delete>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_statistics_prescription_expand (id,app_id,ins_id,doctor_id,pre_id,pre_orgin,pre_num,insert_date,update_date) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.insId},#{item.doctorId},#{item.preId},#{item.preOrgin},#{item.preNum},#{item.insertDate},#{item.updateDate})
        </foreach>
    </insert>

</mapper>