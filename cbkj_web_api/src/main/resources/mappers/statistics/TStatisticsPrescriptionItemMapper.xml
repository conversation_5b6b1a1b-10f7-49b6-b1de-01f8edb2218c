<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.statistics.TStatisticsPrescriptionItemMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.statistics.TStatisticsPrescriptionItem">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="mat_id" jdbcType="VARCHAR" property="matId" />
        <result column="mat_name" jdbcType="VARCHAR" property="matName" />
        <result column="his_mat_id" jdbcType="VARCHAR" property="hisMatId" />
        <result column="his_mat_name" jdbcType="VARCHAR" property="hisMatName" />
        <result column="mat_number" jdbcType="INTEGER" property="matNumber" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="insert_date" jdbcType="TIMESTAMP" property="insertDate" />
    </resultMap>


    <sql id="Base_Column_List">
    id,app_id,app_name,ins_code,ins_name,mat_id,mat_name,his_mat_id,his_mat_name,mat_number,create_date,insert_date
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TStatisticsPrescriptionItem">
        delete from t_statistics_prescription_item where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_statistics_prescription_item where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TStatisticsPrescriptionItem">
        insert into t_statistics_prescription_item (<include refid="Base_Column_List" />) values
        (#{id},#{appId},#{appName},#{insCode},#{insName},#{matId},#{matName},#{hisMatId},#{hisMatName},#{matNumber},#{createDate},#{insertDate})
    </insert>

    <insert id="insertList" parameterType="List">
        replace into t_statistics_prescription_item (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.matId},#{item.matName},#{item.hisMatId},#{item.hisMatName},#{item.matNumber},#{item.createDate},#{item.insertDate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TStatisticsPrescriptionItem">
        update t_statistics_prescription_item
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="matId != null">
                mat_id = #{ matId },
             </if>
             <if test="matName != null">
                mat_name = #{ matName },
             </if>
             <if test="hisMatId != null">
                his_mat_id = #{ hisMatId },
             </if>
             <if test="hisMatName != null">
                his_mat_name = #{ hisMatName },
             </if>
             <if test="matNumber != null">
                mat_number = #{ matNumber },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="insertDate != null">
                insert_date = #{ insertDate },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_statistics_prescription_item where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TStatisticsPrescriptionItem" resultMap="BaseResultMap">
        SELECT id,app_id,app_name,ins_code,ins_name,mat_id,mat_name,his_mat_id,his_mat_name,mat_number,create_date,insert_date
        from t_statistics_prescription_item
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getCountByPreTimeGroupMatName"
            resultType="com.jiuzhekan.cbkj.beans.statistics.TStatisticsPrescriptionItemResponse">

        SELECT
        t.mat_name as matName,
        t.`his_mat_name` as hisMatName,
        SUM(t.mat_number) AS total
        FROM `t_statistics_prescription_item` t
        <where>
            <if test="preTimeBegin != null and preTimeBegin != '' and preTimeEnd != null and preTimeEnd != ''">
                and t.create_date BETWEEN #{preTimeBegin} AND #{preTimeEnd}
            </if>
            <if test="appId != null and appId != '' ">
                and t.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode != '' ">
                and t.INS_CODE = #{insCode}
            </if>
        </where>
        GROUP BY t.mat_name,t.`his_mat_name`


    </select>

</mapper>