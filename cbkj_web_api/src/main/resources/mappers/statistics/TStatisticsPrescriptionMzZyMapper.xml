<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.statistics.mapper.statistics.TStatisticsPrescriptionMzZyMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.statistics.beans.statistics.TStatisticsPrescriptionMzZy">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="total_num" jdbcType="INTEGER" property="totalNum" />
        <result column="inner_num" jdbcType="INTEGER" property="innerNum" />
        <result column="inner_mz_num" jdbcType="INTEGER" property="innerMzNum" />
        <result column="inner_zy_num" jdbcType="INTEGER" property="innerZyNum" />
        <result column="exter_num" jdbcType="INTEGER" property="exterNum" />
        <result column="exter_mz_num" jdbcType="INTEGER" property="exterMzNum" />
        <result column="exter_zy_num" jdbcType="INTEGER" property="exterZyNum" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="insert_date" jdbcType="TIMESTAMP" property="insertDate" />
    </resultMap>


    <sql id="Base_Column_List">
    id,app_id,app_name,ins_code,ins_name,dept_id,dept_name,user_id,user_name,total_num,inner_num,inner_mz_num,inner_zy_num,exter_num,exter_mz_num,exter_zy_num,create_date,insert_date
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.statistics.TStatisticsPrescriptionMzZy">
        delete from t_statistics_prescription_mz_zy where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_statistics_prescription_mz_zy where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.statistics.beans.statistics.TStatisticsPrescriptionMzZy">
        insert into t_statistics_prescription_mz_zy (<include refid="Base_Column_List" />) values
        (#{id},#{appId},#{appName},#{insCode},#{insName},#{deptId},#{deptName},#{userId},#{userName},#{totalNum},#{innerNum},#{innerMzNum},#{innerZyNum},#{exterNum},#{exterMzNum},#{exterZyNum},#{createDate},#{insertDate})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_statistics_prescription_mz_zy (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.deptId},#{item.deptName},#{item.userId},#{item.userName},#{item.totalNum},#{item.innerNum},#{item.innerMzNum},#{item.innerZyNum},#{item.exterNum},#{item.exterMzNum},#{item.exterZyNum},#{item.createDate},#{item.insertDate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.statistics.TStatisticsPrescriptionMzZy">
        update t_statistics_prescription_mz_zy
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="deptName != null">
                dept_name = #{ deptName },
             </if>
             <if test="userId != null">
                user_id = #{ userId },
             </if>
             <if test="userName != null">
                user_name = #{ userName },
             </if>
             <if test="totalNum != null">
                total_num = #{ totalNum },
             </if>
             <if test="innerNum != null">
                inner_num = #{ innerNum },
             </if>
             <if test="innerMzNum != null">
                inner_mz_num = #{ innerMzNum },
             </if>
             <if test="innerZyNum != null">
                inner_zy_num = #{ innerZyNum },
             </if>
             <if test="exterNum != null">
                exter_num = #{ exterNum },
             </if>
             <if test="exterMzNum != null">
                exter_mz_num = #{ exterMzNum },
             </if>
             <if test="exterZyNum != null">
                exter_zy_num = #{ exterZyNum },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="insertDate != null">
                insert_date = #{ insertDate },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_statistics_prescription_mz_zy where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.statistics.beans.statistics.TStatisticsPrescriptionMzZy" resultMap="BaseResultMap">
        SELECT id,app_id,app_name,ins_code,ins_name,dept_id,dept_name,user_id,user_name,total_num,inner_num,inner_mz_num,inner_zy_num,exter_num,exter_mz_num,exter_zy_num,create_date,insert_date
        from t_statistics_prescription_mz_zy
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.statistics.beans.statistics.TStatisticsPrescriptionMzZy">
        <id column="id" jdbcType="INTEGER"  property="id" />
    </resultMap>

    <select id="selectStatisticsPrescriptionMzZyResult"
            resultMap="BaseResultMap2" parameterType="Map">
        SELECT b.id,a.`APP_ID` as appId,a.`INS_CODE` insCode,a.`DEPT_ID` deptId,CAST(a.`PRE_TIME` AS DATE) createDate ,a.`PRE_DOCTOR` userId,a.PRE_DOCTORNAME userName,
        COUNT(a.PRE_ID) number,
        ifnull(b.`total_num`,0) totalNum,
        ifnull(b.`inner_num`,0) innerNum,
        ifnull(b.`inner_mz_num`,0) innerMzNum,
        ifnull(b.`inner_zy_num`,0) innerZyNum,
        ifnull(b.`exter_num`,0) exterNum,
        ifnull(b.`exter_mz_num`,0) exterMzNum,
        ifnull(b.`exter_zy_num`,0) exterZyNum
        FROM t_prescription AS a LEFT JOIN  t_statistics_prescription_mz_zy AS b ON(
        a.`APP_ID`=b.`app_id` AND a.`INS_CODE`=b.`ins_code` AND a.`PRE_DOCTOR`=b.`user_id` AND b.create_date=CAST(IFNULL(a.PRE_TIME,'1900-01-01 00:00:00') AS DATE)
        )
        WHERE a.`PRE_TYPE`=#{preType} and a.PRE_MZ_ZY=#{preMzZy}
        and TIMESTAMPDIFF(DAY,DATE_FORMAT(a.PRE_TIME,'%Y-%m-%d'), DATE_ADD(#{curDate},INTERVAL 1 DAY)) = #{timeDiff} AND a.app_id IS NOT NULL
        <!-- AND a.REC_EXT_TYPE >= 50 AND a.REC_EXT_TYPE != 110 AND a.IS_DEL='0' -->
        <!--        and a.IS_PAY = '1' -->
        GROUP BY a.`APP_ID`,a.`INS_CODE`,a.`PRE_DOCTOR`,createDate,a.PRE_MZ_ZY
    </select>

</mapper>