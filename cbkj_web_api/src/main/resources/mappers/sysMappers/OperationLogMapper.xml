<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysMapper.OperationLogMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysBeans.OperationLog">
        <id column="log_id" jdbcType="VARCHAR"  property="logId" />
        <result column="admin_name" jdbcType="VARCHAR" property="adminName" />
        <result column="admin_id" jdbcType="VARCHAR" property="adminId" />
        <result column="descr" jdbcType="VARCHAR" property="descr" />
        <result column="params" jdbcType="VARCHAR" property="params" />
        <result column="results" jdbcType="VARCHAR" property="results" />
        <result column="http_ip" jdbcType="VARCHAR" property="httpIp" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="is_ok" jdbcType="INTEGER" property="isOk" />
        <result column="reason" jdbcType="VARCHAR" property="reason" />
        <result column="method_name" jdbcType="VARCHAR" property="methodName" />
        <result column="http_mode" jdbcType="VARCHAR" property="httpMode" />
    </resultMap>

    <sql id="Base_Column_List">
    log_id,admin_name,admin_id,descr,params,results,http_ip,create_date,is_ok,reason,method_name,http_mode
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="OperationLog">
        delete from sys_operation_log where log_id = #{ logId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_operation_log where log_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="createTimeTable" parameterType="OperationLog">
        CREATE TABLE IF NOT EXISTS sys_operation_log${tableSuffix} LIKE sys_operation_log
    </insert>

    <!--单个插入-->
    <insert id="insert"  parameterType="OperationLog">
        insert into sys_operation_log${tableSuffix} (log_id,admin_name,admin_id,descr,params,results,http_ip,create_date,is_ok,reason,method_name,http_mode) values
        (#{logId},#{adminName},#{adminId},#{descr},#{params},#{results},#{httpIp},now(),#{isOk},#{reason},#{methodName},#{httpMode})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into sys_operation_log (log_id,admin_name,admin_id,descr,params,results,http_ip,create_date,is_ok,reason,method_name,http_mode) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.logId},#{item.adminName},#{item.adminId},#{item.descr},#{item.params},#{item.results},#{item.httpIp},#{item.createDate},#{item.isOk},#{item.reason},#{item.methodName},#{item.httpMode})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="OperationLog">
        update sys_operation_log
        set
        admin_name = #{ adminName },
        admin_id = #{ adminId },
        descr = #{ descr },
        params = #{ params },
        http_ip = #{ httpIp },
        create_date = #{ createDate },
        is_ok = #{ isOk },
        reason = #{ reason },
        method_name = #{ methodName },
        http_mode = #{ httpMode }
        where log_id = #{ logId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update sys_operation_log set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from sys_operation_log where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getObjectByMap" resultMap="BaseResultMap" parameterType="Map">
        select <include refid="Base_Column_List" />
        from  sys_operation_log${tableSuffix} where log_id = #{id}
    </select>

    <!--分页查询基础语句-->
    <select id="getPageDatas" parameterType="OperationLog" resultType="Map">
        SELECT log_id,admin_name,admin_id,descr,params,http_ip,DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') create_date,is_ok,reason,method_name,http_mode
        from sys_operation_log${tableSuffix}
        <where>
            <if test="descr != null and descr!='' ">
                and descr like CONCAT('%',trim(#{descr}),'%')
            </if>
            <if test="adminName != null and adminName!='' ">
                and admin_name = #{adminName}
            </if>
            <if test=" beginTime != null">
                and create_date >= #{beginTime}
            </if>
            <if test=" endTime!=null ">
                and create_date &lt;= #{endTime}
            </if>
        </where>
        order by create_date desc
    </select>

</mapper>
