<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.template.TRecordTemplateDetailMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.template.TRecordTemplateDetail">
        <id column="DETAIL_ID" jdbcType="VARCHAR"  property="detailId" />
        <result column="TEMPL_ID" jdbcType="VARCHAR" property="templId" />
        <result column="DETAIL_NAME" jdbcType="VARCHAR" property="detailName" />
        <result column="DETAIL_TYPE" jdbcType="VARCHAR" property="detailType" />
        <result column="IS_SEX" jdbcType="VARCHAR" property="isSex" />
        <result column="DETAIL_DISPLAY" jdbcType="TINYINT" property="detailDisplay" />
        <result column="DETAIL_TEXT" jdbcType="VARCHAR" property="detailText" />
        <result column="DETAIL_DEFAULT" jdbcType="VARCHAR" property="detailDefault" />
        <result column="DETAIL_HINT" jdbcType="VARCHAR" property="detailHint" />
        <result column="DETAIL_CONTENT" jdbcType="VARCHAR" property="detailContent" />
        <result column="DETAIL_SUFFIX" jdbcType="VARCHAR" property="detailSuffix" />
        <result column="IS_DEFAULT" jdbcType="VARCHAR" property="isDefault" />
        <result column="IS_SHOW" jdbcType="VARCHAR" property="isShow" />
        <result column="IS_MUTEX" jdbcType="VARCHAR" property="isMutex" />
        <result column="IS_REQUIRED" jdbcType="VARCHAR" property="isRequired" />
        <result column="DIS_ID" jdbcType="VARCHAR" property="disId" />
        <result column="DETAIL_PID" jdbcType="VARCHAR" property="detailPid" />
        <result column="DETAIL_LEVEL" jdbcType="TINYINT" property="detailLevel" />
        <result column="DETAIL_NUM" jdbcType="TINYINT" property="detailNum" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="CREATE_USERNAME" jdbcType="VARCHAR" property="createUsername" />
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
        <result column="UPDATE_USERNAME" jdbcType="VARCHAR" property="updateUsername" />
        <result column="DEL_DATE" jdbcType="TIMESTAMP" property="delDate" />
        <result column="DEL_USER" jdbcType="VARCHAR" property="delUser" />
        <result column="DEL_USERNAME" jdbcType="VARCHAR" property="delUsername" />
        <result column="IS_DEL" jdbcType="VARCHAR" property="isDel" />
        <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    </resultMap>


    <sql id="Base_Column_List">
    DETAIL_ID,TEMPL_ID,DETAIL_NAME,DETAIL_TYPE,IS_SEX,DETAIL_DISPLAY,DETAIL_TEXT,DETAIL_DEFAULT,DETAIL_HINT,DETAIL_CONTENT,DETAIL_SUFFIX,IS_DEFAULT,IS_SHOW,IS_MUTEX,IS_REQUIRED,DIS_ID,DETAIL_PID,DETAIL_LEVEL,DETAIL_NUM,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRecordTemplateDetail">
        delete from t_record_template_detail where DETAIL_ID = #{ detailId }
    </delete>

    <delete id="deleteByTempId" parameterType="String">
        update t_record_template_detail set is_del = '1' where TEMPL_ID = #{ tempId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_record_template_detail where DETAIL_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TRecordTemplateDetail">
        insert into t_record_template_detail (DETAIL_ID,TEMPL_ID,DETAIL_NAME,DETAIL_TYPE,IS_SEX,DETAIL_DISPLAY,DETAIL_TEXT,DETAIL_DEFAULT,DETAIL_HINT,DETAIL_CONTENT,DETAIL_SUFFIX,IS_DEFAULT,IS_SHOW,IS_MUTEX,IS_REQUIRED,DIS_ID,DETAIL_PID,DETAIL_LEVEL,DETAIL_NUM,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL) values
        (#{detailId},#{templId},#{detailName},#{detailType},#{isSex},#{detailDisplay},#{detailText},#{detailDefault},#{detailHint},#{detailContent},#{detailSuffix},#{isDefault},#{isShow},#{isMutex},#{isRequired},#{disId},#{detailPid},#{detailLevel},#{detailNum},#{createDate},#{createUser},#{createUsername},#{updateDate},#{updateUser},#{updateUsername},#{delDate},#{delUser},#{delUsername},#{isDel})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_record_template_detail (DETAIL_ID,TEMPL_ID,DETAIL_NAME,DETAIL_TYPE,IS_SEX,DETAIL_DISPLAY,DETAIL_TEXT,DETAIL_DEFAULT,DETAIL_HINT,DETAIL_CONTENT,DETAIL_SUFFIX,IS_DEFAULT,IS_SHOW,IS_MUTEX,IS_REQUIRED,DIS_ID,DETAIL_PID,DETAIL_LEVEL,DETAIL_NUM,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.detailId},#{item.templId},#{item.detailName},#{item.detailType},#{item.isSex},#{item.detailDisplay},#{item.detailText},#{item.detailDefault},#{item.detailHint},#{item.detailContent},#{item.detailSuffix},#{item.isDefault},#{item.isShow},#{item.isMutex},#{item.isRequired},#{item.disId},#{item.detailPid},#{item.detailLevel},#{item.detailNum},#{item.createDate},#{item.createUser},#{item.createUsername},#{item.updateDate},#{item.updateUser},#{item.updateUsername},#{item.delDate},#{item.delUser},#{item.delUsername},#{item.isDel})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRecordTemplateDetail">
        update t_record_template_detail
        <set>
             <if test="templId != null">
                TEMPL_ID = #{ templId },
             </if>
             <if test="detailName != null">
                DETAIL_NAME = #{ detailName },
             </if>
             <if test="detailType != null">
                DETAIL_TYPE = #{ detailType },
             </if>
             <if test="isSex != null">
                IS_SEX = #{ isSex },
             </if>
             <if test="detailDisplay != null">
                DETAIL_DISPLAY = #{ detailDisplay },
             </if>
             <if test="detailText != null">
                DETAIL_TEXT = #{ detailText },
             </if>
             <if test="detailDefault != null">
                DETAIL_DEFAULT = #{ detailDefault },
             </if>
             <if test="detailHint != null">
                DETAIL_HINT = #{ detailHint },
             </if>
             <if test="detailContent != null">
                DETAIL_CONTENT = #{ detailContent },
             </if>
             <if test="detailSuffix != null">
                 DETAIL_SUFFIX = #{ detailSuffix },
             </if>
             <if test="isDefault != null">
                IS_DEFAULT = #{ isDefault },
             </if>
             <if test="isShow != null">
                IS_SHOW = #{ isShow },
             </if>
             <if test="isMutex != null">
                IS_MUTEX = #{ isMutex },
             </if>
             <if test="isRequired != null">
                IS_REQUIRED = #{ isRequired },
             </if>
             <if test="disId != null">
                DIS_ID = #{ disId },
             </if>
             <if test="detailPid != null">
                DETAIL_PID = #{ detailPid },
             </if>
             <if test="detailLevel != null">
                DETAIL_LEVEL = #{ detailLevel },
             </if>
             <if test="detailNum != null">
                DETAIL_NUM = #{ detailNum },
             </if>
             <if test="createDate != null">
                CREATE_DATE = #{ createDate },
             </if>
             <if test="createUser != null">
                CREATE_USER = #{ createUser },
             </if>
             <if test="createUsername != null">
                CREATE_USERNAME = #{ createUsername },
             </if>
             <if test="updateDate != null">
                UPDATE_DATE = #{ updateDate },
             </if>
             <if test="updateUser != null">
                UPDATE_USER = #{ updateUser },
             </if>
             <if test="updateUsername != null">
                UPDATE_USERNAME = #{ updateUsername },
             </if>
             <if test="delDate != null">
                DEL_DATE = #{ delDate },
             </if>
             <if test="delUser != null">
                DEL_USER = #{ delUser },
             </if>
             <if test="delUsername != null">
                DEL_USERNAME = #{ delUsername },
             </if>
             <if test="isDel != null">
                IS_DEL = #{ isDel },
             </if>
        </set>
        where DETAIL_ID = #{ detailId }
    </update>


    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_record_template_detail where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record_template_detail where DETAIL_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_record_template_detail where DETAIL_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TRecordTemplateDetail" resultType="Map">
        SELECT DETAIL_ID,TEMPL_ID,DETAIL_NAME,DETAIL_TYPE,IS_SEX,DETAIL_DISPLAY,DETAIL_TEXT,DETAIL_DEFAULT,DETAIL_HINT,DETAIL_CONTENT,DETAIL_SUFFIX,IS_DEFAULT,IS_SHOW,IS_MUTEX,IS_REQUIRED,DIS_ID,DETAIL_PID,DETAIL_LEVEL,DETAIL_NUM,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL  from t_record_template_detail
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TRecordTemplateDetail" resultMap="BaseResultMap">
        SELECT DETAIL_ID,TEMPL_ID,DETAIL_NAME,DETAIL_TYPE,IS_SEX,DETAIL_DISPLAY,DETAIL_TEXT,DETAIL_DEFAULT,DETAIL_HINT,DETAIL_CONTENT,DETAIL_SUFFIX,IS_DEFAULT,IS_SHOW,IS_MUTEX,IS_REQUIRED,DIS_ID,DETAIL_PID,DETAIL_LEVEL,DETAIL_NUM
        from t_record_template_detail
        <where>
            IS_DEL = '0'
            <if test=" templId != null and templId != '' ">
                and TEMPL_ID = #{templId}
            </if>
            <if test=" isSex != null and isSex != '' ">
                and IS_SEX = #{isSex}
            </if>
            <if test=" isDefault != null and isDefault != '' ">
                and IS_DEFAULT = #{isDefault}
            </if>
            <if test=" isShow != null and isShow != '' ">
                and IS_SHOW = #{isShow}
            </if>
            <if test=" isMutex != null and isMutex != '' ">
                and IS_MUTEX = #{isMutex}
            </if>
            <if test=" isRequired != null and isRequired != '' ">
                and IS_REQUIRED = #{isRequired}
            </if>
            <if test=" detailPid != null and detailPid != '' ">
                and DETAIL_PID = #{detailPid}
            </if>
            <if test=" detailLevel != null ">
                and DETAIL_LEVEL = #{detailLevel}
            </if>
            <if test=" detailType != null ">
                and DETAIL_TYPE = #{detailType}
            </if>
            <if test=" detailDisplay != null ">
                and DETAIL_DISPLAY = #{detailDisplay}
            </if>
        </where>
        order by DETAIL_LEVEL, DETAIL_NUM
    </select>

    <!--获取病历模板明细和明细内容-->
    <select id="getDetailContentList" parameterType="TRecordTemplateDetail" resultMap="BaseResultMap">
        SELECT rec.rec_id as recId,tem.DETAIL_ID,tem.TEMPL_ID,tem.DETAIL_NAME,tem.DETAIL_TYPE,tem.IS_SEX,tem.DETAIL_DISPLAY,tem.DETAIL_TEXT,tem.DETAIL_DEFAULT,tem.DETAIL_HINT,tem.DETAIL_CONTENT,tem.DETAIL_SUFFIX,tem.IS_DEFAULT,tem.IS_SHOW,tem.IS_MUTEX,tem.IS_REQUIRED,tem.DIS_ID,tem.DETAIL_PID,tem.DETAIL_LEVEL,tem.DETAIL_NUM,rec.DETAIL_CONTENT as CONTENT
        from t_record_template_detail tem
        left join t_record_detail rec on rec.DETAIL_ID = tem.DETAIL_ID and rec.rec_id = #{recId}
        where tem.is_del = '0' and tem.TEMPL_ID = #{templId}
        order by tem.DETAIL_LEVEL, tem.DETAIL_NUM
    </select>

</mapper>