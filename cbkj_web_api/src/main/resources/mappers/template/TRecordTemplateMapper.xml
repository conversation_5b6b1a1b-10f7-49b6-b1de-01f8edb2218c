<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.template.TRecordTemplateMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.template.TRecordTemplate">
        <id column="TEMPL_ID" jdbcType="VARCHAR" property="templId"/>
        <result column="TEMPL_NAME" jdbcType="VARCHAR" property="templName"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="DEPT_ID" jdbcType="VARCHAR" property="deptId"/>
        <result column="DIS_ID" jdbcType="VARCHAR" property="disId"/>
        <result column="DIS_NAME" jdbcType="VARCHAR" property="disName"/>
        <result column="TEMPL_TYPE" jdbcType="VARCHAR" property="templType"/>
        <result column="TEMPL_CLASSIFY" jdbcType="VARCHAR" property="templClassify"/>
        <result column="IS_FIRST" jdbcType="VARCHAR" property="isFirst"/>
        <result column="IS_DEFAULT" jdbcType="VARCHAR" property="isDefault"/>
        <result column="IS_USING" jdbcType="VARCHAR" property="isUsing"/>
        <result column="IS_SHARE" jdbcType="VARCHAR" property="isShare"/>
        <result column="TEMPL_NUM" jdbcType="TINYINT" property="templNum"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="createDateStr" jdbcType="TIMESTAMP" property="createDateStr"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="CREATE_USERNAME" jdbcType="VARCHAR" property="createUsername"/>
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATE_USERNAME" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="DEL_DATE" jdbcType="TIMESTAMP" property="delDate"/>
        <result column="DEL_USER" jdbcType="VARCHAR" property="delUser"/>
        <result column="DEL_USERNAME" jdbcType="VARCHAR" property="delUsername"/>
        <result column="IS_DEL" jdbcType="VARCHAR" property="isDel"/>
        <result column="SELF_TEMP" jdbcType="BOOLEAN" property="selfTemp"/>
    </resultMap>


    <sql id="Base_Column_List">
    TEMPL_ID,TEMPL_NAME,APP_ID,INS_CODE,DEPT_ID,DIS_ID,DIS_NAME,TEMPL_TYPE,TEMPL_CLASSIFY,IS_FIRST,IS_DEFAULT,IS_USING,IS_SHARE,TEMPL_NUM,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TRecordTemplate">
        delete from t_record_template where TEMPL_ID = #{ templId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        update t_record_template set is_del = '1' where TEMPL_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" parameterType="TRecordTemplate">
        insert into t_record_template (TEMPL_ID,TEMPL_NAME,APP_ID,INS_CODE,DEPT_ID,DIS_ID,DIS_NAME,TEMPL_TYPE,TEMPL_CLASSIFY,IS_FIRST,IS_DEFAULT,IS_USING,IS_SHARE,TEMPL_NUM,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL) values
        (#{templId},#{templName},#{appId},#{insCode},#{deptId},#{disId},#{disName},#{templType},#{templClassify},#{isFirst},#{isDefault},#{isUsing},#{isShare},#{templNum},#{createDate},#{createUser},#{createUsername},#{updateDate},#{updateUser},#{updateUsername},#{delDate},#{delUser},#{delUsername},#{isDel})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_record_template
        (TEMPL_ID,TEMPL_NAME,APP_ID,INS_CODE,DEPT_ID,DIS_ID,DIS_NAME,TEMPL_TYPE,TEMPL_CLASSIFY,IS_FIRST,IS_DEFAULT,IS_USING,IS_SHARE,TEMPL_NUM,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.templId},#{item.templName},#{item.appId},#{item.insCode},#{item.deptId},#{item.disId},#{item.disName},#{item.templType},#{item.templClassify},#{item.isFirst},#{item.isDefault},#{item.isUsing},#{item.isShare},#{item.templNum},#{item.createDate},#{item.createUser},#{item.createUsername},#{item.updateDate},#{item.updateUser},#{item.updateUsername},#{item.delDate},#{item.delUser},#{item.delUsername},#{item.isDel})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TRecordTemplate">
        update t_record_template
        <set>
            <if test="templName != null">
                TEMPL_NAME = #{ templName },
            </if>
            <if test="appId != null">
                APP_ID = #{ appId },
            </if>
            <if test="insCode != null">
                INS_CODE = #{ insCode },
            </if>
            <if test="deptId != null">
                DEPT_ID = #{ deptId },
            </if>
                DIS_ID = #{ disId },
                DIS_NAME = #{ disName },
            <if test="templType != null">
                TEMPL_TYPE = #{ templType },
            </if>
            <if test="templClassify != null">
                TEMPL_CLASSIFY = #{ templClassify },
            </if>
            <if test="isFirst != null">
                IS_FIRST = #{ isFirst },
            </if>
            <if test="isDefault != null">
                IS_DEFAULT = #{ isDefault },
            </if>
            <if test="isUsing != null">
                IS_USING = #{ isUsing },
            </if>
            <if test="isShare != null">
                IS_SHARE = #{ isShare },
            </if>
            <if test="templNum != null">
                TEMPL_NUM = #{ templNum },
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{ createDate },
            </if>
            <if test="createUser != null">
                CREATE_USER = #{ createUser },
            </if>
            <if test="createUsername != null">
                CREATE_USERNAME = #{ createUsername },
            </if>
            <if test="updateDate != null">
                UPDATE_DATE = #{ updateDate },
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{ updateUser },
            </if>
            <if test="updateUsername != null">
                UPDATE_USERNAME = #{ updateUsername },
            </if>
            <if test="delDate != null">
                DEL_DATE = #{ delDate },
            </if>
            <if test="delUser != null">
                DEL_USER = #{ delUser },
            </if>
            <if test="delUsername != null">
                DEL_USERNAME = #{ delUsername },
            </if>
            <if test="isDel != null">
                IS_DEL = #{ isDel },
            </if>
        </set>
        where TEMPL_ID = #{ templId }
    </update>


    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_record_template where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_record_template where TEMPL_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_record_template where TEMPL_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TRecordTemplate" resultType="Map">
        SELECT TEMPL_ID,TEMPL_NAME,APP_ID,INS_CODE,DEPT_ID,DIS_ID,DIS_NAME,TEMPL_TYPE,TEMPL_CLASSIFY,IS_FIRST,IS_DEFAULT,IS_USING,IS_SHARE,TEMPL_NUM,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL  from t_record_template
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TRecordTemplate" resultMap="BaseResultMap">
        SELECT
        TEMPL_ID,TEMPL_NAME,APP_ID,INS_CODE,DEPT_ID,DIS_ID,DIS_NAME,TEMPL_TYPE,TEMPL_CLASSIFY,
        IS_FIRST,IS_DEFAULT,IS_USING,IS_SHARE,TEMPL_NUM,CREATE_USERNAME,CREATE_DATE,CREATE_USER,
        DATE_FORMAT(CREATE_DATE,'%Y-%m-%d %H:%i:%s') as createDateStr
        from t_record_template
        <where>
            IS_DEL = '0' AND
             ( (IS_SHARE = '3' AND APP_ID = #{appId})
            OR (IS_SHARE = '2' AND APP_ID = #{appId} AND INS_CODE = #{insCode})
            OR (IS_SHARE = '1' AND APP_ID = #{appId} AND INS_CODE = #{insCode} AND DEPT_ID = #{deptId})
            OR CREATE_USER = #{createUser}
            OR TEMPL_CLASSIFY = '1'
            )
            <if test=" templName != null and templName != '' ">
                and TEMPL_NAME like CONCAT('%',trim(#{templName}),'%')
            </if>
            <if test=" disName != null and disName != '' ">
                and DIS_NAME like CONCAT('%',trim(#{disName}),'%')
            </if>
            <if test=" templType != null and templType != '' ">
                and TEMPL_TYPE = #{templType}
            </if>
            <if test=" templClassify != null and templClassify != '' ">
                and TEMPL_CLASSIFY = #{templClassify}
            </if>
            <if test=" isUsing != null and isUsing != '' ">
                and IS_USING = #{isUsing}
            </if>
            <if test=" isDefault != null and isDefault != '' ">
                and IS_DEFAULT = #{isDefault}
            </if>
        </where>
        order by TEMPL_CLASSIFY desc,  IS_SHARE, IS_DEFAULT DESC, CREATE_DATE desc
    </select>

    <!--默认电子病历模板（用户TEMPL_NUM为空，系统TEMPL_NUM为0，排序时先获取用户的，后获取系统的）-->
    <select id="getDefaultTemplate" parameterType="TRecordTemplate" resultMap="BaseResultMap">
        SELECT
        TEMPL_ID,TEMPL_NAME,DIS_ID,DIS_NAME
        from t_record_template
        <where>
            is_del = '0' and is_using = '1'
            <if test=" templId != null and templId != '' ">
                and TEMPL_ID = #{templId}
            </if>
            <if test=" isDefault != null and isDefault != '' ">
                and IS_DEFAULT = #{isDefault}
            </if>
            <if test=" isShare != null and isShare != '' ">
                and IS_SHARE = #{isShare}
            </if>
            <if test=" appId != null and appId != '' ">
                and APP_ID = #{appId}
            </if>
            <if test=" insCode != null and insCode != '' ">
                and INS_CODE = #{insCode}
            </if>
            <if test=" deptId != null and deptId != '' ">
                and DEPT_ID = #{deptId}
            </if>
            <choose>
                <when test=" disId != null and disId != '' ">
                    and DIS_ID = #{disId}
                </when>
                <when test=" disName != null and disName != '' ">
                    and DIS_NAME = #{disName}
                </when>
                <otherwise>
                    and (DIS_NAME IS NULL OR DIS_NAME = '')
                </otherwise>
            </choose>
            <if test=" createUser != null and createUser != '' ">
                and CREATE_USER = #{createUser}
            </if>
            <if test=" templClassify != null and templClassify != '' ">
                and TEMPL_CLASSIFY = #{templClassify}
            </if>
        </where>
        order by CREATE_DATE desc
        limit 1
    </select>


    <!--默认电子病历模板（用户TEMPL_NUM为空，系统TEMPL_NUM为0，排序时先获取用户的，后获取系统的）-->
<!--    <select id="getDefaultTemplate" parameterType="TRecordTemplate" resultMap="BaseResultMap">-->
<!--        SELECT-->
<!--        TEMPL_ID,TEMPL_NAME,DIS_ID,DIS_NAME,-->
<!--        CREATE_USER = #{createUser} AS isMy,-->
<!--        DIS_NAME = #{disName} as isMyDis,-->
<!--        APP_ID = #{appId} as isMyApp,-->
<!--        INS_CODE = #{insCode} as isMyIns,-->
<!--        DEPT_ID = #{deptId} as isMyDept-->
<!--        from t_record_template-->
<!--        <where>-->
<!--            is_del = '0' and is_using = '1'-->
<!--            <if test=" isDefault != null and isDefault != '' ">-->
<!--                and IS_DEFAULT = #{isDefault}-->
<!--            </if>-->
<!--            <if test=" isShare != null and isShare != '' ">-->
<!--                and IS_SHARE = #{isShare}-->
<!--            </if>-->
<!--            <if test=" disName != null and disName != '' ">-->
<!--                and DIS_NAME = #{disName}-->
<!--            </if>-->
<!--        </where>-->
<!--        order by TEMPL_CLASSIFY desc, isMyDis desc,isMyApp desc, isMyIns desc, isMyDept desc, isMy DESC,-->
<!--        IS_SHARE, IS_DEFAULT DESC, CREATE_DATE desc-->
<!--        limit 1-->
<!--    </select>-->


    <!--根据疾病名称获取默认模板数量-->
    <select id="getDefaultCountByDisName" parameterType="TRecordTemplate" resultType="Integer">
        SELECT count(*)
        from t_record_template
        <where>
            IS_DEL = '0' and IS_DEFAULT = '1'
            and APP_ID = #{appId}
            <if test=" disName != null and disName != '' ">
                and DIS_NAME = #{disName}
            </if>
            <if test=" disName == null or disName == '' ">
                and (DIS_NAME = '' or DIS_NAME is null)
            </if>
            <if test=" createUser != null and createUser != '' ">
                and CREATE_USER = #{createUser}
            </if>
        </where>
    </select>

    <!--根据疾病名称把所有模板设为非默认-->
    <update id="noDefaultByDisName" parameterType="TRecordTemplate" >
        update t_record_template
        set IS_DEFAULT = '0'
        <where>
            IS_DEL = '0'
            and APP_ID = #{appId}
            <if test=" disName != null and disName != '' ">
                and DIS_NAME = #{disName}
            </if>
            <if test=" disName == null or disName == '' ">
                and (DIS_NAME = '' or DIS_NAME is null)
            </if>
            <if test=" createUser != null and createUser != '' ">
                and CREATE_USER = #{createUser}
            </if>
        </where>
    </update>

</mapper>