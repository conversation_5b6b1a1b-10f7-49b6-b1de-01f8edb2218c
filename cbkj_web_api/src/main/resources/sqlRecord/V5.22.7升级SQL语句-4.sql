ALTER TABLE `cbkj_web_api`.`t_statistics_dis_num`
    CHANGE `dis_name` `dis_name` VARCHAR(256) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '疾病名称';
ALTER TABLE `cbkj_web_api`.`t_statistics_dis_age`
    CHANGE `dis_name` `dis_name` VARCHAR(256) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '疾病名称';
ALTER TABLE `cbkj_web_api`.`t_statistics_dis_gender`
    CHANGE `dis_name` `dis_name` VARCHAR(256) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '疾病名称';
ALTER TABLE `cbkj_web_api`.`t_statistics_dis_rate`
    CHANGE `dis_name` `dis_name` VARCHAR(256) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '疾病名称';
ALTER TABLE `cbkj_web_api`.`t_statistics_dis_terms`
    CHANGE `dis_name` `dis_name` VARCHAR(256) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '疾病名称';
