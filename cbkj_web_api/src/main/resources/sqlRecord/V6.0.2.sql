replace into `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) values('000310','子指标数据',NULL,NULL,'0','000330',NULL,NULL,'1',NULL,NULL,'1','17','2','1','1','2');
REPLACE into `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) values('00031001','医疗机构','/indicator-data-child/indicator-data-org',NULL,'0','000310',NULL,NULL,'1',NULL,NULL,'1','1','3','0','1','2');
REPLACE into `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) values('00031002','科室','/indicator-data-child/indicator-data-dep',NULL,'0','000310',NULL,NULL,'1',NULL,NULL,'1','2','3','0','1','2');
REPLACE into `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) values('000320','主指标数据',NULL,NULL,'0','000330','2025-04-28 09:26:31','70810c874405453b99c6c2cf72296fe5','1',NULL,NULL,'1','16','2','1','1','2');
REPLACE into `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) values('00032001','医疗机构','/indicator-data/indicator-data-org',NULL,'0','000320',NULL,NULL,'1',NULL,NULL,'1','1','3','0','1','2');
REPLACE into `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) values('00032002','科室','/indicator-data/indicator-data-dep',NULL,'0','000320',NULL,NULL,'1',NULL,NULL,'1','2','3','0','1','2');
REPLACE into `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) values('000330','绩效考核',NULL,NULL,'0','00020','2025-04-28 09:26:31','70810c874405453b99c6c2cf72296fe5','1',NULL,NULL,NULL,'17','1','1',NULL,'2');