INSERT INTO `cbkj_web_api`.`sys_cue_word` (
    `cue_word_id`,
    `cue_word_type`,
    `cue_word_text`,
    `app_id`,
    `ins_code`,
    `status`,
    `create_time`
)
VALUES
    (
        '8',
        'record_analysis',
        '从辨证思路、遣方用药分析、药对、药物功效和性味归经、方解、安全合理用药等维度进行病历分析；',
        '000000',
        '000000',
        '0',
        '2025-03-28 10:17:28'
    );

INSERT INTO `cbkj_web_api`.`sys_cue_word` (`cue_word_id`, `cue_word_type`, `cue_word_text`, `app_id`, `ins_code`, `status`, `create_time`) VALUES ('9', 'symptom_analysis', '症状发生频率、持续时间、部位、严重程度变化，伴随症状有无，药物反应、治疗依从性，体质、舌象，影像资料、实验室指标。', '000000', '000000', '0', '2025-03-28 10:20:42');



INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'OPEN_AI_RECORD_ANALYSIS', '开启病历解析', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '', '2', '2201', '开（）关【默认】', 'L04'
FROM DUAL WHERE  NOT EXISTS (
    SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'OPEN_AI_RECORD_ANALYSIS'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关', 0 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='OPEN_AI_RECORD_ANALYSIS'
                                   ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开', 1 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='OPEN_AI_RECORD_ANALYSIS'
                                   ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开'
);




ALTER TABLE `cbkj_web_api`.`t_prescription`
    ADD COLUMN `DC_TYPE_SUBCLASS` VARCHAR(1) DEFAULT '1' NULL COMMENT '配送类型（0.定点配送点1.配送到家）';


CREATE TABLE `cbkj_web_api`.`bs_self_pickup_point`(
                                                      `self_pickup_id` VARCHAR(32) NOT NULL,
                                                      `self_pickup_name` VARCHAR(64) COMMENT '自提点名称',
                                                      `self_pickup_address` VARCHAR(256) COMMENT '自提点详细地址',
                                                      `head_name` VARCHAR(16) COMMENT '自提点负责人',
                                                      `head_phone` VARCHAR(12) COMMENT '自提点电话',
                                                      `self_pickup_type_name` VARCHAR(32) COMMENT '自提点类型名',
                                                      `self_pickup_type_code` INT(2) COMMENT '自提点类型代码01  中医药医院',
                                                      `DC_COUNTY_CODE` VARCHAR(32) COMMENT '省代码',
                                                      `DC_COUNTY` VARCHAR(32) COMMENT '省',
                                                      `DC_TOWN_CODE` VARCHAR(32) COMMENT '市代码',
                                                      `DC_TOWN` VARCHAR(32) COMMENT '市',
                                                      `DC_VILLAGE_CODE` VARCHAR(32) COMMENT '区代码',		/* 复制栏位名称 */
                                                      `DC_VILLAGE` VARCHAR(32) COMMENT '区',		/* 复制栏位名称 */
                                                      `DC_STREET_CODE` VARCHAR(32) COMMENT '街道代码',
                                                      `DC_STREET` VARCHAR(32) COMMENT '街道',
                                                      `DC_VILLAGE_CODE` VARCHAR(32) COMMENT '村代码',		/* 复制栏位名称 */
                                                      `DC_VILLAGE` VARCHAR(32) COMMENT '村',		/* 复制栏位名称 */
                                                      `CREATE_USER_ID` VARCHAR(32) COMMENT '创建人',
                                                      `CREATE_DATE` DATETIME COMMENT '创建日期',
                                                      `CREATE_USERNAME` VARCHAR(32) COMMENT '创建人名',
                                                      `IS_DEL` INT(1) DEFAULT 0 COMMENT '0否 1是',
                                                      `sort` INT COMMENT '排序',
                                                      PRIMARY KEY (`self_pickup_id`)
);

