INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'AI_ELE_RECORD_QUA_CON', '是否开启电子病历质控', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '', '4', '2201', '开启电子病历质控【开/关】 默认否', 'M006'
FROM DUAL WHERE  NOT EXISTS (
    SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'AI_ELE_RECORD_QUA_CON'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关', 0 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='AI_ELE_RECORD_QUA_CON'
                                   ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开', 1 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='AI_ELE_RECORD_QUA_CON'
                                   ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开'
);



ALTER TABLE `cbkj_web_api`.`sys_cue_word`
    CHANGE `cue_word_type` `cue_word_type` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '0.病历结构。1.AI方解2.诊断依据';
ALTER TABLE `cbkj_web_api`.`sys_cue_word`
    ADD COLUMN `cue_trans_text` TEXT NULL COMMENT '转化词';


INSERT INTO `cbkj_web_api`.`sys_cue_word` (`cue_word_id`, `cue_word_type`, `cue_word_text`, `app_id`, `ins_code`, `status`, `create_time`,cue_trans_text) VALUES

    ('10', 'AI_ELE_RECORD_QUA_CON', '请结合患者信息，分析该中医门诊病历在诊断规范性，辨证要素，症状关联等是否合规，无需扩充推荐处置方案。', '000000', '000000', '0', '2025-05-28 11:23:03','中医电子病历质控分析');





INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '6', '健康处方', 6 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='VALID_PRESCRIPTION_TYPE'
                                   ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='6' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='健康处方'
);


ALTER TABLE `cbkj_web_parameter`.`sys_admin_infoex`
    CHANGE `default_prescription_share` `default_prescription_share` VARCHAR(8) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '默认处方类型(1内服 2外用 4适宜技术 5中药制剂 6健康处方)';

