#2022.4.1 gw 患者增加证件类型，身份证号改为证件号码
alter table t_patients add column `PATIENT_CERTIFICATE_TYPE` varchar(2) DEFAULT '01'
        COMMENT '证件类型（01居民身份证，02居民户口簿，03护照，04军官证（士兵证），05驾驶执照，06港澳居民来往内地通行证，07台湾居民来往内地通行证，99其他）' after PATIENT_BIRTHDAY;
ALTER TABLE `t_patients` CHANGE `PATIENT_CERTIFICATE` `PATIENT_CERTIFICATE` VARCHAR(512) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '证件号码';

/*
INSERT INTO `t_sys_code` (`CODE_ID`, `CODE_VALUE`, `CODE_NAME`, `CODE_NUM`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`)
    VALUES ('34', 'certificateType', '证件类型', '34', '2022-04-01 15:19:21', 'guowei', 'guowei');
INSERT INTO `t_sys_code_item` ( `ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `ZIFU2`, `ZIFU5`, CREATE_DATE, CREATE_USER, CREATE_USERNAME) VALUES
('3401','居民身份证','34','1','01','1','2022-04-01 15:26:23','guowei','guowei'),
('3402','居民户口簿','34','2','02','0','2022-04-01 15:26:23','guowei','guowei'),
('3403','护照','34','3','03','0','2022-04-01 15:26:23','guowei','guowei'),
('3404','军官证（士兵证）','34','4','04','0','2022-04-01 15:26:23','guowei','guowei'),
('3405','驾驶执照','34','5','05','0','2022-04-01 15:26:23','guowei','guowei'),
('3406','港澳居民来往内地通行证','34','6','06','0','2022-04-01 15:26:23','guowei','guowei'),
('3407','台湾居民来往内地通行证','34','7','07','0','2022-04-01 15:26:23','guowei','guowei'),
('3499','其他','34','99','99','0','2022-04-01 15:26:23','guowei','guowei');
*/

#2022.5.9 guowei 电子病历模板明细分类的字段类型改为字符串
ALTER TABLE `t_record_template_detail` CHANGE `DETAIL_TYPE` `DETAIL_TYPE` VARCHAR(32) NOT NULL COMMENT '模版明细类型(0为其他，1为主诉，2为现病史，3为既往史，4为家族史，5为过敏史，6为舌象，7为脉象，8为中医四诊，9为体格检查，10为辅助检查)';
#2022.5.9 guowei 电子病历明细分类的字段类型改为字符串
ALTER TABLE `t_record_detail` CHANGE `DETAIL_TYPE` `DETAIL_TYPE` VARCHAR(32) NULL COMMENT '模版明细类型(0为其他，1为主诉，2为现病史，3为既往史，4为家族史，5为过敏史，6为舌象，7为脉象，8为中医四诊，9为体格检查，10为辅助检查)';

#2022.5.12 guowei 智能辩证问卷保存病历分类代码
ALTER TABLE `t_record_syndrome_answer` ADD COLUMN `RECORD_TYPE_CODE` VARCHAR(32) NULL COMMENT '病历分类代码' AFTER `TEMP_TYPE`;
ALTER TABLE `t_record_syndrome_answer` ADD COLUMN `RECORD_TYPE_NAME` VARCHAR(32) NULL COMMENT '病历分类名称' AFTER `RECORD_TYPE_CODE`;
ALTER TABLE `t_record_syndrome_answer` ADD COLUMN `RECORD_TYPE_OPTION_CODE` VARCHAR(32) NULL COMMENT '病历分类选项代码' AFTER `RECORD_TYPE_NAME`;




