
#2022.6.9 guowei 处方触发器预扣库存调综合平台预扣库存表
DELIMITER $$

USE `cbkj_web_api`$$

DROP TRIGGER /*!50032 IF EXISTS */ `g_afterUpdate_prescription`$$

CREATE
    /*[DEFINER = { user | CURRENT_USER }]*/
    TRIGGER `g_afterUpdate_prescription` AFTER UPDATE ON `t_prescription`
    FOR EACH ROW
BEGIN
    DECLARE operationTypeTr INT(11);
    DECLARE operationContentTr VARCHAR(50);
    DECLARE needInsertOrderTr VARCHAR(50);
    DECLARE operationIdTr VARCHAR(32);
    DECLARE operationNameTr VARCHAR(32);
    DECLARE operationTimeTr DATETIME;

    SET needInsertOrderTr = 0;

    IF old.IS_DEL != '1' AND new.IS_DEL = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 10;
        SET operationContentTr = '删除处方';
        SET operationIdTr = new.PRE_DOCTOR;
        SET operationNameTr = new.PRE_DOCTORNAME;
        SET operationTimeTr = NOW();
        /**回退预扣库存**/
        DELETE FROM `cbkj_web_parameter`.`t_withholding_stock` WHERE PRE_ID = old.PRE_ID;

    ELSEIF old.IS_CHECK != '1' AND new.IS_CHECK = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 30;
        SET operationContentTr = '审核通过';
        SET operationIdTr = new.CHECK_USERID;
        SET operationNameTr = new.CHECK_USERNAME;
        SET operationTimeTr = new.CHECK_TIME;

    ELSEIF old.IS_CHECK != '2' AND new.IS_CHECK = '2' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 40;
        SET operationContentTr = '审核未通过';
        SET operationIdTr = new.CHECK_USERID;
        SET operationNameTr = new.CHECK_USERNAME;
        SET operationTimeTr = new.CHECK_TIME;

    ELSEIF old.REC_EXT_TYPE != '45' AND new.REC_EXT_TYPE = '45' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 45;
        SET operationContentTr = '推送失败';
        SET operationIdTr = '';
        SET operationNameTr = '';
        SET operationTimeTr = NOW();
        /**回退预扣库存**/
        DELETE FROM `cbkj_web_parameter`.`t_withholding_stock` WHERE PRE_ID = old.PRE_ID;

    ELSEIF old.IS_PAY != '1' AND new.IS_PAY = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 50;
        SET operationContentTr = '处方缴费成功';
        SET operationIdTr = new.PAY_USERID;
        SET operationNameTr = new.PAY_USERNAME;
        SET operationTimeTr = new.PAY_TIME;
        /**回退预扣库存**/
        DELETE FROM `cbkj_web_parameter`.`t_withholding_stock` WHERE PRE_ID = old.PRE_ID;
    END IF;

    IF needInsertOrderTr = 1 THEN
        INSERT INTO `t_order_status` (
            `STATUS_ID`,`REGISTER_ID`,`PRE_ID`,`OPERATION_USERID`,`OPERATION_USERNAME`,`OPERATION_TIME`,
            `OPERATION_TYPE`,`OPERATION_CONTENT`,CREATE_TIME
        ) VALUES (
                     REPLACE (UUID(), '-', ''),'0',new.PRE_ID,operationIdTr,operationNameTr,operationTimeTr,
                     operationTypeTr,operationContentTr,NOW()
                 ) ;
    END IF;
END;
$$

DELIMITER ;


#2022.6.10 guowei 处方增加字段：药房名称
ALTER TABLE `t_prescription` ADD COLUMN `STORE_NAME` VARCHAR(64) NULL COMMENT '药房名称' AFTER `STORE_ID`;

#2022.6.10 guowei 处方明细增加字段：his药品规格ID，药房药品规格ID
ALTER TABLE `t_prescription_item` ADD COLUMN `YPGGDM_HIS` VARCHAR(32) NULL COMMENT '药品规格ID-HIS' AFTER `YPMC_CENTER`,
                                  ADD COLUMN `YPGGDM_CENTER` VARCHAR(32) NULL COMMENT '药品规格ID-中心' AFTER `YPGGDM_HIS`;

#2022.6.14 guowei 增加医生书籍表
CREATE TABLE `t_doctor_book` (
                                 `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 `DOCTOR_ID` varchar(32) NOT NULL COMMENT '医生ID',
                                 `DOCTOR_NAME` varchar(64) DEFAULT NULL COMMENT '医生姓名',
                                 `BOOK_ID` varchar(32) NOT NULL COMMENT '书籍ID',
                                 `BOOK_NAME` varchar(500) DEFAULT NULL COMMENT '书籍名称',
                                 `AUTHOR` varchar(128) DEFAULT NULL COMMENT '作者',
                                 `CHAPTER_ID` varchar(32) DEFAULT NULL COMMENT '章节ID',
                                 `PROGRESS` varchar(128) DEFAULT NULL COMMENT '阅读进度',
                                 `RATE` decimal(4,2) DEFAULT NULL COMMENT '已读比例',
                                 `SORT` int(11) DEFAULT NULL COMMENT '排序',
                                 `SHELF` int(1) DEFAULT NULL COMMENT '加入书架 1是0否',
                                 `CREATE_DATE` datetime DEFAULT NULL COMMENT '创建时间',
                                 `UPDATE_DATE` datetime DEFAULT NULL COMMENT '修改时间',
                                 PRIMARY KEY (`ID`,`DOCTOR_ID`,`BOOK_ID`),
                                 KEY `IDX_DOCTOR_ID` (`DOCTOR_ID`),
                                 KEY `IDX_BOOK_ID` (`BOOK_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='书籍阅读记录';




