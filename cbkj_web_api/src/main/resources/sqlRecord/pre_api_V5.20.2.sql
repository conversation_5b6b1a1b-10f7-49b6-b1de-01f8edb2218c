

#2022.6.29 guowei 处方增加煎药方式、包装方式、取药方式
ALTER TABLE `t_prescription`
    CHANGE `PRE_SPECIAL_FEE` `PRE_SPECIAL_FEE` DECIMAL(10,4) NULL COMMENT '特殊调配费' AFTER `HIS_PRE_ID`,
    CHANGE `DECOCT_TYPE` `DECOCT_TYPE` VARCHAR(1)  NULL COMMENT '是否代煎（1为代煎，0为自煎）' AFTER `PRE_SPECIAL_FEE`,
    CHANGE `PRE_DECOCT_NUM` `PRE_DECOCT_NUM` SMALLINT(6) NULL COMMENT '代煎贴数' AFTER `DECOCT_TYPE`,
    <PERSON>ANGE `PRE_DECOCTION_FEE` `PRE_DECOCTION_FEE` DECIMAL(10,4) NULL COMMENT '代煎费' AFTER `PRE_DECOCT_NUM`,
    CHANGE `IS_PRODUCTION` `IS_PRODUCTION` VARCHAR(1)  NULL COMMENT '是否膏方（0否 1是）' AFTER `PRE_DECOCTION_FEE`,
    CHANGE `PRODUCTION_TYPE_ID` `PRODUCTION_TYPE_ID` VARCHAR(32)  NULL COMMENT '膏方类型代码' AFTER `IS_PRODUCTION`,
    CHANGE `PRODUCTION_TYPE` `PRODUCTION_TYPE` VARCHAR(100)  NULL COMMENT '膏方类型' AFTER `PRODUCTION_TYPE_ID`,
    CHANGE `PRE_PRODUCTION_FEE` `PRE_PRODUCTION_FEE` DECIMAL(10,4) NULL COMMENT '制膏费' AFTER `PRODUCTION_TYPE`,
    CHANGE `DC_TYPE` `DC_TYPE` VARCHAR(1)  NULL COMMENT '是否配送（1配送，0自提）' AFTER `PRE_PRODUCTION_FEE`,
    CHANGE `PRE_SMO_MONEY` `PRE_SMO_MONEY` DECIMAL(10,4) NULL COMMENT '治疗费' AFTER `PRE_SMOKE_TYPE`;


CREATE TABLE `t_pre_decoction` (
                                   `pre_id` varchar(32) NOT NULL COMMENT '处方ID',
                                   `decoct_num` int(6) DEFAULT NULL COMMENT '代煎贴数',
                                   `dic_id` varchar(32) DEFAULT NULL COMMENT '煎药方式ID',
                                   `dic_code` varchar(32) DEFAULT NULL COMMENT '煎药方式代码',
                                   `dic_name` varchar(64) DEFAULT NULL COMMENT '煎药方式名称',
                                   `charge_code` varchar(32) DEFAULT NULL COMMENT '煎药方式收费代码',
                                   `charge_name` varchar(32) DEFAULT NULL COMMENT '煎药方式收费项目名称',
                                   `price` decimal(6,2) DEFAULT NULL COMMENT '价格',
                                   PRIMARY KEY (`pre_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='处方代煎';

CREATE TABLE `t_pre_express` (
                                 `pre_id` varchar(32) NOT NULL COMMENT '处方ID',
                                 `dic_id` varchar(32) DEFAULT NULL COMMENT '取药方式ID',
                                 `dic_code` varchar(32) DEFAULT NULL COMMENT '取药方式代码',
                                 `dic_name` varchar(64) DEFAULT NULL COMMENT '取药方式名称',
                                 `charge_code` varchar(32) DEFAULT NULL COMMENT '取药方式收费代码',
                                 `charge_name` varchar(32) DEFAULT NULL COMMENT '取药方式收费项目名称',
                                 `price` decimal(6,2) DEFAULT NULL COMMENT '价格',
                                 PRIMARY KEY (`pre_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='处方配送';

CREATE TABLE `t_pre_production` (
                                    `pre_id` varchar(32) NOT NULL COMMENT '处方ID',
                                    `type_id` varchar(32) DEFAULT NULL COMMENT '膏方类型ID',
                                    `type_name` varchar(64) DEFAULT NULL COMMENT '膏方类型名称',
                                    `dic_id` varchar(32) DEFAULT NULL COMMENT '包装方式ID',
                                    `dic_code` varchar(32) DEFAULT NULL COMMENT '包装方式代码',
                                    `dic_name` varchar(64) DEFAULT NULL COMMENT '包装方式名称',
                                    `charge_code` varchar(32) DEFAULT NULL COMMENT '包装方式收费代码',
                                    `charge_name` varchar(32) DEFAULT NULL COMMENT '包装方式收费项目名称',
                                    `price` decimal(6,2) DEFAULT NULL COMMENT '价格',
                                    PRIMARY KEY (`pre_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='处方制膏';


#2022.7.7 guowei 自动授权科室协定方触发器，去掉参数判断
DELIMITER $$

USE `cbkj_web_api`$$

DROP PROCEDURE IF EXISTS `update_personal_rule_auth`$$

CREATE PROCEDURE `update_personal_rule_auth`(deptId VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci, userId VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci)
BEGIN
    DECLARE num INT DEFAULT 0;
    SET num = (SELECT (SELECT COUNT(*) FROM t_personal_rule_auth WHERE rule_id = '1' AND dept_id = deptId AND user_id = userId) > 0);
    IF num = 0 THEN
        INSERT INTO t_personal_rule_auth (rule_id, dept_id, dept_check_all, user_id, pers_pre_id, create_date, create_user)
        SELECT pra.rule_id, pra.dept_id, 1, userId, pra.pers_pre_id, NOW(), 'system'
        FROM t_personal_rule_auth pra
        WHERE pra.rule_id = '1' AND pra.dept_id = deptId
        GROUP BY pra.rule_id, pra.dept_id, pra.pers_pre_id;
    END IF;

END$$

DELIMITER ;


#第一次对接 2022.5.17
DROP TABLE `sys_admin_info`;
DROP TABLE `sys_admin_info_rule`;
DROP TABLE `sys_admin_infoex`;
DROP TABLE `sys_admin_menu`;
DROP TABLE `sys_admin_practice`;
DROP TABLE `sys_admin_rule`;
DROP TABLE `sys_admin_rule_menu`;
DROP TABLE `sys_admin_url`;
DROP TABLE `sys_app`;
DROP TABLE `sys_department`;
DROP TABLE `sys_institution`;
DROP TABLE `b_material`;
DROP TABLE `t_app_material_mapping`;
DROP TABLE `t_center_his_mapping`;
DROP TABLE `t_center_his_ypml`;
DROP TABLE `t_center_his_ypmlmx`;
DROP TABLE `t_center_store_ins_mapping`;
DROP TABLE `t_center_ypkc`;
DROP TABLE `t_center_ypkc_yk`;
DROP TABLE `t_his_ypbm`;
DROP TABLE `t_ypml_ins_mapping`;
DROP TABLE `t_sys_param`;
DROP TABLE `t_sys_code`;
DROP TABLE `t_sys_code_item`;
DROP TABLE `t_his_code_item`;
DROP TABLE `t_sys_his_codeitem_mapping`;

DELETE FROM `t_business_proposal` WHERE is_del = '1';

#没有用到的表
DROP TABLE `t_interface_knowledge`;
DROP TABLE `t_mat_analysis`;
DROP TABLE `t_my_mark`;
DROP TABLE `t_record_template_content`;
DROP TABLE `t_record_template_mapping`;
DROP TABLE `t_sms_code`;

#药房配置迁移到综合平台
DROP TABLE `t_display`;
DROP TABLE `t_display_bak`;
DROP TABLE `t_display_code_item`;
DROP TABLE `t_display_mapping`;

#配方迁移到药房
DROP TABLE `t_formula`;
DROP TABLE `t_formula_auth`;
DROP TABLE `t_formula_item`;
