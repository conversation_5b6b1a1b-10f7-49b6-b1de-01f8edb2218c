#修改西医诊断参数
UPDATE cbkj_web_parameter.t_sys_param SET param_desc='[1:禁用][2:必填][3:校验]', param_type='3', PAR_CODE='PRESCRIPTION_ICD',PAR_NAME='西医诊断配置'
WHERE par_number='B102';

UPDATE cbkj_web_parameter.t_sys_param_init_desc,cbkj_web_parameter.t_sys_param SET t_sys_param_init_desc.param_init_name='禁用'
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`par_number`='B102'
  AND t_sys_param_init_desc.param_init_name='是';

UPDATE cbkj_web_parameter.t_sys_param_init_desc,cbkj_web_parameter.t_sys_param SET t_sys_param_init_desc.param_init_name='必填',param_init_code='2'
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`par_number`='B102'
  AND t_sys_param_init_desc.param_init_name='否';

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'3','校验',3
FROM cbkj_web_parameter.`t_sys_param` AS tsp INNER JOIN t_sys_param_init_desc AS tspi ON tsp.`PAR_ID`=tspi.`param_id`
WHERE tsp.`par_number`='B102' AND tspi.param_init_name='必填';

#西医疾病版本
INSERT INTO cbkj_web_parameter.t_sys_param (PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,PAR_VALUES,CREATE_DATE,CREATE_USER,create_user_name, `STATUS`,param_type,is_global,param_init_value,sort,menu_id,param_desc,par_number)
VALUES(REPLACE(UUID(),'-',''),'000000','000000','000000','WEST_DIS_VISION','西医疾病版本','1',NOW(),'admin','admin','0','2','0','2','9','1011','1:ICD10，2:国家临床诊断2.0','B107');

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'1','ICD10',1
FROM cbkj_web_parameter.`t_sys_param` AS tsp
WHERE tsp.`par_number`='B107';

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'2','国家临床诊断2.0',2
FROM cbkj_web_parameter.`t_sys_param` AS tsp
WHERE tsp.`par_number`='B107';


-- 增加处方表 患者类型字段
-- 增加处方表 患者类型字段
ALTER TABLE `t_prescription`
    ADD COLUMN `patient_types` VARCHAR (32) NULL COMMENT '患者类型(0自费1普通医保2医保离休3职工特慢J居民特慢)';
ALTER TABLE `t_prescription`
    ADD COLUMN `patient_types_name` VARCHAR (64) NULL COMMENT '患者类型名称';