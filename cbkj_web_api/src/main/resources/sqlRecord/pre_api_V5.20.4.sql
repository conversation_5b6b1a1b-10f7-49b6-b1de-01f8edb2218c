CREATE TABLE `t_statistics_register` (
                                         `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                         `app_id` VARCHAR(32) DEFAULT NULL COMMENT '医联体ID',
                                         `app_name` VARCHAR(32) DEFAULT NULL COMMENT '医联体',
                                         `ins_code` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构代码',
                                         `ins_name` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构',
                                         `dept_id` VARCHAR(32) DEFAULT NULL COMMENT '科室ID',
                                         `dept_name` VARCHAR(32) DEFAULT NULL COMMENT '科室',
                                         `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID',
                                         `user_name` VARCHAR(32) DEFAULT NULL COMMENT '用户',
                                         `register_times` INT(11) NOT NULL DEFAULT '0' COMMENT '就诊人次',
                                         `electronic_record_num` INT(11) NOT NULL DEFAULT '0' COMMENT '电子病历',
                                         `create_date` DATE NOT NULL COMMENT '统计日期',
                                         `insert_date` DATETIME DEFAULT NULL COMMENT '插入时间',
                                         PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=302808 DEFAULT CHARSET=utf8mb4 COMMENT='就诊人次统计表';

CREATE TABLE `t_statistics_prescription` (
                                             `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                             `app_id` VARCHAR(32) DEFAULT NULL COMMENT '医联体ID',
                                             `app_name` VARCHAR(32) DEFAULT NULL COMMENT '医联体',
                                             `ins_code` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构代码',
                                             `ins_name` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构',
                                             `dept_id` VARCHAR(32) DEFAULT NULL COMMENT '科室ID',
                                             `dept_name` VARCHAR(32) DEFAULT NULL COMMENT '科室',
                                             `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID',
                                             `user_name` VARCHAR(32) DEFAULT NULL COMMENT '用户',
                                             `total_num` INT(11) NOT NULL DEFAULT '0' COMMENT '总开方数量',
                                             `inner_num` INT(11) NOT NULL DEFAULT '0' COMMENT '内服方数量',
                                             `exter_num` INT(11) NOT NULL DEFAULT '0' COMMENT '外用方数量',
                                             `acu_num` INT(11) NOT NULL DEFAULT '0' COMMENT '适宜技术方数量',
                                             `patent_num` INT(11) NOT NULL DEFAULT '0' COMMENT '中成药数量',
                                             `prepare_num` INT(11) NOT NULL DEFAULT '0' COMMENT '制剂数量',
                                             `create_date` DATE NOT NULL COMMENT '统计日期',
                                             `insert_date` DATETIME DEFAULT NULL COMMENT '插入时间',
                                             PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=302805 DEFAULT CHARSET=utf8mb4 COMMENT='处方统计表';

CREATE TABLE `t_statistics_personal_prescription` (
                                                      `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                                      `app_id` VARCHAR(32) DEFAULT NULL COMMENT '医联体ID',
                                                      `app_name` VARCHAR(32) DEFAULT NULL COMMENT '医联体',
                                                      `ins_code` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构代码',
                                                      `ins_name` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构',
                                                      `dept_id` VARCHAR(32) DEFAULT NULL COMMENT '科室ID',
                                                      `dept_name` VARCHAR(32) DEFAULT NULL COMMENT '科室',
                                                      `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID',
                                                      `user_name` VARCHAR(32) DEFAULT NULL COMMENT '用户',
                                                      `total_num` INT(11) NOT NULL DEFAULT '0' COMMENT '协定方总数',
                                                      `self_num` INT(11) NOT NULL DEFAULT '0' COMMENT '个人协定方数量',
                                                      `dept_num` INT(11) NOT NULL DEFAULT '0' COMMENT '科室协定方数量',
                                                      `ins_num` INT(11) NOT NULL DEFAULT '0' COMMENT '全院协定方数量',
                                                      `app_num` INT(11) NOT NULL DEFAULT '0' COMMENT '专家经验共享数量',
                                                      `create_date` DATE NOT NULL COMMENT '统计日期',
                                                      `insert_date` DATETIME DEFAULT NULL COMMENT '插入时间',
                                                      PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=302805 DEFAULT CHARSET=utf8mb4 COMMENT='协定方统计表';

CREATE TABLE `t_statistics_analysis` (
                                         `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                         `app_id` VARCHAR(32) DEFAULT NULL COMMENT '医联体ID',
                                         `app_name` VARCHAR(32) DEFAULT NULL COMMENT '医联体',
                                         `ins_code` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构代码',
                                         `ins_name` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构',
                                         `dept_id` VARCHAR(32) DEFAULT NULL COMMENT '科室ID',
                                         `dept_name` VARCHAR(32) DEFAULT NULL COMMENT '科室',
                                         `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID',
                                         `user_name` VARCHAR(32) DEFAULT NULL COMMENT '用户',
                                         `usage_times` INT(11) NOT NULL DEFAULT '0' COMMENT '体质辨识报告数量',
                                         `create_date` DATE NOT NULL COMMENT '统计日期',
                                         `insert_date` DATETIME DEFAULT NULL COMMENT '插入时间',
                                         PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=302821 DEFAULT CHARSET=utf8mb4 COMMENT='体质辨识统计表';

CREATE TABLE `t_statistics_function2` (
                                          `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                          `app_id` VARCHAR(32) DEFAULT NULL COMMENT '医联体ID',
                                          `app_name` VARCHAR(32) DEFAULT NULL COMMENT '医联体',
                                          `ins_code` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构代码',
                                          `ins_name` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构',
                                          `dept_id` VARCHAR(32) DEFAULT NULL COMMENT '科室ID',
                                          `dept_name` VARCHAR(32) DEFAULT NULL COMMENT '科室',
                                          `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID',
                                          `user_name` VARCHAR(32) DEFAULT NULL COMMENT '用户',
                                          `classify` VARCHAR(64) DEFAULT NULL COMMENT '分类',
                                          `function` VARCHAR(64) NOT NULL COMMENT '功能',
                                          `usage_times` INT(11) NOT NULL COMMENT '使用次数',
                                          `create_date` DATE NOT NULL COMMENT '统计日期',
                                          `insert_date` DATETIME DEFAULT NULL COMMENT '插入时间',
                                          PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=302809 DEFAULT CHARSET=utf8mb4 COMMENT='功能统计表';



ALTER TABLE t_prescription_item ADD `tcxs` DECIMAL(18,6) DEFAULT NULL COMMENT '提纯系数';
ALTER TABLE t_prescription_item ADD `tcxs_mat_dose` DECIMAL(18,6) DEFAULT NULL COMMENT '饮片/颗粒 提纯系数计算结果';



-- 功能使用统计表增加索引
ALTER TABLE `t_statistics_function2` ADD INDEX `IDX_FUNCTION` (`classify`, `function`), ADD INDEX `IDX_CREATE_DATE` (`create_date`);


-- 增加处方表 患者类型字段
ALTER TABLE `t_prescription`
    ADD COLUMN `patient_types` VARCHAR (32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '患者类型(0自费1普通医保2医保离休3职工特慢J居民特慢)';
ALTER TABLE `t_prescription`
    ADD COLUMN `patient_types_name` VARCHAR (64) NULL COMMENT '患者类型名称';

-- 修改协定放明细表matDose范围
ALTER TABLE `t_personal_prescription_item`
    CHANGE `MAT_DOSE` `MAT_DOSE` DECIMAL (8, 4) NULL COMMENT '剂量';


-- 增加处方表 患者类型字段
ALTER TABLE t_prescription`
    ADD COLUMN `patient_types` VARCHAR (32) NULL COMMENT '患者类型(0自费1普通医保2医保离休3职工特慢J居民特慢)';
ALTER TABLE `t_prescription`
    ADD COLUMN `patient_types_name` VARCHAR (64) NULL COMMENT '患者类型名称';

