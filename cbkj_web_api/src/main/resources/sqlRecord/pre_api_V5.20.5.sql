#知识库索引
ALTER TABLE `zyznyxt_basic`.`b_icd_10`
    ADD INDEX `IDX_WEST_CODE` (`WEST_CODE`);
ALTER TABLE `zyznyxt_basic`.`b_disease_cw`
    ADD INDEX `IDX_WEST_CODE` (`WEST_CODE`),
    ADD INDEX `IDX_DIS_ID` (`DIS_ID`);

#一件事插件表
ALTER TABLE cbkj_web_api.`t_record`
    CHANGE `WESTERN_DISEASE_ID` `WESTERN_DISEASE_ID` varchar(32) COLLATE utf8mb4_general_ci NULL COMMENT '西医诊断ID' after `INS_TRE_ID`,
    ADD COLUMN `PATIENT_HEIGHT` varchar(5) COLLATE utf8mb4_general_ci NULL COMMENT '身高' after `AUXILIARY_EXAM`,
    ADD COLUMN `PATIENT_WEIGHT` varchar(5) COLLATE utf8mb4_general_ci NULL COMMENT '体重' after `PATIENT_HEIGHT`,
    ADD COLUMN `THE_CODE` varchar(32) COLLATE utf8mb4_general_ci NULL COMMENT '治法编码' after `SYM_NAME`,
    CHANGE `SPECIAL_NAME` `SPECIAL_NAME` varchar(64) COLLATE utf8mb4_general_ci NULL COMMENT '特病名称' after `IS_SPECIAL_DIS`;

ALTER TABLE cbkj_web_api.`t_his_record`
    ADD COLUMN `patient_height` varchar(8)  COLLATE utf8mb4_general_ci NULL COMMENT '患者身高' after `MEDICAL_TYPE_NAME` ,
    ADD COLUMN `patient_weight` varchar(8)  COLLATE utf8mb4_general_ci NULL COMMENT '患者体重' after `patient_height` ,
    ADD COLUMN `cfbsdm` varchar(8)  COLLATE utf8mb4_general_ci NULL COMMENT '处方辨识代码;1 普通处方; 2 急诊处方; 3 儿科处方; 4 麻醉处方; 5 精神药品处方; 9 不明;' after `patient_weight` ,
    ADD COLUMN `cfbs` varchar(8)  COLLATE utf8mb4_general_ci NULL COMMENT '处方辨识名称' after `cfbsdm` ,
    ADD COLUMN `systole` varchar(4)  COLLATE utf8mb4_general_ci NULL COMMENT '上臂收缩压的测量值，计量单位为mmHg' after `cfbs` ,
    ADD COLUMN `diastole` varchar(4)  COLLATE utf8mb4_general_ci NULL COMMENT '上臂舒张压的测量值，计量单位为mmHg' after `systole` ,
    ADD COLUMN `lxbxs` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '流行病学史' after `diastole` ,
    ADD COLUMN `jbs` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '疾病史（含外伤）' after `lxbxs` ,
    ADD COLUMN `yfjzs` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '预防接种史' after `jbs` ,
    ADD COLUMN `sss` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '手术史' after `yfjzs` ,
    ADD COLUMN `css` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '出生史' after `sss` ,
    ADD COLUMN `szs` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '生长史' after `css` ,
    ADD COLUMN `wys` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '喂养史' after `szs` ,
    ADD COLUMN `sxs` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '输血史' after `wys` ,
    ADD COLUMN `gms` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '过敏史' after `sxs` ,
    ADD COLUMN `grs` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '个人史' after `gms` ,
    ADD COLUMN `hys` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '婚育史' after `grs` ,
    ADD COLUMN `yjs` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '月经史' after `hys` ,
    ADD COLUMN `jzs` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '家族史' after `yjs` ,
    ADD COLUMN `ml` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '脉率' after `jzs` ,
    ADD COLUMN `yz` varchar(512)  COLLATE utf8mb4_general_ci NULL COMMENT '孕周（d）' after `ml` ,
    ADD COLUMN `fzbz` varchar(2)  COLLATE utf8mb4_general_ci NULL COMMENT '门诊初复诊（1初诊 2复诊）门诊必填' after `yz` ;

#一件事插件表
CREATE TABLE cbkj_web_api.`t_record2_ask`
(
    `id`          int(11) unsigned                        NOT NULL auto_increment COMMENT 'id',
    `rec_id`      varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '病历编号',
    `tag_no`      varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '标签编号',
    `wzysykwdm`   varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '饮食与口味代码',
    `wzysykwqt`   varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '饮食与口味其他',
    `wzysykw`     varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '饮食与口味',
    `wzsmdm`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '睡眠代码',
    `wzsmqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '睡眠其他',
    `wzsm`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '睡眠',
    `wzdbdm`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '大便代码',
    `wzdbqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '大便其他',
    `wzdb`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '大便',
    `wzhr`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '寒热',
    `wzhrqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '寒热其他',
    `wzch`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '出汗',
    `wzchqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '出汗其他',
    `wzts`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '头身',
    `wztsqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '头身其他',
    `wzxxwf`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '胸胁脘腹',
    `wzxxwfqt`    varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '胸胁脘腹其他',
    `wzem`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '耳目',
    `wzemqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '耳目其他',
    `wzxb`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '小便',
    `wzxbqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '小便其他',
    `wzfn`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '妇女',
    `wzfnqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '妇女其他',
    `wzxe`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '小儿',
    `wzxeqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '小儿其他',
    `wzqt3`       varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '其他',
    `status`      varchar(1) COLLATE utf8mb4_general_ci   NULL COMMENT '状态【0:可用，1:禁用】',
    `create_date` datetime                                NULL COMMENT '创建时间',
    `create_user` varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '创建人',
    `update_date` datetime                                NULL COMMENT '修改时间',
    `update_user` varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '修改人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_rec_id` (`rec_id`),
    UNIQUE KEY `idx_tag_no` (`tag_no`)
) ENGINE = InnoDB
  DEFAULT CHARSET = 'utf8mb4'
  COLLATE = 'utf8mb4_general_ci' COMMENT ='问诊信息表';


CREATE TABLE cbkj_web_api.`t_record2_cut`
(
    `id`          int(11) unsigned                        NOT NULL auto_increment COMMENT 'id',
    `rec_id`      varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '病历编号',
    `tag_no`      varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '标签编号',
    `qzmz`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '脉诊',
    `qzmzqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '脉诊其他',
    `qzmzdm`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '脉诊代码',
    `qzaz`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '按诊',
    `qzazqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '按诊其他',
    `qzqt`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '其他',
    `status`      varchar(1) COLLATE utf8mb4_general_ci   NULL COMMENT '状态【0:可用，1:禁用】',
    `create_date` datetime                                NULL COMMENT '创建时间',
    `create_user` varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '创建人',
    `update_date` datetime                                NULL COMMENT '修改时间',
    `update_user` varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '修改人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_rec_id` (`rec_id`),
    UNIQUE KEY `idx_tag_no` (`tag_no`)
) ENGINE = InnoDB
  DEFAULT CHARSET = 'utf8mb4'
  COLLATE = 'utf8mb4_general_ci' COMMENT ='切诊信息表';


CREATE TABLE cbkj_web_api.`t_record2_look`
(
    `id`           int(11) unsigned                        NOT NULL auto_increment COMMENT 'id',
    `rec_id`       varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '病历编号',
    `tag_no`       varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '标签编号',
    `wzsdm`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '舌代码',
    `wzsqt`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '舌其他',
    `wzs`          varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '舌',
    `wztdm`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '苔代码',
    `wztqt`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '苔其他',
    `wzt`          varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '苔',
    `wzws`         varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '神',
    `wzwsqt`       varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '神其他',
    `wzwms`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '面',
    `wzwmsqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '面其他',
    `wzwxt`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '形态',
    `wzwxtqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '形态其他',
    `wzwtlwgjq`    varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '头颅五官九窍',
    `wzwtlwgjqqt`  varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '头颅五官九窍其他',
    `wzwpf`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '皮肤',
    `wzwpfqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '皮肤其他',
    `wzwlm`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '络脉',
    `wzwlmqt`      varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '络脉其他',
    `wzwpxwyfmw`   varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '排泄物与分泌物',
    `wzwpxwyfmwqt` varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '排泄物与分泌物其他',
    `wzqt1`        varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '其他',
    `status`       varchar(1) COLLATE utf8mb4_general_ci   NULL COMMENT '状态【0:可用，1:禁用】',
    `create_date`  datetime                                NULL COMMENT '创建时间',
    `create_user`  varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '创建人',
    `update_date`  datetime                                NULL COMMENT '修改时间',
    `update_user`  varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '修改人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_rec_id` (`rec_id`),
    UNIQUE KEY `idx_tag_no` (`tag_no`)
) ENGINE = InnoDB
  DEFAULT CHARSET = 'utf8mb4'
  COLLATE = 'utf8mb4_general_ci' COMMENT ='望诊信息表';


CREATE TABLE cbkj_web_api.`t_record2_mutex`
(
    `id`             bigint(11)                             NOT NULL auto_increment COMMENT 'id',
    `dis_id`         varchar(32) COLLATE utf8mb4_general_ci NULL COMMENT '中医疾病Id',
    `dis_name`       varchar(32) COLLATE utf8mb4_general_ci NULL COMMENT '中医疾病名称',
    `parent_id`      varchar(32) COLLATE utf8mb4_general_ci NULL COMMENT '父ID',
    `mutex_name`     varchar(32) COLLATE utf8mb4_general_ci NULL COMMENT '互斥症状名称',
    `child_code`     varchar(32) COLLATE utf8mb4_general_ci NULL COMMENT '子代码',
    `mutex_sub_data` varchar(32) COLLATE utf8mb4_general_ci NULL COMMENT '互斥子数据',
    `status`         varchar(1) COLLATE utf8mb4_general_ci  NULL COMMENT '状态【0:可用，1:禁用】',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = 'utf8mb4'
  COLLATE = 'utf8mb4_general_ci' COMMENT ='疾病症状互斥信息表';

insert into cbkj_web_api.`t_record2_mutex` (`id`, `dis_id`, `dis_name`, `parent_id`, `mutex_name`, `child_code`, `mutex_sub_data`, `status`) values('1','2b9d00dc4a8c11eb8b470242ac110002','不寐','CVX-WZ-SMDM','睡眠','1','正常','0');
insert into cbkj_web_api.`t_record2_mutex` (`id`, `dis_id`, `dis_name`, `parent_id`, `mutex_name`, `child_code`, `mutex_sub_data`, `status`) values('2','2b9cd0a44a8c11eb8b470242ac110002','便秘','CVX-WZ-DBDM','大便','1','正常','0');


CREATE TABLE cbkj_web_api.`t_record2_smell`
(
    `id`          int(11) unsigned                        NOT NULL auto_increment COMMENT 'id',
    `rec_id`      varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '病历编号',
    `tag_no`      varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '标签编号',
    `wztsy`       varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '声音',
    `wztsyqt`     varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '声音其他',
    `wzxqw`       varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '嗅气味',
    `wzxqwqt`     varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '嗅气味其他',
    `wzqt2`       varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '其他',
    `status`      varchar(1) COLLATE utf8mb4_general_ci   NULL COMMENT '状态【0:可用，1:禁用】',
    `create_date` datetime                                NULL COMMENT '创建时间',
    `create_user` varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '创建人',
    `update_date` datetime                                NULL COMMENT '修改时间',
    `update_user` varchar(32) COLLATE utf8mb4_general_ci  NULL COMMENT '修改人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_rec_id` (`rec_id`),
    UNIQUE KEY `idx_tag_no` (`tag_no`)
) ENGINE = InnoDB
  DEFAULT CHARSET = 'utf8mb4'
  COLLATE = 'utf8mb4_general_ci' COMMENT ='闻诊信息表';


CREATE TABLE cbkj_web_api.`t_record2_tag`
(
    `tag_no`          varchar(32) NOT NULL COMMENT '标签编号',
    `tag_name`        varchar(32)  DEFAULT NULL COMMENT '标签名称',
    `tag_py`          varchar(32)  DEFAULT NULL COMMENT '标签首字母拼音',
    `doctor_id`       varchar(32)  DEFAULT NULL COMMENT '医生工号',
    `doctor_name`     varchar(32)  DEFAULT NULL COMMENT '医生名称',
    `app_id`          varchar(32)  DEFAULT NULL COMMENT '医共体id',
    `ins_id`          varchar(32)  DEFAULT NULL COMMENT '医疗机构编号',
    `patient_content` text COMMENT '主诉',
    `now_desc`        text COMMENT '现病史',
    `past_desc`       text COMMENT '既往史',
    `physical`        text COMMENT '体格检查',
    `auxiliary_exam`  text COMMENT '辅助检查',
    `patient_height`  varchar(5)   DEFAULT NULL COMMENT '身高',
    `patient_weight`  varchar(5)   DEFAULT NULL COMMENT '体重',
    `xyzdbzbm`        varchar(255) DEFAULT NULL COMMENT '西医诊断标准编码',
    `xyzdbzmc`        varchar(255) DEFAULT NULL COMMENT '西医诊断标准名称',
    `zybmdm`          varchar(255) DEFAULT NULL COMMENT '中医病名代码',
    `zybmmc`          varchar(255) DEFAULT NULL COMMENT '中医病名名称',
    `zyzhdm`          varchar(255) DEFAULT NULL COMMENT '中医证候代码',
    `zyzhmc`          varchar(255) DEFAULT NULL COMMENT '中医证候名称',
    `zzzfdm`          varchar(255) DEFAULT NULL COMMENT '治则治法代码',
    `zzzfmc`          varchar(255) DEFAULT NULL COMMENT '治则治法名称',
    `status`          varchar(1)   DEFAULT NULL COMMENT '状态【0:可用，1:未保存，2:禁用】',
    `create_date`     datetime     DEFAULT NULL COMMENT '创建时间',
    `create_user`     varchar(32)  DEFAULT NULL COMMENT '创建人',
    `update_date`     datetime     DEFAULT NULL COMMENT '修改时间',
    `update_user`     varchar(32)  DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`tag_no`),
    UNIQUE KEY `uk_tag_no` (`tag_no`)
) ENGINE = InnoDB
  DEFAULT CHARSET = 'utf8mb4'
  COLLATE = 'utf8mb4_general_ci' COMMENT ='标签信息表';


#综合平台 B101 增加参数选项
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
    SELECT t.PAR_ID,'4','中医处方一件事',4 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.`par_number`='B101'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='4' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='中医处方一件事'
    );

-- 增加菜单：中医处方一件事
insert into cbkj_web_parameter.sys_admin_menu ( `menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`)
values ( '0002111', '中医处方一件事', '/diagnosis/questionnaire', NULL, '0', '00024', '2020-12-10 09:21:54', '70810c874405453b99c6c2cf72296fe5', '2', NULL, NULL, NULL, '1', '2', '1', '2');
UPDATE cbkj_web_parameter.sys_admin_menu SET STATUS='1' WHERE menu_id='0002214';


-- 增加处方表 患者类型字段
ALTER TABLE cbkj_web_api.`t_prescription`
    ADD COLUMN `patient_types` VARCHAR (32) NULL COMMENT '患者类型(0自费1普通医保2医保离休3职工特慢J居民特慢)';
ALTER TABLE cbkj_web_api.`t_prescription`
    ADD COLUMN `patient_types_name` VARCHAR (64) NULL COMMENT '患者类型名称';


-- 1、参数D002病历打印按钮是否显示：新增“智能开方”参数值；
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
    SELECT t.PAR_ID,'3','智能开方',3 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.par_code='PRINT_RECORD_SHOW'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='3' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='智能开方'
    );

UPDATE cbkj_web_parameter.t_sys_param_init_desc,cbkj_web_parameter.t_sys_param
SET t_sys_param_init_desc.`param_init_code`='2'
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.PAR_CODE='PRINT_RECORD_SHOW'
  AND t_sys_param_init_desc.param_init_name='中医电子病历';

UPDATE cbkj_web_parameter.t_sys_param_init_desc,cbkj_web_parameter.t_sys_param
SET t_sys_param_init_desc.`param_init_code`='1'
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.PAR_CODE='PRINT_RECORD_SHOW'
  AND t_sys_param_init_desc.param_init_name='今日病人/我的历史病历/病历管理-查看订单';

UPDATE cbkj_web_parameter.t_sys_param SET PAR_VALUES='2'
    WHERE PAR_VALUES='1' AND t_sys_param.PAR_CODE='PRINT_RECORD_SHOW';
UPDATE cbkj_web_parameter.t_sys_param SET PAR_VALUES='1'
    WHERE PAR_VALUES='2' AND t_sys_param.PAR_CODE='PRINT_RECORD_SHOW';
UPDATE cbkj_web_parameter.t_sys_param SET PAR_VALUES='1,2'
    WHERE PAR_VALUES='1,1' AND t_sys_param.PAR_CODE='PRINT_RECORD_SHOW';

-- 2、参数G002-处方打印按钮是否显示：新增“智能开方”参数值；
UPDATE cbkj_web_parameter.t_sys_param SET param_type='3'
    WHERE PAR_CODE='PRINT_PRESCRIPTION_SHOW';

UPDATE cbkj_web_parameter.t_sys_param_init_desc,cbkj_web_parameter.t_sys_param
    SET t_sys_param_init_desc.param_init_name='今日病人/我的历史病历/病历管理/历史处方'
    WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id`
    AND t_sys_param.PAR_CODE='PRINT_PRESCRIPTION_SHOW'
    AND t_sys_param_init_desc.param_init_name='显示';

UPDATE cbkj_web_parameter.t_sys_param_init_desc,cbkj_web_parameter.t_sys_param
    SET t_sys_param_init_desc.param_init_name='智能开方',t_sys_param_init_desc.`param_init_code`='3'
    WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id`
    AND t_sys_param.PAR_CODE='PRINT_PRESCRIPTION_SHOW'
    AND t_sys_param_init_desc.param_init_name='不显示';


ALTER TABLE `cbkj_web_api`.`t_prescription_examine` ADD INDEX `IDX_PRE_ID` (`PRE_ID`);
