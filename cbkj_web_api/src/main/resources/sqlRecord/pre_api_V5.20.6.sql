ALTER TABLE cbkj_web_api.t_prescription
    CHANGE `PRE_ORIGIN` `PRE_ORIGIN` VARCHAR(2) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' NULL COMMENT '处方来源(0空白方 1智能辩证 2智能推方 3方剂搜索 4协定方转方 5院内方转方 6我的验案转方 7名家验案转方 8历史病历转方 9国医大师 10配方 11传承经方 12膏方';

#新增参数B510 搜索(配方|膏方)处方配置
INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES,
 CREATE_DATE, CREATE_USER, create_user_name, `STATUS`, param_type, is_global, param_init_value, sort, menu_id,
 param_desc, par_number)
SELECT REPLACE(UUID(), '-', ''),
       '000000',
       '000000',
       '000000',
       'PRESCRIPTION_SEARCH_RECIPE_OPTIONS_2',
       '搜索(配方|膏方)处方配置',
       '',
       NOW(),
       'admin',
       'admin',
       '0',
       '41',
       '0',
       '',
       61,
       '1024',
       '搜索(配方|膏方)处方配置',
       'B510'
FROM DUAL
WHERE NOT EXISTS(
        SELECT APP_ID, INS_CODE, DEPT_ID, PAR_CODE
        FROM cbkj_web_parameter.t_sys_param
        WHERE APP_ID = '000000'
          AND INS_CODE = '000000'
          AND DEPT_ID = '000000'
          AND PAR_CODE = 'PRESCRIPTION_SEARCH_RECIPE_OPTIONS_2'
    );
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`(param_id, param_init_code, param_init_name, sort)
SELECT t.PAR_ID, 'formula', '中药配方', 1
FROM (
         SELECT tsp.PAR_ID
         FROM cbkj_web_parameter.`t_sys_param` AS tsp
         WHERE tsp.PAR_CODE = 'PRESCRIPTION_SEARCH_RECIPE_OPTIONS_2'
     ) AS t
WHERE NOT EXISTS(
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code = 'formula'
          AND tsp.param_id = t.PAR_ID
          AND tsp.param_init_name = '中药配方'
    );
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`(param_id, param_init_code, param_init_name, sort)
SELECT t.PAR_ID, 'unguent', '膏方', 2
FROM (
         SELECT tsp.PAR_ID
         FROM cbkj_web_parameter.`t_sys_param` AS tsp
         WHERE tsp.PAR_CODE = 'PRESCRIPTION_SEARCH_RECIPE_OPTIONS_2'
     ) AS t
WHERE NOT EXISTS(
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code = 'unguent'
          AND tsp.param_id = t.PAR_ID
          AND tsp.param_init_name = '膏方'
    );

#更新选项值
UPDATE cbkj_web_parameter.t_sys_param AS t1
    INNER JOIN (
        SELECT REPLACE(PAR_VALUES, '|formula', '') AS PAR_VALUES
        FROM cbkj_web_parameter.t_sys_param
        WHERE PAR_VALUES LIKE '%formula%'
          AND PAR_CODE = 'PRESCRIPTION_SEARCH_RECIPE_OPTIONS'
    ) AS t2
SET t1.`PAR_VALUES`=t2.PAR_VALUES
WHERE t1.PAR_VALUES LIKE '%formula%'
  AND t1.PAR_CODE = 'PRESCRIPTION_SEARCH_RECIPE_OPTIONS';

#更改B504参数名称
UPDATE cbkj_web_parameter.t_sys_param
SET PAR_NAME='搜索(协定方|方剂)处方配置'
WHERE PAR_CODE = 'PRESCRIPTION_SEARCH_RECIPE_OPTIONS';
#删除B504参数配方选项
DELETE FROM cbkj_web_parameter.t_sys_param_init_desc
WHERE t_sys_param_init_desc.`param_id`
    IN (SELECT par_id
        FROM cbkj_web_parameter.t_sys_param
        WHERE t_sys_param.par_code = 'PRESCRIPTION_SEARCH_RECIPE_OPTIONS')
  AND t_sys_param_init_desc.`param_init_code` = 'formula';