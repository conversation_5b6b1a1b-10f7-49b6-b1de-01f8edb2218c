ALTER TABLE `cbkj_web_api`.`t_record2_tag`
    ADD COLUMN `tag_type` VARCHAR (1) NULL COMMENT '1.成人2.妇女3.小儿';

ALTER TABLE `cbkj_web_api`.`t_record2_tag`
    ADD COLUMN `version` VARCHAR (32) NULL COMMENT '病历保存版本';


ALTER TABLE `cbkj_web_api`.`t_record`
    ADD COLUMN `VERSION` VARCHAR (32) NULL COMMENT '病历保存版本';


ALTER TABLE `cbkj_web_api`.`t_record2_look`
    ADD COLUMN `wzwsdm` VARCHAR (255) NULL COMMENT '神代码' ,
    ADD COLUMN `wzwmsdm` VARCHAR (255) NULL COMMENT '面代码' ,
    ADD COLUMN `wzwxtdm` VARCHAR (255) NULL COMMENT '形态代码' ,
    ADD COLUMN `wzwtlwgjqdm` VARCHAR (1000) NULL COMMENT '头颅五官九窍代码' ,
    ADD COLUMN `wzwpfdm` VARCHAR (255) NULL COMMENT '皮肤代码' ,
    ADD COLUMN `wzwlmdm` VARCHAR (255) NULL COMMENT '络脉代码' ,
    ADD COLUMN `wzwpxwyfmwdm` VARCHAR (255) NULL COMMENT '排泄物与分泌物代码' ,
    ADD COLUMN `wzwxexfqt` VARCHAR (255) NULL COMMENT '望小儿胸腹其他',
    ADD COLUMN `wzwxexfdm` VARCHAR (255) NULL COMMENT '望小儿胸腹代码' ,
    ADD COLUMN `wzwxexf` VARCHAR (255) NULL COMMENT '望小儿胸腹'
;



ALTER TABLE `cbkj_web_api`.`t_record2_smell`
    ADD COLUMN `wztsydm` VARCHAR (255) NULL COMMENT '声音代码' AFTER `wztsy`,
    ADD COLUMN `wzxqwdm` VARBINARY (255) NULL COMMENT '嗅气味代码' AFTER `wzxqw`;



ALTER TABLE `cbkj_web_api`.`t_record2_ask`
    ADD COLUMN `wzhrdm` VARCHAR (255) NULL COMMENT '寒热代码' AFTER `wzhr`,
    ADD COLUMN `wzchdm` VARCHAR (255) NULL COMMENT '出汗代码' AFTER `wzch`,
    ADD COLUMN `wztsdm` VARCHAR (255) NULL COMMENT '头身代码' AFTER `wzts`,
    ADD COLUMN `wzxxwfdm` VARCHAR (255) NULL COMMENT '胸胁脘腹代码' AFTER `wzxxwf`,
    ADD COLUMN `wzemdm` VARCHAR (255) NULL COMMENT '耳目代码' AFTER `wzem`,
    ADD COLUMN `wzxbdm` VARCHAR (255) NULL COMMENT '小便代码' AFTER `wzxb`,
    ADD COLUMN `wzfndm` VARCHAR (255) NULL COMMENT '妇女代码' AFTER `wzfn`,
    ADD COLUMN `wzxedm` VARCHAR (255) NULL COMMENT '小儿代码' AFTER `wzxe`;


ALTER TABLE `cbkj_web_api`.`t_record2_cut`
    ADD COLUMN `qzazdm` VARCHAR (255) NULL COMMENT '按诊代码' AFTER `qzaz`;



ALTER TABLE `cbkj_web_api`.`t_record2_look`
    CHANGE `wzwtlwgjq` `wzwtlwgjq` VARCHAR (1000) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '头颅五官九窍';

DELETE FROM t_record2_mutex WHERE id IN('1','2');

insert into cbkj_web_api.`t_record2_mutex` (`id`, `dis_id`, `dis_name`, `parent_id`, `mutex_name`, `child_code`, `mutex_sub_data`, `status`) values('1','2b9d00dc4a8c11eb8b470242ac110002','不寐','CVX-WZ-WSMDM','睡眠','3.13.01.01','正常','0');
insert into cbkj_web_api.`t_record2_mutex` (`id`, `dis_id`, `dis_name`, `parent_id`, `mutex_name`, `child_code`, `mutex_sub_data`, `status`) values('2','2b9cd0a44a8c11eb8b470242ac110002','便秘','CVX-WZ-WDBDM','大便','3.15.01.01','正常','0');




INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
VALUES (REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'TREATMENT_IS_MUST', '中医治法必填', '0', NOW(),
        'admin', 'admin', '0', '1', '0', '0', '12', '1011', '用于控制智能开方、处方一件事病历治法是否必填', 'B110');

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`(param_id, param_init_code, param_init_name, sort)
SELECT tsp.PAR_ID, '1', '是', 1
FROM cbkj_web_parameter.`t_sys_param` AS tsp
WHERE tsp.PAR_CODE = 'TREATMENT_IS_MUST';
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`(param_id, param_init_code, param_init_name, sort)
SELECT tsp.PAR_ID, '0', '否', 2
FROM cbkj_web_parameter.`t_sys_param` AS tsp
WHERE tsp.PAR_CODE = 'TREATMENT_IS_MUST';

UPDATE
    `cbkj_web_parameter`.`sys_admin_menu`
SET
    `menu_name` = '处方一件事病历'
WHERE `menu_path` = '/diagnosis/questionnaire';










