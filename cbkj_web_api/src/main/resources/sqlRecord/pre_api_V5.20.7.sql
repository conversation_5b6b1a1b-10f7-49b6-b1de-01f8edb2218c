#KOO2参数修改
UPDATE cbkj_web_parameter.t_sys_param SET param_type='3' WHERE PAR_CODE='PRE_INTERFACE';

UPDATE cbkj_web_parameter.t_sys_param_init_desc, cbkj_web_parameter.t_sys_param
    SET t_sys_param_init_desc.param_init_name='审核通过(推方至HIS)', t_sys_param_init_desc.`param_init_code`='push'
    WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_CODE`='PRE_INTERFACE' AND t_sys_param_init_desc.`param_init_code`='1';

UPDATE cbkj_web_parameter.t_sys_param_init_desc, cbkj_web_parameter.t_sys_param
    SET t_sys_param_init_desc.param_init_name='处方作废', t_sys_param_init_desc.`param_init_code`='del'
    WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_CODE`='PRE_INTERFACE' AND t_sys_param_init_desc.`param_init_code`='0';

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
    SELECT t.PAR_ID,'pay','处方支付(演示)',3 FROM (
                                          SELECT tsp.PAR_ID
                                          FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                          WHERE tsp.PAR_CODE='PRE_INTERFACE'
                                      ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='2' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='处方支付(演示)'
    );

update cbkj_web_parameter.t_sys_param set PAR_VALUES = 'push,del' where PAR_VALUES = '1' and PAR_CODE='PRE_INTERFACE';
update cbkj_web_parameter.t_sys_param set PAR_VALUES = '' where PAR_VALUES = '0' and PAR_CODE='PRE_INTERFACE';

#B243开启物流配送查询
INSERT INTO cbkj_web_parameter.t_sys_param (PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,PAR_VALUES,CREATE_DATE,CREATE_USER,create_user_name, `STATUS`,param_type,is_global,param_init_value,sort,menu_id,param_desc,par_number)
    SELECT
        REPLACE(UUID(),'-',''),'000000','000000','000000',
        'LOGISTICS_DISTRIBUTION_QUERY','开启物流配送查询','',
        NOW(),'admin','admin',
        '0','1','0','',24,
        '1004','订单信息，物流配送状态，物流单号后显示','B243'
    FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'LOGISTICS_DISTRIBUTION_QUERY'
    );
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
    SELECT t.PAR_ID,'1','开启',1 FROM (
                                    SELECT tsp.PAR_ID
                                    FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                    WHERE tsp.PAR_CODE='LOGISTICS_DISTRIBUTION_QUERY'
                                ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开启'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`(param_id,param_init_code,param_init_name,sort)
    SELECT t.PAR_ID,'0','关闭',2 FROM (
                                    SELECT tsp.PAR_ID
                                    FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                    WHERE tsp.PAR_CODE='LOGISTICS_DISTRIBUTION_QUERY'
                                ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关闭'
    );

#B244开启物流配送查询
INSERT INTO cbkj_web_parameter.t_sys_param (PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,PAR_VALUES,CREATE_DATE,CREATE_USER,create_user_name, STATUS,param_type,is_global,param_init_value,sort,menu_id,param_desc,par_number)
    SELECT
        REPLACE(UUID(),'-',''),'000000','000000','000000',
        'GET_PATIENT_ADDRESS','开启获取患者浙里办登记地址按钮','',
        NOW(),'admin','admin',
        '0','1','0','',25,
        '1004','受药房配送开关限制，若药房开关未开启配送，则此参数开启无效','B244'
    FROM DUAL WHERE  NOT EXISTS (
            SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'GET_PATIENT_ADDRESS'
        );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`(param_id,param_init_code,param_init_name,sort)
    SELECT t.PAR_ID,'1','开启',1 FROM (
                                        SELECT tsp.PAR_ID
                                        FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                        WHERE tsp.PAR_CODE='GET_PATIENT_ADDRESS'
                                    ) AS t WHERE NOT EXISTS (
            SELECT 1
            FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
            WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开启'
        );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`(param_id,param_init_code,param_init_name,sort)
    SELECT t.PAR_ID,'0','关闭',2 FROM (
                                    SELECT tsp.PAR_ID
                                    FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                    WHERE tsp.PAR_CODE='GET_PATIENT_ADDRESS'
                                ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关闭'
    );



-- 查询速度优化
ALTER TABLE `cbkj_web_api`.`t_record` ADD INDEX `IDX_REGISTER_ID` (`REGISTER_ID`);
ALTER TABLE `cbkj_web_api`.`b_mapping_disease` ADD INDEX `IDX_DIS_HIS` (`DIS_CODE_HIS`, `DIS_NAME_HIS`);
ALTER TABLE `cbkj_web_api`.`b_mapping_symptom` ADD INDEX `IDX_SYM_HIS` (`SYM_CODE_HIS`, `SYM_NAME_HIS`);

-- 对getBigRecord大病历查询sql优化。
ALTER TABLE cbkj_web_api.t_record ADD INDEX (`REC_FIRSTID`);
