UPDATE
    cbkj_web_parameter.`t_sys_param`
SET
    `PAR_NAME` = '医保限制第一次保存时提醒内容'
WHERE `PAR_CODE` = 'INSURANCE_LIMIT_TIP';


DELETE FROM  cbkj_web_parameter.`t_sys_param_init_desc` WHERE param_id IN(SELECT PAR_ID FROM cbkj_web_parameter.t_sys_param WHERE PAR_CODE='INSURANCE_LIMIT_OBJECT');
DELETE FROM cbkj_web_parameter.t_sys_param WHERE PAR_CODE='INSURANCE_LIMIT_OBJECT';

INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'INSURANCE_LIMIT_OBJECT', '医保提醒患者范围控制', '1,3', NOW(),
    'admin', 'admin', '0', '3', '0', '1,3', '96', '1401', '1.门诊医保、2.门诊自费、3.住院医保、4.住院自费、5.门诊特病', 'F009'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'INSURANCE_LIMIT_OBJECT'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '门诊医保', 1 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='INSURANCE_LIMIT_OBJECT'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='门诊医保'
    );
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '2', '门诊自费', 2 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INSURANCE_LIMIT_OBJECT'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='2' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='门诊医保'
    );
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '3', '住院医保', 3 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INSURANCE_LIMIT_OBJECT'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='3' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='住院医保'
    );
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '4', '住院自费', 4 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INSURANCE_LIMIT_OBJECT'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='4' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='住院自费'
    );
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '5', '门诊特病', 5 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INSURANCE_LIMIT_OBJECT'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='5' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='门诊特病'
    );


INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'HISTORY_PRESCRIPTION_PAY', '是否智能云开启模拟收退费', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '0', '38', '1009', '是否智能云开启,模拟收退费1开0关', 'G002'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'ANALOG_RECEIVING_REFUND'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关', 1 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='HISTORY_PRESCRIPTION_PAY'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开', 2 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='HISTORY_PRESCRIPTION_PAY'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开'
    );


INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '4', '处方退费（演示）', 4 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='PRE_INTERFACE'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='4' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='处方退费（演示）'
    );


INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'INSURANCE_LIMIT_REMIND', '医保限制开药时提醒', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '0', '101', '1401', '医保限制开药时提醒，1开0关', 'F014'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'INSURANCE_LIMIT_REMIND'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关', 1 FROM (
                                    SELECT tsp.PAR_ID
                                    FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                    WHERE tsp.PAR_CODE='INSURANCE_LIMIT_REMIND'
                                ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开', 2 FROM (
                                        SELECT tsp.PAR_ID
                                        FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                        WHERE tsp.PAR_CODE='INSURANCE_LIMIT_REMIND'
                                    ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开'
    );



#增加药品明细表-医保说明-医保强制提醒
ALTER TABLE cbkj_web_parameter.`t_material_price`
    ADD COLUMN `xzsm` VARCHAR (128) NULL COMMENT '医保说明';

insert into `cbkj_web_parameter`.`sys_admin_menu` (
    `menu_id`,
    `menu_name`,
    `menu_path`,
    `menu_class`,
    `status`,
    `parent_menu_id`,
    `create_date`,
    `cteate_user`,
    `menu_type`,
    `btn_class`,
    `btn_type`,
    `btn_weight`,
    `sort`,
    `menu_level`,
    `open_type`,
    `modual_code`
)
values
    (
        '0002314',
        '监管看板',
        '/statistical/supervise-board',
        NULL,
        '0',
        '000214',
        '2021-07-21 11:22:20',
        '70810c874405453b99c6c2cf72296fe5',
        '2',
        NULL,
        NULL,
        NULL,
        '-1',
        '2',
        '1',
        '2'
    );
UPDATE `cbkj_web_parameter`.`t_sys_param` SET `par_number` = 'B601' WHERE `PAR_CODE` = 'PRINT_PRESCRIPTION_SHOW';
UPDATE `cbkj_web_parameter`.`t_sys_param` SET `par_number` = 'B602' WHERE `PAR_CODE` = 'PRINT_PRESCRIPTION_SHOW_QRCODE';



#增加表
CREATE TABLE cbkj_web_api.`t_statistics_health_care` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                            `app_id` varchar(32) DEFAULT NULL,
                                            `app_name` varchar(32) DEFAULT NULL,
                                            `ins_code` varchar(32) DEFAULT NULL,
                                            `ins_name` varchar(32) DEFAULT NULL,
                                            `dept_id` varchar(32) DEFAULT NULL,
                                            `create_date` date DEFAULT NULL COMMENT '统计日期',
                                            `insert_date` datetime DEFAULT NULL COMMENT '插入时间',
                                            `ypdm_his` varchar(32) DEFAULT NULL,
                                            `remind_times` int(11) DEFAULT NULL COMMENT '医保控费提醒 提醒次数',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;

CREATE TABLE cbkj_web_api.`t_statistics_prescription_audit` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                                   `app_id` varchar(32) DEFAULT NULL,
                                                   `app_name` varchar(32) DEFAULT NULL,
                                                   `ins_code` varchar(32) DEFAULT NULL,
                                                   `ins_name` varchar(32) DEFAULT NULL,
                                                   `dept_id` varchar(32) DEFAULT NULL,
                                                   `create_date` date DEFAULT NULL COMMENT '统计日期',
                                                   `insert_date` datetime DEFAULT NULL COMMENT '插入时间',
                                                   `tcm_all_num` int(11) DEFAULT NULL COMMENT '中药审方数量：系统审方+人工审方之和，统计所有状态处方；',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

CREATE TABLE cbkj_web_api.`t_statistics_prescription_medication` (
                                                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键自增长',
                                                        `app_id` varchar(32) DEFAULT NULL,
                                                        `app_name` varchar(32) DEFAULT NULL,
                                                        `ins_code` varchar(32) DEFAULT NULL,
                                                        `ins_name` varchar(32) DEFAULT NULL,
                                                        `dept_id` varchar(32) DEFAULT NULL,
                                                        `create_date` date DEFAULT NULL COMMENT '统计日期',
                                                        `insert_date` datetime DEFAULT NULL COMMENT '插入时间',
                                                        `number` int(11) DEFAULT NULL COMMENT '安全用药提醒次数',
                                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;

CREATE TABLE cbkj_web_api.`t_statistics_prescription_type` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                                  `app_id` varchar(32) DEFAULT NULL,
                                                  `app_name` varchar(32) DEFAULT NULL,
                                                  `ins_code` varchar(32) DEFAULT NULL,
                                                  `ins_name` varchar(32) DEFAULT NULL,
                                                  `dept_id` varchar(32) DEFAULT NULL,
                                                  `create_date` date DEFAULT NULL COMMENT '统计日期',
                                                  `insert_date` datetime DEFAULT NULL COMMENT '插入时间',
                                                  `number` int(11) DEFAULT NULL,
                                                  `num_type` int(11) DEFAULT NULL COMMENT '1.煎煮中处方:【实时】处方状态为“85泡药、130煎药、135包装”的处方数量之和;2.已签收处方:已签收处方【实时】处方状态为“150收货”的处方数量之和',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



ALTER TABLE cbkj_web_api.`t_statistics_dis_num` ADD COLUMN `REC_TRE_TYPE` VARCHAR (1) NULL COMMENT '病历类型（1门诊 2住院）' ;
#增加索引
ALTER TABLE cbkj_web_api.t_order_status ADD KEY (`OPERATION_TYPE`);
ALTER TABLE cbkj_web_api.t_statistics_dis_num CHANGE `time_type` `time_type` INT (1) NOT NULL COMMENT '时间类型:1昨天,2近半月,3近一个季度,4近一年 5 近半年';
ALTER TABLE `cbkj_web_api`.`t_order_status` ADD COLUMN `IS_DEL` INT NULL COMMENT '0正常1删除（药房pdf手持）' AFTER `CREATE_TIME`;

