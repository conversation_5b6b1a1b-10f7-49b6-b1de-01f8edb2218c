ALTER TABLE t_patients
    CHANGE `PATIENT_NAME` `PATIENT_NAME` VARCHAR(500) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
    CHANGE `PATIENT_GENDER` `PATIENT_GENDER` VARCHAR(250) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '性别（m 男  f 女）',
    CHANGE `PATIENT_MOBILE` `PATIENT_MOBILE` VARCHAR(250) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '手机号',
    CHANGE `PATIENT_COUNTY` `PATIENT_COUNTY` VARCHAR(250) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '省',
    CHANGE `PATIENT_TOWN` `PATIENT_TOWN` VARCHAR(250) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '市',
    CHANGE `PATIENT_VILLAGE` `PATIENT_VILLAGE` VARCHAR(250) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '区',
    CHANGE `PATIENT_STREET` `PATIENT_STREET` VARCHAR(250) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '街道',
    CHANGE `PATIENT_ADDRESS` `PATIENT_ADDRESS` TEXT CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '地址';
UPDATE t_patients AS a SET PATIENT_NAME = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`PATIENT_NAME`, 'S@T#K$J')),'TWGDH@BTZHY$'));
UPDATE t_patients AS a SET PATIENT_GENDER = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`PATIENT_GENDER`, 'S@T#K$J')),'TWGDH@BTZHY$'));
UPDATE t_patients AS a SET PATIENT_MOBILE = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`PATIENT_MOBILE`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));
UPDATE t_patients AS a SET PATIENT_CERTIFICATE = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`PATIENT_CERTIFICATE`, 'S@T#K$J')),'TWGDH@BTZHY$'));
UPDATE t_patients AS a SET PATIENT_COUNTY = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`PATIENT_COUNTY`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));
UPDATE t_patients AS a SET PATIENT_TOWN = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`PATIENT_TOWN`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE t_patients AS a SET PATIENT_VILLAGE = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`PATIENT_VILLAGE`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE t_patients AS a SET
    PATIENT_STREET = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`PATIENT_STREET`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE t_patients AS a SET
    PATIENT_ADDRESS = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`PATIENT_ADDRESS`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));




ALTER TABLE t_dc_address
    CHANGE `DC_NAME` `DC_NAME` VARCHAR(500) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '收货人',
    CHANGE `DC_ADDRESS` `DC_ADDRESS` TEXT CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '收货人详细地址',
    CHANGE `DC_COUNTY` `DC_COUNTY` VARCHAR(500) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '省',
    CHANGE `DC_TOWN` `DC_TOWN` VARCHAR(500) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '市',
    CHANGE `DC_VILLAGE` `DC_VILLAGE` VARCHAR(500) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '区',
    CHANGE `DC_MOBILE` `DC_MOBILE` VARCHAR(1000) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '收货人手机号',
    CHANGE `DC_STREET` `DC_STREET` VARCHAR(500) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '街道';

ALTER TABLE `t_record`
    CHANGE `REC_ADDRESS` `REC_ADDRESS` TEXT CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '住址';



UPDATE t_dc_address AS a SET
    DC_NAME = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`DC_NAME`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE t_dc_address AS a SET
    DC_ADDRESS = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`DC_ADDRESS`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE t_dc_address AS a SET
    DC_MOBILE = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`DC_MOBILE`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE t_dc_address AS a SET
    DC_STREET = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`DC_STREET`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE t_dc_address AS a SET
    DC_VILLAGE = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`DC_VILLAGE`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE t_dc_address AS a SET
    DC_TOWN = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`DC_TOWN`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE t_dc_address AS a SET
    DC_COUNTY = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`DC_COUNTY`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));




UPDATE t_record AS a SET
    REC_ADDRESS = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`REC_ADDRESS`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));