

USE `cbkj_web_api`;

CREATE TABLE `zkxc_chi_cun_guan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `collected_data` tinyint(1) DEFAULT NULL,
  `jie_lv` float DEFAULT NULL COMMENT '对应脉象节律值',
  `jie_lv_standard` varchar(32) DEFAULT NULL COMMENT '节律标准范围（0-0.15）',
  `jin_zhang_du` float DEFAULT NULL COMMENT '对应脉象紧张度值',
  `jin_zhang_du_standard` varchar(32) DEFAULT NULL COMMENT '紧张度标准范围（<0.7）',
  `liu_li_du` float DEFAULT NULL COMMENT '对应脉象流利度值',
  `liu_li_du_standard` varchar(32) DEFAULT NULL COMMENT '流利度标准范围（<0.6）',
  `mai_li` float DEFAULT NULL COMMENT '对应脉象脉率值',
  `mai_li_deviation` float DEFAULT NULL COMMENT '脉率标准范围（60-100）',
  `mai_li_down_line_value` float DEFAULT NULL COMMENT '-25.0（取值范围-100~100）',
  `mai_li_standard` varchar(32) DEFAULT NULL COMMENT '脉⼒标准范围（16-25）',
  `mai_li_up_line_value` float DEFAULT NULL COMMENT '25.0（取值范围-100~100）',
  `mai_lv` float DEFAULT NULL COMMENT '对应脉象脉率值',
  `mai_lv_deviation` float DEFAULT NULL COMMENT '脉率偏差值',
  `mai_lv_down_line_value` float DEFAULT NULL COMMENT '-25.0（取值范围-100~100）',
  `mai_lv_standard` varchar(32) DEFAULT NULL COMMENT '脉率标准范围（60-100）',
  `mai_lv_up_line_value` float DEFAULT NULL COMMENT '25.0（取值范围-100~100）',
  `mai_wei` float DEFAULT NULL COMMENT '对应脉象脉位值',
  `mai_wei_deviation` float DEFAULT NULL COMMENT '脉位偏差值',
  `mai_wei_down_line_value` float DEFAULT NULL COMMENT '-27.27（取值范围-100~100）',
  `mai_wei_standard` varchar(32) DEFAULT NULL COMMENT '脉位标准范围（200-350）',
  `mai_wei_up_line_value` float DEFAULT NULL COMMENT '27.27（取值范围-100~100）',
  `opt_pulse1` text,
  `pulse_potential` varchar(32) DEFAULT NULL COMMENT '对应脉象特征描述',
  `vein_element_down_line_value` float DEFAULT NULL COMMENT '-0.7（取值范围-1~1）',
  `vein_element_up_line_value` float DEFAULT NULL COMMENT '0.6（取值范围-1~1）',
  `vein_elements` varchar(120) DEFAULT NULL COMMENT '脉象要素分析图（弦、滑、正常）',
  `mz_id` int(11) DEFAULT NULL COMMENT '脉整id',
  `mz_result_type` int(1) DEFAULT NULL COMMENT '1.左尺2.左寸3.左关4.右尺5.右寸6.右关',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=88 DEFAULT CHARSET=utf8mb4;



CREATE TABLE `zkxc_collectinfo` (
  `collect_id` int(11) NOT NULL AUTO_INCREMENT,
  `account_name` varchar(125) DEFAULT NULL COMMENT '医⽣账户名称',
  `age` int(11) DEFAULT NULL,
  `blood_pressure` varchar(32) DEFAULT NULL,
  `blood_sugar` varchar(32) DEFAULT NULL,
  `card_no` varchar(32) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `doctor_name` varchar(32) DEFAULT NULL,
  `height` varchar(32) DEFAULT NULL,
  `uuid` varchar(48) DEFAULT NULL,
  `mac` varchar(50) DEFAULT NULL,
  `org` varchar(128) DEFAULT NULL COMMENT '医⽣所属机构',
  `phone` varchar(50) DEFAULT NULL,
  `real_name` varchar(128) DEFAULT NULL COMMENT '患者姓名',
  `sex` varchar(1) DEFAULT NULL,
  `weight` varchar(10) DEFAULT NULL,
  `register_id` varchar(32) DEFAULT NULL,
  `patient_id` varchar(32) DEFAULT NULL,
  `pdf_path` varchar(150) DEFAULT NULL,
  `insert_time` datetime DEFAULT NULL COMMENT '系统插入时间-多条挂号id一样，取最新条',
  PRIMARY KEY (`collect_id`)
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4;



CREATE TABLE `zkxc_equipment` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `mac_address` varchar(48) NOT NULL COMMENT '设备mac地址',
  `equipment_name` varchar(200) NOT NULL COMMENT '设备名字',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `mac_address` (`mac_address`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `zkxc_equipment_ip` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `mac_address` varchar(48) NOT NULL,
  `computer_ip` bigint(11) NOT NULL COMMENT '转十位数字ip：INET_ATON(''ip'');转正常ipINET_NTOA('''')',
  `default_ip` int(1) NOT NULL DEFAULT '1' COMMENT '0默认1不是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;



CREATE TABLE `zkxc_equipment_patient` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `PATIENT_ID` varchar(32) NOT NULL COMMENT '患者id',
  `register_id` varchar(32) NOT NULL COMMENT '挂号id',
  `id_card` varchar(18) DEFAULT NULL,
  `check_time` date NOT NULL COMMENT '检查时间',
  `mac_address` varchar(48) NOT NULL COMMENT 'mac地址',
  `check_range` tinyint(1) NOT NULL COMMENT '（1上午 2下午）',
  `name` varchar(250) DEFAULT NULL,
  `sex` varchar(1) DEFAULT NULL COMMENT '0女1男',
  `age` int(11) DEFAULT NULL,
  `phone` varchar(250) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `zkxc_mapping` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `zkxc_name` varchar(255) NOT NULL,
  `zkxc_code` varchar(255) DEFAULT NULL,
  `map_type` int(11) NOT NULL COMMENT '映射对象1.一件事',
  `map_name` varchar(255) NOT NULL,
  `map_code` varchar(255) DEFAULT NULL,
  `map_other` varchar(255) DEFAULT NULL,
  `type_other` varchar(8) DEFAULT NULL COMMENT '舌、苔、脉',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `zkxc_mz` (
  `mz_id` int(11) NOT NULL AUTO_INCREMENT,
  `factor_enumeration` text COMMENT '因素枚举',
  `pulse_quantification_img` varchar(100) DEFAULT NULL COMMENT 'base64 图片',
  `symptom_tips` text COMMENT '症状提示',
  `collect_id` int(11) DEFAULT NULL,
  `register_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`mz_id`)
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4;



CREATE TABLE `zkxc_sz` (
  `sz_id` int(11) NOT NULL AUTO_INCREMENT,
  `crack_str` varchar(200) DEFAULT NULL COMMENT '其他特征',
  `fn_standard` varchar(100) DEFAULT NULL,
  `fn_index` double DEFAULT NULL COMMENT '腐腻指数',
  `fn_str` varchar(100) DEFAULT NULL COMMENT '腐腻特征',
  `hb_standard` varchar(100) DEFAULT NULL,
  `hb_index` double DEFAULT NULL COMMENT '厚薄指数',
  `hb_str` varchar(100) DEFAULT NULL COMMENT '厚薄特征，',
  `l_coat_str` varchar(100) DEFAULT NULL COMMENT '苔色',
  `ln_standard` varchar(100) DEFAULT NULL,
  `ln_index` double DEFAULT NULL COMMENT '舌嫩指数',
  `ln_str` varchar(100) DEFAULT NULL,
  `l_zhi_str` varchar(100) DEFAULT NULL COMMENT '舌尖',
  `ps_index` double DEFAULT NULL COMMENT '胖瘦指数',
  `ps_standard` varchar(100) DEFAULT NULL,
  `ps_str` varchar(100) DEFAULT NULL COMMENT '舌体胖瘦',
  `t_coat_standard` varchar(100) DEFAULT NULL,
  `t_coat_index` double DEFAULT NULL COMMENT '苔舌指数',
  `tongue_division_img` varchar(200) DEFAULT NULL COMMENT '舌象分割图',
  `tongue_original_img` varchar(200) DEFAULT NULL COMMENT '舌象原图',
  `t_zhi_standard` varchar(100) DEFAULT NULL,
  `t_zhi_index` varchar(100) DEFAULT NULL COMMENT '舌色指数',
  `collect_id` int(11) NOT NULL,
  `register_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`sz_id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4;




CREATE TABLE `zkxc_wz` (
  `wz_id` varchar(32) NOT NULL,
  `constitution` varchar(50) DEFAULT NULL,
  `constitution_question_type` varchar(1) DEFAULT NULL COMMENT '题⽬类型，1-完整版；2精简版',
  `nums` varchar(128) DEFAULT NULL,
  `option_result` varchar(128) DEFAULT NULL COMMENT '题⽬选项',
  `tendency` varchar(128) DEFAULT NULL COMMENT '体质结果（兼体质）',
  `acupoint_advice` varchar(128) DEFAULT NULL,
  `acupoint_id` varchar(128) DEFAULT NULL,
  `diet_advice` text,
  `exercise_advice` text COMMENT '运动调养',
  `expert_advice` text,
  `massage_advice` text,
  `medicine_advice` text COMMENT '中药建议',
  `mental_culture_advice` text COMMENT '情志调养',
  `paroxysm_tendency` text COMMENT '可能过敏信息',
  `possible_phenomen` text COMMENT '季节性可能',
  `season` varchar(2) DEFAULT NULL,
  `season_advice` text COMMENT '季节调养',
  `work_and_rest_advice` text COMMENT '起居调养',
  PRIMARY KEY (`wz_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

insert into cbkj_web_parameter.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `CREATE_DATE`, `CREATE_USER`, `create_user_name`, `status`, `param_type`, `is_global`, `param_init_value`, `sort`, `menu_id`, `modual_code`, `param_desc`, `parameter_diagram`, `par_number`) values('fc3c1a48b29011edad8d00163f006620','000000','000000','000000','ZKXC_DIAGNOSTIC','中科芯创四诊仪','','2023-02-22 17:11:30','admin','admin','0','3','0','','3','1701',NULL,'中科芯创四诊仪',NULL,'K003');

SELECT * FROM cbkj_web_parameter.`t_sys_param` WHERE `PAR_ID`='fc3c1a48b29011edad8d00163f006620';

insert into cbkj_web_parameter.`t_sys_param_init_desc` (`param_id`, `param_init_code`, `param_init_name`, `option_diagram`, `sort`) values('fc3c1a48b29011edad8d00163f006620','2','中医电子病历',NULL,'2');
insert into cbkj_web_parameter.`t_sys_param_init_desc` (`param_id`, `param_init_code`, `param_init_name`, `option_diagram`, `sort`) values('fc3c1a48b29011edad8d00163f006620','5','传承',NULL,'5');
insert into cbkj_web_parameter.`t_sys_param_init_desc` (`param_id`, `param_init_code`, `param_init_name`, `option_diagram`, `sort`) values('fc3c1a48b29011edad8d00163f006620','4','国医大师辨证',NULL,'4');
insert into cbkj_web_parameter.`t_sys_param_init_desc` (`param_id`, `param_init_code`, `param_init_name`, `option_diagram`, `sort`) values('fc3c1a48b29011edad8d00163f006620','1','处方一件事病历',NULL,'1');
insert into cbkj_web_parameter.`t_sys_param_init_desc` (`param_id`, `param_init_code`, `param_init_name`, `option_diagram`, `sort`) values('fc3c1a48b29011edad8d00163f006620','3','智能辨证',NULL,'3');


SELECT * FROM `zkxc_mapping` ;



insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('1','红',NULL,'1','红','1.17.03.03',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('2','淡白',NULL,'1','淡白','1.17.03.02',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('3','淡红',NULL,'1','淡红','1.17.03.01',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('4','紫红',NULL,'1','紫','1.17.03.05',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('5','紫红',NULL,'1','红','1.17.03.03',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('6','绛红',NULL,'1','绛','1.17.03.04',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('7','绛红',NULL,'1','红','1.17.03.03',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('8','青红',NULL,'1','青','1.17.03.06',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('9','青红',NULL,'1','红','1.17.03.03',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('10','腻腐正常',NULL,'1','腻腐正常','1.19.99',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('11','腻苔',NULL,'1','腻苔','1.19.03.06',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('12','厚苔',NULL,'1','厚苔','1.19.03.02',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('13','少苔',NULL,'1','苔少','1.20.08',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('14','薄苔',NULL,'1','薄苔','1.19.03.01',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('15','白苔',NULL,'1','白苔','1.19.01.01',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('16','薄白苔',NULL,'1','薄白苔','1.20.02',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('17','薄白苔',NULL,'1','薄苔','1.19.03.01',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('18','薄白苔',NULL,'1','白苔','1.19.01.01',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('19','褐苔',NULL,'1','褐苔','1.19.99',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('20','黑苔',NULL,'1','黑苔','1.19.01.04',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('21','黄苔',NULL,'1','黄苔','1.19.01.02',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('22','灰苔',NULL,'1','灰苔','1.19.01.03',NULL,'苔');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('23','老嫩正常',NULL,'1','老嫩正常','1.17.99',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('24','老舌',NULL,'1','老舌','1.17.05.01',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('25','嫩舌',NULL,'1','嫩舌','1.17.05.02',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('26','胖瘦正常',NULL,'1','胖瘦正常','1.17.99',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('27','胖大',NULL,'1','胖大','1.17.05.03',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('28','瘦小',NULL,'1','瘦小','1.17.99',NULL,'舌');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('29','弱',NULL,'1','弱','4.01.21',NULL,'脉');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('30','弦',NULL,'1','弦','4.01.15',NULL,'脉');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('31','迟',NULL,'1','迟','4.01.03',NULL,'脉');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('32','节律不齐（间歇）\r\n',NULL,'1','节律不齐（间歇）','4.01.99',NULL,'脉');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('33','强',NULL,'1','强','4.01.99',NULL,'脉');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('34','滑',NULL,'1','滑','4.01.11',NULL,'脉');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('35','数',NULL,'1','数','4.01.04',NULL,'脉');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('36','浮',NULL,'1','浮','4.01.01',NULL,'脉');
insert into `zkxc_mapping` (`id`, `zkxc_name`, `zkxc_code`, `map_type`, `map_name`, `map_code`, `map_other`, `type_other`) values('37','沉',NULL,'1','沉','4.01.02',NULL,'脉');
