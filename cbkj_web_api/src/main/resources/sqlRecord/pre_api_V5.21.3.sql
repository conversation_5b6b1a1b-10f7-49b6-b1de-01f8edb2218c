CREATE TABLE `t_his_xdf` (
  `XH` varchar(32) NOT NULL COMMENT 'HIS-协定方序号',
  `NAME` varchar(512) DEFAULT NULL COMMENT 'HIS-协定方名称',
  `SYFW` varchar(1) DEFAULT NULL COMMENT '使用范围 0全院 1科室 2 个人',
  PRIMARY KEY (`XH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `t_his_xdf_mapping` (
  `XH` varchar(32) DEFAULT NULL,
  `PERS_PRE_ID` varchar(32) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


ALTER TABLE `cbkj_web_api`.`t_his_record`
  ADD COLUMN `XDFXH` VARCHAR(512) NULL COMMENT '协定方号',
   ADD COLUMN `CISXH` VARCHAR(512) NULL COMMENT '';




   INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'PRE_DESCRIPTION_HIS_MAPPING', '协定方关联临床路径', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '0', '66', '1024', '1开0关', 'B511'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param
        WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'PRE_DESCRIPTION_HIS_MAPPING'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开', 1 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='PRE_DESCRIPTION_HIS_MAPPING'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开'
    );

    INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关', 0 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='PRE_DESCRIPTION_HIS_MAPPING'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关'
    );




    INSERT INTO `cbkj_web_parameter`.`t_dic_base` ( dic_id, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `create_date`, `create_user`, `create_user_name`, `status`, `display_id`) VALUES
    (REPLACE(UUID(),"-",""), '6', 'HIS临床路径',
 (SELECT t.parent_id FROM (SELECT parent_id FROM t_dic_base WHERE dic_name='药品目录' ) t )
    , NULL, '0', NULL, '6', '000000', '000000', '000000', NULL, '2022-06-24 11:08:33', '70810c874405453b99c6c2cf72296fe5', 'admin', '0', '000000');


ALTER TABLE `sys_operation_log` ADD INDEX `IDX_CREATE_DATE` (`create_date`);
ALTER TABLE `sys_logentity` ADD INDEX `IDX_CREATE_DATE` (`create_date`);



ALTER TABLE `cbkj_web_api`.`t_prescription`
  ADD COLUMN `pre_origin_xh` VARCHAR(64) NULL COMMENT 'his协定方序号';


  ALTER TABLE `cbkj_web_api`.`t_his_xdf_mapping`
  ADD UNIQUE INDEX (`XH`, `PERS_PRE_ID`);



   INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'PRE_ONE_THING_RECORD_PUSH', '处方一件事病历数据推送', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '0', '110', '1701', '1开启0关闭', 'K004'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param
        WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'PRE_ONE_THING_RECORD_PUSH'
    );


    INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开启', 1 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='PRE_ONE_THING_RECORD_PUSH'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开启'
    );

        INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关闭', 0 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='PRE_ONE_THING_RECORD_PUSH'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关闭'
    );


