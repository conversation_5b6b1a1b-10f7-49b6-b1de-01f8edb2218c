ALTER TABLE `t_order_status` ADD INDEX `IDX_CREATE_TIME` (`CREATE_TIME`);
ALTER TABLE t_record_detail ADD CREATE_DATE DATETIME DEFAULT CURRENT_TIMESTAMP NULL;
UPDATE t_record_detail d, cbkj_web_api.t_record r SET d.CREATE_DATE = r.REC_TRE_TIME WHERE d.REC_ID  = r.REC_ID ;
ALTER TABLE `sys_operation_log` ADD COLUMN `results` TEXT NULL AFTER `params`;

DELIMITER $$

DROP PROCEDURE IF EXISTS `move_history_data_yearly`$$

CREATE PROCEDURE `move_history_data_yearly`(IN table_name VARCHAR(64), IN date_field VARCHAR(32))
    COMMENT '迁移历史数据，入参：表名、时间字段名，迁移后的表名格式：表名_年份'
BEGIN

    #迁移数据的时间范围，默认两年前，根据实际情况修改，单位：年
    DECLARE move_date_range INT DEFAULT 2;

    DECLARE move_date_max DATE;
    DECLARE move_date_start DATE;
    DECLARE move_date_end DATE;
    DECLARE new_table_name VARCHAR(64);


    #要迁移数据的最大时间点（两年前的今天）
    SET move_date_max = DATE_SUB(CURDATE(), INTERVAL move_date_range YEAR);

    #源数据中最早时间min_day
    SET @check_sql = CONCAT(" SELECT MIN(DATE(", date_field, ")) INTO @min_day FROM ", table_name);
    PREPARE pre_check FROM @check_sql;
    EXECUTE pre_check;
    DEALLOCATE PREPARE pre_check;

    SELECT @min_day, move_date_max;

    WHILE @min_day < move_date_max DO
            START TRANSACTION;
            SET new_table_name = CONCAT(table_name, '_', YEAR(@min_day));
            SET move_date_start = STR_TO_DATE(CONCAT(@min_day, ' 00:00:00'), '%Y-%m-%d %H:%i:%s');

            IF YEAR(@min_day) < YEAR(move_date_max) THEN
                SET move_date_end = STR_TO_DATE(CONCAT(YEAR(@min_day) + 1, '-01-01 00:00:00'), '%Y-%m-%d %H:%i:%s');
                SET @min_day = move_date_end;
            ELSE
                SET move_date_end = move_date_max;
                SET @min_day = move_date_max;
            END IF;

            SELECT move_date_start, move_date_end;

            #创建历史表
            SET @creat_table_sql = CONCAT(" CREATE TABLE IF NOT EXISTS ", new_table_name, " LIKE ", table_name);
            PREPARE creat_table FROM @creat_table_sql;
            EXECUTE creat_table;
            DEALLOCATE PREPARE creat_table;

            #复制数据到历史表
            SET @move_data_sql = CONCAT(" INSERT INTO ", new_table_name, " SELECT * FROM ", table_name,
                                        " WHERE ", date_field, " BETWEEN '", move_date_start,  "' AND '", move_date_end, "'");
            PREPARE move_data FROM @move_data_sql;
            EXECUTE move_data;
            DEALLOCATE PREPARE move_data;

            #删除原数据
            SET @delete_old_sql = CONCAT(" DELETE FROM ", table_name,
                                         " WHERE ", date_field, " BETWEEN '", move_date_start,  "' AND '", move_date_end, "'");
            PREPARE delete_old FROM @delete_old_sql;
            EXECUTE delete_old;
            DEALLOCATE PREPARE delete_old;

            COMMIT ;

        END WHILE;

END$$

DELIMITER ;



DELIMITER $$

DROP EVENT IF EXISTS `move_history_data`$$

CREATE EVENT `move_history_data` ON SCHEDULE EVERY 1 DAY STARTS '2023-06-10 01:10:00' ON COMPLETION NOT PRESERVE ENABLE DO BEGIN

    #时间字段最好有索引
    CALL move_history_data_yearly('t_prescription', 'pre_time');
    CALL move_history_data_yearly('t_prescription_examine', 'CHECK_TIME');
    CALL move_history_data_yearly('t_prescription_item', 'INSERT_TIME');
    CALL move_history_data_yearly('t_record', 'REC_TRE_TIME');
    CALL move_history_data_yearly('t_record2_ask', 'create_date');
    CALL move_history_data_yearly('t_record2_cut', 'create_date');
    CALL move_history_data_yearly('t_record2_look', 'create_date');
    CALL move_history_data_yearly('t_record2_smell', 'create_date');
    CALL move_history_data_yearly('t_record2_tag', 'create_date');
    CALL move_history_data_yearly('t_record_detail', 'CREATE_DATE');
    CALL move_history_data_yearly('t_record_master', 'SAVE_TIME');
    CALL move_history_data_yearly('t_record_syndrome_answer', 'CREATE_DATE');
    CALL move_history_data_yearly('t_record_syndrome_ask', 'CREATE_DATE');
    CALL move_history_data_yearly('t_record_syndrome_group', 'CREATE_DATE');
    CALL move_history_data_yearly('t_register', 'REGISTER_TIME');
    CALL move_history_data_yearly('t_order_status', 'CREATE_TIME');

END$$

DELIMITER ;