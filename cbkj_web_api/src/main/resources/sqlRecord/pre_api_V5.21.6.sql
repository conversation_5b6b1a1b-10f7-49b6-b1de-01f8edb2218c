INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'ASR_DIAGNOSTIC', '开启语音识别', '', NOW(),
    'admin', 'admin', '0', '3', '0', '', 111, '1701', '中科芯创四诊仪', 'K005'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'ASR_DIAGNOSTIC'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '处方一件事病历', 1 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='ASR_DIAGNOSTIC'
                                   ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='处方一件事病历'
    );


    INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'ASR_DIAGNOSTIC_URL', '语音识别地址', '', NOW(),
    'admin', 'admin', '0', '5', '0', '', 112, '1701', '语音识别地址', 'K006'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'ASR_DIAGNOSTIC_URL'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '', '语音识别地址', 1 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='ASR_DIAGNOSTIC_URL'
                                   ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='语音识别地址'
    );



        INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'TEXT_DIAGNOSTIC_URL', '文本结构化地址', '', NOW(),
    'admin', 'admin', '0', '5', '0', '', 113, '1701', '文本结构化地址', 'K007'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'TEXT_DIAGNOSTIC_URL'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '', '文本结构化地址', 1 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='TEXT_DIAGNOSTIC_URL'
                                   ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='文本结构化地址'
    );




    INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'MODIFY_PRESCRIPTION_LIMIT_TYPE', '修改处方链接是否限制处方类型（默认是）', '1', NOW(),
    'admin', 'admin', '0', '1', '0', '1', '104', '1009', '修改处方链接是否限制处方类型 1限制 0不限制', 'B248'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'MODIFY_PRESCRIPTION_LIMIT_TYPE'
    );


INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '不限制', 0 FROM (
                                           SELECT tsp.PAR_ID
                                           FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                           WHERE tsp.PAR_CODE='MODIFY_PRESCRIPTION_LIMIT_TYPE'
                                       ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='不限制'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '限制', 1 FROM (
                                           SELECT tsp.PAR_ID
                                           FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                           WHERE tsp.PAR_CODE='MODIFY_PRESCRIPTION_LIMIT_TYPE'
                                       ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='限制'
    );