###云系统表更新
#处方一件事病历：增加治疗意见
ALTER TABLE `t_record` ADD COLUMN `TREATMENT_ADVICE` TEXT COLLATE utf8_general_ci  NULL COMMENT '治疗意见' AFTER `ALLERGY_DESC`;
ALTER TABLE `t_record2_tag` ADD COLUMN `treatment_advice` TEXT COLLATE utf8_general_ci  NULL COMMENT '治疗意见' AFTER `auxiliary_exam`;
ALTER TABLE `t_his_record` ADD COLUMN `treatment_advice` TEXT COLLATE utf8_general_ci  NULL COMMENT '治疗意见' AFTER `AUXILIARY_EXAM`;

#处方表：修改浓煎代码字段类型，增加浓煎字典名称
ALTER TABLE `t_prescription`
    CHANGE `PRE_N_BAG` `PRE_N_BAG` VARCHAR(16) NULL COMMENT '每次几袋',
    CHANGE `PRE_N_ML` `PRE_N_ML` VARCHAR(16) NULL COMMENT '每次几ml',
    ADD COLUMN `PRE_N_ML_NAME` VARCHAR(32) NULL COMMENT '每次几ml' AFTER `PRE_N_ML`;
#历史数据填充浓煎名称
update `t_prescription` set PRE_N_ML_NAME = CONCAT(PRE_N_ML, 'ml') where PRE_N_ML is not null and PRE_N_ML_NAME is null;

#处方明细中记录药品金额小计
ALTER TABLE `t_prescription_item` ADD COLUMN `mat_total_price` DECIMAL(18,6) NULL COMMENT '处方药品金额小计' AFTER `tcxs_mat_dose`;


#修改处方触发器修改对预扣库存的处理
DELIMITER $$

DROP TRIGGER /*!50032 IF EXISTS */ `g_afterUpdate_prescription`$$

CREATE
    TRIGGER `g_afterUpdate_prescription` AFTER UPDATE ON `t_prescription`
    FOR EACH ROW BEGIN
    DECLARE operationTypeTr INT(11);
    DECLARE operationContentTr VARCHAR(50);
    DECLARE operationIdTr VARCHAR(32);
    DECLARE operationNameTr VARCHAR(32);
    DECLARE operationTimeTr DATETIME;
    DECLARE needInsertOrderTr INT(1);
    DECLARE needDeleteWithholdingStock INT(1);
    SET needInsertOrderTr = 0;
    SET needDeleteWithholdingStock = 0;

    IF old.IS_DEL != '1' AND new.IS_DEL = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 10;
        SET operationContentTr = '删除处方';
        SET operationIdTr = new.PRE_DOCTOR;
        SET operationNameTr = new.PRE_DOCTORNAME;
        SET operationTimeTr = NOW();
        /**作废预扣库存**/
        SET needDeleteWithholdingStock = 1;

    ELSEIF old.IS_CHECK != '1' AND new.IS_CHECK = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 30;
        SET operationContentTr = '审核通过';
        SET operationIdTr = new.CHECK_USERID;
        SET operationNameTr = new.CHECK_USERNAME;
        SET operationTimeTr = new.CHECK_TIME;
    ELSEIF old.IS_CHECK != '2' AND new.IS_CHECK = '2' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 40;
        SET operationContentTr = '审核未通过';
        SET operationIdTr = new.CHECK_USERID;
        SET operationNameTr = new.CHECK_USERNAME;
        SET operationTimeTr = new.CHECK_TIME;
    ELSEIF old.REC_EXT_TYPE != '45' AND new.REC_EXT_TYPE = '45' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 45;
        SET operationContentTr = '推送失败';
        SET operationIdTr = '';
        SET operationNameTr = '';
        SET operationTimeTr = NOW();
    ELSEIF old.IS_PAY != '1' AND new.IS_PAY = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 50;
        SET operationContentTr = '处方缴费成功';
        SET operationIdTr = new.PAY_USERID;
        SET operationNameTr = new.PAY_USERNAME;
        SET operationTimeTr = new.PAY_TIME;
    END IF;

    IF needInsertOrderTr = 1 THEN
        INSERT INTO `t_order_status` (
            `STATUS_ID`,`REGISTER_ID`,`PRE_ID`,`OPERATION_USERID`,`OPERATION_USERNAME`,`OPERATION_TIME`,
            `OPERATION_TYPE`,`OPERATION_CONTENT`,CREATE_TIME
        ) VALUES (
                     REPLACE (UUID(), '-', ''),'0',new.PRE_ID,operationIdTr,operationNameTr,operationTimeTr,
                     operationTypeTr,operationContentTr,NOW()
                 ) ;
    END IF;

    IF needDeleteWithholdingStock = 1 THEN
        UPDATE `cbkj_web_parameter`.`t_withholding_stock` SET `status` = '20' WHERE PRE_ID = old.PRE_ID;
    END IF;
END;
$$

DELIMITER ;


#删除处方触发器对预扣库存的处理
DELIMITER $$

DROP TRIGGER /*!50032 IF EXISTS */ `g_afterDelete_prescription`$$

CREATE
    TRIGGER `g_afterDelete_prescription` AFTER DELETE ON `t_prescription`
    FOR EACH ROW BEGIN

    DELETE FROM `cbkj_web_parameter`.`t_withholding_stock` WHERE PRE_ID = old.PRE_ID;
END;
$$

DELIMITER ;


# 优化索引，旧联合索引未有效被使用。
ALTER TABLE `t_personal_rule_auth`
  DROP INDEX `IDX_KEY`,
  ADD INDEX (`pers_pre_id`),
  ADD INDEX (`user_id`),
  ADD INDEX (`dept_id`);



CREATE TABLE `t_personal_prescription_dis_mapping` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `PERS_PRE_ID` varchar(32) NOT NULL,
  `DIS_ID` varchar(32) DEFAULT NULL,
  `DIS_NAME` varchar(64) DEFAULT NULL,
  `SYM_ID` varchar(32) DEFAULT NULL,
  `SYM_NAME` varchar(64) DEFAULT NULL,
  `THE_CODE` varchar(32) DEFAULT NULL,
  `THE_NAMES` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `PERS_PRE_ID` (`PERS_PRE_ID`,`DIS_ID`,`SYM_ID`,`THE_CODE`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4;



###以下是综合后台表更新

ALTER TABLE cbkj_web_parameter.`t_pharmacy` ADD `is_open_batch_price` varchar(1) DEFAULT '0' COMMENT '是否开启批次预扣';

ALTER TABLE cbkj_web_parameter.`t_withholding_stock` ADD `sto_batch_id` varchar(32) DEFAULT NULL COMMENT '批次id';
ALTER TABLE cbkj_web_parameter.`t_withholding_stock` ADD `production_batch` varchar(32) DEFAULT NULL COMMENT '批次号';
ALTER TABLE cbkj_web_parameter.`t_withholding_stock` ADD `mat_pack_mount` decimal(18,2) DEFAULT NULL COMMENT '包装量';
ALTER TABLE cbkj_web_parameter.`t_withholding_stock` ADD `mat_name` varchar(32) DEFAULT NULL COMMENT '药品名称';
ALTER TABLE cbkj_web_parameter.`t_withholding_stock` ADD `retail_price` decimal(18,4) DEFAULT NULL COMMENT '批次零售价';
ALTER TABLE cbkj_web_parameter.`t_withholding_stock` ADD `purchase_price` decimal(18,4) DEFAULT NULL COMMENT '批次批发价';
ALTER TABLE cbkj_web_parameter.`t_withholding_stock` ADD `status` int(1) DEFAULT NULL COMMENT '状态（10 已开方   20 已删除（作废）  50已收费  90已发药  100取消发药   110 、退费 ）';
ALTER TABLE cbkj_web_parameter.`t_withholding_stock` ADD `create_date` datetime DEFAULT NULL COMMENT '新增时间';
ALTER TABLE cbkj_web_parameter.`t_withholding_stock` ADD `update_date` datetime DEFAULT NULL;
ALTER TABLE cbkj_web_parameter.`t_withholding_stock` ADD `update_username` varchar(32) DEFAULT NULL COMMENT '更新人';


CREATE INDEX idx_pre_id ON cbkj_web_parameter.t_withholding_stock (pre_id);
CREATE INDEX idx_sto_batch_id ON cbkj_web_parameter.t_withholding_stock (sto_batch_id);

DROP TABLE IF EXISTS cbkj_web_parameter.`t_stock_agr_batch`;
CREATE TABLE cbkj_web_parameter.`t_stock_agr_batch` (
                                                        `bid` int(11) NOT NULL AUTO_INCREMENT,
                                                        `agr_id` varchar(32) DEFAULT NULL COMMENT '配方id',
                                                        `agr_production_batch` varchar(32) DEFAULT NULL COMMENT '配方批号',
                                                        `mat_price_id` varchar(32) DEFAULT NULL COMMENT '药品价格id',
                                                        `production_batch` varchar(32) DEFAULT NULL COMMENT '药品批号',
                                                        `purchase_price` decimal(18,2) DEFAULT NULL COMMENT '进价',
                                                        `retail_price` decimal(18,2) DEFAULT NULL COMMENT '零售价',
                                                        `make_num` int(11) DEFAULT NULL COMMENT '制作数量',
                                                        `create_date` datetime DEFAULT NULL,
                                                        PRIMARY KEY (`bid`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;


  ALTER TABLE `cbkj_web_api`.`zkxc_chi_cun_guan`
  CHANGE `opt_pulse1` `opt_pulse1` TEXT CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '脉图点位，用于绘制脉象波形图；左寸、左关、左尺、右寸、';

  ALTER TABLE `cbkj_web_api`.`zkxc_chi_cun_guan`
  CHANGE `vein_elements` `vein_elements` TEXT CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '脉象要素分析图（弦、滑、正常）';



