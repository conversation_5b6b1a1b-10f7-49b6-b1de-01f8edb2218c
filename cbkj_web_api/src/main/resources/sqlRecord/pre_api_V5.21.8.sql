ALTER TABLE `sys_log_interface` ADD INDEX `SLI_IDX_CREATE_TIME` (`CREATE_TIME`);
ALTER TABLE `sys_log_interface_error` ADD INDEX `SLIE_IDX_CREATE_TIME` (`CREATE_TIME`);
ALTER TABLE `zkxc_equipment` ADD COLUMN `is_default` INT (1)  NULL COMMENT '是否默认展示【0默认1不是】';
ALTER TABLE `zkxc_equipment` ADD COLUMN `ins_code` VARCHAR (32)  NULL COMMENT '医疗机构id';

DELIMITER $$
DROP EVENT `delete_logs_older_six_months`$$
CREATE EVENT `delete_logs_older_six_months` ON SCHEDULE EVERY 1 DAY STARTS '2023-04-17 00:10:10' ON COMPLETION NOT PRESERVE ENABLE DO BEGIN
    DELETE FROM `sys_log_interface` WHERE CREATE_TIME < DATE_SUB(NOW(), INTERVAL 6 MONTH);
    DELETE FROM `sys_log_interface_error` WHERE CREATE_TIME < DATE_SUB(NOW(), INTERVAL 6 MONTH);
    DELETE FROM `sys_logentity` WHERE create_date < DATE_SUB(NOW(), INTERVAL 6 MONTH);
    DELETE FROM `sys_operation_log` WHERE create_date < DATE_SUB(NOW(), INTERVAL 6 MONTH);
END$$

DELIMITER ;





select 1;



ALTER TABLE `cbkj_web_api`.`zkxc_equipment_patient`
  ADD COLUMN `info_status` INT(2) NULL COMMENT '（新华）-1.待分配四诊仪器0.等待四诊仪数据 1.获取到四诊仪数据 2.已推送给HIS -2.推送失败' AFTER `phone`;


  ALTER TABLE `cbkj_web_api`.`zkxc_collectinfo`
  ADD INDEX (`register_id`);



  ALTER TABLE `cbkj_web_api`.`zkxc_equipment_patient`
  ADD COLUMN `fail_info` TEXT NULL COMMENT '推送失败原因' AFTER `info_status`;


ALTER TABLE `cbkj_web_api`.`zkxc_equipment_patient`
  ADD COLUMN `MEDICAL_CARD_NO` VARCHAR(64) NULL COMMENT '就诊卡号' ,
  ADD COLUMN `VISIT_NO` VARCHAR(32) NULL COMMENT 'HIS诊序号' AFTER `MEDICAL_CARD_NO`,
  ADD COLUMN `app_id` VARCHAR(32) NULL COMMENT 'APPID' AFTER `VISIT_NO`,
  ADD COLUMN `ins_code` VARCHAR(32) NULL COMMENT '医疗机构' AFTER `app_id`;



  ALTER TABLE `cbkj_web_api`.`zkxc_equipment`
  ADD COLUMN `equipment_type` INT(1) DEFAULT 1 NULL COMMENT '1.四诊仪2.经络仪3.热成像仪4.脉象复现仪' AFTER `create_time`;


select 1;

  ALTER TABLE `cbkj_web_api`.`zkxc_equipment_patient`
  CHANGE `info_status` `info_status` INT(2) NULL COMMENT '（新华：以下字段是） -1.待分配设备 0.等待设备数据 1.获取到设备数据 2.已推送给HIS  10.推送失败',
  ADD COLUMN `equipment_type` INT(1) DEFAULT 1 NULL COMMENT '1.四诊仪2.经络仪3.热成像仪4.脉象复现仪' AFTER `ins_code`;

  select 1;
  ALTER TABLE `cbkj_web_api`.`zkxc_equipment`

  CHANGE `equipment_type` `equipment_type` INT(1) DEFAULT 1 NOT NULL COMMENT '1.四诊仪2.经络仪3.热成像仪4.脉象复现仪';


  ALTER TABLE `cbkj_web_api`.`zkxc_equipment_patient`
  CHANGE `mac_address` `mac_address` VARCHAR(48) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'mac地址';


  select 1;

  ALTER TABLE `cbkj_web_api`.`zkxc_equipment_patient`
  ADD COLUMN `send_time` DATETIME NULL COMMENT '推送报告时间' AFTER `equipment_type` ,
  ADD COLUMN `patient_source` INT(1) DEFAULT 0 NULL COMMENT '0.自己系统录入 1.新华四诊仪器HIS厂家调用录入' AFTER `send_time`,
  ADD COLUMN register_time DATETIME  NULL COMMENT 'his录入我们系统的时间',
  ADD COLUMN `receive_time` DATETIME NULL COMMENT '接受报告时间',
  ADD COLUMN `collect_id` INT(11) NULL COMMENT '接收报告的id' ;

CREATE TABLE `zkxc_equiment_push`(
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `equipment_patient_id` INT(10) NOT NULL COMMENT 'zkxc_equipment_patient表主键id',
  `mac_address` VARCHAR(48) COMMENT '设备mac地址',
  `fail_info` TEXT COMMENT '失败原因',
  `push_status` INT(2) COMMENT '（新华：以下字段是） -1.待分配设备 0.等待设备数据 1.获取到设备数据 2.已推送给HIS  10.推送失败',
  `register_id` VARCHAR(32) COMMENT '挂号id',
   `send_time` DATETIME NULL COMMENT '推送时间',
   type INT(10) NOT NULL COMMENT '类型：1.推给his2.推给云系统3.推给脉象仪',
  PRIMARY KEY (`id`)
);


insert into `cbkj_web_parameter`.`sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) values ('000251', '数字诊间', '', NULL, '0', '00020', NULL, NULL, '1', NULL, NULL, NULL, '11', '1', '1', '2')

insert into `cbkj_web_parameter`.`sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `open_type`, `modual_code`) values ('000251001', '登记管理', '/patient/register', NULL, '0', '000251', '2024-01-18 11:39:40', NULL, '2', NULL, NULL, NULL, '1', '2', '1', '2')

select 1;

ALTER TABLE `cbkj_web_api`.`zkxc_equiment_push`
  ADD INDEX (`equipment_patient_id`),
  ADD INDEX (`register_id`);


#病历明细表增加时间索引
ALTER TABLE `cbkj_web_api`.`t_record_detail` ADD INDEX `TRD_IDX_CREATE_DATE` (`CREATE_DATE`);

# 菜单增加标记 1默认展开
ALTER TABLE `cbkj_web_parameter`.`sys_admin_menu` ADD COLUMN `menu_open` INT(1) DEFAULT 0 NULL COMMENT '1默认展开 0默认挂起' AFTER `menu_level`;


ALTER TABLE `cbkj_web_api`.`zkxc_collectinfo`
  ADD COLUMN `mz_file` VARCHAR(150) NULL COMMENT '脉诊zip地址' AFTER `insert_time`;


  ALTER TABLE zkxc_mz
  ADD COLUMN `pulse_deviation_img` VARCHAR(100) NULL COMMENT '偏差图 base64' AFTER `pulse_quantification_img`,
    ADD COLUMN `total_pulse_feature_desc` VARCHAR(128) NULL COMMENT '整体脉象结论' ;

  ALTER TABLE `zkxc_chi_cun_guan`
  ADD COLUMN `maiwei_feature_desc` VARCHAR(64) NULL COMMENT '脉位特征' AFTER `mz_result_type`,
  ADD COLUMN `mailv_feature_desc` VARCHAR(64) NULL COMMENT '脉率特征' AFTER `maiwei_feature_desc`,
  ADD COLUMN `jielv_feature_desc` VARCHAR(64) NULL COMMENT '节律特征' AFTER `mailv_feature_desc`,
  ADD COLUMN `maili_feature_fesc` VARCHAR(64) NULL COMMENT '脉力特征' AFTER `jielv_feature_desc`,
  ADD COLUMN `jinzhangdu_feature_desc` VARCHAR(64) NULL COMMENT '紧张度特征' AFTER `maili_feature_fesc`,
  ADD COLUMN `liulidu_feature_desc` VARCHAR(64) NULL COMMENT '流利度特征' AFTER `jinzhangdu_feature_desc`;

CREATE TABLE `zkxc_face` (
  `face_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '面诊自增id',
  `area` varchar(128) DEFAULT NULL COMMENT '脸颊特征',
  `area_description` text COMMENT '脸颊特征解析',
  `color` varchar(128) DEFAULT NULL COMMENT '面色特征',
  `color_description` text COMMENT '面色特征',
  `light` varchar(128) DEFAULT NULL COMMENT '面色光泽',
  `light_description` text COMMENT '面色光泽解析',
  `lip` varchar(128) DEFAULT NULL COMMENT '唇色',
  `lip_description` text COMMENT '唇色 解析',
  `face_original_img` varchar(200) DEFAULT NULL COMMENT '面原图 base64',
  `collect_id` int(11) DEFAULT NULL,
  `register_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`face_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;



