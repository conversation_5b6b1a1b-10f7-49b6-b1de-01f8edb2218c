insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('舌神','1.17.01.','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('荣舌','1.17.01.01','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('枯舌','1.17.01.02','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('舌色','1.17.03.','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('淡红','1.17.03.01','淡红','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('淡白','1.17.03.02','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('红','1.17.03.03','红','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('绛','1.17.03.04','绛','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('紫','1.17.03.05','紫','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('青','1.17.03.06','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('瘀点','1.17.03.07','瘀点','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('舌形','1.17.05.','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('老舌','1.17.05.01','老','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('嫩舌','1.17.05.02','嫩','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('胖大','1.17.05.03','胖大','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('肿胀','1.17.05.04','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('瘦薄','1.17.05.05','瘦','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('点刺','1.17.05.06','点、刺舌','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('裂纹','1.17.05.07','裂纹','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('光滑','1.17.05.08','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('齿痕','1.17.05.09','边有齿痕','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('舌衄','1.17.05.10','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('舌疮','1.17.05.11','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('舌态','1.17.07.','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('痿软','1.17.07.01','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('强硬','1.17.07.02','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('歪斜','1.17.07.03','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('颤动','1.17.07.04','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('吐弄','1.17.07.05','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('短缩','1.17.07.06','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('舌麻痹','1.17.07.07','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('舌下络脉','1.17.09.','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('舌下络脉正常','1.17.09.01','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('舌下络脉曲张','1.17.09.02','下静脉迂曲','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('其他','1.17.99','','舌','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('成人望苔','1.19.','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('苔色','1.19.01.','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('白苔','1.19.01.01','白','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('黄苔','1.19.01.02','黄','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('灰苔','1.19.01.03','灰','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('黑苔','1.19.01.04','黑','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('苔质','1.19.03.','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('薄苔','1.19.03.01','薄','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('厚苔','1.19.03.02','厚','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('润苔','1.19.03.03','润','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('燥苔','1.19.03.04','燥','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('腐苔','1.19.03.05','腐','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('腻苔','1.19.03.06','腻','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('剥苔','1.19.03.07','剥落','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('偏苔','1.19.03.08','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('全苔','1.19.03.09','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('假苔','1.19.03.10','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('其他成人望苔','1.19.99','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('小儿望苔','1.20.','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('薄黄苔','1.20.01','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('薄白苔','1.20.02','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('剥苔','1.20.03','剥落','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('腻苔','1.20.04','腻','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('地图舌','1.20.05','剥落','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('舌面光而无苔','1.20.06','无','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('腐苔','1.20.07','腐','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('苔少','1.20.08','少','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('燥苔','1.20.09','燥','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('厚苔','1.20.10','厚','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('黄腻','1.20.11','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('脓腐苔','1.20.12','腐','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('浮垢苔','1.20.13','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('霉腐苔','1.20.14','腐','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('其他小儿望苔','1.20.99','','苔','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('浮','4.01.01','浮','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('沉','4.01.02','沉','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('迟','4.01.03','迟','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('数','4.01.04','数','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('洪','4.01.05','洪','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('微','4.01.06','微','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('细','4.01.07','细','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('散','4.01.08','散','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('虚','4.01.09','虚','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('实','4.01.10','实','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('滑','4.01.11','滑','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('涩','4.01.12','涩滞','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('长','4.01.13','长','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('短','4.01.14','短','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('弦','4.01.15','弦','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('芤','4.01.16','芤','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('紧','4.01.17','紧','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('缓','4.01.18','缓','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('革','4.01.19','革','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('牢','4.01.20','牢','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('弱','4.01.21','弱','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('濡','4.01.22','濡','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('伏','4.01.23','伏','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('动','4.01.24','动','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('促','4.01.25','促','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('结','4.01.26','结','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('代','4.01.27','代','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('疾','4.01.28','疾','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('斜飞脉','4.01.29','','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('反关脉','4.01.30','','脉','6');
insert into `zkxc_mapping` (`zkxc_name`, `zkxc_code`, `map_name`, `type_other`, `map_type`) values('其他','4.01.99','','脉','6');






INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '6', '胃痞病', 6 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='ZKXC_DIAGNOSTIC'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='6' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='胃痞病'
    );

#增加参数：搜索全院协定方是否过滤当前机构
INSERT INTO `cbkj_web_parameter`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `CREATE_DATE`, `CREATE_USER`, `create_user_name`, `status`, `param_type`, `is_global`, `param_init_value`, `sort`, `menu_id`, `modual_code`, `param_desc`, `parameter_diagram`, `par_number`)
VALUES('df565cdd20af48a5966661a2856fg456','000000','000000','000000','PERSONAL_PRESCRIPTION_FILTER_INSCODE','搜索全院协定方是否过滤当前机构','1','2024-04-03 15:09:43','07f7352a938411ecad8d00163f006620','admin','0','1','0','1','50','1023',NULL,'0否（可以看所有授权的全院协定方） 1是（只能看当前机构的全院协定方）',NULL,'B244');
#启用参数：搜索科室协定方是否过滤当前科室
UPDATE `cbkj_web_parameter`.t_sys_param SET STATUS = '0', par_values = '1'
WHERE PAR_CODE = 'PERSONAL_PRESCRIPTION_FILTER_DEPT' ;


alter table cbkj_web_parameter.sys_admin_info add column `last_update_pwd` datetime DEFAULT NULL COMMENT '上次修改密码时间';
update cbkj_web_parameter.sys_admin_info set last_update_pwd = now() where last_update_pwd is null;
