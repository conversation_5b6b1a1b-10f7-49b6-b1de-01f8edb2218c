ALTER TABLE `cbkj_web_api`.`zkxc_equipment_patient`
  ADD COLUMN `other_word` VARCHAR(32) NULL COMMENT '下面字段新华PAd需求' AFTER `receive_time`,
  ADD COLUMN `physique_status` INT(1) DEFAULT 0 NULL COMMENT '体格状态 0 未完成 1完成' AFTER `collect_id`,
  ADD COLUMN `diagnosis_status` INT(1) DEFAULT 0 NULL COMMENT '预问诊 区分上午下午！0 未完成 1完成' AFTER `physique_status`,
  ADD COLUMN `composite_medical` INT(1) DEFAULT 0 NULL COMMENT '病历复合 0 未符合 1 复合' AFTER `diagnosis_status`,


  ADD COLUMN `order_time` DATETIME NULL AFTER `composite_medical`;






  ALTER TABLE `cbkj_web_api`.`zkxc_equipment_patient`
  ADD COLUMN `dept_id` VARCHAR(32) NULL  ,
  ADD COLUMN `dept_name` VARCHAR(64) NULL AFTER `dept_id`,
  ADD COLUMN `doctor_id` VARCHAR(32) NULL AFTER `dept_name`,
  ADD COLUMN `doctor_name` VARCHAR(128) NULL AFTER `doctor_id`;



  ALTER TABLE `cbkj_web_api`.`zkxc_equipment_patient`
  ADD INDEX (`id_card`);

  ALTER TABLE `cbkj_web_api`.`zkxc_equipment_patient`
  ADD INDEX (`check_time`);


ALTER TABLE `cbkj_web_api`.`zkxc_equipment_patient`
  ADD COLUMN `composite_time` DECIMAL NULL COMMENT '复核时间' AFTER `order_time`,
  ADD COLUMN `composite_name` VARCHAR(256) NULL COMMENT '复核人' AFTER `composite_time`,
  ADD COLUMN `composite_id` VARCHAR(32) NULL COMMENT '复核人id' AFTER `composite_name`;



  DROP TABLE IF EXISTS `zkxc_pad_record`;

CREATE TABLE `zkxc_pad_record` (
  `zkxc_pad_record_id` int(11) NOT NULL AUTO_INCREMENT,
  `patient_name` varchar(128) DEFAULT NULL,
  `patient_sex` varchar(1) DEFAULT NULL,
  `patient_age` varchar(6) DEFAULT NULL,
  `pre_diagnosis_time` datetime DEFAULT NULL COMMENT '预诊时间',
  `zhu_shu` text,
  `xian_bing_shi` text,
  `ji_wang_shi` text,
  `ti_ge_jian_cha` text,
  `create_time` datetime DEFAULT NULL,
  `equipment_patient_id` int(11) DEFAULT NULL,
  `composite_medical` int(11) DEFAULT '0' COMMENT '病历复合 0 未符合 1 复合(预诊推过来按这个字段判断是否覆盖)',
  `check_time` date DEFAULT NULL COMMENT '检查时间(日期)',
  `patient_card_no` varchar(128) DEFAULT NULL COMMENT '证件号',
  `check_range` tinyint(1) DEFAULT NULL COMMENT '1上午2下午',
  PRIMARY KEY (`zkxc_pad_record_id`),
  KEY `patient_card_no` (`patient_card_no`),
  KEY `check_time` (`check_time`),
  KEY `equipment_patient_id` (`equipment_patient_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;

#用户表增加字段（5.21.9有相同SQL）
alter table cbkj_web_parameter.sys_admin_info add column `last_update_pwd` datetime DEFAULT NULL COMMENT '上次修改密码时间';
update cbkj_web_parameter.sys_admin_info set last_update_pwd = now() where last_update_pwd is null;



ALTER TABLE `cbkj_web_api`.`t_record2_ask`
  ADD COLUMN `equipment_patient_id` INT(11) NULL ;


ALTER TABLE `cbkj_web_api`.`t_record2_cut`
  ADD COLUMN `equipment_patient_id` INT(11) NULL;

  ALTER TABLE `cbkj_web_api`.`t_record2_look`
  ADD COLUMN `equipment_patient_id` INT(11) NULL ;



  ALTER TABLE `cbkj_web_api`.`t_record2_smell`
  ADD COLUMN `equipment_patient_id` INT(11) NULL ;


  ALTER TABLE `cbkj_web_api`.`t_record2_cut`
  ADD COLUMN `equipment_patient_id` INT(11) NULL ;



  ALTER TABLE `cbkj_web_api`.`zkxc_pad_record`
  ADD COLUMN `fu_zhu_jian_cha` TEXT NULL COMMENT '辅助检查' AFTER `check_range`,
  ADD COLUMN `zhi_liao_yi_jian` TEXT NULL COMMENT '治疗意见' AFTER `fu_zhu_jian_cha`,
  ADD COLUMN `weight` VARCHAR(32) NULL COMMENT '体重' AFTER `zhi_liao_yi_jian`,
  ADD COLUMN `height` VARCHAR(32) NULL COMMENT '身高' AFTER `weight`;


  ALTER TABLE `cbkj_web_api`.`zkxc_equipment_patient`
  CHANGE `composite_time` `composite_time` DATETIME NULL COMMENT '复核时间';



  INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '7', '患者登记(web端)', 7 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='ZKXC_DIAGNOSTIC'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='7' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='患者登记(web端)'
    );


