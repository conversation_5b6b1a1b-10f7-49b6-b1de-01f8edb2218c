ALTER TABLE `cbkj_web_api`.`t_prescription`
  ADD COLUMN `mat_tol_weight` DECIMAL(10,4) NULL COMMENT '总重量';




    INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, 'formula-insurance', '医保配方', 3 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='PRESCRIPTION_SEARCH_RECIPE_OPTIONS_2'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='formula-insurance' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='医保配方'
    );

        INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, 'formula-selfPaying', '自费配方', 4 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='PRESCRIPTION_SEARCH_RECIPE_OPTIONS_2'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='formula-selfPaying' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='自费配方'
    );


    update `cbkj_web_parameter`.`t_sys_param` set `PAR_NAME` = '搜索(配方|膏方|医保配方|自费配方)处方配置' where `PAR_ID` = '8852740f5e5e11edad8d00163f006620';





INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'INTELLIGENT_JUMP', '智能辨证推导跳转', '/diagnosis/prescribe', NOW(),
    'admin', 'admin', '0', '2', '0', '/diagnosis/prescribe', '3', '1901', '智能辨证：/diagnosis/dialectical智能开方：/diagnosis/prescribe特色病历：/diagnosis/questionnaire电子病历：/manage/case/record/create', 'A003'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'INTELLIGENT_JUMP'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '/diagnosis/dialectical', '智能辨证', 1 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INTELLIGENT_JUMP'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='/diagnosis/dialectical' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='智能辨证'
    );

    INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '/diagnosis/prescribe', '智能开方', 2 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INTELLIGENT_JUMP'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='/diagnosis/prescribe' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='智能开方'
    );

        INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '/diagnosis/questionnaire', '特色病历', 3 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INTELLIGENT_JUMP'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='/diagnosis/questionnaire' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='特色病历'
    );


            INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '/manage/case/record/create', '电子病历', 3 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INTELLIGENT_JUMP'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='/manage/case/record/create' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='电子病历'
    );

