ALTER TABLE `cbkj_web_api`.`zkxc_equipment`
  ADD COLUMN `app_id` VARCHAR(32) NULL AFTER `ins_code`;

ALTER TABLE `cbkj_web_api`.`zkxc_equipment`
    ADD COLUMN `ins_name` VARCHAR(64) NULL AFTER `app_id`,
    ADD COLUMN `app_name` VARCHAR(64) NULL AFTER `ins_name`;


INSERT INTO `cbkj_web_parameter`.`sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) VALUES ('0003000', '设备管理', '/system/device/list', NULL, '0', '000222', '2024-12-05 10:07:59', '70810c874405453b99c6c2cf72296fe5', '2', NULL, NULL, NULL, '4', '2', '0', '1', '2');