INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'CHECK_RATIONAL_USE_BY_SDK', '云系统是否使用安全合理用药SDK', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '0', '115', '1701', '云系统是否使用安全合理用药SDK', 'K009'
FROM DUAL WHERE  NOT EXISTS (
    SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'CHECK_RATIONAL_USE_BY_SDK'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关闭', 0 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='CHECK_RATIONAL_USE_BY_SDK'
                                     ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关闭'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开启', 1 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='CHECK_RATIONAL_USE_BY_SDK'
                                     ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开启'
);






INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'PRE_CHECK_WAITTING_TIME', '审方等待时间配置（超过多少秒药师未处理则自动审方）', '15', NOW(),
    'admin', 'admin', '0', '5', '0', '15', '82', '1301', '审方等待时间配置（超过多少秒药师未处理则自动审方）', 'C017'
FROM DUAL WHERE  NOT EXISTS (
    SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'PRE_CHECK_WAITTING_TIME'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '15', '超时时间', 0 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='PRE_CHECK_WAITTING_TIME'
                                     ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='超时时间'
);




CREATE TABLE `t_prescription_evaluation_sdk_other` (
                                                       `sdk_other_id` varchar(32) NOT NULL,
                                                       `PRE_ID` varchar(32) DEFAULT NULL,
                                                       `ypml_id` varchar(32) NOT NULL,
                                                       `total_json` text NOT NULL COMMENT '合理用药返回的对象json',
                                                       PRIMARY KEY (`sdk_other_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE `t_prescription_evaluation_sdk` (
                                                 `evaluation_sdk_id` varchar(32) NOT NULL,
                                                 `PRE_ID` varchar(32) NOT NULL,
                                                 `sdk_name` varchar(32) NOT NULL,
                                                 `content` text,
                                                 PRIMARY KEY (`evaluation_sdk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;