INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'OPEN_CDSS_MODE', ' 开启CDSS模式', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '0', '4', '1901', '开启CDSS模式', 'A004'
FROM DUAL WHERE  NOT EXISTS (
    SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'OPEN_CDSS_MODE'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关闭', 0 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='OPEN_CDSS_MODE'
                                     ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关闭'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开启', 1 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='OPEN_CDSS_MODE'
                                     ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开启'
);


CREATE TABLE `t_statistics_prescription_cdss` (
                                                  `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                                  `app_id` VARCHAR(32) DEFAULT NULL COMMENT '医联体ID',
                                                  `app_name` VARCHAR(32) DEFAULT NULL COMMENT '医联体',
                                                  `ins_code` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构代码',
                                                  `ins_name` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构',
                                                  `dept_id` VARCHAR(32) DEFAULT NULL COMMENT '科室ID',
                                                  `dept_name` VARCHAR(32) DEFAULT NULL COMMENT '科室',
                                                  `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID',
                                                  `user_name` VARCHAR(32) DEFAULT NULL COMMENT '用户',
                                                  `total_num` INT(11) NOT NULL DEFAULT '0' COMMENT '总开方数量',
                                                  `inner_num` INT(11) NOT NULL DEFAULT '0' COMMENT '内服方数量',
                                                  `exter_num` INT(11) NOT NULL DEFAULT '0' COMMENT '外用方数量',
                                                  `acu_num` INT(11) NOT NULL DEFAULT '0' COMMENT '适宜技术方数量',
                                                  `patent_num` INT(11) NOT NULL DEFAULT '0' COMMENT '中成药数量',
                                                  `prepare_num` INT(11) NOT NULL DEFAULT '0' COMMENT '制剂数量',
                                                  `create_date` DATE NOT NULL COMMENT '统计日期',
                                                  `insert_date` DATETIME DEFAULT NULL COMMENT '插入时间',
                                                  PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=303311 DEFAULT CHARSET=utf8mb4 COMMENT='处方统计表(CDSS)';

ALTER TABLE `cbkj_web_api`.`t_statistics_register`
    ADD INDEX (`user_id`);
ALTER TABLE `cbkj_web_api`.`t_statistics_prescription`
    ADD INDEX (`user_id`);
ALTER TABLE `cbkj_web_api`.`t_statistics_analysis`
    ADD INDEX (`user_id`);
ALTER TABLE `cbkj_web_api`.`t_prescription`
    ADD INDEX (`PRE_DOCTOR`);

-- 更新 就诊次数和电子病历的医生名字
UPDATE `t_record` AS a JOIN `t_statistics_register` AS b SET b.`user_name` = a.`DOC_NAME` WHERE b.`user_id` = a.`DOC_ID` AND a.`DOC_NAME` IS NOT NULL;
-- 更新 t_statistics_prescription 处方统计
UPDATE t_prescription AS a JOIN t_statistics_prescription AS b SET b.`user_name` = a.`PRE_DOCTORNAME` WHERE a.`PRE_DOCTOR` = b.`user_id`;
-- 更新体制辨识医生名称
UPDATE cbkj_web_parameter.sys_admin_info AS a JOIN cbkj_web_api.t_statistics_analysis AS b SET b.`user_name` = a.`name_zh` WHERE a.`user_id` = b.`user_id` AND a.`name_zh` IS NOT NULL;
-- 更新协定放数量医生名字
UPDATE t_personal_prescription AS a JOIN t_statistics_personal_prescription AS b SET b.`user_name` = a.`PRE_OWNER_NAME` WHERE a.`PRE_OWNER` = b.`user_id`;


ALTER TABLE `cbkj_web_parameter`.`t_sys_param`
    CHANGE `PAR_VALUES` `PAR_VALUES` VARCHAR(500) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参数值';



ALTER TABLE `cbkj_web_parameter`.`t_sys_param_init_desc`
    CHANGE `param_init_code` `param_init_code` VARCHAR(64) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '参数初始选项code';


INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'SYS_USE_COLUMN', '系统使用统计项目', '就诊人次:registerTimes,中药饮片处方数:sumInnerAndExtPrescription,中药饮片处方数(CDSS):sumInnerAndExtPrescriptionCDSS,适宜技术方:sumStatisticsPrescription,中医电子病历:electronicRecordNum,体质辨识报告:number,协定方:sumPersonalPrescription', NOW(),
    'admin', 'admin', '0', '3', '0', '就诊人次:registerTimes,中药饮片处方数:sumInnerAndExtPrescription,中药饮片处方数(CDSS):sumInnerAndExtPrescriptionCDSS,适宜技术方:sumStatisticsPrescription,中医电子病历:electronicRecordNum,体质辨识报告:number,协定方:sumPersonalPrescription', '89', '1501', '系统使用统计项目', 'E004'
FROM DUAL WHERE  NOT EXISTS (
    SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'SYS_USE_COLUMN'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '就诊人次:registerTimes', '就诊人次', 0 FROM (
                                                                  SELECT tsp.PAR_ID
                                                                  FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                                  WHERE tsp.PAR_CODE='SYS_USE_COLUMN'
                                                              ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='就诊人次:registerTimes' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='就诊人次'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '中药饮片处方数:sumInnerAndExtPrescription', '中药饮片处方数', 1 FROM (
                                                                                           SELECT tsp.PAR_ID
                                                                                           FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                                                           WHERE tsp.PAR_CODE='SYS_USE_COLUMN'
                                                                                       ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='中药饮片处方数:sumInnerAndExtPrescription' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='中药饮片处方数'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '中药饮片处方数(CDSS):sumInnerAndExtPrescriptionCDSS', '中药饮片处方数(CDSS)', 2 FROM (
                                                                                                           SELECT tsp.PAR_ID
                                                                                                           FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                                                                           WHERE tsp.PAR_CODE='SYS_USE_COLUMN'
                                                                                                       ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='中药饮片处方数(CDSS):sumInnerAndExtPrescriptionCDSS' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='中药饮片处方数(CDSS)'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '适宜技术方:sumStatisticsPrescription', '适宜技术方', 3 FROM (
                                                                                  SELECT tsp.PAR_ID
                                                                                  FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                                                  WHERE tsp.PAR_CODE='SYS_USE_COLUMN'
                                                                              ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='适宜技术方:sumStatisticsPrescription' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='适宜技术方'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '中医电子病历:electronicRecordNum', '中医电子病历', 4 FROM (
                                                                                SELECT tsp.PAR_ID
                                                                                FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                                                WHERE tsp.PAR_CODE='SYS_USE_COLUMN'
                                                                            ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='中医电子病历:electronicRecordNum' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='中医电子病历'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '体质辨识报告:number', '体质辨识报告', 5 FROM (
                                                                   SELECT tsp.PAR_ID
                                                                   FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                                   WHERE tsp.PAR_CODE='SYS_USE_COLUMN'
                                                               ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='体质辨识报告:number' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='体质辨识报告'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '协定方:sumPersonalPrescription', '协定方', 6 FROM (
                                                                        SELECT tsp.PAR_ID
                                                                        FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                                        WHERE tsp.PAR_CODE='SYS_USE_COLUMN'
                                                                    ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='协定方:sumPersonalPrescription' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='协定方'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '门诊中药处方数:sumOutPatientPrescription', '门诊中药处方数', 7 FROM (
                                                                        SELECT tsp.PAR_ID
                                                                        FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                                        WHERE tsp.PAR_CODE='SYS_USE_COLUMN'
                                                                    ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='门诊中药处方数:sumOutPatientPrescription' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='门诊中药处方数'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '住院中药处方数:sumInPatientPrescription', '住院中药处方数', 8 FROM (
                                                                        SELECT tsp.PAR_ID
                                                                        FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                                        WHERE tsp.PAR_CODE='SYS_USE_COLUMN'
                                                                    ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='住院中药处方数:sumInPatientPrescription' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='住院中药处方数'
);

CREATE TABLE `t_statistics_prescription_mz_zy` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                                   `app_id` varchar(32) DEFAULT NULL COMMENT '医联体ID',
                                                   `app_name` varchar(32) DEFAULT NULL COMMENT '医联体',
                                                   `ins_code` varchar(32) DEFAULT NULL COMMENT '医疗机构代码',
                                                   `ins_name` varchar(32) DEFAULT NULL COMMENT '医疗机构',
                                                   `dept_id` varchar(32) DEFAULT NULL COMMENT '科室ID',
                                                   `dept_name` varchar(32) DEFAULT NULL COMMENT '科室',
                                                   `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID',
                                                   `user_name` varchar(32) DEFAULT NULL COMMENT '用户',
                                                   `total_num` int(11) NOT NULL DEFAULT '0' COMMENT '总开方数量',
                                                   `inner_num` int(11) NOT NULL DEFAULT '0' COMMENT '内服方数量',
                                                   `inner_mz_num` int(11) DEFAULT NULL COMMENT '内服方门诊数量',
                                                   `inner_zy_num` int(11) DEFAULT NULL COMMENT '内服方住院数量',
                                                   `exter_num` int(11) NOT NULL DEFAULT '0' COMMENT '外用方数量',
                                                   `exter_mz_num` int(11) DEFAULT NULL COMMENT '外用方门诊数量',
                                                   `exter_zy_num` int(11) DEFAULT NULL COMMENT '外用方住院数量',
                                                   `create_date` date NOT NULL COMMENT '统计日期',
                                                   `insert_date` datetime DEFAULT NULL COMMENT '插入时间',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='处方统计表';





