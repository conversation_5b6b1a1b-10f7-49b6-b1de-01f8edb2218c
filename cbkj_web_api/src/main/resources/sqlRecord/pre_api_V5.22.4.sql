INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'PRE_REFERRAL_IS_OPEN', '开启转诊', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '0', '104', '105', '开启转诊', 'G008'
FROM DUAL WHERE  NOT EXISTS (
    SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'PRE_REFERRAL_IS_OPEN'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关闭', 0 FROM (
                                             SELECT tsp.PAR_ID
                                             FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                             WHERE tsp.PAR_CODE='PRE_REFERRAL_IS_OPEN'
                                         ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关闭'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开启', 1 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='PRE_REFERRAL_IS_OPEN'
                                     ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开启'
);



CREATE TABLE `t_pre_referral` (
                                  `pre_referral_id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                  `app_id` varchar(32) DEFAULT NULL COMMENT '医工体id',
                                  `ins_code` varchar(32) DEFAULT NULL COMMENT '机构代码',
                                  `ins_name` varchar(32) DEFAULT NULL COMMENT '机构名称',
                                  `doctor_id` varchar(32) DEFAULT NULL COMMENT '医生id',
                                  `doctor_name` varchar(32) DEFAULT NULL COMMENT '医生姓名',
                                  `patient_id` varchar(32) DEFAULT NULL COMMENT '患者id',
                                  `patient_name` varchar(32) DEFAULT NULL COMMENT '患者姓名',
                                  `patient_address` varchar(128) DEFAULT NULL COMMENT '患者住址',
                                  `patient_gender` varchar(1) DEFAULT NULL COMMENT '患者性别（0未知1男2女9未说明）',
                                  `patient_gender_name` varchar(6) DEFAULT NULL COMMENT '患者性别名称',
                                  `patient_nation_name` varchar(20) DEFAULT NULL COMMENT '患者民族名称',
                                  `patient_nation_code` varchar(20) DEFAULT NULL COMMENT '患者民族代码',
                                  `patient_age` varchar(3) DEFAULT NULL COMMENT '患者年龄',
                                  `patient_certificate` varchar(18) DEFAULT NULL COMMENT '患者证件号',
                                  `patient_occupation_code` varchar(20) DEFAULT NULL COMMENT '患者职业代码',
                                  `patient_occupation_name` varchar(20) DEFAULT NULL COMMENT '患者职业名称',
                                  `dis_id` varchar(32) DEFAULT NULL COMMENT '疾病id',
                                  `dis_name` varchar(32) DEFAULT NULL COMMENT '疾病名',
                                  `sym_id` varchar(32) DEFAULT NULL COMMENT '证型id',
                                  `sym_name` varchar(32) DEFAULT NULL COMMENT '证型名称',
                                  `the_code` varchar(32) DEFAULT NULL COMMENT '治法代码',
                                  `the_name` varchar(32) DEFAULT NULL COMMENT '治法名称',
                                  `referral_result` text COMMENT '转诊原因',
                                  `PATIENT_CONTENT` text COMMENT '主诉',
                                  `NOW_DESC` text COMMENT '现病史',
                                  `PAST_DESC` text COMMENT '既往史',
                                  `PHYSICAL` text COMMENT '体格检查',
                                  `FOUR_DIAGNOSIS` text COMMENT '中医四诊',
                                  `AUXILIARY_EXAM` text COMMENT '辅助检查',
                                  `referral_images` text COMMENT '转诊图片地址多个图逗号分开',
                                  `referral_time` datetime DEFAULT NULL COMMENT '转诊提交时间',
                                  `dept_id` varchar(32) DEFAULT NULL COMMENT '科室id',
                                  `dept_name` varchar(32) DEFAULT NULL COMMENT '科室名称',
                                  `status` varchar(1) DEFAULT NULL COMMENT '0正常1删除',
                                  `western_disease_id` varchar(32) DEFAULT NULL COMMENT '西医疾病id',
                                  `western_disease` varchar(32) DEFAULT NULL COMMENT '西医疾病名称',
                                  `marital_code` varchar(32) DEFAULT NULL COMMENT '婚姻状态代码',
                                  `marital_name` varchar(32) DEFAULT NULL COMMENT '婚姻状态名称',
                                  PRIMARY KEY (`pre_referral_id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4




ALTER TABLE `cbkj_web_api`.`t_record`
    ADD COLUMN `PRESONAL_DESC` TEXT NULL COMMENT '个人史' AFTER `TREATMENT_ADVICE`;

insert into `cbkj_web_parameter`.`t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) values ('record_type_gms', 'gms', '过敏史', 'record_type', NULL, '0', NULL, '4', '000000', '000000', '000000', '000000', '000000', '2022-05-09 11:06:28', '', '导入', '0');
insert into `cbkj_web_parameter`.`t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) values ('record_type_grs', 'grs', '个人史', 'record_type', NULL, '0', NULL, '5', '000000', '000000', '000000', '000000', '000000', '2022-05-09 11:06:28', '', '导入', '0');
insert into `cbkj_web_parameter`.`t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) values ('record_type_zlyj', 'zlyj', '治疗意见', 'record_type', NULL, '0', NULL, '6', '000000', '000000', '000000', '000000', '000000', '2022-05-09 11:06:28', '', '导入', '0');
