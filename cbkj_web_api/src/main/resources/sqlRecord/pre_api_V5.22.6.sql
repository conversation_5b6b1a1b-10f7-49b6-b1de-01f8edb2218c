郑佳辉: INSERT INTO cbkj_web_parameter.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `CREATE_DATE`, `CREATE_USER`,
                                                      `create_user_name`, `status`, `param_type`, `is_global`, `param_init_value`, `sort`, `menu_id`, `modual_code`, `param_desc`, `parameter_diagram`, `par_number`)
        VALUES('13ef8ba3332b48d2a0efe0e281aa4d61','000000','000000','000000','DISCHARGE_MEDICATION','出院带药是否显示','1',NOW(),'07f7352a938411ecad8d00163f006620','管理员','0','1','0','1','36','1021',NULL,'1开启 0关闭',NULL,'B512');


INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc` (`param_id`, `param_init_code`, `param_init_name`, `option_diagram`, `sort`) VALUES('13ef8ba3332b48d2a0efe0e281aa4d61','0','不开启',NULL,'2');
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc` (`param_id`, `param_init_code`, `param_init_name`, `option_diagram`, `sort`) VALUES('13ef8ba3332b48d2a0efe0e281aa4d61','1','开启',NULL,'1');


ALTER TABLE cbkj_web_api.t_prescription ADD COLUMN discharge_medication_mark VARCHAR(1) COMMENT '出院带药(1是，0否)';



INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'LOAD_GPT', '是否开启智小云GPT', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '', '114', '1201', '0关（默认） 1开', 'J004'
FROM DUAL WHERE  NOT EXISTS (
    SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'LOAD_GPT'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关', 0 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='LOAD_GPT'
                                   ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关'
);

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开', 1 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='LOAD_GPT'
                                   ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开'
);