
/* 2020-11-10 */

ALTER TABLE `cbkj_web_api`.`t_prescription` ADD INDEX `IDX_IS_DEL` (`IS_DEL`);

/*202011-16*/

DROP TABLE IF EXISTS `cbkj_web_api`.`t_prescription_store` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_admin_menu_url` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`t_login` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`t_prescription_dc` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`t_prescription_order` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`t_set_record_content` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`t_syndrome_infer` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`t_syndrome_infer_item` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`t_temp_record_preitem_tohis` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`t_temp_record_tohis` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`test_demo1`;
DROP TABLE IF EXISTS `cbkj_web_api`.`video_admin_cycle` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`video_http_params`;
DROP TABLE IF EXISTS `cbkj_web_api`.`video_room_message` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`video_room_record` ;

/*2020-11-20*/
ALTER TABLE `cbkj_web_api`.`sys_admin_info` ADD INDEX `INDEX_CREATE_ID` (`create_id`), ADD INDEX `INDEX_APP_ID` (`APP_ID`), ADD INDEX `INDEX_INS_CODE` (`INS_CODE`), ADD INDEX `INDEX_DEP_ID` (`DEP_ID`), ADD INDEX `INDEX_IS_DEL` (`IS_DEL`);
ALTER TABLE `cbkj_web_api`.`sys_admin_info_rule` ADD INDEX `INDEX_ADMIN_ID` (`admin_id`), ADD INDEX `INDEX_RID` (`rid`);
ALTER TABLE `cbkj_web_api`.`sys_admin_rule_menu` ADD INDEX `INDEX_RID` (`rid`), ADD INDEX `INDEX_MID` (`mid`);
ALTER TABLE `cbkj_web_api`.`sys_institution` ADD INDEX `INDEX_APP_INSCODE` (`APP_ID`, `INS_CODE`);
ALTER TABLE `cbkj_web_api`.`t_display` CHANGE `SORT_NUM` `SORT_NUM` INT(6) DEFAULT 999999 NULL COMMENT '排序号';
ALTER TABLE `cbkj_web_api`.`t_personal_prescription_item` ADD INDEX `INDEX_PRES_PRE_ID` (`PERS_PRE_ID`);
ALTER TABLE `cbkj_web_api`.`t_personal_prescription` ADD INDEX `IDX_APP_ID` (`APP_ID`), ADD INDEX `IDX_INS_CODE` (`INS_CODE`), ADD INDEX `IDX_DEPT_ID` (`DEPT_ID`), ADD INDEX `IDX_PRE_OWNER` (`PRE_OWNER`), ADD INDEX `IDX_PRE_TYPE` (`PRE_TYPE`), ADD INDEX `IDX_IS_SHARE` (`IS_SHARE`);
ALTER TABLE `cbkj_web_api`.`t_record` CHANGE `REC_TRE_TYPE` `REC_TRE_TYPE` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '病历类型（1门诊 2住院）';
ALTER TABLE `cbkj_web_api`.`t_register` CHANGE `CLINIC_TYPE_ID` `CLINIC_TYPE_ID` INT(11) NULL COMMENT '门诊类型（1门诊 2住院）';

ALTER TABLE `cbkj_web_api`.`sys_institution` CHANGE `INS_PROVINCE` `INS_PROVINCE` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '省代码', CHANGE `INS_CITY` `INS_CITY` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '城代码', CHANGE `INS_AREA` `INS_AREA` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '区代码', ADD COLUMN `INS_STREET` VARCHAR(10) NULL COMMENT '街代码' AFTER `INS_AREA_NAME`, ADD COLUMN `INS_STREET_NAME` VARCHAR(32) NULL COMMENT '街名称' AFTER `INS_STREET`;

ALTER TABLE `cbkj_web_api`.`t_center_his_ypml` ADD COLUMN `SYNC_SOURCE` VARCHAR(10) NULL COMMENT '同步来源 cbkj_drug：聪宝共享药房 his_view：HIS提供视图 his_http：HIS提供http接口' AFTER `IS_DEL`;

ALTER TABLE `cbkj_web_api`.`t_display` CHANGE `PRE_MAT_TYPE` `PRE_MAT_TYPE` VARCHAR(20) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '药品剂型（1散装饮片  2散装颗粒  3膏方 4小包装饮片 5小包装颗粒 ）';
ALTER TABLE `cbkj_web_api`.`t_personal_prescription` CHANGE `PRE_MAT_TYPE` `PRE_MAT_TYPE` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '药品剂型（1散装饮片  2散装颗粒  3膏方 4小包装饮片 5小包装颗粒 ）';

/*2020-11-23*/

INSERT INTO `cbkj_web_api`.`sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) VALUES('198','同步协定方','/recipe/sync',NULL,NULL,'2','10','2020-11-20 14:08:43','4b5e0852cdb845279f41a47823026d08','2',NULL,NULL,NULL,'4',NULL,'1');
INSERT INTO `cbkj_web_api`.`sys_admin_rule_menu` SET `rmid` = '794c12d8673e4c7bad177e8f4497da57', `rid` = 'b4a17b6b635c4de48f95178676905aa5', `mid` = '198';
UPDATE      `cbkj_web_api`.`sys_admin_info` a, `sys_admin_info_rule` r SET r.rid = 'b4a17b6b635c4de48f95178676905aa5' WHERE a.`id` = r.`admin_id` AND a.`name` = 'admin';


/*2020-11-24*/

ALTER TABLE `zyznyxt_basic`.`b_scheme` ADD INDEX `IDX_DIS_ID` (`DIS_ID`), ADD INDEX `IDX_SYM_ID` (`SYM_ID`), ADD INDEX `IDX_PRE_ID` (`PRE_ID`), ADD INDEX `IDX_IS_DEL` (`IS_DEL`);
ALTER TABLE `zyznyxt_basic`.`b_symptom` ADD INDEX `IDX_SYM_NAME` (`SYM_NAME`), ADD INDEX `IDX_IS_DEL` (`IS_DEL`), ADD INDEX `IDX_DIS_PY` (`SYM_PY`), ADD INDEX `IDX_DIS_WB` (`SYM_WB`), ADD INDEX `IDX_SYM_CODE` (`SYM_CODE`), ADD INDEX `IDX_SYM_TYPE` (`SYM_TYPE`);
ALTER TABLE `zyznyxt_basic`.`b_disease` ADD INDEX `IDX_DIS_NAME` (`DIS_NAME`), ADD INDEX `IDX_IS_DEL` (`IS_DEL`), ADD INDEX `IDX_SYM_PY` (`DIS_PY`), ADD INDEX `IDX_SYM_WB` (`DIS_WB`), ADD INDEX `IDX_DIS_CODE` (`DIS_CODE`), ADD INDEX `IDX_DIS_TYPE` (`DIS_TYPE`);
ALTER TABLE `zyznyxt_basic`.`b_material` ADD INDEX `IDX_MAT_NAME` (`MAT_NAME`), ADD INDEX `IDX_IS_DEL` (`IS_DEL`), ADD INDEX `IDX_MAT_PY` (`MAT_PY`), ADD INDEX `IDX_MAT_WB` (`MAT_WB`);


/*2020-11-27*/
ALTER TABLE `cbkj_web_api`.`t_personal_prescription` ADD INDEX `IDX_IS_DEL` (`IS_DEL`);
ALTER TABLE `cbkj_web_api`.`t_sys_param` DROP INDEX `index_par_code`, ADD UNIQUE INDEX `index_par_code` (`APP_ID`, `INS_CODE`, `PAR_CODE`, `IS_DEL`);


/*2020-12-04*/
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_qrtz_blob_triggers` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_qrtz_calendars` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_qrtz_cron_triggers` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_qrtz_fired_triggers` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_qrtz_paused_trigger_grps` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_qrtz_scheduler_state` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_qrtz_simple_triggers` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_qrtz_simprop_triggers` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_qrtz_locks` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_qrtz_triggers` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_qrtz_job_details` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_quartz` ;
DROP TABLE IF EXISTS `cbkj_web_api`.`sys_quartz_task` ;

DROP TABLE IF EXISTS `cbkj_web_api`.`sys_webservice_basic` ;


/*2020-12-10*/
INSERT INTO `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`)
	VALUES ('199','未缴费处方','/audit/preHistory',NULL,NULL,'2','4','2020-12-10 09:21:54','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'0',NULL,'1');

INSERT INTO `sys_admin_rule_menu` (`rmid`, `rid`, `mid`) VALUES('fe68d4da4f904853aef4f7196dbeeaa4','b4a17b6b635c4de48f95178676905aa5','199');

ALTER TABLE t_personal_prescription ADD pre_mz_zy VARCHAR(2) COMMENT '门诊/住院标识(1门诊 2住院)';

/*2020-12-14*/
INSERT INTO `cbkj_web_api`.`t_sys_param` (
  `PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`
)
VALUES
  ( '70810c87hh05453b99c6c2cf72296fe5', '000000', '000000', 'PRESCRIPTION_COVERAGE', '覆盖率是否计算末级服务站', '0', '0-计算至已标识，1-计算至末级，默认0', NOW(), '', '', '0') ;



/*2020-12-15*/

ALTER TABLE `cbkj_web_api`.`t_display` ADD COLUMN `SHOW_PRODUCTION_MZ` VARCHAR(1) DEFAULT '0' NULL COMMENT '门诊是否显示膏方（0否1是）' AFTER `PRE_MAT_TYPE`, ADD COLUMN `SHOW_PRODUCTION_ZY` VARCHAR(1) DEFAULT '0' NULL COMMENT '住院是否显示膏方（0否1是）' AFTER `SHOW_PRODUCTION_MZ`, ADD COLUMN `SHOW_DECOCTION_MZ` VARCHAR(1) DEFAULT '0' NULL COMMENT '门诊是否显示代煎（0否1是）' AFTER `SHOW_PRODUCTION_ZY`, ADD COLUMN `SHOW_DECOCTION_ZY` VARCHAR(1) DEFAULT '0' NULL COMMENT '住院是否显示代煎（0否1是）' AFTER `SHOW_DECOCTION_MZ`, ADD COLUMN `SHOW_EXPRESS_MZ` VARCHAR(1) DEFAULT '0' NULL COMMENT '门诊是否显示配送（0否1是）' AFTER `SHOW_DECOCTION_ZY`, ADD COLUMN `SHOW_EXPRESS_ZY` VARCHAR(1) DEFAULT '0' NULL COMMENT '住院是否显示配送（0否1是）' AFTER `SHOW_EXPRESS_MZ`;
ALTER TABLE `cbkj_web_api`.`t_display` ADD COLUMN `IS_PRODUCTION_MZ` VARCHAR(1) DEFAULT '0' NULL COMMENT '门诊默认膏方（1勾选  0不勾选）' AFTER `SHOW_EXPRESS_ZY`, CHANGE `IS_PRODUCTION` `IS_PRODUCTION_ZY` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' NULL COMMENT '住院默认膏方（1勾选  0不勾选）', ADD COLUMN `IS_DECOCTION_MZ` VARCHAR(1) DEFAULT '0' NULL COMMENT '门诊默认代煎（1勾选  0不勾选）' AFTER `IS_PRODUCTION_ZY`, CHANGE `IS_DECOCTION` `IS_DECOCTION_ZY` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' NULL COMMENT '住院默认代煎（1勾选  0不勾选）', ADD COLUMN `IS_EXPRESS_MZ` VARCHAR(1) DEFAULT '0' NULL COMMENT '门诊默认配送（1勾选  0不勾选）' AFTER `IS_DECOCTION_ZY`, CHANGE `IS_EXPRESS` `IS_EXPRESS_ZY` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' NULL COMMENT '住院默认配送（1勾选  0不勾选）';
ALTER TABLE `cbkj_web_api`.`t_display` ADD COLUMN `FEE_PRODUCTION_MZ` DECIMAL(6,2) NULL COMMENT '门诊制膏费' AFTER `IS_EXPRESS_ZY`, CHANGE `FEE_PRODUCTION` `FEE_PRODUCTION_ZY` DECIMAL(6,2) NULL COMMENT '住院制膏费', ADD COLUMN `FEE_DECOCTION_MZ` DECIMAL(6,2) NULL COMMENT '门诊代煎费' AFTER `FEE_PRODUCTION_ZY`, CHANGE `FEE_DECOCTION` `FEE_DECOCTION_ZY` DECIMAL(6,2) NULL COMMENT '住院代煎费', ADD COLUMN `FEE_EXPRESS_MZ` VARCHAR(32) NULL COMMENT '门诊配送费（若分市内外，用逗号拼接如0,20，通过比较地址编号区分）' AFTER `FEE_DECOCTION_ZY`, CHANGE `FEE_EXPRESS` `FEE_EXPRESS_ZY` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '住院配送费（若分市内外，用逗号拼接如0,20，通过比较地址编号区分）';


/*2020-12-22*/
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('45ef51b798b544fd8e68a537f188882c','000000','000000','PERSONAL_PRESCRIPTION_MZ_ZY','协定方是否区分到门诊和住院','0','0不区分 1区分','2020-12-10 10:55:19','70810c874405453b99c6c2cf72296fe5','管理员','2020-12-15 16:55:41','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,'0');

/*2020-12-24*/
ALTER TABLE `zyznyxt_basic`.`b_scheme` ADD INDEX `IDX_PRE_TYPE` (`PRE_TYPE`);


/*2020-12-29*/
INSERT INTO `cbkj_web_api`.`t_sys_code` (`CODE_ID`, `CODE_VALUE`, `CODE_NAME`, `CODE_NUM`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `DISABLE_DATE`, `DISABLE_USER`, `DISABLE_USERNAME`, `IS_DISABLE`) VALUES('27','preNMl','内服处方浓煎','8','2020-12-29 09:50:35','34d5ccaa2f014d69b766d68a3dba91ad','cbys',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL,NULL,NULL,'0');

INSERT INTO `cbkj_web_api`.`t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('0ab57f0f2aef46719a83f2ab79986969','150ml','27','3','','150',NULL,NULL,'1','2020-12-29 09:52:51','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,NULL,NULL,NULL,'0');
INSERT INTO `cbkj_web_api`.`t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('26a552468d6d49a49b151ca9ce42215e','250ml','27','5','','250',NULL,NULL,'0','2020-12-29 09:53:05','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,NULL,NULL,NULL,'0');
INSERT INTO `cbkj_web_api`.`t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('301f18253a96486390d8cd29130d51df','200ml','27','4','','200',NULL,NULL,'0','2020-12-29 09:52:57','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,NULL,NULL,NULL,'0');
INSERT INTO `cbkj_web_api`.`t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('6b72e14aaffe4b9d93c45f02dc7c2e89','50ml','27','1','','50',NULL,NULL,'0','2020-12-29 09:52:23','70810c874405453b99c6c2cf72296fe5','admin','2020-12-29 09:53:10','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,'0');
INSERT INTO `cbkj_web_api`.`t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('85ec08ebd6b64048992d28d97bf45565','100ml','27','2','','100',NULL,NULL,'0','2020-12-29 09:52:43','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,NULL,NULL,NULL,'0');

INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('09a0d4810c184e6f893b795ec63a9473','000000','000000','PRESCRIPTION_ICD_DISABLED','西医诊断禁用','1','1是，其余否','2020-11-21 14:11:16','70810c874405453b99c6c2cf72296fe5','管理员','2020-11-21 14:32:02','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,'0');
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('1065d35779634de2b9176d6bd1c828c6','000000','000000','PRESCRIPTION_YB_SHOW_UNGUENT','医保患者是否显示膏方','0','0否（默认值），1是','2020-12-15 22:18:18','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0');
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('c432cfc73505422a9dabe80f11531ce6','000000','000000','PRESCRIPTION_SAVE_DOSAGE_STANDARD_REPLACE','代煎处方剂量受规格限制','0','0不限制，1限制（默认值）','2020-12-15 14:56:30','70810c874405453b99c6c2cf72296fe5','管理员','2020-12-15 15:41:10','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,'0');
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('f65f4d37e2e74ce8907898714987fee0','000000','000000','PRESCRIPTION_SAVE_DOSAGE_STANDARD','处方剂量受规格限制','0','0不限制，1限制（默认值）','2020-12-15 14:55:53','70810c874405453b99c6c2cf72296fe5','管理员','2020-12-15 15:28:58','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,'0');
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('59d1b6ef786d4817ac58497a1cd02e63','000000','000000','PRESCRIPTION_INTERNAL_ML','是否显示内服处方浓煎','1','1显示 0不显示','2020-12-29 09:49:20','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0');
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('83b4b6c10af74738a52e3ae5e54e425c','000000','000000','KNOWLEDGE_SHOW_TYPE','知识库疾病证型显示类型','1','1：95标准，2：95修订','2020-12-29 13:38:49','70810c874405453b99c6c2cf72296fe5','管理员','2020-12-29 13:39:18','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,'0');

UPDATE `cbkj_web_api`.`t_sys_param` SET `PAR_DES` = 'HIS_CLOSE：关闭界面；HIS_PRINT：打印处方；HIS_PRINT_CLOSE：打印处方后关闭界面；KIOSK_CLOSE：通过插件关闭标签；KIOSK_PRINT_CLOSE：打印处方后并通过插件关闭标签；空字符串：其他' WHERE `PAR_CODE` = 'PRESCRIPTION_CHECK_MODE';

INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('ce2d33a0312141448acf0f669f2364b6','000000','000000','PRESCRIPTION_AVG_AMOUNT','均贴金额提示','-1','-1时不提示，其他都提示，且达到均贴金额时，数据显示为红色。默认-1（不显示）','2021-01-04 16:43:46','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0');



/*2021-1-8*/
CREATE TABLE `cbkj_web_api`.`t_statistics_avg_amount` (
	`id` INT (11),
	`app_id` VARCHAR (96),
	`ins_code` VARCHAR (96),
	`doctor_id` VARCHAR (96),
	`doctor_name` VARCHAR (96),
	`avg_amount` DECIMAL (8),
	`start_date` DATETIME ,
	`end_date` DATETIME
);
UPDATE `cbkj_web_api`.`sys_admin_infoex` SET USER_EX_TEXT = '个人' WHERE USER_EX_TYPE = '3' AND USER_EX_TEXT = '私有';
UPDATE `cbkj_web_api`.`sys_admin_infoex` SET USER_EX_TEXT = '科室' WHERE USER_EX_TYPE = '3' AND USER_EX_TEXT = '科室';
UPDATE `cbkj_web_api`.`sys_admin_infoex` SET USER_EX_TEXT = '全院' WHERE USER_EX_TYPE = '3' AND USER_EX_TEXT = '医疗机构';

/* 2021-01-12 qgs 删除“医共体管理”菜单及其下面的“设置”子菜单 */
DELETE from `cbkj_web_api`.`sys_admin_rule_menu` where mid in (20,21);
/* 2021-01-12 qgs 水煎服设置子项 浓煎 （每次多少毫升）*/
UPDATE `cbkj_web_api`.`t_sys_code_item` SET `ZIFU1`='preNMl' WHERE (`ITEM_ID`='f186af805ccb4b26be8805dafab14235');


/*2021-1-13*/
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('40e17b706ad14a96a4eeda45417e2f54', '000000', '000000', 'OPEN_APPOINT_RECIPE', '开放协定方功能（新增/修改/删除/存为）', '1', '0不开放，1开放（默认值）', '2021-01-07 17:28:47', '70810c874405453b99c6c2cf72296fe5', '管理员', '2021-01-07 18:25:02', '70810c874405453b99c6c2cf72296fe5', '管理员', NULL, NULL, NULL, '0');
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('41418a77e7b7491a882f4649bc348346', '000000', '000000', 'HISTORY_PRESCRIPTION_DEL', '历史处方是否显示作废按钮', '0', '1显示 0不显示', '2021-01-06 15:16:21', '70810c874405453b99c6c2cf72296fe5', '管理员', '2021-01-07 15:20:23', '70810c874405453b99c6c2cf72296fe5', '管理员', NULL, NULL, NULL, '0');



/* 2021-01-15 统计分析模块表扩展 */
CREATE TABLE `cbkj_web_api`.`t_statistics_dis_age` (
  `app_id` varchar(32) NOT NULL COMMENT '医联体ID',
  `ins_code` varchar(32) NOT NULL COMMENT '医疗机构代码',
  `time_type` int(1) NOT NULL COMMENT '时间类型:1昨天,2近半月,3近一个季度,4近一年',
  `age_range` varchar(32) NOT NULL COMMENT '年龄段',
  `dis_name` varchar(32) NOT NULL COMMENT '疾病名称',
  `num` int(11) NOT NULL COMMENT '数量',
  `create_time` date NOT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='疾病年龄分布';

CREATE TABLE `cbkj_web_api`.`t_statistics_dis_gender` (
  `app_id` varchar(32) NOT NULL COMMENT '医联体ID',
  `ins_code` varchar(32) NOT NULL COMMENT '医疗机构代码',
  `time_type` int(1) NOT NULL COMMENT '时间类型:1昨天,2近半月,3近一个季度,4近一年',
  `gender` varchar(32) NOT NULL COMMENT '性别',
  `dis_name` varchar(32) NOT NULL COMMENT '疾病名称',
  `num` int(11) NOT NULL COMMENT '数量',
  `create_time` date NOT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='疾病性别分布';

CREATE TABLE `cbkj_web_api`.`t_statistics_dis_num` (
  `app_id` varchar(32) NOT NULL COMMENT '医联体ID',
  `ins_code` varchar(32) NOT NULL COMMENT '医疗机构代码',
  `time_type` int(1) NOT NULL COMMENT '时间类型:1昨天,2近半月,3近一个季度,4近一年',
  `dis_name` varchar(32) NOT NULL COMMENT '疾病名称',
  `num` int(11) NOT NULL COMMENT '数量',
  `create_time` date NOT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='高发疾病';

CREATE TABLE `cbkj_web_api`.`t_statistics_dis_rate` (
  `app_id` varchar(32) NOT NULL COMMENT '医联体ID',
  `ins_code` varchar(32) NOT NULL COMMENT '医疗机构代码',
  `time_type` int(1) NOT NULL COMMENT '时间类型:1昨天,2近半月,3近一个季度,4近一年',
  `group_name` varchar(32) DEFAULT NULL COMMENT '分组名称',
  `dis_name` varchar(32) NOT NULL COMMENT '疾病名称',
  `rate` varchar(32) NOT NULL COMMENT '增长百分比',
  `create_time` date NOT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上升趋势前五的疾病';

CREATE TABLE `cbkj_web_api`.`t_statistics_dis_terms` (
  `app_id` varchar(32) NOT NULL COMMENT '医联体ID',
  `ins_code` varchar(32) NOT NULL COMMENT '医疗机构代码',
  `terms` varchar(32) NOT NULL COMMENT '节气',
  `dis_name` varchar(32) NOT NULL COMMENT '疾病名称',
  `num` int(11) NOT NULL COMMENT '数量',
  `begin_time` date NOT NULL COMMENT '开始时间',
  `end_time` date NOT NULL COMMENT '结束时间',
  `create_time` date NOT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='疾病节气分布';

CREATE TABLE `cbkj_web_api`.`t_statistics_sym_num` (
  `app_id` varchar(32) NOT NULL COMMENT '医联体ID',
  `ins_code` varchar(32) NOT NULL COMMENT '医疗机构代码',
  `time_type` int(1) NOT NULL COMMENT '时间类型:1昨天,2近半月,3近一个季度,4近一年',
  `sym_name` varchar(32) NOT NULL COMMENT '证型名称',
  `num` int(11) NOT NULL COMMENT '数量',
  `create_time` date NOT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='证型分布';


/*2021-1-19*/
CREATE TABLE `cbkj_web_api`.`t_display_code_item` (
  `ITEM_ID` VARCHAR(32) NOT NULL COMMENT '代码明细ID',
  `ITEM_NAME` VARCHAR(128) NOT NULL COMMENT '代码明细名称',
  `CODE_ID` VARCHAR(32) NOT NULL COMMENT '大类ID',
  `DISPLAY_ID` VARCHAR(32) NOT NULL COMMENT '药房配置ID',
  `ITEM_NUM` INT(6) DEFAULT NULL COMMENT '序号',
  `ZIFU1` VARCHAR(32) DEFAULT NULL COMMENT '显示子项',
  `ZIFU2` VARCHAR(32) DEFAULT NULL COMMENT '代码明细编码',
  `ZIFU3` VARCHAR(32) DEFAULT NULL COMMENT '单贴价格',
  `ZIFU4` VARCHAR(32) DEFAULT NULL COMMENT '贴数',
  `ZIFU5` VARCHAR(32) DEFAULT NULL COMMENT '是否默认选中（1是，0否）',
  `CREATE_DATE` DATETIME NOT NULL COMMENT '创建时间',
  `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建人',
  `CREATE_USERNAME` VARCHAR(32) DEFAULT NULL COMMENT '创建人姓名',
  `UPDATE_DATE` DATETIME DEFAULT NULL COMMENT '修改时间',
  `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '修改人',
  `UPDATE_USERNAME` VARCHAR(32) DEFAULT NULL COMMENT '修改人姓名',
  `DEL_DATE` DATETIME DEFAULT NULL COMMENT '删除时间',
  `DEL_USER` VARCHAR(32) DEFAULT NULL COMMENT '删除人',
  `DEL_USERNAME` VARCHAR(32) DEFAULT NULL COMMENT '删除人姓名',
  `IS_DEL` VARCHAR(1) NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
  PRIMARY KEY (`ITEM_ID`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='药房配置代码明细表';

ALTER TABLE `cbkj_web_api`.`t_prescription` ADD COLUMN `PRE_OLD_NO` VARCHAR(32) NULL COMMENT '修改时旧处方号' AFTER `SPECIAL_CODE`;
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('446fa29d83bd4a41860a281fdfabf61f','000000','000000','SAVE_PRESCRIPTION_NO','保存生成处方号','new','new：生成新的，old：使用旧的','2021-01-19 09:09:50','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0');

ALTER TABLE `cbkj_web_api`.`t_center_his_ypmlmx` CHANGE `ZHUANHUANXS` `ZHUANHUANXS` DOUBLE NULL COMMENT '颗粒转成饮片的转换系数';
ALTER TABLE `cbkj_web_api`.`t_center_his_ypmlmx` CHANGE `ZIFU2` `DAILY_MAX_DOSE` DOUBLE NULL COMMENT '日最大剂量';
ALTER TABLE `cbkj_web_api`.`t_sys_code_item` ADD COLUMN `HIDE_PROJECT` VARCHAR(32) NULL COMMENT '隐藏子项' AFTER `ITEM_NUM`;
ALTER TABLE `cbkj_web_api`.`t_his_code_item` ADD COLUMN `HIDE_PROJECT` VARCHAR(32) NULL COMMENT '隐藏子项' AFTER `CODE_ID`;
ALTER TABLE `cbkj_web_api`.`t_display_code_item` ADD COLUMN `HIDE_PROJECT` VARCHAR(32) NULL COMMENT '隐藏子项' AFTER `ITEM_NUM`;

ALTER TABLE `cbkj_web_api`.`t_prescription` ADD COLUMN `PRE_SPECIAL_FEE` DECIMAL(10,4) NULL COMMENT '特殊调配费' AFTER `PRE_SMO_MONEY`;
ALTER TABLE `cbkj_web_api`.`t_prescription_evaluation` ADD COLUMN `EVA_JLPD` TEXT NULL COMMENT '计量偏低' AFTER `EVA_JLCB`, ADD COLUMN `EVA_JLCGD` TEXT NULL COMMENT '计量超规定' AFTER `EVA_JLPD`;

/*2021-1-22*/
insert into `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) values('606410ab415e44069727a02670e3af2e','000000','000000','MAT_SPECIAL_USAGE_SOURCE','中药特殊用法来源','his','know：知识库；his：HIS；其他值不开启特殊用法','2021-01-22 17:03:59','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0');
ALTER TABLE `cbkj_web_api`.`t_center_his_ypmlmx` CHANGE `ZIFU3` `YAOPINYF` VARCHAR(32) NULL COMMENT '药品用法';

/* 2021-01-26 qgs 历史处方菜单位置修改 */
update `cbkj_web_api`.sys_admin_menu set parent_mid='10',sort_number='2' where mid='8';
update `cbkj_web_api`.sys_admin_menu set sort_number='3' where mid='12';
update `cbkj_web_api`.sys_admin_menu set sort_number='4' where mid='13';
update `cbkj_web_api`.sys_admin_menu set sort_number='5' where mid='198';

/*2021-01-26*/
ALTER TABLE `cbkj_web_api`.`t_prescription_item` ADD COLUMN `CENTER_STORE_ID` VARCHAR(32) NULL COMMENT '药房ID' AFTER `ZHUANHUANXS`;

/*2021-01-28*/
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('f696b88f04b94ee8b90b6a0689d9c270','000000','000000','SPECIAL_DIS_CAN_CHOOSE','特病能否选择','1','1是0否','2021-01-28 10:17:03','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0');
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('5b803a316a924d5e9dcb48f3f3ff690f','000000','000000','SPECIAL_DIS_PRES_SOURCE','特病处方来源','','his：接口his/hisSpecialPres；其他开空白方','2021-01-28 11:28:28','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0');
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('3dd96bd7cbed4949b59ed077c4749fd4','000000','000000','SPECIAL_DIS_MODIFY_COLUMN','特病（内服处方）可修改列','5,7','3药品名称 4规格 5剂量 6单位 7用法 8产地 9单价 10库存 11医保','2021-01-28 11:31:43','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0');
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('bd60e01fc52f4bcc9e1e8df73dbe2a81','000000','000000','PRES_COMPUTE_MODE','处方金额计算方式','2','1、单剂金额(保存2位)=∑(药品金额*剂量)；总金额(保存2位)=单剂金额*贴数。 2、单剂金额(保存2位)=∑(药品金额*剂量)；总金额(保存2位)=∑(药品金额*剂量)*贴数。 3、单个药的小计(保存2位)=药品金额*剂量*贴数；总金额=∑(单个药的小计)。','2021-01-28 10:13:25','70810c874405453b99c6c2cf72296fe5','管理员','2021-01-28 13:21:17','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,'0');

/*UPDATE `cbkj_web_api`.`t_center_his_ypmlmx` SET DAILY_MAX_DOSE = 150 WHERE DAILY_MAX_DOSE IS NULL;*/

/*2021-01-29  jtf 外用浓煎*/
INSERT INTO `cbkj_web_api`.`t_sys_code` (`CODE_ID`, `CODE_VALUE`, `CODE_NAME`, `CODE_NUM`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `DISABLE_DATE`, `DISABLE_USER`, `DISABLE_USERNAME`, `IS_DISABLE`) VALUES ('28', 'preNMlW', '外用处方浓煎', '9', '2021-01-29 09:50:35', '34d5ccaa2f014d69b766d68a3dba91ad', 'cbys', NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, '0');
INSERT INTO `cbkj_web_api`.`t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('08e3f85d252e499bbf56c0257a8394ea', '50ml', '28', '1', NULL, NULL, '50', NULL, NULL, '0', '2021-01-29 15:13:27', 'e37c638c400e4f028308e513a412c1e2', 'admin', NULL, NULL, NULL, NULL, NULL, NULL, '0');
INSERT INTO `cbkj_web_api`.`t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('35adc43286004ac99fc717536ea5d5d3', '200ml', '28', '4', NULL, NULL, '200', NULL, NULL, '0', '2021-01-29 15:14:13', 'e37c638c400e4f028308e513a412c1e2', 'admin', NULL, NULL, NULL, NULL, NULL, NULL, '0');
INSERT INTO `cbkj_web_api`.`t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('824aee1df4124e9d8da1c2cab4a0d081', '250ml', '28', '5', NULL, NULL, '250', NULL, NULL, '0', '2021-01-29 15:14:23', 'e37c638c400e4f028308e513a412c1e2', 'admin', NULL, NULL, NULL, NULL, NULL, NULL, '0');
INSERT INTO `cbkj_web_api`.`t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('8c72ca9cd7ae44eab2d3262053d6856a', '150ml', '28', '3', NULL, NULL, '150', NULL, NULL, '0', '2021-01-29 15:14:00', 'e37c638c400e4f028308e513a412c1e2', 'admin', NULL, NULL, NULL, NULL, NULL, NULL, '0');
INSERT INTO `cbkj_web_api`.`t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('d9ff1c4fbb2148b5a54852653bc9a0ff', '100ml', '28', '2', NULL, NULL, '100', NULL, NULL, '1', '2021-01-29 15:13:44', 'e37c638c400e4f028308e513a412c1e2', 'admin', '2021-01-29 15:14:31', 'e37c638c400e4f028308e513a412c1e2', 'jtfanji', NULL, NULL, NULL, '0');

/*2021-2-1*/
ALTER TABLE `cbkj_web_api`.`t_prescription` ADD COLUMN `DEPT_ID` VARCHAR(32) NULL COMMENT '科室ID' AFTER `INS_CODE`;
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES('4327788a418e44728093a575c50ff631','000000','000000','SAVE_REGISTER_ID','未缴费处方去修改使用的挂号ID','new','new：生成新的，old：使用旧的','2021-02-01 18:18:09','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0');

/*2021-02-02 zhaolei 统计医生处方使用量表 */
CREATE TABLE `cbkj_web_api`.`t_statistics_prescription_expand` (
`id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
`app_id` varchar(32) NOT NULL COMMENT '应用id',
`ins_id` varchar(32) NOT NULL COMMENT '医疗机构id',
`doctor_id` varchar(32) NOT NULL COMMENT '医生id',
`pre_id` varchar(32) NOT NULL COMMENT '处方id',
`pre_orgin` varchar(2) NOT NULL COMMENT '处方来源(1.协定方)',
`pre_num` int(6) NOT NULL COMMENT '处方使用次数',
`insert_date` datetime NOT NULL COMMENT '新增时间',
`update_date` datetime DEFAULT NULL COMMENT '修改时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统计医生处方使用量表（用于协定方排序）';


/*2021-2-3 gw 知识库增加接口*/
INSERT INTO `zyznyxt_basic`.`i_apicall_main` ( `APICALL_MAIN_UUID`, `APP_ID`, `APICALL_F_UUID`, `APICALL_NAME`, `APICALL_URL`, `IS_DEL`, `IS_DISABLE`) VALUES ( 'd7676492ec734b16809edb89497c4f66', '', '03治疗方案', '8治疗方案治法', '/zyznyxt_basic/interface/scheme/therapy', '0', '0' ) ;
INSERT INTO `zyznyxt_basic`.`t_sys_app_apicall` (`ID`, `APP_ID`, `APICALL_ID`, `INSERT_USERNAME`, `INSERT_DATE`) VALUES('8e9b76f2c9ea475bbcff639af53aae97','100001','d7676492ec734b16809edb89497c4f66','guowei','2021-02-02 15:26:01');

/*2021-2-3 gw 处方特病字段长度 */
ALTER TABLE `cbkj_web_api`.`t_prescription` CHANGE `SPECIAL_NAME` `SPECIAL_NAME` VARCHAR(64) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '特病名称';
/*2021-2-3 gw 患者增加接到代码 */
ALTER TABLE `cbkj_web_api`.`t_patients` ADD COLUMN `PATIENT_STREET_CODE` VARCHAR(32) NULL COMMENT '街道代码' AFTER `PATIENT_STREET`;

/*2021-2-5 jtf 新都执行 参数值为1 显示转病历*/
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('ffc6db6c62834f2a836e8e2488576294', '000000', '000000', 'TRANSFER_RECORD_SHOW', '就诊记录-转病例按钮是否显示', '1', '1显示 0不显示', '2021-02-05 14:57:24', '70810c874405453b99c6c2cf72296fe5', '管理员', '2021-02-05 16:23:10', '70810c874405453b99c6c2cf72296fe5', '管理员', NULL, NULL, NULL, '0');
/*2021-2-5 jtf 安吉执行 参数值为0 不显示转病例*/
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('ffc6db6c62834f2a836e8e2488576294', '000000', '000000', 'TRANSFER_RECORD_SHOW', '就诊记录-转病例按钮是否显示', '0', '1显示 0不显示', '2021-02-05 14:57:24', '70810c874405453b99c6c2cf72296fe5', '管理员', '2021-02-05 16:23:10', '70810c874405453b99c6c2cf72296fe5', '管理员', NULL, NULL, NULL, '0');

#2021-02-19 茅佳伟 客户ID转化标识
ALTER TABLE `cbkj_web_api`.`t_personal_prescription` ADD IS_CHANGE_OWNER INT(1) COMMENT '医生id是否转过，1已转，0未转' DEFAULT '0';

/*2021-2-22 jtf  参数控制 处方打印按钮是否显示*/
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('1ab757e0d33a4e5f87ea51ee77caa1d8', '000000', '000000', 'PRINT_PRESCRIPTION_SHOW', '处方打印按钮是否显示', '1', '1显示 0不显示（按钮位置：1 今日病人-查看订单；2 我的历史病历-查看；3 病历管理-查看订单 4 历史处方-查看-打印）', '2021-02-22 11:08:27', '70810c874405453b99c6c2cf72296fe5', '管理员', '2021-02-23 09:28:43', '70810c874405453b99c6c2cf72296fe5', '管理员', NULL, NULL, NULL, '0');
/*2021-2-22 jtf  参数控制 病历打印按钮是否显示*/
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('1209308cd3634cb0ba6d21a6997f9cbe', '000000', '000000', 'PRINT_RECORD_SHOW', '病历打印按钮是否显示', '1', '1显示 0不显示（按钮位置：1 今日病人-查看订单；2 我的历史病历-查看；3 病历管理-查看订单）', '2021-02-22 11:09:12', '70810c874405453b99c6c2cf72296fe5', '管理员', '2021-02-23 09:29:11', '70810c874405453b99c6c2cf72296fe5', '管理员', NULL, NULL, NULL, '0');

/*2021-2-22 gw 处方自动作废的时间*/
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('hhh6dddd62834f2a8355555488522222', '000000', '000000', 'PRESCRIPTION_DEL_TIME', '处方自动作废的时间', '72', '值必须为整数，单位小时', '2021-02-22 14:57:24', '70810c874405453b99c6c2cf72296fe5', '管理员', NULL, NULL, NULL, NULL, NULL, NULL, '0');
/*2021-2-23 gw 医保支付条件限制的对象*/
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`) VALUES ('hffgghdd62834f2a8344g4548sdfsdgt', '000000', '000000', 'INSURANCE_LIMIT_OBJECT', '医保支付条件限制的对象', '0', '0）不限制（所有人不提醒），1）门特病人和住院病人', '2021-02-22 14:57:24', '70810c874405453b99c6c2cf72296fe5', '管理员', '0');
/*2021-2-23 gw 医保支付条件限制*/
ALTER TABLE `cbkj_web_api`.`t_center_his_ypmlmx` CHANGE `ZIFU4` `NOT_PAY_ALONE` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '单独使用时不予支付（1不支持）';
ALTER TABLE `cbkj_web_api`.`t_center_his_ypmlmx` CHANGE `ZIFU5` `NOT_PAY_IN_FUND` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '不纳入基金支付范围（1不支持）';
/*2021-3-4 gw 5默认处方类型 6用户习惯内服 7用户习惯外用 8用户习惯适宜技术 9剂量不能整除一个月内不提醒*/
ALTER TABLE `cbkj_web_api`.`sys_admin_infoex` CHANGE `USER_EX_TYPE` `USER_EX_TYPE` TINYINT(4) NOT NULL COMMENT '扩展类型：1输入码 2介绍 3协定方分享权限 4病历模板分享权限 5默认处方类型 6用户习惯内服 7用户习惯外用 8用户习惯适宜技术 9一个月内不提醒剂量不能整除';
ALTER TABLE `cbkj_web_api`.`sys_admin_infoex` CHANGE `USER_EX_CONTENT` `USER_EX_CONTENT` VARCHAR(100) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展编码：输入码(1拼音 2五笔)；权限:(0私有 1科室 2医疗机构 3医共体)；处方类型(1内服 2外用 4适宜技术)；用户习惯（1:表示一行一列,2:表示一行2列）；一个月内不提醒(1四舍五入2自己填)';
/*2021-3-4 wt 增加接收his医保类型id和医保类型中文名 字段*/
ALTER TABLE `t_his_record` ADD MEDICAL_TYPE_ID VARCHAR(32) COMMENT '保存his的医保类型ID';
ALTER TABLE `t_his_record` ADD MEDICAL_TYPE_NAME VARCHAR(32) COMMENT '保存his的医保类型中文';
/*2021-3-5 gw 参数控制 修改处方时是否删除原处方*/
INSERT INTO `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`) VALUES ('da9bb3a585014f9f94872f0de6a70c62', '000000', '000000', 'UPDATE_PRE_DEL_OLD', '修改处方后是否作废原处方', '1', '0-不作废、1-住院和门诊都作废', '2021-03-05 09:46:01', '70810c874405453b99c6c2cf72296fe5', '管理员', '0');
/*2021-3-5 gw 增加炮制品转化、校验 */
CREATE TABLE `cbkj_web_api`.`b_mat_category_mapping` (
 `ID` varchar(32) NOT NULL,
 `MAT_ID` varchar(32) DEFAULT NULL,
 `MAT_NAME` varchar(500) DEFAULT NULL,
 `MAT_PREPARE_ID` varchar(32) DEFAULT NULL,
 `MAT_PREPARE_NAME` varchar(500) DEFAULT NULL,
 `SEQN` int(11) DEFAULT NULL,
 PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
insert into `cbkj_web_api`.`b_mat_category_mapping`(`ID`,`MAT_ID`,`MAT_NAME`,`MAT_PREPARE_ID`,`MAT_PREPARE_NAME`,`SEQN`) values ('006569bc05344de5bffd88baa5acf19c','6bd15657db6745868b53e85604061c4a','竹茹','7b31427058a5424fbbe8acc825a9608b','姜竹茹',2),('00cdb65e66c648dbb15348b5b895e337','becd1cf4b738442a86d5164dcdbf262f','干姜','f0382b19b90648d2a395e4f294e6f6dc','炮姜',NULL),('013014c5c2f641619889aeaaa592cdce','97e4ef5fe2dc4270834693c4835893e1','槐角','2aea74dafef947fd84450beb00f5703e','炒槐角',1),('0134b0eae02242ce8db6dab5c149a016','b5707bae50a643568e8142506610162e','生地黄','e2de2de975c6432fa20dbeeab94263c6','熟地黄炭',NULL),('01970cec92ac43d1b283037a35ae6c34','211df79477f6498d9366411fb47754c2','花椒','e4b376fba78447d7bd8a1f644b6c8658','炒花椒',1),('026a7df13d094d2e9de97c8648be2d49','b658e389b822469394ec9a02c42cfa7f','紫石英','1447bc1f6654425fbb6f5e890ffb3995','煅紫石英',1),('026ea59a0c6a43809f29aed23bfd6c30','0a9eaac17b5b4f9085b8a27a879c7390','鸡冠花','d3626f5a7f684267a4c72944e3bace5b','鸡冠花炭',NULL),('04b06f2daa274016a992d35753cb5be7','29b32168391149fab54a758da8418425','黑豆','1c40c3e62bf4478fbee2b82b66bdd197','淡豆豉',NULL),('05666826f8f748aa84fc06f69d2c2a0f','f1642307a3424108a27310f38cb9db5a','禹余粮','db710f2b554e478a868a294cd6cb897c','煅禹余粮',NULL),('0740a460c3f94eeb8a18259ac4ac4302','75eab7fd31b248238e402cd87d13b95a','黄芩','cb66968afa834c7e9c157f2c14b5ef48','黄芩炭',3),('08045c4b14d244008ef02a6f4a15f5fb','3f448c75436e4a8d904e4b91ef869d91','白芍','f90176f243134a91b4ebf6551cacb3ae','土白芍',6),('09b346ddbc2c42179b3f54431d8f5591','2b34b5f054204b6a8cba2cab93652a37','白术','296941acee0c4b4abc45363b860a3b8b','炒白术',1),('09bf6a8369a641f59054fe384d5c00a5','f05c8c55325c450e8feb2b2df75fc1a5','青皮','4217923f8fff4997ab7f6723a2b1009b','醋青皮',2),('0a69f0ea4dc748c8a0bf46f58d8a0b79','912dc02f337e440a8690dca76dbfa4a3','肉豆蔻','7e9f71d15a4f456084f0c9d4701c58d3','炒肉豆蔻',1),('0d800e0ce3e54f11b618a2b7f659fa4a','ba4c2900e7a54247be085e00b7111f9b','九香虫','223ccbcd733c4b6891ab3f110a55c510','炒九香虫',1),('0e45fa8403cd4bfcba7aef18405f5cb0','cbac6db3ba194556af9c239b9d185119','前胡','ef1fe02fba914332b70a9af5fc2ac395','炙前胡',1),('0f408d2f232c44fdbe6a6042973e68a3','d5a9133d76dd4e4f9c03300ac48cabd4','黄精','3e72fa1544654ba080fa2be85b6edfb9','酒黄精',1),('132911d44ea94c368c4d1c50b6e59518','92b74152ba7e410c80508f03248b493e','橘核','5b3989a843d643d79c74476de7913421','炒橘核',1),('13f1ebeaddb149efb1e8041aa0cf9a27','a1ecb73ad11445289352fd18712cbbed','巴豆','c8453c96834f4d1f8d7d79dce3762dcc','巴豆霜',1),('140dff8bbcef4b7aaafae3bcda20f72a','2533d214199c4e78895763a2d45fcf86','莲房','e1840b97f722457b8e54823612e0f38a','莲房炭',NULL),('14205f809e404e8888f5cceef1b19530','b5707bae50a643568e8142506610162e','生地黄','e25e0f6e617545988ab7cee0ec0de24a','熟地黄',NULL),('147a2df5e9964e628204e1c5956a9a49','286025b2d74a475280888d437a10c1e4','白扁豆','954060f2ae4045fd8a5d4f20e974a07b','炒白扁豆',NULL),('155781e6bba048e8bce52338a67f9269','440767b791ec417da2cd8d29e9706ae8','建神曲','75cdb141b8094fbc99653ced9acd2e6f','炒建神曲',1),('1728c25de7be42d9b87a212ce0eb3b7f','e1fb5bda5d434782992fdec1eb9e27c5','补骨脂','a64a8dc45e4a451f893fe60515d65606','盐补骨脂',1),('18b49c5913cf4325a28672d2a9be45c6','6d060480796c4b40b52bfdc19508275c','贯众','a502b6e407aa47d59787903651fb5fc4','贯众炭',NULL),('1a9d9f6ad47e4d01ba7dae7413f7dc44','dedef1751c494c2698cca485435e855d','大黄','d2bcb8b963d644e688155009667efe00','酒大黄',NULL),('1af95e94407c417b896aa839f55f29d7','3cce7eb0e17a4146a0aa5e77d2c6ce7f','艾叶','ae4cce96adc1468b83cb3d5e3daa3ee3','醋艾炭',NULL),('1afa371c1ca84033ae26a4d09b254ef3','d70113660edd4f9aba8aa1cad0a39d17','苦杏仁','639e2a1261e7490bb2de3d34f50c2669','燀苦杏仁',1),('1b4c4e0d59934ceeacdddd955972b40a','040442e797d04cec95a88f924edf1815','六神曲','db9cf3c886324801abe3792dccd38674','焦神曲',1),('1b8b77f22f1a4998ba3be1ff6e18b828','8d9d30db53864f2797f8a3f2745dc845','瓜蒌子','089ab37d53434046b0dc1c3e6f45e906','蜜瓜蒌子',2),('1c1d02eff1ff4d019f991abb9f482ef6','d7bbe394c5ba499bb6391533eb56a37b','珍珠母','b57be2e151704e96aec27efecf66ff2f','煅珍珠母',1),('1d6a96918450412f86db0ed10fefe40a','75fcbc10a9824299bdd121dad2e27e98','水蛭','59e196c7a1c84853b897e877dd8bd4bc','制水蛭',1),('1f47695a91fa4927b7d334bd78b4b021','c2b946215f3a4ed7bd8d2dd2a646c9ea','蒲黄','4edcdd0fddf2421db0d5c6223e887f06','蒲黄炭',1),('1f5013cf6a3f42cd9a9a3c2b711d84e8','7eb61eb2f5f04d8b929f4be434df67c3','大蓟','1db97773e9694aef91cbeb685cf6204e','大蓟炭',1),('20e598290ad640ef865000519cd4f9ba','62bbe4cc908c49968b2c0d69e2d04066','玄参','59fd0baf22634fd4b68041ad2ff741c5','盐玄参',1),('211b9cb1f24f4393bd9fa29471f57f50','f9fc4eca893249b58f28ec07809a3202','槟榔','911c34a0392849f58160fd6ba35b06bd','炒槟榔',NULL),('21aaa8023289435a8f6e347f980733ac','8a94fa4acb844fc89112b0fc890d197a','槐花','121f99ed17304d998f449d6cc2bca036','炒槐花',2),('22049769919c410bb1bc6a691abef32c','62565a7e93c945c7bba4f86c6cfcdf97','附子','301261f7138e492bac971b6a47c2b334','淡附片',2),('2270314e51da467b92d1ea3c3cee6dd2','68d7904240ab436fb2775d6a18873ef8','柴胡','4c195dd8465a4c0f8f3d4fee54e2116b','醋柴胡',1),('22a81d1c551e4e82b063bd9b9b2d06d6','e10438b83b384e31bfd77d46fce085d8','升麻','b7ce91849a5f47bfbf3387fa3a170fa6','炙升麻',2),('24d84913eb714d4d98dcb0647f13084d','ec0339556c0d4ed386fb42c73a895ecb','沙苑子','54909b604ce44ce285ea6b63f40f69bc','盐沙苑子',NULL),('2585aabae6494f61845d366049d728bb','13b4a3a6844b46588c811b393e06572d','苎麻根','05c515bee035468ab813654cc7395cec','苎麻根炭',1),('2667d50dfcc84da9be52b4d059e80f4c','4c254bd2a3d44d5480663b11d44c040a','当归','a4adc1969e4c4484918bd3b3ca4bdc19','酒当归',1),('27cc41c04cfd45df8d6ec7b1124cdb49','8b1c2c638ee0444ead823f6ba6a98ee8','百部','58c9589ebe314b7b89ff92c4d22eda04','炙百部',1),('293df74c03794965b83d30368735d68d','b7a0072dbcd447d9aed5de7453883dab','瓦楞子','ffe752e67907457aaf3dd04866cf7dcd','煅瓦楞子',1),('2a5668386ff043eb8498dbbea244404e','fc86d6a6fa624c03bcb63c59c2f19bc6','水半夏','df7a0f37a8cb4d26a5a8c036e3ddaa56','姜水半夏',1),('2a603f5f25fb457a92b6773df8019be0','7ed1f248737741dead494640469b1fb6','椿皮','ccbfe8efe8b84244a384f658b0eba574','炒椿皮',1),('2b92137b20b2475d96320c599a459afe','43b3abbf3ba24ef6ba5441d959ce61df','生姜','6547029910a2452cbc33d4a162c3ddaf','煨姜',1),('2bd46c84dad4443c991cd35460910348','d4b9f82a02f94160baf24327417ed21e','桑枝','e5ea4989a25e4ca68393d9c6b35301a3','炒桑枝',1),('2bd93c355d9e43128eff75c5fca86cf5','4c254bd2a3d44d5480663b11d44c040a','当归','7d264b664b5f4059af22523fae607c9b','当归炭',2),('2cca7f45351f4bfb923e470f397e99f4','c2b946215f3a4ed7bd8d2dd2a646c9ea','蒲黄','d7f1ad48a00f4c3eb05e3de4e86df9a1','炒蒲黄',2),('2cfeab166b5645659258ffcf2312cccc','f0d39ddd63e940ce9c3ca3475fcc9037','郁李仁','c0b0ef4daa654f04a6edf936bb42284d','炒郁李仁',1),('2d58da6bd76d43e2890b9dadc3779c6e','4ac1e98232b84e3d80c6cfa5b7b5a286','远志','b709f2293bab4c5999926cf32585d00c','炒远志',1),('2d668bec9c9843268e67bd63baecdc77','dedef1751c494c2698cca485435e855d','大黄','57a3636e7fda4f5c946633b933a36b76','熟大黄',NULL),('2d803c7e847d490e86de846021cfc382','7d8222d85ed44d629613d5a34b0bb9b6','党参','b47807e15ce14f65a36754873115e6c1','炒党参',1),('308bd81d88a44ce5b188deafc0233dd5','50498f25956a4205816c644eee426254','自然铜','428cf6a601364e5da75441c3978f9ff6','煅自然铜',1),('327fa73729ab44c8b902196fe35777be','1580dc261e1e42428c8c586243da5806','鸡内金','fd7fffdc2100433d8b1fbb8400b7f946','炒鸡内金',NULL),('328c062ed5b74be8bb251fdf377c87e8','f62c153341f746b2ae0c67ab51ea0a5c','木鳖子','f68384b441914f1c83ff97d28d088ae5','炒木鳖子',NULL),('330b8e98f0e640299e130c571b53295e','3f448c75436e4a8d904e4b91ef869d91','白芍','f7318e0a8e8442bdb87460eae508df6b','白芍炭',2),('3367ea90072f47f88fba4bd032384a10','080140197e664361a3cd2c14abb9abf4','防风','5bc3bb8c8a0740a8a0e9881c3e70bedf','防风炭',1),('33a8dc3882ed4021a982f4040cd225a1','d016e1869684401f9cab302a1cb61ce8','紫贝齿','63de4fd5970e430c8d76b7e44bfc3ba5','煅紫贝齿',1),('33f4e5ebc7634e55a17438644125d7c7','a801f881ae2042578436b0972fe145b2','黄连','9faba51a2f54407aa764a0f55a869469','姜黄连',2),('3420fd2177304359a27ef44b66c11938','3b9a87ddea264f68983694abd8213b7f','麦冬','4ff25bceeb784af58c0e3371ad7908e8','辰麦冬',1),('37a18e63360c446d8f9a35073fede867','ad4f1d8f9919498fa0147d5d2a73434e','瓜蒌皮','351a5e8bd1db4509ac1bd7acbd02d3ca','蜜瓜蒌皮',1),('37cd83119f1f4b4199765ecb6757a1bf','fb5bce253e9441bfaf142172e76d17e4','海蛤壳','d2e43c49749740069c290b583147a82f','煅蛤壳',NULL),('37fbd194434647cf956ca46900944dda','1d12cb54ea5b4d79831b887f17f08dac','延胡索','8a5d0b54f997461a8502b5a061c7ff2a','醋延胡索',1),('382cb0d55398452fb6167f3069642a2a','3f448c75436e4a8d904e4b91ef869d91','白芍','054bdfd59db748d9aa982cfe66517a55','炒白芍',3),('38fe005f4392409badb8a167bfd98b6e','bf7f0da35d8146f6b5eeddd55ed98a87','龙齿','b37f831d036a4e83a5bf15bf0d909841','煅龙齿',1),('39a3c7f971ea492f8702a1af8739f64c','d70113660edd4f9aba8aa1cad0a39d17','苦杏仁','a0b276f0e2c943b5b0990eb825f78215','炒苦杏仁',2),('39d4b1051fcb453a988b3cf71304dcc1','a801f881ae2042578436b0972fe145b2','黄连','bcfa501bac004404802f2939062a3732','炒黄连',3),('3af0d9ca811c4be7b6888eb7e7231669','d5a9133d76dd4e4f9c03300ac48cabd4','黄精','ec0f150c4ea54751bec51c72152da9de','蒸黄精',NULL),('3bda44d17cab47cb83ef00dcc7df8f53','e10438b83b384e31bfd77d46fce085d8','升麻','b309977a6d2541fa8bde2eed7c4932fe','升麻炭',1),('3c040daf23f045659f6cca5d3fd2fa38','60855dee28144db7914f67ac01075f99','硫黄','81ce62f11b474b518dee029990902353','制硫黄',1),('3d8b7f593d3847b3ac9b56355c7fc5ce','3f448c75436e4a8d904e4b91ef869d91','白芍','8e09178830334f9c8055a8b7bac66838','酒白芍',4),('3f3f7a609b164dde8e5c1dd18fadad6f','e791ff1954564fc089e2e9592b9f34df','五味子','2fc097b784bf452086fa0040b6f3e19b','制五味子',1),('4094641e314041809cd33af5d8980bf7','656c4a891d914326b5a673fdec178abe','青蒿','e15419a463074967b241e9b2eaec4931','炒青蒿',1),('40cf37ca99dd4c14b923962828bab3fb','2b4e73029aab4d099f65ba40115881c3','半夏','469cc91caf994fd285f101585c20bbba','麸炒半夏曲',4),('426d91ed2a574b948d2b0eba8eed805b','9fc935e6c5664df7953d503252a0bd7b','龙骨','16f1ad0a78d544428bcbb67d3e5cd13f','煅龙骨',1),('4378b2931bdb4691bbdc0ac6fd9da8b9','e39d48e3af2a49c4b5250de1120bb1f6','款冬花','65e4b7ff78944361bfea778f5d1ec7b8','炙款冬花',1),('43c962f19502485faa89788756a4882c','75eab7fd31b248238e402cd87d13b95a','黄芩','196e6f481ed246f29ebffe75428facdb','酒黄芩',1),('46d69bb1727a4eb2a3cd2f17b1adf9be','409d7621ccf64670bd5adccd17b73abf','山药','540ec02fe2934706a296a57214b1dfe3','炙山药',2),('47bfde8cae4a4a08a212ba816275179f','fe2a069931db4fa19982e5cbf61953a3','厚朴','86506dd51aa64e4dbf9265e9f19d9c33','姜厚朴',2),('4a24545df18340d6aab2c890c4403eb5','f9a4e5a6e8504447a30385c7ba856b53','人中白','8780120e909c487ea0bc2e54828b6def','煅人中白',NULL),('4c6cea7c3b4b409c914ce9a9cf21ec47','36861dfdd4d14fcc9e2bc23ea241e04d','紫苏子','b513bdbf662b4dad9d57db8ec8704e1a','炒紫苏子',NULL),('4c9a6dc107dd419d9bf99ba76a4d712e','d9bb541fe8d349989c50411484190f72','藕节','9ebd92bfd7a649aeb324cfa6686421cf','藕节炭',1),('4e1c0b0b37ee4804960930217d4f9803','79908797c1894b638477758d1f8766e4','阳起石','bd7b05ea1d56464a88fece5ce6a3beff','煅阳起石',1),('4fd7d691743b45f4ada933cb818df53b','d7404f526b7c4e6198959b4f3522d664','鳖甲','2e4ba39481764179b2c2cbc172e17133','醋鳖甲',1),('50103e74e265474f8e482333e05dc3fe','e66d040ad72743d4a977021183770224','牡丹皮','d905eaba0efe4db9bdbb44d16e0df63e','炒牡丹皮',2),('50992df366694f9d80af856eec009d68','8b1c2c638ee0444ead823f6ba6a98ee8','百部','5ff9f5be18ac4c9db946291f7954b66f','蜜百部',2),('52266cd769c941d1847dcc934fa640dd','14aa4bca1b6648a5b407d5eaca2bab63','知母','5836bebcfd4f46d2b438af86986316ce','盐知母',2),('541b54573a824a6986d30a15e333a74d','62565a7e93c945c7bba4f86c6cfcdf97','附子','17e8bf3d2b6b45b9b251bd145d2524b3','黑附子',1),('54516c10b63247c8b9dac895b52bcebf','d978ed52ee744c529f572c145a844ddf','葶苈子','f81cd6b0176b486693d9eba791a4b652','炒葶苈子',1),('54e3c691bb194076822e5b1daa485c3e','ce528cbff1374af3b26521b49b5b8c5b','马兜铃','b40fa54155f6489fbded2cb5b1a84a1f','炙马兜铃',1),('5547d320851547bdb8f78a845b0da112','a801f881ae2042578436b0972fe145b2','黄连','b51d2520b49d4b06b6b019d4e37db8b9','酒黄连',4),('55896e4cf36a4bfca977b33688f3c574','2b4e73029aab4d099f65ba40115881c3','半夏','d7274130e7304ccba1e37b10e8f7bd62','法半夏',3),('565fcb719092468e97f43e88e3d5059a','c38526aa2a0041c795291ed3f3928198','女贞子','792038513e0349e2bb4e4f043cc9ba6a','酒女贞子',1),('5678d9a7117b42ec817964c0b578a38d','b7e3071975c34f0cb8917d813e73ea5c','蔓荆子','b9ea76bb8c8e4ec4952ce12ea5cd8af0','炒蔓荆子',1),('585503d85cda4cf19aa8674b3649f7a8','0d3b4aec2e764553a94d77781e693d59','栀子','6a0d1a32ff8b4d8297fb4d6cdc23fa96','炒栀子',2),('5ad784649ec047d78fd745626f8f3d93','fe2a069931db4fa19982e5cbf61953a3','厚朴','0e83d42a4bf0433b90fb71f45bda5a1f','制厚朴',1),('5be7673eea3f492cab979ce10b467478','6d9ade2304354e22a488cca61126669e','莪术','d8923e96862f42e3a78f2b44dcd48de7','醋莪术',1),('5df1ab46545b468f969083e7d5172267','75eab7fd31b248238e402cd87d13b95a','黄芩','7f45e7198bf94cfba84ffbd088549f55','炒黄芩',2),('60c5264fbff843498f3bb2af3321de62','5bdb28196f79463290ea7ee6b01f266a','狼毒','39df1dcf8c5d42f6b670c1ba21fc8d3e','醋狼毒',NULL),('616ec4fa15d64c03bb0ef148a2079e55','9ef33b6d990a4f5d86c53ba45fa3f232','麦芽','2131e18607e94e3d8fdfa529a7ff0a0f','炒麦芽',NULL),('6170f004931f4bba808eed03f02d8e56','122dff3cfff84fa190dff8811c18fb89','麻黄','99c0549617554b518b6a61f2350c1fe5','蜜麻黄',2),('61fcfb77d8eb44509c0e8ff927cb8c62','3f448c75436e4a8d904e4b91ef869d91','白芍','877fdf412e50443894be09f063ead6bb','醋白芍',1),('6264666632464ee2b59b68629bafb9d2','f7823befd3b14b4293c73098a6d5179b','赤石脂','ea4928746f2b426faad27c319dc88bcf','醋赤石脂',2),('627a0cdd656f45c3af5ae4c2f2f84a93','a094574e2361426586e13645874a60f4','五灵脂','206cd674a4214903b3463095b0a93ec5','醋五灵脂',2),('628bdda7346e412d8db4f9828c0a9f1f','190f7a000df040e38c407c6856d198ce','芫花','f08fe2dc6425430b9d6ad134dbe0a21d','醋芫花',1),('65203f673de84aeeb7c84bcadc4cf345','f8b0b3793eab4a9fa428d7ec313cdda9','怀牛膝','a8cdea9aafe44f19b96fa6c199cea7d6','酒牛膝',2),('6571c61dd1864129b9deb752b3106bd9','6d90c6756e7f450992903036f4869aeb','西洋参','4521e1c951624ed3a62ece77b75b0521','西洋参粉',1),('66dbc24059e24a8982a00e9559aaa54e','040442e797d04cec95a88f924edf1815','六神曲','eecc02a2b6b144549c80258a6e3fc175','炒六神曲',NULL),('66ffee2b725b4e55a7f82b37359f7866','dedef1751c494c2698cca485435e855d','大黄','adc1c8502d934cf29d262b8058874e0d','制大黄',3),('6786b135591147c68517408ee3349cad','dac4ad47c86c4112a928c8ab899242ff','僵蚕','d4c82d35278143d4a4560dd15e79ea17','炒僵蚕',1),('6802b9aa1a4547ed828784751a6d4a87','4136582ed6e44106908904ffdc7de4bf','石决明','9838ceef25e544bdac41342166f8a453','煅石决明',1),('68589316e1d3405b9473f7190becd691','a216b311351e439eb3cc42ba9f26bc72','巴戟天','1a94c69c587b4595ab35beda2a7c992b','盐巴戟天',1),('68604aa75c9c405fa24c50c76e83a922','14aa4bca1b6648a5b407d5eaca2bab63','知母','1c25939fe4934503ab48b3814166dcdd','炒知母',1),('690fdba6f4f6474dbf6e24941d19e1b9','122dff3cfff84fa190dff8811c18fb89','麻黄','60135ffcc97e4b27be7b54e16676ee99','麻黄绒',1),('69eb5441247347328940856d8da84ad4','1c900811bbac4e4d8689b819150b2bb1','骨碎补','d0516d4c11264708936c9f9e974321c4','烫骨碎补',NULL),('6b2b24bc0e2149f9aeba2e318555d1a8','dedef1751c494c2698cca485435e855d','大黄','ac70c0136cc241d0a742ac0ba24ba11b','大黄炭',NULL),('6c7ed742100947238007430491e0ed4a','912dc02f337e440a8690dca76dbfa4a3','肉豆蔻','0cc3c1741bd34da49c19eb75d1ae9420','煨肉豆蔻',2),('6cb6945c8bbd46f1b5aa1203492583c4','7d8222d85ed44d629613d5a34b0bb9b6','党参','d8ae4a30eed245f8b077c558eaad574c','蜜党参',2),('6cf39ea652fb42f3a179e79bfef21d19','2d68ec41aded4051aad476c312e069a4','崖桑皮','a572303d203a4f43a03db2a0efd73d7b','蜜崖桑皮',1),('6e302e1f7bb54c689ae905c5eb903cfc','7270060c8384453bb7c194603ad34aab','吴茱萸','c46721e860dd4597b07d4b62a7d5ab76','制吴茱萸',NULL),('6e528d0d7cc2421f982a01d25edb242c','6a62b9f3814b4623817ad44831068e1a','茯神','2ebb3f9d1bde4583982d9d4377b27d2a','朱茯神',1),('6e7c8b1ece5b4746bfa5f32947fdff25','99179e8ef8244c7f8a59a714ea4eb6eb','桔梗','7fc7a410a6274c4599f3a129efe67755','炒桔梗',1),('6ed169a14971430796c8fae24008c575','13aafd3dcd044fa2bbf8ec55f6eb769d','车前子','5edcbed074d942029073655e81e3b338','炒车前子',2),('71f7597f9aef4feb8dae4c1445496a54','a9512dd51712401cbd2a5d66cc6062b2','杜仲','b3dd6e3030274cd59436240e06d63da5','盐杜仲',1),('720ef98f95f048fa90644657f48a446f','07b37cdd9dec430082798994eb9d6706','海螵蛸','47ec7d65b9a541448e563bbf41bc645c','炒海螵蛸',1),('7269c86a0ec54d41a408c1d20a145281','6788fa3f0f074094aad1721c93765dd1','菟丝子','0524e78214524e6fa231fd2b6bd77692','炒菟丝子',NULL),('72bcb5ab61a04267af0ba21d8869c3c9','f1361472172b43df9f9861eb95c90fa7','川乌','0049a7f06cf84a43bc4def9abc22d69f','制川乌',1),('72f58712d15948da84f1de345a2b1605','92d98d48cebf4be6a40cbd5f1a9f0df0','商陆','ffbf751e898b4fd0b4f835f0b3162949','醋商陆',NULL),('7454103df65c49bb80932d25a13cddfd','6ba66e1031b945b8a3e027850d2cb1dd','牡蛎','5692d36c809f419e9f1796c18eefae29','煅牡蛎',1),('74e4b5ba04b2478094810957f17589bc','b42d8cf549f64e6186f06634b41c4e1c','阿胶','d2ac0d17b763493ab494dbe495af6bdf','阿胶珠',1),('7532e551f4974464ab8c02188fdcdad9','4c254bd2a3d44d5480663b11d44c040a','当归','40e13e579483490a8eb88a7b68f4b63a','炒当归',3),('755d90864cf745ae9cb92a96991f2820','996c39bf5c51443fa7536f0826cba96c','枇杷叶','5cbacadf4a504f0ea355bba240373961','炙枇杷叶',1),('77ac2d5d25e34e2a99e2103955a0b82f','1fc06ee64fba4759a763339e30568efc','黑芝麻','bdcc2254bb7648d085d1b6d4922a4905','炒黑芝麻',1),('77e53c161b3c42ac9ee76749e5a894e7','8a94fa4acb844fc89112b0fc890d197a','槐花','2571eede3bcc46218855e0b9ee4d909a','槐花炭',1),('7805c42a89014aa0bc56d46ceff0e22d','7a2de45383714ca19d8374e03de33a58','桂枝','dff2b3f6106f4abe84477a26bac4a31a','炙桂枝',1),('7862449c02f7430dbbec6d9e08a1f516','9ef33b6d990a4f5d86c53ba45fa3f232','麦芽','dfb40993786442278d711e06b7f30f3b','焦麦芽',NULL),('78aef88486524c32897a8598edc938b8','1d571a7dfe9a410ab8f3a07b43b8c65e','乌梅','004f4ce70b9e45478717ba4d98e48311','乌梅炭',1),('79368cf92664417585fc858fef0a5ed0','33f5239b45964a50bd6aad31bede116f','甘草','55de13cb209b4861a5c69c6bff1fb55e','炒甘草',2),('7967ceb1189c4c84bef0f57783399811','9914846e8f814cd8896ea6ff3d003732','水银','1959b8fc97dc409c88c17fd751e5ef46','制水银',1),('79790f522cff4261a5127b4d902a8454','bf56997a776d4b37af5e84214f874b97','黄芪','179b67bc6674436982e28f698d8b4340','炙黄芪',2),('7b0bcea7260c45f6854edac6d3ce4eb9','0d3b4aec2e764553a94d77781e693d59','栀子','e79939a5397947d890cdb374e2438540','焦栀子',1),('7d86f994450841a1b52b93c67a7e8ad1','3985cf5a2b8643fd8146356a1de5f89e','金银花','e74397d7c4a64f7a86d97b48f4ab2eb9','金银花炭',1),('7e5ca94e597b404592bd2c718cd717b5','44a0b65cb53b4eee936e29d9157f3259','地龙','092d391eabc44f1e8e064cda32b35c12','酒地龙',1),('7ef273f607ff478bb39d0bce9df6f8ed','ec0339556c0d4ed386fb42c73a895ecb','沙苑子','52b8661d9feb4365a000b762f6d2e122','炒沙苑子',1),('7f0aa9bc67c24ff7afdafc7042a8d03e','145f82c0387a45c7808a637b8eae9162','小蓟','04ae68e5585844f191ef48eeddc6e462','小蓟炭',1),('7f9c44a20d64489cbd406b0839f51b77','d2547fa8ab314fa9852d55e7c31d5dcd','赤芍','837682a65dbd4e26ad68df22b8f23c36','炒赤芍',2),('808e6a96154048f5a2c77d3b59511333','b7208ddbd2814c6b9878cb016d120ee9','磁石','ceadc338026b43b68e946f9b8a4832b4','煅磁石',1),('80f22200aa7948c2a94c75e63ffe69c6','231a3ba907384275977d3dac1ab842f4','白前','b9cd81b48ad14e3692555a0a8dd46650','炙白前',1),('8134d364a97c43a2b974082d1427a8b4','27f63360afeb44848d023270c8ec9882','木香','40e84b7b3941430db3a34ec399067dd5','炒木香',1),('8260004bb4bd4100bc7850c470fb1163','44a0b65cb53b4eee936e29d9157f3259','地龙','aff2fa82d1f74dd19297b94d4578f7e5','炒地龙',2),('837cfc678b08491b89f9b134385dd306','2b4e73029aab4d099f65ba40115881c3','半夏','e97730e051b548779b599fba11fd7229','清半夏',6),('85c860d61bfd48c5af0b16e27f483304','2b4e73029aab4d099f65ba40115881c3','半夏','988ad7d24580415389f516f1b2962db9','陕姜半夏',5),('863ee1362e2e45c28a7e0b0d8ab6cdf6','fc86d6a6fa624c03bcb63c59c2f19bc6','水半夏','ae90713930ab4ef9abf0f0f090dab384','清水半夏',2),('878bceeb0b1b4f68b3c16f4c1967b3bf','989689470f17496382704bcecb3adc7c','王不留行','affa23102e0a4174969c1d157416b184','炒王不留行',NULL),('87bfe5202f8548ebbb4c8eee0d5e8e55','03d60737aafa4e57936b9d168a7e7376','酸枣仁','1797f9907ce54da3acbc1165136919a9','炒酸枣仁',1),('88253bf396bd48009a77bf67ca45f0d6','8080a27b6d0a4bd6998b51f5ce017a1a','山楂','3491348a8e2f4347899d54afbabef81e','炒山楂',3),('88abee3e74f84284a198ad0b35fabaf5','a0da90e9d8b64efc91d457ed0be3ebd7','千金子','cef1599697b246d788bc32a82bd8ec97','千金子霜',1),('8a2a94deb7ee4901b60993b3a846dc8a','ccf52506d02247949790cea37cc93f2d','天南星','dede9d0ef7604729a03feeab7deb3001','制天南星',1),('8b4f07541aa34aea8caaa77e58667acf','766c8652f858496299a73483ea97a48b','黄柏','6f5484f5992b4e0fa7757d254ff64a81','酒黄柏',2),('8c348a62c1824e6795a7c940098b761f','a6ec5ee5c01e47a0af8df903b461f29b','白薇','adfdeb1827c84c22a7099f2577bb5e8c','炒白薇',1),('8d047f7243fd4bf69016fa94d2ef0ee6','e02042141a404d69ae4e09eff0600ca9','川牛膝','30f99433088246c494d1fc96bb469f06','酒川牛膝',NULL),('8ea2d6f592524f8ab4e62a603e827d39','932a3e0016c34d919a8dfbd8be39ff35','枳实','1af73c431d754fb68ba6b2c96010e135','麸炒枳实',3),('8f5781fda0c84501a653907ab1f4e687','13aafd3dcd044fa2bbf8ec55f6eb769d','车前子','58f3c5e6dc2f4d6a925d1bc3e6bf80be','盐车前子',1),('917859e767ae4adbb083c8a4582b32f3','f7266c9db20049b396a6f44801a72e6a','荆芥','815fd2abdb32487b855064567a5581c3','炒荆芥',NULL),('9251ec8a6c354c1c8e8c9a5784ac7cd5','1feb501a426a4a839259e947bfea103d','刺猬皮','8077389aae8345a99baf46710f5b6339','烫刺猬皮',NULL),('94057db0bef34352b672fba1ba70cbf4','1c40c3e62bf4478fbee2b82b66bdd197','淡豆豉','70317daf0b47452ca063dc64afaa3a75','炒淡豆豉',NULL),('94363a05f18d4fc781a1145f9c02af82','4ac1e98232b84e3d80c6cfa5b7b5a286','远志','d2839d97eae54cbebc94b0b23e61534d','蜜远志',NULL),('959d3255ddba491fa5c8d7a635390ee6','25b26436bffc43f687b26dfe23e15566','薏苡仁','a99b7f79f00c44d1bdafc163fd366967','炒薏苡仁',2),('985d4497ddf44d269bee65f801e5767f','11f7add89bec4e0c97592f4573613cab','谷芽','351d7d9b47b448db82de86ab664de291','炒谷芽',1),('98611cfee27e4596b3267d94ed2000f4','954afb14064f434fa0265d7739ff9f21','牛蒡子','3358364e7258448a908f4a8be090a6e5','炒牛蒡子',1),('98f1482a36db49d8804e54733bc46a0c','becd1cf4b738442a86d5164dcdbf262f','干姜','6e260de211dc4bd8a4bb08baa7a2730a','干姜炭',2),('995459098b9c4e8e9b5d0ce34c72a6d8','25b26436bffc43f687b26dfe23e15566','薏苡仁','0bd1275507984373be687238b0955272','麸炒薏苡仁',NULL),('995eb8d165684e1b95e9494691378605','25b26436bffc43f687b26dfe23e15566','薏苡仁','7e38f316942e409da501ef46fe7bc34c','焦薏苡仁',1),('9a45a442b02849cdb52801c294838408','5409bd654c3e43dc812b3c32cdbae328','石膏','a0e10c8e2863439c97d0ef9d1f5dcf11','煅石膏',1),('9bb7979087c54da88921246d079a625f','1d12cb54ea5b4d79831b887f17f08dac','延胡索','c086375de9ed4ea085341710e856ad6e','炒延胡索',2),('9bdb955455bd44b483887faf558e68ba','32a4eba08c554439bb7bb5619a9b0e24','肉苁蓉','a9902597cc7242f2909f696a0e759440','酒肉苁蓉',1),('9cba2b36ad5144dabbe5bc5371b8fcd1','162c4aa86f7a49ec8ac1ed99519132e9','地榆','131bc6b37fd94767b7d2e534bc3a9ce2','地榆炭',1),('9d610a59fb9247fab1df6a945ca5e0f2','932a3e0016c34d919a8dfbd8be39ff35','枳实','659feaabb1594b678a3ee1c1788cf7d7','炒枳实',1),('9e0e168bff34468faf11d7af32e06e1f','8d9d30db53864f2797f8a3f2745dc845','瓜蒌子','c56e3f9f4b0749af92f06f6c351650e6','炒瓜蒌子',1),('9ea1cb3705a7475ea0410089644e0267','bda41894d3d3479ab3527da40cf34ad2','桑白皮','f1b7fbd7260846d9b54aa2b2d94e5c32','炙桑白皮',2),('9eceaf13fd9e4e9092a7ac76ae01b49e','7e561402e9d6460fa9ef03378d5971a4','续断','c65748614bb2451e8bc66a57f78ea4b9','炒续断',1),('9f72df98389f48ea8763f3ab4d534f7a','9eba86e9c5c34de1a87de0cc4da4f1b1','白矾','de6183251bab4fefb1c8931302d20f34','枯矾',1),('a04c1991117e49ffa16a585f4fb7e510','e985508b0d80457fa7013cebf040eb91','乳香','e0945c967bde4bc5b4fe07d2c44cf567','制乳香',1),('a31837ac716145949e075474759d7fae','76e357076d6746e4bcf4aac9b783b4b3','龟甲','58969aebd079422e9a7553e8ea04e6ac','龟甲胶',1),('a404258e87a742799105d3dedaccbf47','60135ffcc97e4b27be7b54e16676ee99','麻黄绒','0b91df28b69b4596a1764c236cbc4a29','蜜麻黄绒',NULL),('a44881b14d004a92a47179e7d0d58898','90fcb41bf13741268ac4e79b8d4a801f','穿山甲','fcdb185a20984eaca1e15305dfc5a488','炙穿山甲',1),('a45469cf070e407eafa09790e3c5c4eb','2e975e9399ca441bbbe0ce05c8bc9f39','何首乌','d97022da8e244ec790b820c8940c2f34','制何首乌',1),('a50bc386371c451e8878488694f9e3fb','766c8652f858496299a73483ea97a48b','黄柏','c6a4cc21105e4984a2f1123477c817f8','盐黄柏',4),('a595b73d7e564086858ef75851760a43','2b4e73029aab4d099f65ba40115881c3','半夏','ec97287db3954d918074ce53f2d09775','姜半夏',2),('a658c6f9b9154560ae8ae534b588b361','f7266c9db20049b396a6f44801a72e6a','荆芥','cd7f1b8708734a2a8c8797d168c966f2','荆芥炭',1),('a6c18f6885ce4e07a5be402b6149009e','2a5a40ee89f3470d826ac4b9c373cfae','槐米','d555f167e034437fbda0f0b3fdc7eab2','炒槐米',NULL),('a7181990db9c4e44ad0361f0f7aa012c','9b453d0ed1794b96a53293a7f12f28b8','苍术','bd3c8271ca844c5dad0413c26fc83db2','炒苍术',1),('a800eb1cb95f4cd78d950684237e642e','b1685678e9c443e78b6ed0ae0b80fdb9','稻芽','b90bab6ad83c4d7f8f5b802c942ef73a','炒稻芽',NULL),('a98ecb97c84b49268d19f66a45ae7260','69df12e9ee2a4b0c8ab11d8c135a8be9','葛根','a07db0d9e393435bba64e31e32af19dd','炒葛根',1),('ab9aae5ce3ac49fb805c2a72fb3dd3be','59d3f6d3fb0a4a09b4392d4bb88f2b80','胡芦巴','e03cd17a48ce4e7d9e807a55b53e7b3e','炒胡芦巴',1),('abcf818f7abf445aa60d63e8ed8ee8f6','1c0071e984ea44e6aae4d7810d0ecb51','郁金','bd650e3453dc4d388e0cf106617ce920','醋郁金',1),('ac98077aa3e34942b9b5e6f1a92842e3','1d571a7dfe9a410ab8f3a07b43b8c65e','乌梅','54d0706fa748416c9de5eed1bc16addc','炙乌梅',2),('ae294dcaacb941dc91371aa71d55e5d3','3cce7eb0e17a4146a0aa5e77d2c6ce7f','艾叶','1555fcbf80744c87bee81303c1435fe8','艾叶炭',1),('ae502f8a4412405c90b28d064a76ff90','42be36890a3f4f459f90ff164e9f2579','石榴皮','240d34ee69374816ade9ec05b188365e','石榴皮炭',1),('afb0ab5c847b4c94a701ac394742d22c','080140197e664361a3cd2c14abb9abf4','防风','49ac41ffdb6143be9355728551149cbb','炒防风',2),('b02a33cdf96b4dd4ac0aa9f0c755de7f','ea7ab400d04a4b1194c7e941af18e514','京大戟','bceeeeeacb814282b172ab77eec82aeb','醋京大戟',1),('b3c4f3189388415e9376fbbde65566e0','bf35dc18c7984109a410e28fec2ab108','白果','51672a32871f41289dadb350a988cdac','炒白果',1),('b60d3c4907ea4b6fac817011e784c015','56be0b9593b243fc8c138fb7f3cd1468','草乌','d1ab5256a842428eaaa170c73c0b229a','制草乌',1),('b62512ae80304700b777d08367b05753','2a5a40ee89f3470d826ac4b9c373cfae','槐米','a69c5ef20e7e468e935bf7355554598d','槐米炭',NULL),('b68defc3817c4609a9ead7c4cf330ba4','6a65dfdece66469bab326b415aaf6632','关白附','ad18d9ae907642c1a596f91892499ee3','制关白附',1),('bb09e14d1ac44e0199e6a98ec79751dd','718dba97485d403a901c39e97f1c53fb','白附子','9d23143a0b904b5994787efdd0c70720','制白附子',1),('bb13dafb3ddf4533a808efa2566afdfc','a67ed6590c6d4d67bb0c35e678a64f08','诃子','298ce44d313649c686fd81e37460ba35','诃子炭',1),('bdae7d1eba2a496faa2479e8022aa32c','8526677a6ae8416696861d0f4ec3cda2','苍耳子','8450eae830054a5484d3a0bcd2933feb','炒苍耳子',1),('c16b6fb23d1c4c96803ba1d1243d2783','413f618ea4ec46d2a9fed9d63b4fe99f','莱菔子','b5b5f070b03a413d964b251ebaa6ab50','炒莱菔子',1),('c18422880ff64ac695cb7200047bc267','76e357076d6746e4bcf4aac9b783b4b3','龟甲','7e5ddaba1c1749c980c4842288e28c88','醋龟甲',3),('c2300695646b482d9a4fa51dce6efa6c','eb0ee93929c94e46b9bd82af344e4e91','龙胆','eb339a78457d4ec3a7af73d3c8bce072','酒龙胆',1),('c299dc9d89c14599bdcd0ab73dd8182e','f05c8c55325c450e8feb2b2df75fc1a5','青皮','d6d354db9ecf4fe1a3d121f01b7d54d7','炒青皮',1),('c2e22dc4599d4275b324cf6d642fa0ec','2b34b5f054204b6a8cba2cab93652a37','白术','715e9f7fc8f94f1ba5bd1af6a9f8249b','焦白术',2),('c301f662d3024e8ab62c7b4b6ea63a0d','91f0f53609014df287e8991225e6031b','白茅根','4d492fbfabae44a08ec64ad6bb816079','白茅根炭',1),('c3ab636a5def469a86d0273089790af6','8080a27b6d0a4bd6998b51f5ce017a1a','山楂','183768e1a3bd4997947e10afcfa20a8d','山楂炭',2),('c3c13f0725d3457687ffc876e9976b41','4ea5a7c45e874145bf40bf812000cafa','枳壳','a3b0d9e995034945947e4d5b70488217','麸炒枳壳',2),('c45ebb635d674f14bd82029343800be5','7d64985f0114427a9e2c90e28374462b','礞石','3bcc7cc374c54d55ba1f09e24d917f0c','煅礞石',1),('c48e09e71bff459a856ce7284794b34e','1973739d2b6d4d998c37f616a332789c','刺蒺藜','08bea2c19b6247aba0bb29086d804f61','炒刺蒺藜',1),('c56cd6b9d0834ab9b5207051846433c6','a044d17205dc4ead812d955fc2bd6048','旋覆花','0cb23e5fe1d448128e1df39a1102fd2c','蜜旋覆花',NULL),('c5e6794fe7af46d9bf710a3122442c17','a801f881ae2042578436b0972fe145b2','黄连','3f091e34e0494b35a218aa2cb1520da9','黄连炭',1),('c6e9f7c8f19644579c57ebf52f448661','1e5a5e49613e41eabce32e63d5d008cf','决明子','1af65a52b5674605839e19b854f46ec3','炒决明子',1),('c7b8a5d660f4414ab9bdc3014aaf2153','173653bf4f5b4397bb4e1f2945854939','狗脊','4dfc5fa8ceb24b08b0226d4fbf2e211b','狗脊炭',1),('c9699689f00a44cc86286429bf08541f','6791f3fce0a6479698687f15afa8db28','荆芥穗','d47d163a70154ee892f0afab8ccec0ed','荆芥穗炭',NULL),('ca680ffa2adb4633bb0ad2687daa1173','033c3d37a7e94a7d9d76bdac4b2eb50e','木瓜','b1f5390f2b9042edbfb0c01ebb922254','炒木瓜',1),('caa062790eb943d297bfc9f1a13e646e','3f448c75436e4a8d904e4b91ef869d91','白芍','58120e641ff646128814f340d7aec57e','麸炒白芍',5),('cad48357b8624ff983a1d8e0e93e23ad','a67ed6590c6d4d67bb0c35e678a64f08','诃子','701b4d24914f4c9d8bdd3e7e342aeaf9','炒诃子',2),('cb1edbc7781c4cffb5858ca3b346e044','69df12e9ee2a4b0c8ab11d8c135a8be9','葛根','0584b23aac824041870c18643284203a','煨葛根',2),('cd73e23be9244487a272be60a702befd','8ff5332893f542a0a250ddf9abce7adc','川楝子','deafd136f5584bbeb4f39ed9604ffcc7','炒川楝子',1),('d02c2f40a3a949fcb5e6e27ca7d4ee67','90fcb41bf13741268ac4e79b8d4a801f','穿山甲','9198818ad50741c29d67d62d05001194','炒穿山甲',2),('d1bd6230948b412bb100cf247da25f4c','47c6296cd7cd484498a2a39fefd17888','代赭石','ed461f05b9c341dea49f60924759f646','煅代赭石',1),('d2811f960ac646cd9d3fcd21fc6ea156','409d7621ccf64670bd5adccd17b73abf','山药','fb346b5c72c84c39b887fedbc6099ac3','炒山药',1),('d2a7407c366249da8dfb6147ba4129f8','4e744d5cc5df439a8ab0fb0750655a85','茯苓','c820974e5df342b4ad95132eeac24b86','辰茯苓',1),('d39c1b7241c5447ca1089ececc0d4d5d','7fbf0ca1591540c88717120ce7f25cbc','橘红','2dcdc366ecd643e6b90fb06f5a86e7ca','盐橘红',1),('d3f2431ab9a44c80afae077b363ce59b','62565a7e93c945c7bba4f86c6cfcdf97','附子','690e322f7c1b4edb9eb8978714cd643b','制附子',4),('d4b2328189ee4a42a8cd4ce4ad622995','62564ac88a9740769e7c85f6d8ef2b6c','丹参','47239d2c687d40cd82fed9e88253b94d','酒丹参',2),('d5a45fce48a44909b67f2b151daef053','36861dfdd4d14fcc9e2bc23ea241e04d','紫苏子','9568f61a58344393ae9e0e48e0e58b74','蜜紫苏子',1),('d5bcb2380b954fcc8c354d596502fecf','f2a03b90c11d43478c6bfbd634b3c1ff','火麻仁','94050719193b4c98806a7b257cc4bb55','炒火麻仁',1),('d7a289b19590487d9e1e363cfdb4aa39','7d8bf39b32ab4171b414799bc15e9407','紫菀','fee5985f63d94781a78e508a745b6db1','蜜紫菀',1),('dbb99092105d48188f82a80d1c32bed0','1feb501a426a4a839259e947bfea103d','刺猬皮','37a82750b5d946618eed0e0a4955afeb','炒刺猬皮',2),('dc13e025b1f244f0a74cbc1961610fd6','359d7b44889d49de93a5d5f71dec123f','棕榈皮','be6efd339f1d4eb286c04588e6ae2b40','棕榈炭',NULL),('dc85d99be3db48cab641fa235cde8d0f','8080a27b6d0a4bd6998b51f5ce017a1a','山楂','e51a9dd7646e467db4ff36ba177c09bc','焦山楂',1),('dcbee32816f54ee9a82612b51949238a','e66d040ad72743d4a977021183770224','牡丹皮','8855503b7f76467ea2a1c4ec3df69d44','牡丹皮炭',1),('dd836d26bb964056b3eca3057492a934','fa1ef5658fec401b9a21d24fdce86476','侧柏叶','88781eb2564b45f68f3b1449ccf9f300','侧柏叶炭',NULL),('ddc37927a5e54ab2be19400fbf1271cd','76e357076d6746e4bcf4aac9b783b4b3','龟甲','d8a73e75ae00481aa068534cc6fbe15c','炙龟甲',2),('de03615f67ed4f4281b5935708b7eded','8204cba5e9194ea7b6e48c011a12a1e0','马钱子','1fef0fd6e4aa409eb81a4edfd8acaec9','制马钱子',1),('de9059603a8e4fd2997930a79c8768da','e5feb65dc6ff46d196924ba672ebf629','益智仁','1d55ab43045d4b67996388b1083e254f','炒益智仁',1),('dfcdefc9cb2b4f46b6c2ad3d77dd3e16','62564ac88a9740769e7c85f6d8ef2b6c','丹参','063dd510c6a4495ba8e2318963d0bc78','炒丹参',1),('dfe8fef3663d4991bca5948e5aba1341','e10438b83b384e31bfd77d46fce085d8','升麻','5bd05853f28c4f5ebd42327355b7c5ee','炒升麻',3),('e2ab60aa4f25462ba85e93714e1816e8','e1fb5bda5d434782992fdec1eb9e27c5','补骨脂','dead5edd35e2453d86345a71c5fac5d7','炒补骨脂',2),('e30cc51d3682488b86bde43c69e51e77','81d2e4d9d5f340699d5d8a4f903adea5','全蝎','e1a9d8e037064fbaad0b52a0e5757e8a','炙全蝎',1),('e31ab02d96594861b27b848a7afa78e9','766c8652f858496299a73483ea97a48b','黄柏','f49c842062bf49c7b396dd9fc0e4bf53','炒黄柏',1),('e363c6d6cd8048e8b8678cc69c44c390','62565a7e93c945c7bba4f86c6cfcdf97','附子','6962b437997b44c590760b31c5db8269','白附片',NULL),('e37626199c964469a289dc37c9184f1a','c1132d818dd94dd8a86fb858a2a03c02','三棱','c0d427cae38b4150aeacc16e919b9fb5','醋三棱',1),('e3a2373d425c4f038f4f03a0685592f2','f8b0b3793eab4a9fa428d7ec313cdda9','怀牛膝','52d37a9c5c814cbeb092ca32b7cd1f22','炒怀牛膝',1),('e447bbacc9014797a389ec63cd191bc9','27f63360afeb44848d023270c8ec9882','木香','b55ac3657f884321a16d59e37c5a7be1','煨木香',2),('e4cec11bd61a4da48a6b2a6587e7f3f7','c77c1483c848496b87907e391728fef3','芡实','1cb90410d9524176bbddb7090b978aeb','炒芡实',1),('e52d52769f6242f6883ad3272f96c733','33f5239b45964a50bd6aad31bede116f','甘草','ec64206130d242f1b5970de79291908b','炙甘草',NULL),('e5bc581dcfa14c5699ce91c1bbc19c64','2658903ab87e4b679eaef38ec684d637','莲子','5ff6c228555f440ab3b8647d8eb2f1ff','炒莲子',1),('e71e872712354ce5baf37b4a12b4dede','6788fa3f0f074094aad1721c93765dd1','菟丝子','de878fba46a94984b1d319899fd0cffd','酒菟丝子',1),('e792710b65d7412ba8330dd0d32e5119','f5af0b234a7841e6bdc8d745c712c9ed','川芎','e506708face04b448e26b030285b8fe1','酒川芎',1),('e816c9be3ff545988ecb04a0b8d34a4d','f7823befd3b14b4293c73098a6d5179b','赤石脂','ed95a2a431324405b74360fb8eb4ca64','煅赤石脂',1),('e8982632b3bf4d6b983c82b60e2cd586','4979411a2d524ffd98cd06cf34a31a7c','香附','d7eb989a02d44bc5ae67c74d13e6601e','醋香附',1),('ea97e3e6d2f144f2a3e2a9da9f3aa2ea','836e752e46fb4885801d3da4beb08b7b','白芥子','285c526732be488bb63d1f7beea45e18','炒白芥子',1),('eb1b1e3ab26e480b9f6c998ac12aa292','766c8652f858496299a73483ea97a48b','黄柏','d9c29ac7fbb94d9e91d554be00829cc1','黄柏炭',3),('ebe32f1d237e48139c24a45afeeb72ac','bf56997a776d4b37af5e84214f874b97','黄芪','740915701d05400d84e854679bfdaa24','炒黄芪',1),('ec8afdeb7f344f0cbbdc6593fdcf53f9','bda41894d3d3479ab3527da40cf34ad2','桑白皮','20254babd3ad46eeb53971734b18ac69','炒桑白皮',1),('ed3a5f29245941999bca8cae11054414','2b4e73029aab4d099f65ba40115881c3','半夏','5a58a606e73c4befb3e9ff546e7b1f57','半夏曲',1),('ed9af41cf5b546be964fee51cf673d1e','bed9bfc9f9e14e5fac687aca8151bd58','枸杞子','e6810b0fbb3b4aaf9c84b9479a87953f','炒枸杞子',1),('ef750156611c4eb794ce1c996f88f6d4','173653bf4f5b4397bb4e1f2945854939','狗脊','e0a928e629364de997b54f825a7c45df','烫狗脊',2),('f034a32f768843769552f80c5b8a7471','8204cba5e9194ea7b6e48c011a12a1e0','马钱子','82349aa03cac4fbd87a2bdd6db180411','马钱子粉',2),('f047ae3cb1f444f082ed1ca76682091e','a094574e2361426586e13645874a60f4','五灵脂','42c153ed44424d2ab7e71b2bdd1a6985','酒五灵脂',1),('f05fc357db744b9693569f2052750074','14aa4bca1b6648a5b407d5eaca2bab63','知母','bd141cfdfa1348a4be4635c1fcd05756','酒知母',3),('f09f902fd25b41b393ad24e64b64d5ef','6bd15657db6745868b53e85604061c4a','竹茹','7ac2b25edcbd4de9b61119fbfc975807','炒竹茹',1),('f0e30578a16244418f2df7fc3307ba50','0d3b4aec2e764553a94d77781e693d59','栀子','4837e4fb32d34b95868e52f9e017b495','栀子炭',3),('f20be70223a447569444910f4c256f85','932a3e0016c34d919a8dfbd8be39ff35','枳实','37eec0359d62433f996e3e91138ec4ea','炙枳实',2),('f21a43967e1f47a49584c94d1fee2d30','6e3248f356084216b1fc4102c164a932','桃仁','5aa24a756081411ab53b0bc95a159ce0','炒桃仁',NULL),('f22cca155e7f4a25becfc4527fe760b2','c1d631be283e440ba88ef7da0b5786be','陈皮','637e8a0a2c5b41de964bdc551ed7038e','炒陈皮',1),('f336540511d140a4a16bbc59b3287e91','a094574e2361426586e13645874a60f4','五灵脂','e634cf53e0114193afc01adb2c38b058','炒五灵脂',3),('f3af2ef1559e444da775ccad7907bff2','7e561402e9d6460fa9ef03378d5971a4','续断','8804d6575e894caf9dda2fcc01e4cc9c','续断炭',2),('f47bbfa26f9c41c69299504bf2ba9cc5','4ea5a7c45e874145bf40bf812000cafa','枳壳','f6b245b34f38424e9190861bc8aaf5c2','炒枳壳',NULL),('f62b654534cf43bba6252aefe38abe6d','040442e797d04cec95a88f924edf1815','六神曲','9151214c4b9f4f9f8ffcc1c53d27ff7d','焦六神曲',2),('f704b666025449509ca60d6ec921e05f','ccf52506d02247949790cea37cc93f2d','天南星','5207f94674264f179ad6e2a6ea0a21f3','胆南星',2),('f8193bd52f0d409e830c877cab4caad4','68d7904240ab436fb2775d6a18873ef8','柴胡','56ecc522fb284fdc8b0497d53ee5add5','炒柴胡',2),('f832d85841eb4f64b418db46b5697926','fa1ef5658fec401b9a21d24fdce86476','侧柏叶','f38105dd6fbf4ad995c5d59372413fe2','炒柏叶',1),('fa52bee6e6aa4d51aa665b3231c00cfe','99179e8ef8244c7f8a59a714ea4eb6eb','桔梗','220e9ccfca9445a68777ea69376cdae2','蜜桔梗',2),('faece29b4686498cbf444e7c668c881c','6e3248f356084216b1fc4102c164a932','桃仁','aee7f48559904cac8f49ef0d12e7fd24','燀桃仁',1),('fb39064eafb74832a604d99ec8ed7ec6','b5707bae50a643568e8142506610162e','生地黄','9ef06e711c774338b93a6711e43343aa','生地黄炭',2),('fbbb2a60857745c49b05e9a20ba4147b','d8fc6070c2db45839e1f20da8b6b652a','甘遂','2dfd7258c3f04368bf52653655d0ee12','制甘遂',1),('fc5b05f071724c8795f6f003f5620b5f','3cce7eb0e17a4146a0aa5e77d2c6ce7f','艾叶','01f7987cb3ab4051b294c49ea8cf0246','艾绒',NULL),('fdce9e8efb4045918b31f7809ada0ac7','c3094424febb4491bf221244c3c9e46d','桑叶','cb2072d2ea3543cd80611bff88fe9056','炒桑叶',1),('fe20a0eb0cf54d83bc4203d0b274a1f3','d12d8c0ba196486aba73114e7189b3b6','小茴香','c013c79c5180430ba01018e46e4344a6','炒小茴香',1),('feb3150a003e4d7495fbe5204aac3516','29b32168391149fab54a758da8418425','黑豆','c77030cb32b04900a869296597d8fa7b','大豆黄卷',1),('ff54905f3f1640c2afc719f46d98ce55','d2547fa8ab314fa9852d55e7c31d5dcd','赤芍','ad56893eaedc40e7aa80a9df719968b1','酒赤芍',1),('ff5e7ae05e2442c980b448714c1215cc','20d02b3a1fa341799b0a561736bfc018','柏子仁','36495de85a224093a04501763d542084','炒柏子仁',1),('ffc081f6b84d42c48966b9576a398610','956a42cebeda467f9c8531b01689be88','红大戟','e3446a418f63447c96b3dc4a8c9f5ae3','醋红大戟',NULL);
/*2021-3-10 gw 挂号表增加是否怀孕*/
ALTER TABLE `cbkj_web_api`.`t_register` ADD COLUMN `GRAVIDITY` VARCHAR(1) NULL COMMENT '患者是否怀孕（Y为是，N为否）' AFTER `VISIT_NO`;
/*2021-3-16 gw 药房配置与科室映射*/
CREATE TABLE `cbkj_web_api`.`t_display_bak` (SELECT * FROM `cbkj_web_api`.`t_display`);
CREATE TABLE `cbkj_web_api`.`t_display_mapping` (
    `display_id` VARCHAR(32) NOT NULL COMMENT '药房配置ID',
    `app_id` VARCHAR(32) NOT NULL COMMENT '医共体ID',
    `ins_code` VARCHAR(32) NOT NULL COMMENT '医疗机构代码',
    `dept_id` VARCHAR(32) NOT NULL COMMENT 'HIS科室ID',
    `create_date` DATETIME NOT NULL COMMENT '创建时间',
    `create_user` VARCHAR(32) NOT NULL COMMENT '创建人',
    PRIMARY KEY (`display_id`,`app_id`,`ins_code`,`dept_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='药房-机构映射表';
UPDATE `cbkj_web_api`.`t_display` SET APP_ID = '' WHERE APP_ID IS NULL;
UPDATE `cbkj_web_api`.`t_display` SET INS_CODE = '' WHERE INS_CODE IS NULL;
UPDATE `cbkj_web_api`.`t_display` SET dept_id = '' WHERE dept_id IS NULL;
INSERT INTO `cbkj_web_api`.`t_display_mapping` SELECT DISPLAY_ID, APP_ID, INS_CODE, DEPT_ID, NOW(), '' FROM `cbkj_web_api`.`t_display`;
ALTER TABLE `cbkj_web_api`.`t_display` DROP COLUMN `APP_ID`, DROP COLUMN `INS_CODE`, DROP COLUMN `DEPT_ID`;

UPDATE `cbkj_web_api`.`sys_department` SET DEP_ORIGIN_ID = DEP_ID WHERE DEP_ORIGIN_ID IS NULL OR DEP_ORIGIN_ID = '';
ALTER TABLE `cbkj_web_api`.`sys_department` CHANGE `DEP_ORIGIN_ID` `DEP_ORIGIN_ID` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '第三方科室ID(一般来保存his科室ID,没有HIS科室时等于DEP_ID)';

/*2021-3-16 gw his科室表没用,删除*/
DROP TABLE IF EXISTS `cbkj_web_api`.`his_department`;

/*2021-3-17 wt 处方收费统计中转表*/
DROP TABLE IF EXISTS `cbkj_web_api`.`t_prescription_charged_number`;
CREATE TABLE `cbkj_web_api`.`t_prescription_charged_number` (
  `ID` VARCHAR(32) NOT NULL COMMENT 'id',
  `PRE_TIME` DATETIME DEFAULT NULL COMMENT '开方时间',
  `INS_NAME` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构名称',
  `PRE_MZ_ZY` VARCHAR(32) DEFAULT NULL COMMENT '科室类型',
  `DEPT_NAME` VARCHAR(32) DEFAULT NULL COMMENT '科室名称',
  `PRE_DOCTORNAME` VARCHAR(32) DEFAULT NULL COMMENT '开方医生',
  `PRE_CHARGED_NUMBER` INT(6) DEFAULT NULL COMMENT '已收费处方数量',
  PRIMARY KEY (`ID`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='统计医生收费处方数量表';

/*2021-3-18 gw 处方表加住院医嘱类型（1临嘱，2长嘱）*/
ALTER TABLE `cbkj_web_api`.`t_prescription` ADD COLUMN `PRE_ADVICE_TYPE` VARCHAR(1) NULL COMMENT '住院医嘱类型（1临嘱，2长嘱）' AFTER `PRE_OLD_NO`;
insert into `cbkj_web_api`.`t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) values('70d44fddba7945a69c6eda34090e7276','000000','000000','INPATIENT_ADVICE_DISPLAY','住院医嘱显示项','[{\"label\": \"临嘱\",\"value\": \"1\",\"checked\": true},{\"label\": \"长嘱\",\"value\": \"2\",\"checked\": false}]','json数组，为空不显示下拉框，不为空显示下拉框，示例：[{\"label\": \"临嘱\",\"value\": \"1\",\"checked\": true},{\"label\": \"长嘱\",\"value\": \"2\",\"checked\": false}] ，label对应显示文本，value对应保存的值，checked设为true表示默认选中','2021-03-18 14:51:31','70810c874405453b99c6c2cf72296fe5','管理员','2021-03-23 11:17:44','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,'0');
/*2021-3-25 wt 国医大师保存辩证记录表*/
DROP TABLE IF EXISTS `cbkj_web_api`.t_record_master;
CREATE TABLE `cbkj_web_api`.`t_record_master` (
  REGISTER_ID VARCHAR (32) PRIMARY KEY COMMENT '挂号id',
  DISEASE_CODE VARCHAR (32) COMMENT '疾病编码',
  PATIENT_CONTENT VARCHAR (512) COMMENT '主诉',
  NOW_DESC VARCHAR (512) COMMENT '现病史',
  PAST_DESC VARCHAR (512) COMMENT '既往史',
  FOUR_DIAGNOSIS VARCHAR (512) COMMENT '中医四诊',
  PHYSICAL VARCHAR (512) COMMENT '体格检查',
  AUXILIARY_EXAM VARCHAR (512) COMMENT '辅助检查',
  JSON_STR TEXT COMMENT '症状详情',
  SAVE_TIME DATETIME COMMENT '保存的时间'
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT = '保存国医大师的辩证结果表';
/*2021-3-29 wt 国医大师保存辩证记录表添加疾病名称字段*/
ALTER TABLE `cbkj_web_api`.`t_record_master` ADD COLUMN `DISEASE_NAME` VARCHAR(32) NULL COMMENT '疾病名称' AFTER `DISEASE_CODE`;
ALTER TABLE `cbkj_web_api`.`t_record_master` CHANGE `DISEASE_NAME` `DIS_ID` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '疾病ID（修订）';

/*2021-3-29 gw 智能辨证菜单调整*/
UPDATE `sys_admin_menu` SET url = '/diagnosis/dialectical' WHERE mname = '智能辨证';

/*2021-3-29 gw 参数：处方未超出医保单帖剂量上限则自动审核通过*/
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`) values('e370c30f5ebf4ae4b72103d8f209a419','000000','000000','SYS_CHECK_DAILY_MAX_DOSE','处方未超出医保单帖剂量上限则自动审核通过','1','（1开启 0关闭）','2021-03-29 17:05:50','70810c874405453b99c6c2cf72296fe5','管理员','0');

/*2021-3-30 gw 药品明细：日最大剂量需要区分内服和外用*/
ALTER TABLE `t_center_his_ypmlmx` CHANGE `DAILY_MAX_DOSE` `DAILY_MAX_DOSE_IN` DOUBLE NULL COMMENT '日最大剂量（内服）';
ALTER TABLE `t_center_his_ypmlmx` ADD COLUMN `DAILY_MAX_DOSE_EXT` DOUBLE NULL COMMENT '日最大剂量（外用）' AFTER `DAILY_MAX_DOSE_IN`;
UPDATE `t_center_his_ypmlmx` SET DAILY_MAX_DOSE_IN = DAILY_MAX_DOSE_IN / zhuanhuanxs WHERE DAILY_MAX_DOSE_IN IS NOT NULL AND BAOZHUANGDW != 'g' AND BAOZHUANGDW != 'G' AND BAOZHUANGDW != '克';
UPDATE `t_center_his_ypmlmx` SET DAILY_MAX_DOSE_EXT = DAILY_MAX_DOSE_IN WHERE DAILY_MAX_DOSE_EXT IS NULL;
/*2021-3-31 gw 协定方*/
ALTER TABLE `t_personal_prescription` CHANGE `IS_SHARE` `IS_SHARE` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' NULL COMMENT '共享级别（0私有 1科室 2全院 3专家经验）';
/*2021-3-31 gw 协定方菜单*/
UPDATE `sys_admin_menu` SET url = '/recipe/appoint', mname ='协定方', `enabled` = '2', sort_number = '3' WHERE `mid` = '12';
UPDATE `sys_admin_menu` SET `enabled` = '1' WHERE `mid` = '13';
/*2021-3-31 gw 医共体处方分析菜单*/
#INSERT INTO `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) VALUES('15','医共体处方分析','/statistical/recipe',NULL,NULL,'2','14',NULL,NULL,'2',NULL,NULL,NULL,'0','2','1');
INSERT INTO `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) VALUES ('203', '医共体处方分析', '/statistical/recipe', NULL, NULL, '2', '14', '2021-03-25 16:19:29', '70810c874405453b99c6c2cf72296fe5', '2', NULL, NULL, NULL, '0', '2', '1');

/*2021-04-06 gw 协定方文件夹*/
CREATE TABLE `t_personal_prescription_folder` (
  `FOLDER_ID` varchar(32) NOT NULL COMMENT '协定方文件夹ID',
  `FOLDER_NAME` varchar(64) NOT NULL COMMENT '文件夹名称',
  `FOLDER_NUM` int(6) NOT NULL DEFAULT '999999' COMMENT '文件夹序号',
  `FOLDER_TYPE` varchar(1) NOT NULL COMMENT '共享级别（0私有 1科室 2全院）',
  `FOLDER_PID` varchar(32) NOT NULL DEFAULT '-1' COMMENT '父文件ID',
  `APP_ID` varchar(32) NOT NULL COMMENT '医共体ID',
  `INS_CODE` varchar(32) NOT NULL COMMENT '医疗机构代码',
  `DEPT_ID` varchar(32) NOT NULL COMMENT '科室ID',
  `CREATE_DATE` datetime NOT NULL COMMENT '创建时间',
  `CREATE_USER` varchar(32) NOT NULL COMMENT '创建人',
  `CREATE_USERNAME` varchar(64) DEFAULT NULL COMMENT '创建人姓名',
  `UPDATE_DATE` datetime DEFAULT NULL COMMENT '修改时间',
  `UPDATE_USER` varchar(32) DEFAULT NULL COMMENT '修改人',
  `UPDATE_USERNAME` varchar(64) DEFAULT NULL COMMENT '修改人姓名',
  `DEL_DATE` datetime DEFAULT NULL COMMENT '删除时间',
  `DEL_USER` varchar(32) DEFAULT NULL COMMENT '删除人',
  `DEL_USERNAME` varchar(64) DEFAULT NULL COMMENT '删除人姓名',
  `IS_DEL` varchar(1) NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
  PRIMARY KEY (`FOLDER_ID`),
  KEY `IND_FOLDER_TYPE` (`FOLDER_TYPE`),
  KEY `IND_APP_INS_DEPT` (`APP_ID`,`INS_CODE`,`DEPT_ID`),
  KEY `IDX_IS_DEL` (`IS_DEL`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='协定方文件夹';

CREATE TABLE `t_personal_prescription_fmap` (
  `FOLDER_ID` varchar(32) NOT NULL,
  `PERS_PRE_ID` varchar(32) NOT NULL,
  PRIMARY KEY (`FOLDER_ID`,`PERS_PRE_ID`),
  KEY `IND_FOLDER_ID` (`FOLDER_ID`),
  KEY `IND_PERS_PRE_ID` (`PERS_PRE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='协定方文件夹与协定方映射表';
/*2021-04-08 gw 协定方增加索引*/
ALTER TABLE `t_personal_prescription` ADD COLUMN `PRE_ORDER` INT(6)  NULL COMMENT '排序' AFTER `pre_mz_zy`;
ALTER TABLE `t_statistics_prescription_expand` ADD INDEX `IDX_DOCTOR_ID` (`doctor_id`), ADD INDEX `IDX_PRE_ID` (`pre_id`), ADD INDEX `IDX_PRE_NUM` (`pre_num`);
/*2021-04-12 gw 参数配置：搜索(协定方/方剂)处方初始类型*/
UPDATE t_sys_param
SET par_values = CASE par_values WHEN '1' THEN 'know' WHEN '2' THEN 'self' ELSE par_values END,
par_des = '方剂(默认值): know ; 个人协定方: self ; 科室协定方: dept ; 全院协定方: ins'
WHERE PAR_CODE = 'PRESCRIPTION_SEARCH_RECIPE_TYPE';
/*2021-04-12 gw 参数配置：搜索(协定方/方剂)显示模式类型*/
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`) values('5546e61081264d77a2f2614f80b2d048','000000','000000','PRESCRIPTION_SEARCH_RECIPE_MODE','搜索(协定方/方剂)显示模式类型','both','分类树：tree ; 单搜索：radio ; 两者(默认值)：both','2021-04-09 16:53:42','70810c874405453b99c6c2cf72296fe5','管理员','0');
/*2021-04-12 gw 参数配置：协定方分类维护创建嵌套文件夹最大级别*/
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`) values('acdfc7815cfb4f2091c5029d4e72fcea','000000','000000','APPOINT-RECIPE-LEVEL','协定方分类维护创建嵌套文件夹最大级别','4','自然数（默认4），-1表示不限制','2021-04-13 09:15:09','70810c874405453b99c6c2cf72296fe5','管理员','0');
UPDATE t_sys_param SET par_code = 'APPOINT_RECIPE_LEVEL' WHERE par_code = 'APPOINT-RECIPE-LEVEL';
/*2021-04-14 gw 统计功能项*/
CREATE TABLE `t_statistics_function` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `function_name` varchar(64) NOT NULL COMMENT '功能项',
  `function_source` varchar(64) DEFAULT NULL COMMENT '功能来源',
  `usage_times` int(11) NOT NULL COMMENT '使用次数',
  `create_date` date NOT NULL COMMENT '统计日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
insert into `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) values('204','系统功能使用量统计报表','/statistical/function',NULL,NULL,'2','14','2021-04-22 14:03:25','e50fba8706324202aa484c236c0422a0','2',NULL,NULL,NULL,'7',NULL,'1');
insert into `sys_admin_rule_menu` (`rmid`, `rid`, `mid`) values('575bf19c1fec4b44a9a5dff5bcad59de','b4a17b6b635c4de48f95178676905aa5','204');

