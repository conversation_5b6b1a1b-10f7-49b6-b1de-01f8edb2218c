/*5.10 2021-06-03 gw 增加单味药的备注*/
ALTER TABLE `t_prescription_item` ADD COLUMN `REMARK` VARCHAR(10) NULL COMMENT '备注' AFTER `CENTER_STORE_ID`;
UPDATE `t_sys_param` SET par_des = '1序号 2图标 3药品名称 4规格 5剂量 6单位 7用法 8产地 9单价 10库存 11医保 12计价总额 13备注' WHERE par_code = 'PRESCRIPTION_INTERNAL_COLUMN';
UPDATE `t_sys_param` SET par_des = '1序号 2图标 3药品名称 4规格 5剂量 6单位 7用法 8产地 9单价 10库存 11医保 12计价总额 13备注' WHERE par_code = 'PRESCRIPTION_EXTERNAL_COLUMN';
UPDATE `t_sys_param` SET par_des =
'HIS_CLOSE：关闭界面；
HIS_PRINT：打印处方；
HIS_PRINT_CLOSE：打印处方后关闭界面；
KIOSK_CLOSE：通过插件关闭标签；
KIOSK_PRINT_CLOSE：打印处方后并通过插件关闭标签；
SKIP_X：跳转至X页面（X为系统中的页面路径，以 / 开始）；
SKIP_X_CLEAR：跳转至X页面并清空处方数据（X为系统中的页面路径，以 / 开始）；'
WHERE par_code = 'PRESCRIPTION_CHECK_MODE';
/*5.10 2021-06-03 gw 开方页疾病选择框不出现以“.”结束的中医病名*/
UPDATE `zyznyxt_basic`.`b_disease` SET SHOW_TYPE = NULL WHERE dis_code LIKE '%.';