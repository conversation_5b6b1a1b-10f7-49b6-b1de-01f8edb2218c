/*v5.9.2的修订版本v5.11.1.2  用户更换科室时协定方BUG */

/*2021.6.23 gw 用户更换科室，自动调整协定方权限 */
DELIMITER $$
DROP PROCEDURE IF EXISTS `update_personal_rule_auth`$$
CREATE PROCEDURE `update_personal_rule_auth`(deptId VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci, userId VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci)
BEGIN
    DECLARE num INT DEFAULT 0;
    SET num = (SELECT (SELECT COUNT(*) FROM t_personal_rule_auth WHERE rule_id = '1' AND dept_id = deptId AND user_id = userId) > 0
                          AND (SELECT PAR_VALUES FROM t_sys_param WHERE par_code = 'AUTO_UPDATE_PERSONAL_RULE_AUTH' AND app_id = '000000' AND ins_code = '000000' AND is_del = '0') = '1');
    IF num = 0 THEN
        INSERT INTO t_personal_rule_auth (rule_id, dept_id, dept_check_all, user_id, pers_pre_id, create_date, create_user)
        SELECT pra.rule_id, pra.dept_id, 1, userId, pra.pers_pre_id, NOW(), 'system'
        FROM t_personal_rule_auth pra
        WHERE pra.rule_id = '1' AND pra.dept_id = deptId
        GROUP BY pra.rule_id, pra.dept_id, pra.pers_pre_id;
    END IF;
END$$
DELIMITER ;

/*2021.6.23 gw 参数：用户更换科室，是否自动调整协定方权限 */
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`) values('7ecf5cdd20af48a5955551a2856db8d7','000000','000000','AUTO_UPDATE_PERSONAL_RULE_AUTH','用户更换科室，是否自动调整协定方权限','1','0否 1是（自动把更换后的科室的所有科室协定方授权给用户）','2021-06-23 15:07:04','70810c874405453b99c6c2cf72296fe5','管理员','0');
/*2021.6.23 gw 参数：搜索科室协定方是否过滤当前科室 */
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`) values('7ecf5cdd20af48a5966661a2856db8d7','000000','000000','PERSONAL_PRESCRIPTION_FILTER_DEPT','搜索科室协定方是否过滤当前科室','1','0否（可以看所有授权的科室协定方） 1是（只能看当前科室的科室协定方）','2021-06-23 15:07:04','70810c874405453b99c6c2cf72296fe5','管理员','0');

/*2021.8.20 gw 药房使用范围科室调整，原来使用系统科室ID，现在改为第三方科室ID */
UPDATE t_display_mapping m, sys_department d SET m.`dept_id` = d.`DEP_ORIGIN_ID` WHERE m.`app_id` = d.`APP_ID` AND m.`ins_code` = d.`INS_CODE` AND m.`dept_id` = d.`DEP_ID`;
