/*v5.9.2的修订版本v5.11.1.3  用户更换科室时协定方 */

/*2021.6.23 gw 用户更换科室，自动调整协定方权限 */
DELIMITER $$

DROP PROCEDURE IF EXISTS `update_personal_rule_auth`$$

CREATE PROCEDURE `update_personal_rule_auth`(deptId VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci, userId VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci)
BEGIN
    DECLARE num INT DEFAULT 0;
    SET num = (SELECT (SELECT COUNT(*) FROM t_personal_rule_auth WHERE rule_id = '1' AND dept_id = deptId AND user_id = userId) > 0 );
    IF num = 0 AND (SELECT PAR_VALUES FROM t_sys_param WHERE par_code = 'AUTO_UPDATE_PERSONAL_RULE_AUTH' AND app_id = '000000' AND ins_code = '000000' AND is_del = '0') = '1' THEN
        INSERT INTO t_personal_rule_auth (rule_id, dept_id, dept_check_all, user_id, pers_pre_id, create_date, create_user)
        SELECT pra.rule_id, pra.dept_id, 1, userId, pra.pers_pre_id, NOW(), 'system'
        FROM t_personal_rule_auth pra
        WHERE pra.rule_id = '1' AND pra.dept_id = deptId
        GROUP BY pra.rule_id, pra.dept_id, pra.pers_pre_id;
    END IF;
END$$

DELIMITER ;