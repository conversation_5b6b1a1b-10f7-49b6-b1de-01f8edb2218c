
/*5.11.1 2021-06-06 gw 库存表主键调整*/
CREATE TABLE t_center_ypkc_c (SELECT `ID` FROM t_center_ypkc GROUP BY `STORE_ID`, `YAOPINDM_TY` HAVING COUNT(*) > 1);
DELETE FROM t_center_ypkc WHERE `ID` IN (SELECT `ID` FROM t_center_ypkc_c );
DROP TABLE  t_center_ypkc_c;
ALTER TABLE `t_center_ypkc` DROP PRIMARY KEY, ADD PRIMARY KEY (`STORE_ID`, `YAOPINDM_TY`);

/*5.11.1 2021-06-22 gw 知识库中药制剂表*/
CREATE TABLE `b_preparation_material` (
`id` VARCHAR (32) NOT NULL,
`name` VARCHAR (32) NOT NULL,
`single_dose` DECIMAL (8, 4),
`single_unit` VARCHAR (32),
`frequency` VARCHAR (32),
`usage` VARCHAR (64),
PRIMARY KEY (`id`)
) ENGINE = INNODB CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

/*5.11.1 2021-06-22 gw 知识库中药制剂映射表*/
CREATE TABLE `t_app_preparation_material_mapping` (
`MAP_ID` VARCHAR(32) NOT NULL COMMENT '映射ID',
`MAT_ID` VARCHAR(32) NOT NULL COMMENT '知识库制剂ID',
`MAT_NAME` VARCHAR(32) NOT NULL COMMENT '知识库制剂名称',
`YPML_ID` VARCHAR(32) DEFAULT NULL COMMENT '药品目录ID（药房）',
`YAOPINDM` VARCHAR(32) DEFAULT NULL COMMENT '药品代码（药房）',
`YAOPINDM_TY` VARCHAR(32) DEFAULT NULL COMMENT '统一药品代码（由开方系统生成）',
`CREATE_TIME` DATETIME DEFAULT NULL COMMENT '映射时间',
`CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建人',
`CREATE_USERNAME` VARCHAR(32) DEFAULT NULL COMMENT '创建人姓名',
PRIMARY KEY (`MAP_ID`),
KEY `IDX_MAT_ID` (`MAT_ID`),
KEY `IDX_YPMLDM` (`YPML_ID`,`YAOPINDM`),
KEY `IDX_YPDM_TY` (`YAOPINDM_TY`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='知识库制剂和第三方药品映射关系表';


INSERT INTO `b_preparation_material` ( `id`, `name`, `single_dose`, `single_unit`, `frequency`, `usage`) VALUES
('5ccea6c834f14fda9bd9e80c8a5cfa9a',	'痛风颗粒',	10,	'g',	'一曰三次','冲服'),
('a9da8ad6b061424cb3520b2bc119c038',	'益肾蠲痹丸',	4,	'g',	'一曰三次','口服'),
('ea911e97055f4bc591448f06667f5f2e',	'新癀片',	3,	'片',	'一日三次','口服'),
('f1d55df244f74945ad6a4d62d860269a',	'芙黄膏',	20,	'g',	'一日二次','外敷');

/*5.11.1 2021-06-23 gw 系统日志菜单*/
INSERT INTO `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) VALUES('208','接口日志','/system/log/interface',NULL,NULL,'2','22','2021-06-18 09:56:52','42fe8c53d2b04f55bba32919ea77cce7','2',NULL,NULL,NULL,'1',NULL,'1');
INSERT INTO `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) VALUES('209','接口异常日志','/system/log/interface-exception',NULL,NULL,'2','22','2021-06-18 15:53:03','42fe8c53d2b04f55bba32919ea77cce7','2',NULL,NULL,NULL,'2',NULL,'1');
INSERT INTO `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) VALUES('210','操作日志','/system/log/operation',NULL,NULL,'2','22','2021-06-21 10:50:06','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'3',NULL,'1');
INSERT INTO `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) VALUES('211','操作异常日志','/system/log/operation-exception',NULL,NULL,'2','22','2021-06-21 11:00:50','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'4',NULL,'1');
INSERT INTO `sys_admin_rule_menu` (`rmid`, `rid`, `mid`) VALUES ('b4a17b6b635c4de48f95178676905208', 'b4a17b6b635c4de48f95178676905aa5', '208'),('b4a17b6b635c4de48f95178676905209', 'b4a17b6b635c4de48f95178676905aa5', '209'),('b4a17b6b635c4de48f95178676905210', 'b4a17b6b635c4de48f95178676905aa5', '210'),('b4a17b6b635c4de48f95178676905211', 'b4a17b6b635c4de48f95178676905aa5', '211');


/*5.11.1 2021-06-24 gw 体质辨识Word，PDF */
ALTER TABLE `t_user_analysis_result` ADD COLUMN `word_path` VARCHAR(512) NULL COMMENT 'Word地址' AFTER `is_del`, ADD COLUMN `pdf_path` VARCHAR(512) NULL COMMENT 'PDF地址' AFTER `word_path`;

