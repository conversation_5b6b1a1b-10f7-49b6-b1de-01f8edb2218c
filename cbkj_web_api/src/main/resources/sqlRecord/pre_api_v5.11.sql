/*5.11 2021-06-08 gw 药品目录明细加字段*/
ALTER TABLE `t_center_his_ypmlmx` CHANGE `YICIJL` `YICIJL` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '一次剂量（中成药/制剂）';
ALTER TABLE `t_center_his_ypmlmx` CHANGE `YICIJLDW` `YICIJLDW` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '一次剂量单位（中成药/制剂）';
ALTER TABLE `t_center_his_ypmlmx` CHANGE `ZHONGYAOLX` `ZHONGYAOLX` TINYINT(4) NOT NULL COMMENT '中药类型（1散装饮片  2散装颗粒  3膏方  4小包装饮片 5小包装颗粒 6配方 7制剂）';
ALTER TABLE `t_center_his_ypmlmx` ADD COLUMN `FREQUENCY_ID` VARCHAR(32) NULL COMMENT '频次ID' AFTER `NOT_PAY_IN_FUND`;
ALTER TABLE `t_center_his_ypmlmx` ADD COLUMN `FREQUENCY` VARCHAR(32) NULL COMMENT '频次' AFTER `FREQUENCY_ID`;
ALTER TABLE `t_center_his_ypmlmx` ADD COLUMN `FREQUENCY_RATE` DECIMAL(4,2) NULL COMMENT '频次系数（次数/天）' AFTER `FREQUENCY`;
ALTER TABLE `t_center_his_ypmlmx` ADD COLUMN `CONTENT` VARCHAR(512) NULL COMMENT '内容' AFTER `FREQUENCY_RATE`;
ALTER TABLE `t_center_his_ypmlmx` ADD COLUMN `EFFECT` VARCHAR(512) NULL COMMENT '功能主治' AFTER `CONTENT`;
ALTER TABLE `t_center_his_ypmlmx` ADD COLUMN `USAGE_DESC` VARCHAR(512) NULL COMMENT '用法用量' AFTER `EFFECT`;
ALTER TABLE `t_center_his_ypmlmx` ADD COLUMN `APPROVAL_NUMBER` VARCHAR(512) NULL COMMENT '批准文号' AFTER `USAGE_DESC`;
ALTER TABLE `t_center_his_ypmlmx` ADD COLUMN `DAILY_MAX_NUM_PREP` DOUBLE NULL COMMENT '医保日最大开药量（制剂）' AFTER `DAILY_MAX_DOSE_EXT`;

/*5.11 2021-06-08 gw 处方类型备注调整*/
ALTER TABLE `t_prescription` CHANGE `PRE_TYPE` `PRE_TYPE` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '处方类型（1内服中药方 2外用中药方 3中成药方 4适宜技术方 5制剂）';
ALTER TABLE `t_prescription` CHANGE `PRE_MAT_TYPE` `PRE_MAT_TYPE` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '中药类型（1散装饮片  2散装颗粒  3膏方  4小包装饮片 5小包装颗粒 6配方 7制剂）';

/*5.11 2021-06-08 gw 制剂公用代码：用法 频次*/
ALTER TABLE `t_sys_code_item` ADD COLUMN `RATE` DECIMAL(4,2) NULL COMMENT '系数（频次：次/天）' AFTER `IS_DEL`;
ALTER TABLE `t_his_code_item` ADD COLUMN `RATE` DECIMAL(4,2) NULL COMMENT '系数（频次：次/天）' AFTER `IS_DEL`;

INSERT  INTO `t_sys_code`(`CODE_ID`,`CODE_VALUE`,`CODE_NAME`,`CODE_NUM`,`CREATE_DATE`,`CREATE_USER`,`CREATE_USERNAME`,`UPDATE_DATE`,`UPDATE_USER`,`UPDATE_USERNAME`,`DEL_DATE`,`DEL_USER`,`DEL_USERNAME`,`IS_DEL`,`DISABLE_DATE`,`DISABLE_USER`,`DISABLE_USERNAME`,`IS_DISABLE`) VALUES (30,'preparationUsage','制剂药品用法',30,'2021-06-04 16:43:27','','',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL,NULL,NULL,'0');
INSERT  INTO `t_sys_code`(`CODE_ID`,`CODE_VALUE`,`CODE_NAME`,`CODE_NUM`,`CREATE_DATE`,`CREATE_USER`,`CREATE_USERNAME`,`UPDATE_DATE`,`UPDATE_USER`,`UPDATE_USERNAME`,`DEL_DATE`,`DEL_USER`,`DEL_USERNAME`,`IS_DEL`,`DISABLE_DATE`,`DISABLE_USER`,`DISABLE_USERNAME`,`IS_DISABLE`) VALUES (31,'preparationFrequency','制剂药品频次',31,'2021-06-04 16:44:23','','',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL,NULL,NULL,'0');

INSERT INTO `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) VALUES('5acc0740bbb044808275b63621475432','口服','30','2',NULL,NULL,'zjyf-2',NULL,NULL,NULL,'2021-06-04 16:51:34','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) VALUES('de824134d70946da9ff1c2d3cbb55c18','开水冲服','30','1',NULL,NULL,'zjyf-1',NULL,NULL,'1','2021-06-04 16:51:21','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) VALUES('b85e252dc51111eba36700163f006620','一日二次','31','1',NULL,NULL,'pc-2',NULL,NULL,'1','2020-07-09 11:34:20','34d5ccaa2f014d69b766d68a3dba91ad','cbys','2020-11-27 17:54:37','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,'0','2.00');
INSERT INTO `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) VALUES('b85e28afc51111eba36700163f006620','一日三次','31','2',NULL,NULL,'pc-3',NULL,NULL,'0','2020-07-09 11:34:27','34d5ccaa2f014d69b766d68a3dba91ad','cbys','2020-11-25 19:19:54','e50fba8706324202aa484c236c0422a0','chenli',NULL,NULL,NULL,'0','3.00');

/*5.11 2021-06-08 gw 制剂处方明细*/
CREATE TABLE `t_prescription_preparation_item` (
    `PRE_ITEM_ID` varchar(32) NOT NULL COMMENT '处方明细UUID',
    `PRE_ID` varchar(32) NOT NULL COMMENT '处方UUID',
    `YAOPINDM_TY` varchar(32) NOT NULL COMMENT '统一药品代码（由开方系统生成）',
    `YPML_HIS` varchar(32) NOT NULL COMMENT '药品目录代码-HIS',
    `YPML_CENTER` varchar(32) NOT NULL COMMENT '药品目录代码-中心',
    `YPDM_HIS` varchar(32) NOT NULL COMMENT '药品代码-HIS',
    `YPDM_CENTER` varchar(32) NOT NULL COMMENT '药品代码-中心',
    `YPMC_HIS` varchar(128) NOT NULL COMMENT '药品名称-HIS',
    `YPMC_CENTER` varchar(128) NOT NULL COMMENT '药品名称-中心',
    `YPGG_HIS` varchar(128) NOT NULL COMMENT '药品规格-HIS',
    `YPGG_CENTER` varchar(128) NOT NULL COMMENT '药品规格-中心',
    `CDID_CENTER` varchar(32) DEFAULT NULL COMMENT '产地ID（存中心药房的）',
    `CDMC_CENTER` varchar(128) DEFAULT NULL COMMENT '产地名称（存中心药房的）',
    `MAT_DOSE` decimal(8,4) NOT NULL COMMENT '剂量',
    `MAT_DOSEUNIT_ID` varchar(32) DEFAULT NULL COMMENT '一次剂量单位ID',
    `MAT_DOSEUNIT` varchar(32) NOT NULL COMMENT '一次剂量单位',
    `MAT_DAY` smallint(6) NOT NULL COMMENT '天数',
    `MAT_NUM` decimal(8,3) NOT NULL COMMENT '数量',
    `BZDW_HIS` varchar(32) NOT NULL COMMENT '包装单位（存中心药房的）',
    `USAGE_ID` varchar(32) DEFAULT NULL COMMENT '用法ID',
    `USAGE` varchar(128) DEFAULT NULL COMMENT '用法名称',
    `MAT_FREQUENCY_ID` varchar(32) DEFAULT NULL COMMENT '频次ID',
    `MAT_FREQUENCY` varchar(256) DEFAULT NULL COMMENT '频次',
    `MAT_FREQUENCY_RATE` varchar(256) DEFAULT NULL COMMENT '频次系数',
    `UNIT_PRICE` decimal(18,6) NOT NULL COMMENT '单价',
    `TOTAL_PRICE` decimal(18,6) NOT NULL COMMENT '总金额',
    `MAT_SEQN` int(11) DEFAULT NULL COMMENT '序号',
    `IS_INSURANCE` varchar(1) DEFAULT NULL COMMENT '是否医保（0自付 1医保）',
    `INSERT_TIME` datetime DEFAULT NULL,
    `REMARK` varchar(64) DEFAULT NULL COMMENT '备注',
    `CENTER_STORE_ID` varchar(32) DEFAULT NULL COMMENT '药房ID',
    PRIMARY KEY (`PRE_ITEM_ID`),
    KEY `IDX_PRE_ID` (`PRE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中药制剂明细表';

/*5.11 2021-06-09 gw 参数：有效的处方类型*/
UPDATE t_sys_param SET par_des = '1内服中药方 2外用中药方 3中成药方 4适宜技术方 5中药制剂' WHERE par_code = 'VALID_PRESCRIPTION_TYPE';
/*5.11 2021-06-09 gw 参数：需审核的处方类型*/
UPDATE t_sys_param SET par_des = '1内服中药方 2外用中药方 3中成药方 4适宜技术方 5中药制剂' WHERE par_code = 'CHECK_PRESCRIPTION_TYPE';
/*5.11 2021-06-10 gw 参数：中药制剂显示项目*/
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) values('7ecf5cdd20af48a59f3cc1a2856db8d7','000000','000000','PRESCRIPTION_PREPARATION_COLUMN','中药制剂显示项目','1,3|3|4|7|9,4,11,15,16,7,17,14,5,6,9,12,13','1序号 3药品名称 4规格 5数量 6包装单位 7用法 9单价 11自费 12总金额 13备注 14天数 15每次用量 16单位 17频次 ','2021-06-04 15:07:04','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0');

/*5.11 2021-06-11 gw 参数：医保支付条件限制*/
UPDATE t_sys_param SET par_des = '0）不限制（所有人不提醒） 1）限制门特病人和住院病人 2）限制医保病人' WHERE par_code = 'INSURANCE_LIMIT_OBJECT';

