/*2021.9.2 gw 增加双签确认---安吉*/
INSERT INTO `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) VALUES('0eb2cf1330d74a938ff40105a730a530','000000','000000','000000','PRESCRIPTION_SAVE_DOSAGE_ULTRALIMIT_TIPS','处方剂量：超剂量提醒文字设置','医保限制报销|强制保存|超医保规定用药量','[提示标题]|[确认按钮文本]|[剂量输入提示]           示例：  医保限制报销|强制保存|超医保规定用药量 双签确认|双签保存|超药典规定用药量','2021-09-02 12:18:04','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');
UPDATE t_sys_param SET PAR_DES = '1禁用药、2忌用药、3慎用药、4十八反、5十九畏、6不宜同用、7孕妇忌用、8孕妇慎用、9使用注意、10剂量超标、11毒、12病症禁忌、13饮食禁忌、14禁孕、15超规定用药量'  WHERE PAR_CODE = 'SAFETY_EVALUATION_SIGN';
UPDATE t_sys_param SET PAR_DES = '0）不限制（所有人不提醒） 1）限制门特病人和住院病人 2）限制医保病人 3）限制所有人'  WHERE PAR_CODE = 'INSURANCE_LIMIT_OBJECT';