/* 2021.7.5 gw KFXT-543 增加医嘱时间选择 */
ALTER TABLE `t_his_record` ADD COLUMN `ADMISSION_TIME` VARCHAR(20) NULL COMMENT '入科时间（格式：yyyy-MM-dd HH:mm:ss）' AFTER `PRE_SMO_MONEY_CODE`;
ALTER TABLE `t_prescription` ADD COLUMN `PRE_ADVICE_TIME` VARCHAR(20) NULL COMMENT '医嘱时间' AFTER `PRE_ADVICE_TYPE`, ADD COLUMN `CREATE_DATE` DATETIME NULL COMMENT '创建时间' AFTER `PRE_ADVICE_TIME`;

/* 2021.7.7 gw KFXT-576 用户执业机构 */
CREATE TABLE `sys_admin_practice` (
                                      `practice_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '执业ID',
                                      `admin_id` varchar(32) NOT NULL COMMENT '用户ID',
                                      `app_id` varchar(32) NOT NULL COMMENT '医联体ID',
                                      `ins_code` varchar(32) NOT NULL COMMENT '医疗机构代码',
                                      `dep_id` varchar(32) DEFAULT NULL COMMENT '科室ID',
                                      `dep_name` varchar(32) DEFAULT NULL COMMENT '科室名称',
                                      `employee_id` varchar(32) DEFAULT NULL COMMENT '工号',
                                      `origin_doctor_id` varchar(32) DEFAULT NULL COMMENT '第三方医生ID',
                                      `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                      `create_user` varchar(32) DEFAULT NULL COMMENT '创建人',
                                      `update_date` datetime DEFAULT NULL COMMENT '修改时间',
                                      `update_user` varchar(32) DEFAULT NULL COMMENT '修改人',
                                      PRIMARY KEY (`practice_id`),
                                      UNIQUE KEY `IDX_ADMIN_APP_INS` (`admin_id`,`app_id`,`ins_code`),
                                      KEY `IDX_ADMIN_ID` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户执业机构';

/* 2021.7.14 gw KFXT-576 初始化用户执业机构数据 */
INSERT INTO `sys_admin_practice` (`admin_id`, `app_id`, `ins_code`, `dep_id`, `dep_name`, `employee_id`, `origin_doctor_id`, `create_date`, `create_user`) SELECT id, APP_ID, INS_CODE, DEP_ID, DEP_NAME, EMPLOYEE_ID, ORIGIN_DOCTOR_ID, NOW(), 'system' FROM `sys_admin_info` WHERE is_del = '0' ;

/* 2021.7.15 gw KFXT-576 增加参数：个人协定方是否关联机构。0-否，1-是。当为1时，只能使用当前医疗机构的个人协定方 */
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) values('88121bb634fe46dbb290b4c2876d198e','000000','000000','PERSONAL_PRESCRIPTION_FILTER_SELF_BY_INS','搜索个人协定方是否过滤医疗机构','0','0（不过滤，可以使用全部个人协定方）1（过滤，只能使用当前医疗机构的个人协定方）','2021-07-15 11:16:04','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0');


/* 2021.07.16 v5.12.2 KFXT-548 gw 版本维护*/
DELETE FROM t_business_edition WHERE edition_num IS NULL;
ALTER TABLE `t_business_edition` CHANGE `edition_num` `edition_num` VARCHAR(100) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版本号';
ALTER TABLE `t_business_edition` ADD UNIQUE INDEX `IDX_NUM` (`edition_num`, `is_del`);

/* 2021.07.16 v5.12.2 KFXT-548 gw 用户查看版本记录*/
CREATE TABLE `t_business_edition_read` (
                                           `read_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '查看ID',
                                           `edition_id` varchar(32) NOT NULL COMMENT '版本ID',
                                           `edition_num` varchar(100) NOT NULL COMMENT '版本号',
                                           `admin_id` varchar(32) NOT NULL COMMENT '用户ID',
                                           `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                           `create_user` varchar(32) DEFAULT NULL COMMENT '创建人',
                                           `update_date` datetime DEFAULT NULL COMMENT '修改时间',
                                           `update_user` varchar(32) DEFAULT NULL COMMENT '修改人',
                                           PRIMARY KEY (`read_id`),
                                           KEY `IDX_ADMIN_ID` (`admin_id`),
                                           KEY `IDX_EDITION_ID` (`edition_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COMMENT='用户查看版本记录';

/* 2021.07.19 v5.12.1 KFXT-296 gw 参数管理*/
ALTER TABLE `t_sys_param` CHANGE `APP_ID` `APP_ID` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '000000' NOT NULL COMMENT 'APPID';
ALTER TABLE `t_sys_param` CHANGE `INS_CODE` `INS_CODE` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '000000' NOT NULL COMMENT '医疗机构代码';
ALTER TABLE `t_sys_param` ADD COLUMN `DEPT_ID` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '000000' NOT NULL COMMENT '科室ID' AFTER `INS_CODE`;
ALTER TABLE `t_sys_param` DROP INDEX `index_par_code`, ADD UNIQUE INDEX `index_par_code` (`APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `IS_DEL`);
ALTER TABLE `t_sys_param` ADD COLUMN `SEQN` INT NULL COMMENT '序号' AFTER `IS_DEL`;

/* 2021.07.27 v5.12.1 KFXT-650 gw 病历表索引*/
ALTER TABLE `t_record` ADD INDEX `IDX_IS_DEL` (`IS_DEL`);

/* 2021.07.27 v5.12.1  圣美孚*/
INSERT INTO `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) VALUES('bcc886e700ba4e228fdddc992f161463','000000','000000','000000','CHECK_PATIENT_INFO','传给圣美孚病人信息','0','1显示0隐藏','2021-07-20 18:11:19','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','0');
CREATE TABLE `t_record_syndrome_file` (
                                          `check_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '检查ID',
                                          `register_id` varchar(32) NOT NULL COMMENT '挂号ID，流水号',
                                          `check_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '检查时间',
                                          `file_type` varchar(5) DEFAULT NULL COMMENT '文件类型，0面向，1舌相，2脉象，3报告',
                                          `file_url` varchar(255) DEFAULT NULL COMMENT '文件地址',
                                          `smf_id` varchar(32) DEFAULT NULL COMMENT '圣美孚ID',
                                          `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
                                          `create_user` varchar(32) DEFAULT 'system',
                                          `update_date` datetime DEFAULT NULL COMMENT '修改时间',
                                          `update_user` varchar(32) DEFAULT NULL COMMENT '修改人',
                                          `delete_date` datetime DEFAULT NULL COMMENT '删除时间',
                                          `delete_user` varchar(32) DEFAULT NULL COMMENT '删除人',
                                          `is_del` varchar(1) DEFAULT '0' COMMENT '是否删除，0未删除，1删除',
                                          PRIMARY KEY (`check_id`),
                                          KEY `INDEX_REGISTER_ID` (`register_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=134 DEFAULT CHARSET=utf8 COMMENT='圣美孚体质检查附件表';

/* 2021.7.22 wt 知识库区分男女疾病 */
ALTER TABLE `t_doctor_disease` ADD  GENDER VARCHAR(2) COMMENT '性别(M男F女)';
ALTER TABLE `b_mapping_disease` ADD GENDER VARCHAR(2) COMMENT '疾病性别(M男F女)';


/* 2021.07.28 v5.1.6  临床业务监管-中医疾病谱分析  统计范围修改（在整个医联体内统计）*/
/* 删除了表中对ins_code属性的非空限制*/
alter table t_statistics_dis_age modify column ins_code VARCHAR(32) null COMMENT '医疗机构代码';
alter table t_statistics_dis_gender modify column ins_code VARCHAR(32) null COMMENT '医疗机构代码';
alter table t_statistics_dis_num modify column ins_code VARCHAR(32) null COMMENT '医疗机构代码';
alter table t_statistics_dis_rate modify column ins_code VARCHAR(32) null COMMENT '医疗机构代码';
alter table t_statistics_dis_terms modify column ins_code VARCHAR(32) null COMMENT '医疗机构代码';
alter table t_statistics_sym_num modify column ins_code VARCHAR(32) null COMMENT '医疗机构代码';
/* 添加表注释*/
alter table t_statistics_dis_age comment '疾病年龄分布';
alter table t_statistics_dis_gender comment '疾病性别分布';
alter table t_statistics_dis_num comment '高发疾病';
alter table t_statistics_dis_rate comment '上升趋势前五的疾病';
alter table t_statistics_dis_terms comment '疾病节气分布';
alter table t_statistics_sym_num comment '证型分布';


/* 2021.8.10 gw 药房映射表的科室ID使用第三方科室ID，可以直接与用户的科室ID关联 */
UPDATE t_display_mapping m, sys_department d SET m.`dept_id` = d.`DEP_ORIGIN_ID` WHERE m.`dept_id` = d.`DEP_ID`;
DROP TABLE IF EXISTS t_display_bak;