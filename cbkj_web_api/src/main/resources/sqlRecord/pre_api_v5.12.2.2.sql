/* 2021.08.16 gw 开方贴数默认值(内服、外用) */
INSERT INTO `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) VALUES('96ef54fde10a46f39ee33122423d17e7','000000','000000','000000','PRESCRIPTION_INTERNAL_DEFAULT_NUMBER','内服中草药方默认贴数值','3','正整数值，0或缺省时不默认','2021-08-13 13:19:58','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');
INSERT INTO `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) VALUES('6ee3d05a7a404e2dba6dd7175a0766ba','000000','000000','000000','PRESCRIPTION_EXTERNAL_DEFAULT_NUMBER','外用中草药方默认贴数值','2','正整数值，0或缺省时不默认','2021-08-13 13:20:23','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');
/* 2021.08.16 gw 协定方添加配方选项 */
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) values('cd749f3266404406b9de4cc0c83f6a7c','000000','000000','000000','APPOINT_AS_FORMULA','协定方添加配方选项','0','1：表示添加配方选项','2021-08-20 13:16:36','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');

/* 2021.08.20 gw 国医大师/传承系统 智能辩证增加来源数据*/
ALTER TABLE `t_record_master` ADD COLUMN `ORIGIN_TYPE` VARCHAR(32) NULL COMMENT '来源类型' AFTER `SAVE_TIME`, ADD COLUMN `ORIGIN_ID` VARCHAR(64) NULL COMMENT '来源ID' AFTER `ORIGIN_TYPE`, ADD COLUMN `ORIGIN_URL` VARCHAR(512) NULL COMMENT '来源URL' AFTER `ORIGIN_ID`;
/* 2021.08.24 gw 协定方增加能否增加配方*/
alter table `t_personal_prescription` add column `CAN_APPEND` int(1) DEFAULT NULL COMMENT '能否加药（1是0否）' after `IS_PREPARATION`;

/* 2021.08.30 gw KFXT-616 处方来源增加 传承经方 */
ALTER TABLE `t_prescription` CHANGE `PRE_ORIGIN` `PRE_ORIGIN` VARCHAR(2) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' NULL COMMENT '处方来源(0自拟方 1智能辩证 2智能推方 3方剂搜索 4协定方 5专家经验共享 7名家验案 9国医大师 10配方 11传承经方)';

/* 2021.08.31 gw KFXT-616 新增参数 智能辩证Tab配置 */
INSERT INTO `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) VALUES('d8adfb50e4e041c49d1fc2c0f11cf6c3','000000','000000','000000','DIALECTICAL_TAB','智能辩证Tab配置','ai:AI 辨证:T|expert:国医大师辨证:','[类型]:[名称][:选中]；ai：AI 辨证；experiential：经方辨证；expert：国医大师辨证；:T 表示选中','2021-08-27 16:18:14','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');


/* 2021.9.7 gw 桐庐 处方状态新增了4个（药房审核、复核、泡药、包装） */
ALTER TABLE `t_prescription` CHANGE `REC_EXT_TYPE` `REC_EXT_TYPE` VARCHAR(3) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '处方状态（3预约挂号 5挂号缴费 7咨询缴费 10开方 20删除 30审核通过 40审核未通过 44推送成功 45推送失败 50处方缴费 61药房审核通过 62药房审核未通过 80配药 82复核 85泡药 90发药 100取消发药 110退药 120取消退药 130煎药 135包装 140配送 150收货）';
