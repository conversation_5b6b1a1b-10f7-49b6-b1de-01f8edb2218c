/* 2021.7.6 gw 配方 */
CREATE TABLE `t_formula` (
                             `FORMULA_ID` varchar(32) NOT NULL COMMENT '配方ID',
                             `APP_ID` varchar(32) NOT NULL COMMENT 'APPID',
                             `INS_CODE` varchar(32) NOT NULL COMMENT '医疗机构代码',
                             `DEPT_ID` varchar(32) DEFAULT NULL COMMENT '科室ID',
                             `PRE_OWNER` varchar(32) NOT NULL COMMENT '拥有者id',
                             `PRE_OWNER_NAME` varchar(200) DEFAULT NULL COMMENT '拥有者',
                             `PRE_NAME` varchar(64) NOT NULL COMMENT '方名',
                             `PRE_PY` varchar(32) DEFAULT NULL COMMENT '拼音',
                             `PRE_WB` varchar(32) DEFAULT NULL COMMENT '五笔',
                             `EFFICACY` varchar(128) DEFAULT NULL COMMENT '功效与适应症',
                             `PRE_TYPE` varchar(1) DEFAULT NULL COMMENT '配方类型（1为内服中草药，2为外用中草药）',
                             `PRE_MAT_TYPE` varchar(1) DEFAULT NULL COMMENT '药品剂型（1散装饮片  2散装颗粒  3膏方 4小包装饮片 5小包装颗粒 ）',
                             `STORE_ID` varchar(32) DEFAULT NULL COMMENT '药房ID',
                             `PRE_NUM` smallint(6) DEFAULT NULL COMMENT '贴数（草药为贴数，熏蒸为总次数，适宜技术为天数）',
                             `PRE_DESCRIPTION_ID` varchar(32) DEFAULT NULL COMMENT '服法ID',
                             `PRE_DESCRIPTION` varchar(100) DEFAULT NULL COMMENT '服法',
                             `PRE_SMOKE_TYPE_ID` varchar(32) DEFAULT NULL COMMENT '外用方式ID',
                             `PRE_SMOKE_TYPE` varchar(100) DEFAULT NULL COMMENT '外用方式',
                             `PRE_SMOKE_INSTRUMENT_ID` varchar(32) DEFAULT NULL COMMENT '熏蒸仪ID',
                             `PRE_SMOKE_INSTRUMENT` varchar(100) DEFAULT NULL COMMENT '熏蒸仪',
                             `PRE_N_BAG` smallint(6) DEFAULT NULL COMMENT '每次几袋',
                             `PRE_N_ML` smallint(6) DEFAULT NULL COMMENT '每次几ml',
                             `PRE_FREQUENCY_ID` varchar(32) DEFAULT NULL COMMENT '频次ID',
                             `PRE_FREQUENCY` varchar(100) DEFAULT NULL COMMENT '频次',
                             `IS_PRODUCTION` varchar(1) DEFAULT NULL COMMENT '是否膏方（0否 1是）',
                             `DECOCT_TYPE` varchar(1) DEFAULT NULL COMMENT '煎药方式（1为代煎，0为自煎）',
                             `IS_USE` varchar(1) DEFAULT '1' COMMENT '是否启用（0否 1是）',
                             `IS_MODIFY` varchar(1) DEFAULT '0' COMMENT '开方时是否能修改（0否  1是）',
                             `STORE_PRICE_ID` varchar(32) DEFAULT NULL COMMENT '药房价格ID',
                             `PRE_SINGLE_MONEY` decimal(10,4) DEFAULT NULL COMMENT '药品总金额',
                             `PRE_TOL_MONEY` decimal(10,4) DEFAULT NULL COMMENT '配方总金额',
                             `PRE_ORDER` int(6) DEFAULT NULL COMMENT '排序',
                             `CREATE_DATE` datetime NOT NULL COMMENT '新增时间',
                             `CREATE_USER` varchar(32) NOT NULL COMMENT '新增人',
                             `UPDATE_DATE` datetime DEFAULT NULL COMMENT '修改时间',
                             `UPDATE_USER` varchar(32) DEFAULT NULL COMMENT '修改人',
                             `DEL_DATE` datetime DEFAULT NULL COMMENT '删除时间',
                             `DEL_USER` varchar(32) DEFAULT NULL COMMENT '删除人',
                             `IS_DEL` varchar(1) NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
                             PRIMARY KEY (`FORMULA_ID`),
                             KEY `IDX_APP_ID` (`APP_ID`),
                             KEY `IDX_INS_CODE` (`INS_CODE`),
                             KEY `IDX_DEPT_ID` (`DEPT_ID`),
                             KEY `IDX_PRE_OWNER` (`PRE_OWNER`),
                             KEY `IDX_PRE_TYPE` (`PRE_TYPE`),
                             KEY `IDX_IS_DEL` (`IS_DEL`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配方';

CREATE TABLE `t_formula_item` (
                                  `ITEM_ID` varchar(32) NOT NULL COMMENT '配方明细ID',
                                  `FORMULA_ID` varchar(32) NOT NULL COMMENT '配方ID',
                                  `YPML_HIS` varchar(32) NOT NULL COMMENT 'HIS药品目录代码',
                                  `YPGGDM_HIS` varchar(32) DEFAULT NULL COMMENT 'HIS药品规格代码',
                                  `YPML_CENTER` varchar(32) NOT NULL COMMENT '中心药品目录代码',
                                  `YPGGDM_CENTER` varchar(32) DEFAULT NULL COMMENT '中心药品规格代码',
                                  `MAT_ID` varchar(32) DEFAULT NULL COMMENT '知识库药品ID',
                                  `YAOPINDM_TY` varchar(32) DEFAULT NULL COMMENT '统一药品代码（由开方系统生成）',
                                  `YPDM_CENTER` varchar(32) DEFAULT NULL COMMENT '药品代码（存药房的）',
                                  `YPDM_HIS` varchar(32) DEFAULT NULL COMMENT '药品代码（存HIS的）',
                                  `YPMC_CENTER` varchar(128) DEFAULT NULL COMMENT '药品名称（存药房的）',
                                  `YPMC_HIS` varchar(128) NOT NULL COMMENT '药品名称（存HIS的）',
                                  `YPGG_HIS` varchar(128) DEFAULT NULL COMMENT '药品规格（存HIS的）',
                                  `BZDW_HIS` varchar(32) NOT NULL COMMENT '包装单位（存HIS的）',
                                  `MAT_DOSE` decimal(6,2) DEFAULT NULL COMMENT '剂量',
                                  `YFID_HIS` varchar(32) DEFAULT NULL COMMENT '用法ID（存HIS的）',
                                  `YFMC_HIS` varchar(32) DEFAULT NULL COMMENT '用法名称（存HIS的）',
                                  `MAT_XSJ` decimal(18,6) DEFAULT NULL COMMENT '单价',
                                  `MAT_SEQN` int(11) DEFAULT NULL COMMENT '序号',
                                  `ZHUANHUANXS` smallint(6) DEFAULT NULL COMMENT '转换系数',
                                  `CREATE_DATE` datetime NOT NULL COMMENT '新增时间',
                                  `CREATE_USER` varchar(32) NOT NULL COMMENT '新增人',
                                  PRIMARY KEY (`ITEM_ID`),
                                  KEY `INDEX_FORMULA_ID` (`FORMULA_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配方明细';

CREATE TABLE `t_formula_auth` (
                                  `AUTH_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '配方权限ID',
                                  `FORMULA_ID` varchar(32) NOT NULL COMMENT '配方ID',
                                  `APP_ID` varchar(32) NOT NULL COMMENT 'APPID',
                                  `INS_CODE` varchar(32) NOT NULL COMMENT '医疗机构代码',
                                  `DEPT_ID` varchar(32) DEFAULT NULL COMMENT '科室ID',
                                  `PRE_STOCK` int(6) DEFAULT NULL COMMENT '库存',
                                  `CREATE_DATE` datetime DEFAULT NULL COMMENT '创建时间',
                                  `CREATE_USER` varchar(32) DEFAULT NULL COMMENT '创建人',
                                  PRIMARY KEY (`AUTH_ID`),
                                  KEY `IDX_APP_INS_DEPT` (`APP_ID`,`INS_CODE`,`DEPT_ID`),
                                  KEY `IDX_FORMULA_ID` (`FORMULA_ID`),
                                  KEY `IDX_PRE_STOCK` (`PRE_STOCK`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='配方权限';


/* 2021.7.30 gw 处方来源增加10配方 */
ALTER TABLE `t_prescription` CHANGE `PRE_MAT_TYPE` `PRE_MAT_TYPE` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '中药类型（1散装饮片  2散装颗粒  3膏方  4小包装饮片 5小包装颗粒 7制剂）';
/* 2021.7.30 gw 搜索配方参数 */
INSERT INTO `t_sys_param` (`PAR_ID`,`APP_ID`,`INS_CODE`,`DEPT_ID`,`PAR_CODE`,`PAR_NAME`,`PAR_VALUES`,`PAR_DES`,`CREATE_DATE`,`CREATE_USER`,`CREATE_USERNAME`,`IS_DEL`,`SEQN`) VALUES ('0cc8eca3aa3f4179b062818f7e9b241e','000000','000000','000000','PRESCRIPTION_SEARCH_RECIPE_OPTIONS','搜索(协定方|方剂|配方)处方配置','self:T|dept|ins|formula|know','开方时搜索处方：类型和排序及默认选中；协定方：self|dept|ins，配方：formula，方剂：know（以|分隔，:T 表示选中）','2021-07-30 11:14:42','70810c874405453b99c6c2cf72296fe5','管理员','0','-1') ;
INSERT INTO `t_sys_param` (`PAR_ID`,`APP_ID`,`INS_CODE`,`DEPT_ID`,`PAR_CODE`,`PAR_NAME`,`PAR_VALUES`,`PAR_DES`,`CREATE_DATE`,`CREATE_USER`,`CREATE_USERNAME`,`IS_DEL`,`SEQN`) VALUES ('66d8d28a77e04851b9210084ef5751ae','000000','000000','000000','PRESCRIPTION_SEARCH_RECIPE_SORT','搜索(协定方|方剂|配方)处方类型和排序','self|dept|ins|formula|know','开方时搜索处方：类型和排序；协定方：self|dept|ins，配方：formula，方剂：know（以|分隔）','2021-07-30 11:16:18','70810c874405453b99c6c2cf72296fe5','管理员','0','-1') ;
UPDATE `t_sys_param` SET `PAR_NAME` = '搜索(协定方|方剂|配方)处方默认类型', `PAR_DES` = '方剂(默认值): know ; 配方: formula ; 个人协定方: self ; 科室协定方: dept ; 全院协定方: ins' WHERE PAR_CODE = 'PRESCRIPTION_SEARCH_RECIPE_TYPE';

/*2021.8.10 gw 药品配置表增加配方的制膏费、代煎费、配送费*/
ALTER TABLE `t_display` ADD COLUMN `FEE_PRODUCTION_MZ_FORMULA` DECIMAL(6,2) NULL COMMENT '门诊制膏费（配方）' AFTER `FEE_PRODUCTION_MZ`, ADD COLUMN `FEE_PRODUCTION_ZY_FORMULA` DECIMAL(6,2) NULL COMMENT '住院制膏费（配方）' AFTER `FEE_PRODUCTION_ZY`, ADD COLUMN `FEE_DECOCTION_MZ_FORMULA` DECIMAL(6,2) NULL COMMENT '门诊代煎费（配方）' AFTER `FEE_DECOCTION_MZ`, ADD COLUMN `FEE_DECOCTION_ZY_FORMULA` DECIMAL(6,2) NULL COMMENT '住院代煎费（配方）' AFTER `FEE_DECOCTION_ZY`, ADD COLUMN `FEE_EXPRESS_MZ_FORMULA` VARCHAR(32) NULL COMMENT '门诊配送费（配方）' AFTER `FEE_EXPRESS_MZ`, ADD COLUMN `FEE_EXPRESS_ZY_FORMULA` VARCHAR(32) NULL COMMENT '住院配送费（配方）' AFTER `FEE_EXPRESS_ZY`;

/*2021.8.16 gw 配方菜单 */
INSERT INTO `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) VALUES('213','中药配方','/recipe/formula',NULL,NULL,'2','33','2021-07-21 11:39:38','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'0',NULL,'1');

/*2021.9.7 gw 挂号增加“互联网在线就诊患者“信息 */
ALTER TABLE `t_his_record` CHANGE `HOSPITAL_NO` `HOSPITAL_NO` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '门诊号/住院号';
ALTER TABLE `t_his_record` ADD COLUMN `IS_INTERNET` INT(1) NULL COMMENT '互联网标志，1是0否' AFTER `ADMISSION_TIME`;
ALTER TABLE `t_register` ADD COLUMN `IS_INTERNET` INT(1) NULL COMMENT '互联网标志，1是0否' AFTER `GRAVIDITY`;
ALTER TABLE `t_register` ADD COLUMN `CLINIC_TYPE_NO` VARCHAR(32) NULL COMMENT '门诊号/住院号' AFTER `CLINIC_TYPE_NAME`;
ALTER TABLE `t_register` CHANGE `CLINIC_TYPE_NAME` `CLINIC_TYPE_NAME` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '门诊/住院';

/* 2021.9.8 gw 处方增加审核人机构 */
ALTER TABLE `t_prescription` ADD COLUMN `CHECK_INS_CODE` VARCHAR(32) NULL COMMENT '审核人机构' AFTER `CHECK_USERNAME`;
/* 2021.9.8 gw 处方审核类型增加2下班自动通过 */
ALTER TABLE `t_prescription` CHANGE `CHECK_TYPE` `CHECK_TYPE` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '审核类型（0为自动审核，1为人工审核，2下班自动通过）';

/* 2021.9.8 gw 修改【中医电子病历】路径/方法的*/
UPDATE sys_admin_menu SET url = '/manage/case/record' WHERE MID = 5;
/* 2021.9.8 gw 修改【中医电子病历】路径/方法的（版本回退时执行）*/
#UPDATE sys_admin_menu SET url = '/manage/case/create' WHERE MID = 5;

/* 2021.9.15 配方自动审核通过*/
INSERT INTO `t_sys_param` (`PAR_ID`,`APP_ID`,`INS_CODE`,`DEPT_ID`,`PAR_CODE`,`PAR_NAME`,`PAR_VALUES`,`PAR_DES`,`CREATE_DATE`,`CREATE_USER`,`CREATE_USERNAME`,`IS_DEL`,`SEQN`) VALUES ('0cc8eca3ff3f4hh9b06jj18f7e9b258e','000000','000000','000000','SYS_CHECK_FORMULA','配方自动审核通过','1','1开启0关闭 默认开启','2021-09-13 17:30:42','70810c874405453b99c6c2cf72296fe5','管理员','0','-1') ;
/* 2021.9.15 审核下班时间*/
INSERT INTO `t_sys_param` (`PAR_ID`,`APP_ID`,`INS_CODE`,`DEPT_ID`,`PAR_CODE`,`PAR_NAME`,`PAR_VALUES`,`PAR_DES`,`CREATE_DATE`,`CREATE_USER`,`CREATE_USERNAME`,`IS_DEL`,`SEQN`) VALUES ('asdfasdfafgdfgsdrty465dyjo452gyd','000000','000000','000000','SYS_CHECK_WORK_TIME','审核工作时间','','审核工作时间（星期与时间段用中文冒号：分割，多个时间段用|分割，多个星期用中文分号；分隔。不在审方时间段将自动审核通过，处方标记为‘下班自动通过’。默认空，不支持下班自动审核）
如：8:00-11:00|14:00-17:00;（每天8点到11点和14点到17点）如：星期一：8:00-11:00；星期三：8:00-10:00|14:00-16:00；（星期一8:00-11:00、星期三8:00-10:00和14:00-16:00）','2021-09-13 17:30:42','70810c874405453b99c6c2cf72296fe5','管理员','0','-1') ;

/* 2021.9.16 系统审核人名称*/
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) values('ed07e0d1b539454bba638f33db971162','000000','000000','000000','SYS_CHECK_USER_NAME','系统审核人名称','system','系统审核人名称，当系统审核通过后，处方审核人为该值','2021-09-16 15:00:40','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');

/* 2021.9.22 预扣库存*/
CREATE TABLE `t_center_ypkc_yk` (
                                    `YK_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '预扣库存ID',
                                    `YPML_ID` varchar(32) NOT NULL COMMENT '药品目录ID',
                                    `YAOPINDM` varchar(32) NOT NULL COMMENT '第三方药品代码',
                                    `STORE_ID` varchar(32) DEFAULT NULL COMMENT '药房ID',
                                    `YUKOUSL` decimal(18,4) NOT NULL COMMENT '预扣库存数量',
                                    `PRE_ID` varchar(32) NOT NULL COMMENT '处方ID',
                                    `PRE_ITEM_ID` varchar(32) NOT NULL COMMENT '处方明细ID',
                                    `PRE_TIME` datetime NOT NULL COMMENT '处方时间',
                                    PRIMARY KEY (`YK_ID`),
                                    KEY `IDX_PRE_ID` (`PRE_ID`),
                                    KEY `IDX_YPMLDM` (`YPML_ID`,`YAOPINDM`,`STORE_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='预扣库存';

/* 2021.9.22 gw 处方触发器删除/推送失败/收费时回退预扣库存*/
DELIMITER $$
DROP TRIGGER /*!50032 IF EXISTS */ `g_afterUpdate_prescription`$$
CREATE
    TRIGGER `g_afterUpdate_prescription` AFTER UPDATE ON `t_prescription`
    FOR EACH ROW
BEGIN
    DECLARE operationTypeTr INT(11);
    DECLARE operationContentTr VARCHAR(50);
    DECLARE needInsertOrderTr VARCHAR(50);
    DECLARE operationIdTr VARCHAR(32);
    DECLARE operationNameTr VARCHAR(32);
    DECLARE operationTimeTr DATETIME;

    SET needInsertOrderTr = 0;

    IF old.IS_DEL != '1' AND new.IS_DEL = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 10;
        SET operationContentTr = '删除处方';
        SET operationIdTr = new.PRE_DOCTOR;
        SET operationNameTr = new.PRE_DOCTORNAME;
        SET operationTimeTr = NOW();
        /**回退预扣库存**/
        DELETE FROM t_center_ypkc_yk WHERE PRE_ID = old.PRE_ID;

    ELSEIF old.IS_CHECK != '1' AND new.IS_CHECK = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 30;
        SET operationContentTr = '审核通过';
        SET operationIdTr = new.CHECK_USERID;
        SET operationNameTr = new.CHECK_USERNAME;
        SET operationTimeTr = new.CHECK_TIME;

    ELSEIF old.IS_CHECK != '2' AND new.IS_CHECK = '2' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 40;
        SET operationContentTr = '审核未通过';
        SET operationIdTr = new.CHECK_USERID;
        SET operationNameTr = new.CHECK_USERNAME;
        SET operationTimeTr = new.CHECK_TIME;

    ELSEIF old.REC_EXT_TYPE != '45' AND new.REC_EXT_TYPE = '45' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 45;
        SET operationContentTr = '推送失败';
        SET operationIdTr = '';
        SET operationNameTr = '';
        SET operationTimeTr = NOW();
        /**回退预扣库存**/
        DELETE FROM t_center_ypkc_yk WHERE PRE_ID = old.PRE_ID;

    ELSEIF old.IS_PAY != '1' AND new.IS_PAY = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 50;
        SET operationContentTr = '处方缴费成功';
        SET operationIdTr = new.PAY_USERID;
        SET operationNameTr = new.PAY_USERNAME;
        SET operationTimeTr = new.PAY_TIME;
        /**回退预扣库存**/
        DELETE FROM t_center_ypkc_yk WHERE PRE_ID = old.PRE_ID;
    END IF;

    IF needInsertOrderTr = 1 THEN
        INSERT INTO `t_order_status` (
            `STATUS_ID`,`REGISTER_ID`,`PRE_ID`,`OPERATION_USERID`,`OPERATION_USERNAME`,`OPERATION_TIME`,
            `OPERATION_TYPE`,`OPERATION_CONTENT`,CREATE_TIME
        ) VALUES (
            REPLACE (UUID(), '-', ''),'0',new.PRE_ID,operationIdTr,operationNameTr,operationTimeTr,
            operationTypeTr,operationContentTr,NOW()
        ) ;
    END IF;
END;
$$
DELIMITER ;

/* 2021.9.28 gw  海盐数据库优化*/
ALTER TABLE `t_prescription` ADD KEY `IDX_PER_TIME` (`PRE_TIME`) ;
ALTER TABLE `t_record` DROP KEY `IDX_APP_INS_ID`;
ALTER TABLE `t_record` ADD KEY `IDX_APP_INS_ID` (`APP_ID`, `INS_CODE`) ;
ALTER TABLE `t_record` ADD KEY `IDX_DIS_ID` (`DIS_ID`) ;
ALTER TABLE `t_record` ADD KEY `IDX_DOC_ID` (`DOC_ID`) ;
ALTER TABLE `t_record` DROP KEY `IDX_IS_DEL` ;
ALTER TABLE `t_record` DROP KEY `IDX_REC_FIRSTID` ;
ALTER TABLE `t_record` DROP KEY `IDX_REC_TRE_TIME` ;
ALTER TABLE `t_record` ADD KEY `IDX_REC_TRE_TIME_ASC` (`REC_TRE_TIME`) ;
ALTER TABLE `t_record` DROP KEY `IDX_REGISTER_ID`;
ALTER TABLE `t_record` CHANGE `DEPT_NAME` `DEPT_NAME` varchar(128) COLLATE utf8mb4_general_ci NULL COMMENT '就诊科室名称' after `DEPT_ID` ;
ALTER TABLE `t_record` CHANGE `DIS_NAME` `DIS_NAME` varchar(256) COLLATE utf8mb4_general_ci NULL COMMENT '疾病名称' after `DIS_ID` ;
ALTER TABLE `t_record` CHANGE `SYM_NAME` `SYM_NAME` varchar(256) COLLATE utf8mb4_general_ci NULL COMMENT '证型名称' after `SYM_ID` ;
ALTER TABLE `t_record` ADD COLUMN `DOC_NAME` varchar(32) COLLATE utf8mb4_general_ci NULL COMMENT '医生名称' after `SPECIAL_CODE` ;
UPDATE t_record rec, t_register reg SET rec.`DOC_NAME` = reg.`DOCTOR_NAME` WHERE rec.`REGISTER_ID` = reg.`REGISTER_ID` AND rec.`DOC_NAME` IS NULL;

/* 2021.10.9 gw 增加不适应症*/
ALTER TABLE `T_CENTER_HIS_YPMLMX` ADD COLUMN `MEDICONTROL_TEXT` VARCHAR(500) NULL COMMENT '不适应症';
