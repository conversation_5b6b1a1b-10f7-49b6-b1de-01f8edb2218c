/* 2021.10.8 czh 增加上级药品代码*/
ALTER TABLE `T_CENTER_HIS_YPMLMX` ADD COLUMN `PARENT_YPDM` VARCHAR(64) NULL COMMENT '上级药品代码';

/*2012.10.9 gw KFXT-164 增加参数：医保限制提醒弹框内容*/
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) values('99392e3ba426432c99531864dce4e7ed','000000','000000','000000','INSURANCE_LIMIT_TIP','医保限制提醒弹框内容','1,2,3','1日最大剂量、2医保外药品、3基金不支付','2021-10-09 09:34:20','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');
/*2012.10.9 gw KFXT-164 增加参数：是否弹框双签提醒*/
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) values('a2071f9cb5ac4ede9ab39dcb7c966dc7','000000','000000','000000','SAFETY_EVALUATION_TIP','是否弹框双签提醒','0','1是0否','2021-10-09 11:16:25','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');
/*2012.10.9 gw KFXT-164 修改参数：安全用药特殊签字配置*/
update `t_sys_param` set PAR_DES = '1禁用药、2忌用药、3慎用药、4十八反、5十九畏、6不宜同用、7孕妇忌用、8孕妇慎用、9使用注意、10剂量超标、11毒、12病症禁忌、13饮食禁忌、14禁孕、15超规定用药量、16剂量偏低、20多规格同时开' where PAR_CODE = 'SAFETY_EVALUATION_SIGN';

/* 2021.10.09 zjh   公用代码-增加膏方有无糖数据行*/
INSERT INTO `t_sys_code` (`CODE_ID`,`CODE_VALUE`, `CODE_NAME`, `CODE_NUM`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`, `IS_DISABLE`) VALUES ('33','sugarfree', '无糖', '33', '2021-10-08 16:08:05', '34d5ccaa2f014d69b766d68a3dba91ad', 'cbys', '0', '0');
insert into `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) values('1f908ab2369143bfa00401b8c3a31694','不限糖','33','1',NULL,NULL,'bxt-01',NULL,NULL,'1','2021-10-08 16:16:16','bafa5fb36e40486b9e84a25c7ffac27f','zjh','2021-10-13 09:36:38','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,'0',NULL);
insert into `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) values('ca8b4f35471c4925a1557da1df7e0d1a','无糖','33','0',NULL,NULL,'wt-01',NULL,NULL,'0','2021-10-08 16:15:51','bafa5fb36e40486b9e84a25c7ffac27f','zjh',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);

/* 2021.10.09 zjh   处方表-增加膏方有无糖两个字段*/
ALTER TABLE `t_prescription`
    ADD COLUMN `PRODUCTION_TYPE` VARCHAR(100) NULL COMMENT '膏方有无糖' AFTER `CREATE_DATE`,
    ADD COLUMN `PRODUCTION_TYPE_ID` VARCHAR(32) NULL COMMENT '膏方有无糖' AFTER `PRODUCTION_TYPE`;

#2012.10.13 gw KFXT-792 单帖最高限价金额，超出提醒
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) values('c660beabb2eb4695a88d4a9670ed34e2','000000','000000','000000','PRESCRIPTION_INTERNAL_MAXIMUM_AMOUNT','内服中草药单帖最高限价金额','20','0不限制','2021-10-09 10:19:19','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) values('5cef427d381646059d5a6dcbc05be57f','000000','000000','000000','PRESCRIPTION_EXTERNAL_MAXIMUM_AMOUNT','外用中草药单帖最高限价金额','10','0不限制','2021-10-09 10:19:09','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');
/* 2021.10.14 zjh   增加参数配置：开方页面“医保”列显示数据来源，可配置数据来源“his接口数据”或“医保外配置”。*/
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) values('e92e1a58e7fc423db0deee56cc39bfde','000000','000000','000000','PRESCRIPTION_MEDICAL_INSURANCE_SOURCE','开方页面“医保”列显示数据来源','1','1.“his接口数据”或 2.“医保外配置”，默认1','2021-10-14 13:24:27','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');
