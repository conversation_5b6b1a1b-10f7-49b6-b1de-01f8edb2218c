# 2021.10.20 gw 知识库中药排序默认值
UPDATE `zyznyxt_basic`.`b_material` SET mat_seqn = 999999 WHERE mat_seqn IS NULL;

# 2021.10.20 gw  历史处方/就诊记录的转方将疾病和证型带过来
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) values('52e0c325ce0c472ea5fd1edb2a42eea9','000000','000000','000000','PRESCRIPTION_TRANSFER_ILLNESS_OPTIONS','转方操作-转病证配置','00000','[未缴费处方][就诊记录][历史处方][名医验案][专家经验共享]。示例：0cc：【就诊记录】和【历史处方】转方转病证；c000c：【未缴费处方】和【专家经验共享】转方转病证','2021-10-21 11:57:12','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');

# 2021.10.20 czh 配方增加付费方式
ALTER TABLE `T_FORMULA` ADD COLUMN `PAY_TYPE` VARCHAR(2) DEFAULT '0' NULL COMMENT '付费方式：0-不限 1-医保 2-自费';

# 2021.10.22 gw 海盐增加111退药
ALTER TABLE `t_prescription` CHANGE `REC_EXT_TYPE` `REC_EXT_TYPE` VARCHAR(3) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '处方状态（3预约挂号 5挂号缴费 7咨询缴费 10开方 20删除 30审核通过 40审核未通过 44推送成功 45推送失败 50处方缴费 61药房审核通过 62药房审核未通过 80配药 82复核 85泡药 90发药 100取消发药 109查询是否可以退费 110退费 111退药 120取消退药 130煎药 135包装 140配送 150收货）';
