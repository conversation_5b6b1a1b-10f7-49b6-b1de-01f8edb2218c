
/*2021.8.18 gw （新都）疾病发病量统计 */
CREATE INDEX IDX_DIS_ID_SYS ON `b_mapping_disease` (DIS_ID_SYS);
CREATE INDEX IDX_DIS_CODE_SYS ON `b_mapping_disease` (DIS_CODE_SYS);


/*2021.8.24 gw 国考指标分析 */

CREATE TABLE `t_national_ananlysis_goal` (
                                             `goal_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '科室占比目标ID',
                                             `app_id` varchar(32) NOT NULL COMMENT '医联体ID',
                                             `ins_code` varchar(32) NOT NULL COMMENT '医疗机构代码',
                                             `dep_id` varchar(32) DEFAULT NULL COMMENT '科室ID',
                                             `dep_name` varchar(32) DEFAULT NULL COMMENT '科室名称',
                                             `prescription_proportion` double(5,2) DEFAULT NULL COMMENT '门诊中药处方比例',
                                             `preszorxbz_proportion` double(5,2) DEFAULT NULL COMMENT '门诊散装中药饮片和小包装中药饮片处方比例',
                                             `people_use_proportion` double(5,2) DEFAULT NULL COMMENT '门诊患者中药饮片使用率',
                                             `people_nonuse_proportion` double(5,2) DEFAULT NULL COMMENT '门诊患者使用中医非药物疗法比例',
                                             `cmedicine_income_proportion` double(5,2) DEFAULT NULL COMMENT '中药收入占药品收入比例',
                                             `cmedicinepieces_income_proportion` double(5,2) DEFAULT NULL COMMENT '中药饮片收入占药品收入比例',
                                             `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                             `create_user` varchar(32) DEFAULT NULL COMMENT '创建人',
                                             `update_date` datetime DEFAULT NULL COMMENT '修改时间',
                                             `update_user` varchar(32) DEFAULT NULL COMMENT '修改人',
                                             PRIMARY KEY (`goal_id`),
                                             UNIQUE KEY `IDX_DEPT_APP_INS` (`dep_id`,`app_id`,`ins_code`),
                                             KEY `IDX_GOAL_ID` (`goal_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4285 DEFAULT CHARSET=utf8mb4 COMMENT='国考指标分析科室目标设置';
/*2021.8.24 gw 菜单：国考指标分析 */
insert into `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) values('212','国考指标分析','/statistical/national',NULL,NULL,'2','14','2021-07-21 11:22:20','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'1',NULL,'1');
INSERT INTO `sys_admin_rule_menu` (`rmid`, `rid`, `mid`) VALUES('8f1061e1327d45ce810bd6f7ca85d1b0','b4a17b6b635c4de48f95178676905aa5','212');
/*2021.8.24 gw 参数：国考指标分析显示方式*/
INSERT INTO `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) VALUES('d1e9754422b74e7cb1857f1a4d3b0822','000000','000000','000000','GUOKAO_INDICATOR_ANALYSIS','国考指标分析显示方式','0','0仅显示数量，1显示数量和占比，默认为0','2021-08-24 10:34:15','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');

/*2021.9.7 gw 增加国考统计序号*/
ALTER TABLE `sys_department` ADD COLUMN `NATIONAL_SEQN` VARCHAR(32) NULL COMMENT '国考统计序号' AFTER `DEP_WB`;

/* 2021.7.22 wt 知识库区分男女疾病 */
ALTER TABLE `t_doctor_disease` ADD  GENDER VARCHAR(2) COMMENT '性别(M男F女)';
ALTER TABLE `b_mapping_disease` ADD GENDER VARCHAR(2) COMMENT '疾病性别(M男F女)';


ALTER TABLE `zyznyxt_basic`.`b_disease` ADD `GENDER` VARCHAR(2) DEFAULT NULL COMMENT '性别(M男F女)' AFTER `SHOW_TYPE`;
ALTER TABLE `zyznyxt_basic`.`b_disease` ADD `AGE` INT(11) DEFAULT NULL COMMENT '年龄' AFTER `GENDER`;
ALTER TABLE `zyznyxt_basic`.`b_disease` ADD `GRAVIDITY` VARCHAR(2) DEFAULT NULL COMMENT ' 患者是否怀孕（Y为是，N为否）' AFTER `AGE`;



