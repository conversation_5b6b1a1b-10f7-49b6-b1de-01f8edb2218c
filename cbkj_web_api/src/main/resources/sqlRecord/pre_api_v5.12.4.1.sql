#2021.12.26 zjh 版本历史维护，增加类型字段
ALTER TABLE t_business_edition ADD COLUMN `edition_type` VARCHAR (1) NULL COMMENT '版本类型（1.系统更新2.知识库更新）' AFTER `is_del`;
#2021.12.27 gw 删除唯一索引
DROP INDEX IDX_NUM ON t_business_edition;

#2021.12.27 gw 增加参数：膏方控制开方贴数
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) values('9bd45116f02f47b688724f9ba1c38f9d','000000','000000','000000','PRESCRIPTION_INTERNAL_PRODUCTION_CONTROL_NUM','膏方控制开方贴数','0','参数值为膏方最高贴数，0不限制，勾选膏方后提示：“是否改为膏方？膏方一张处方最多开*贴”，贴数自动调整并不可修改','2021-12-27 17:14:54','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');

#2021.12.29 gw 原协定方默认启用
update t_personal_prescription set IS_USE = '1' where IS_USE is null;

#2021.12.30 gw 版本历史维护类型字段默认系统更新
UPDATE t_business_edition SET edition_type = '1' WHERE edition_type IS NULL;