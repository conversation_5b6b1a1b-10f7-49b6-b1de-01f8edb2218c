#2021.10.28 gw 增加笔记模块
ALTER TABLE `t_doctor_note` CHANGE `NOTE_MODULE` `NOTE_MODULE` INT(2) NOT NULL COMMENT '笔记模块:1 名医验案 2 中药 3 方剂 4 经络穴位 5 疾病 6中成药 7舌诊 8脉象 9临床诊疗 10书籍';
#2021.10.28 gw 增加收藏模块
ALTER TABLE `t_doctor_collect` CHANGE `COLLECT_MODULE` `COLLECT_MODULE` INT(2) NOT NULL COMMENT '收藏模块:1名医验案 2中药 3方剂 4经络穴位 5疾病 6中成药 7舌诊 8脉象 9临床诊疗 10书籍';

#2021.11.16 gw KFXT-875  内服中药方：膏方不受药位数及处方最高价格限制
update `t_sys_param` set `par_des` = '通用药味数|膏方药味数（0为无限制）' where PAR_CODE = 'PRESCRIPTION_INTERNAL_HIGHEST_MATS';
update `t_sys_param` set `par_des` = '通用单帖金额|膏方单帖金额（0为无限制）（:R为软提醒，可继续开方）' where PAR_CODE = 'PRESCRIPTION_INTERNAL_MAXIMUM_AMOUNT';
update `t_sys_param` set `par_des` = '通用处方金额|膏方处方金额（0为无限制）' where PAR_CODE = 'PRESCRIPTION_INTERNAL_HIGHEST_MONEY';

#2021.11.22 郭伟 增加菜单：古籍馆、中成药、临床指南、舌诊、脉诊
insert into `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) values('214','古书籍','/study/ancient-book',NULL,NULL,'2','182','2021-10-27 13:58:26','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'1',NULL,'1');
insert into `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) values('215','中成药','/study/patent-medicine',NULL,NULL,'2','182','2021-10-28 13:12:01','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'7',NULL,'1');
insert into `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) values('216','临床诊疗指南','/study/clinical-guideline',NULL,NULL,'2','182','2021-10-29 16:12:03','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'8',NULL,'1');
insert into `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) values('217','舌诊','/study/tongue-diagnosis',NULL,NULL,'2','182','2021-11-01 14:07:42','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'9',NULL,'1');
insert into `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) values('218','脉诊','/study/pulse-condition',NULL,NULL,'2','182','2021-11-04 10:02:33','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'10',NULL,'1');

#2021.12.1 郭伟 处方增加开始时间、结束时间
ALTER TABLE `t_prescription` ADD COLUMN `BEGAN_TIME` VARCHAR(20) NULL COMMENT '开始时间' AFTER `CREATE_DATE`, ADD COLUMN `END_TIME` VARCHAR(20) NULL COMMENT '结束时间' AFTER `BEGAN_TIME`;

#2021.12.1 郭伟 新都增加门特从HIS获取治疗方案的疾病
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`,`CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`, `SEQN`)values('8601c862c1bb46c78677b015c6b0ac88','000000','000000','000000','SPECIAL_DIS_FROM_HIS','特病从HIS获取治疗方案','-','新都门特血透从HIS获取治疗方案，参数值配置血透的疾病编码','2021-12-01 15:28:35','70810c874405453b99c6c2cf72296fe5','管理员','0','0');
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`,`CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`, `SEQN`)values('8601c862c1bb46c78677b015c6b0ac89','100007','000000','000000','SPECIAL_DIS_FROM_HIS','特病从HIS获取治疗方案','6032','新都门特血透从HIS获取治疗方案，参数值配置血透的疾病编码','2021-12-01 15:28:35','70810c874405453b99c6c2cf72296fe5','管理员','0','0');

#2021.12.8 郭伟 新都增加第三方审核者：美康
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) values('57c49fb5438343969fca2305609d916a','000000','000000','000000','PRESCRIPTION_CHECK_AUDITOR','第三方审核者','self','self: 云系统审核，medicom: 美康','2021-12-06 10:14:09','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');

#2021.12.8 郭伟 新都特病：选择日期时间范围禁用配置
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) values('b223b33d99194a0cb262f2c53b3cd14b','000000','000000','000000','PRESCRIPTION_SPECIAL_DISEASE_RANGE_PICKER_DISABLED_OPTIONS','特病：选择日期时间范围禁用配置','F','（已当前时间为基准）强制包含|开始时间禁用,结束时间禁用,开始时间偏离量,结束时间偏离量\n   \n格式：[TF]|[-+][\\d][YMDhms]:[Dhms],[-+][\\d][YMDhms]:[Dhms],[-+][\\d][YMDhms],[-+][\\d][YMDhms]\n解析：\n- [TF] 强制包含（true|false默认）指今天、本周、本月是否为日历天数起始日期（默认F，可缺省）\n- [-+][\\d][YMDhms]:[Dhms] 开始/结束时间禁用（已当前时间为基准）\n    * [-+] 向量方向：-指基准向前，+指基准向后（默认+，可缺省）\n    * [\\d] 向量值：数值\n    * [YMDhms] 向量单位：Y年M月D日h时m分s秒\n    * :[Dhms] 时间精确单位：D日h时m分s秒（默认D，可缺省）\n- [-+][\\d][YMDhms] 开始/结束时间偏离量（已当前时间为基准）\n    * [-+] 向量方向：- 指基准向前，+ 指基准向后（默认+，可缺省）\n    * [\\d] 向量值：数值\n    * [YMDhms] 向量单位：Y年M月D日h时','2021-12-13 18:01:01','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');

#2021.12.27 郭伟 补充********新增参数：开方界面是否显示膏方类型（糖）
INSERT INTO `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`) VALUES('0974e25ba6f941fc87ce87f5c15e0047','000000','000000','000000','PRESCRIPTION_INTERNAL_PRODUCTION_TYPE_SHOW','开方界面是否显示膏方类型（糖）','1','1显示（默认） 0不显示','2021-10-11 16:30:33','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1');

#2021.12.27 郭伟 补充********新增公用代码：无糖
insert into `t_sys_code` (`CODE_ID`, `CODE_VALUE`, `CODE_NAME`, `CODE_NUM`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `DISABLE_DATE`, `DISABLE_USER`, `DISABLE_USERNAME`, `IS_DISABLE`) values('33','sugarfree','无糖','33','2021-10-08 16:08:05','34d5ccaa2f014d69b766d68a3dba91ad','cbys',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL,NULL,NULL,'0');
insert into `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) values('1f908ab2369143bfa00401b8c3a31694','不限糖','33','1',NULL,NULL,'bxt-01',NULL,NULL,'1','2021-10-08 16:16:16','bafa5fb36e40486b9e84a25c7ffac27f','zjh','2021-10-13 09:36:38','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,'0',NULL);
insert into `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) values('ca8b4f35471c4925a1557da1df7e0d1a','无糖','33','0',NULL,NULL,'wt-01',NULL,NULL,'0','2021-10-08 16:15:51','bafa5fb36e40486b9e84a25c7ffac27f','zjh',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
