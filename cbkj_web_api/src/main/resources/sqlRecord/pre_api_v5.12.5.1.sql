UPDATE `t_sys_param` SET par_des = '[提示标题]|[确认按钮文本]|[剂量输入提示] [|自费处理按钮文本]

示例： 双签确认|双签保存|超药典规定用药量

示例： 医保限制报销|强制保存|超医保规定用药量

示例： 医保限制报销|强制保存|超医保规定用药量 |自费处理'
WHERE PAR_CODE = 'PRESCRIPTION_SAVE_DOSAGE_ULTRALIMIT_TIPS';

UPDATE
    `t_sys_param`
SET
    `PAR_NAME` = '医保限制第一次保存时提醒内容'
WHERE `PAR_CODE` = 'INSURANCE_LIMIT_TIP';

INSERT INTO `t_sys_param` (
  `PAR_ID`,
  `PAR_CODE`,
  `PAR_NAME`,
  `PAR_VALUES`,
  `PAR_DES`,
  `CREATE_DATE`,
  `CREATE_USER`,
  `CREATE_USERNAME`,
  `SEQN`,
  `PAR_CLASSIFY`
)
VALUES
  (
    'b7556022358111edad8d00163f006620',
    'INSURANCE_LIMIT_REMIND',
    '医保限制开药时提醒',
    '0',
    '1开0关',
    '2022-09-16 13:34:01',
    'admin',
    'admin',
    '-1',
    '医保控制'
  );
#增加药品明细表-医保说明-医保强制提醒
ALTER TABLE `t_center_his_ypmlmx`
  ADD COLUMN `xzsm` VARCHAR (128) NULL COMMENT '医保说明';