
#2021.12.01 zjh 配送增加参数配置控制
INSERT INTO `t_sys_param` (`PAR_ID`,`APP_ID`,`INS_CODE`,`DEPT_ID`,`PAR_CODE`,`PAR_NAME`,`PAR_VALUES`,`PAR_DES`,`CREATE_DATE`,`CREATE_USER`,`CREATE_USERNAME`,`IS_DEL`,`SEQN`) VALUES ('d2107f31de964a95ae2d915dc5d6614c','000000','000000','000000','DC_MOBILE_BAK','配送是否启用备用号码','0','参数第一位为备用号码的数量，默认为0不开启，第二位为备用号码的必填数量。示例：2,1，可添加2个备用号码，一个为必填','2021-12-01 15:30:42','70810c874405453b99c6c2cf72296fe5','管理员','0','-1') ;
#2021.12.30 zjh 中医电子病历打印模板参数控制
INSERT INTO `t_sys_param` (`PAR_ID`,`APP_ID`,`INS_CODE`,`DEPT_ID`,`PAR_CODE`,`PAR_NAME`,`PAR_VALUES`,`PAR_DES`,`CREATE_DATE`,`CREATE_USER`,`CREATE_USERNAME`,`IS_DEL`,`SEQN`) VALUES ('7665f946bfe34ded90641c2cc07a383b','000000','000000','000000','REC_PDF_TEMPLATE','中医电子病历打印模板参数控制','1','1.A4模板 2.A5模板','2021-12-30 15:30:42','70810c874405453b99c6c2cf72296fe5','管理员','0','-1') ;
#2021.12.02 zjh 修改 t_center_his_ypml appid可为空
ALTER TABLE `t_center_his_ypml` CHANGE `APP_ID` `APP_ID` VARCHAR (32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'APPID';

#2021.12.1 郭伟 处方增加开始时间、结束时间
ALTER TABLE `t_prescription` ADD COLUMN `BEGAN_TIME` VARCHAR(20) NULL COMMENT '开始时间' AFTER `CREATE_DATE`, ADD COLUMN `END_TIME` VARCHAR(20) NULL COMMENT '结束时间' AFTER `BEGAN_TIME`;
#2021.12.1 郭伟 新都增加门特从HIS获取治疗方案的疾病
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`,`CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`, `SEQN`)values('8601c862c1bb46c78677b015c6b0ac88','000000','000000','000000','SPECIAL_DIS_FROM_HIS','特病从HIS获取治疗方案','-','新都门特血透从HIS获取治疗方案，参数值配置血透的疾病编码','2021-12-01 15:28:35','70810c874405453b99c6c2cf72296fe5','管理员','0','0');
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`,`CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`, `SEQN`)values('8601c862c1bb46c78677b015c6b0ac89','100007','000000','000000','SPECIAL_DIS_FROM_HIS','特病从HIS获取治疗方案','6032','新都门特血透从HIS获取治疗方案，参数值配置血透的疾病编码','2021-12-01 15:28:35','70810c874405453b99c6c2cf72296fe5','管理员','0','0');


/* 2021.12.12 gw 删除/推送失败/收费时回退预扣库存*/
DELETE FROM t_center_ypkc_yk WHERE PRE_ID IN (SELECT pre_id FROM t_prescription WHERE REC_EXT_TYPE = '45' OR REC_EXT_TYPE >= 50);
/* 2021.12.12 gw 处方触发器删除/推送失败/收费时回退预扣库存*/
DELIMITER $$
DROP TRIGGER /*!50032 IF EXISTS */ `g_afterUpdate_prescription`$$
CREATE
    TRIGGER `g_afterUpdate_prescription` AFTER UPDATE ON `t_prescription`
    FOR EACH ROW
BEGIN
    DECLARE operationTypeTr INT(11);
    DECLARE operationContentTr VARCHAR(50);
    DECLARE needInsertOrderTr VARCHAR(50);
    DECLARE operationIdTr VARCHAR(32);
    DECLARE operationNameTr VARCHAR(32);
    DECLARE operationTimeTr DATETIME;

    SET needInsertOrderTr = 0;

    IF old.IS_DEL != '1' AND new.IS_DEL = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 10;
        SET operationContentTr = '删除处方';
        SET operationIdTr = new.PRE_DOCTOR;
        SET operationNameTr = new.PRE_DOCTORNAME;
        SET operationTimeTr = NOW();
        /**回退预扣库存**/
        DELETE FROM t_center_ypkc_yk WHERE PRE_ID = old.PRE_ID;

    ELSEIF old.IS_CHECK != '1' AND new.IS_CHECK = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 30;
        SET operationContentTr = '审核通过';
        SET operationIdTr = new.CHECK_USERID;
        SET operationNameTr = new.CHECK_USERNAME;
        SET operationTimeTr = new.CHECK_TIME;

    ELSEIF old.IS_CHECK != '2' AND new.IS_CHECK = '2' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 40;
        SET operationContentTr = '审核未通过';
        SET operationIdTr = new.CHECK_USERID;
        SET operationNameTr = new.CHECK_USERNAME;
        SET operationTimeTr = new.CHECK_TIME;

    ELSEIF old.REC_EXT_TYPE != '45' AND new.REC_EXT_TYPE = '45' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 45;
        SET operationContentTr = '推送失败';
        SET operationIdTr = '';
        SET operationNameTr = '';
        SET operationTimeTr = NOW();
        /**回退预扣库存**/
        DELETE FROM t_center_ypkc_yk WHERE PRE_ID = old.PRE_ID;

    ELSEIF old.IS_PAY != '1' AND new.IS_PAY = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 50;
        SET operationContentTr = '处方缴费成功';
        SET operationIdTr = new.PAY_USERID;
        SET operationNameTr = new.PAY_USERNAME;
        SET operationTimeTr = new.PAY_TIME;
        /**回退预扣库存**/
        DELETE FROM t_center_ypkc_yk WHERE PRE_ID = old.PRE_ID;
    END IF;

    IF needInsertOrderTr = 1 THEN
        INSERT INTO `t_order_status` (
            `STATUS_ID`,`REGISTER_ID`,`PRE_ID`,`OPERATION_USERID`,`OPERATION_USERNAME`,`OPERATION_TIME`,
            `OPERATION_TYPE`,`OPERATION_CONTENT`,CREATE_TIME
        ) VALUES (
                     REPLACE (UUID(), '-', ''),'0',new.PRE_ID,operationIdTr,operationNameTr,operationTimeTr,
                     operationTypeTr,operationContentTr,NOW()
                 ) ;
    END IF;
END;
$$
DELIMITER ;

#2022.01.04 郭伟 修改参数：病历打印按钮是否显示
update t_sys_param set PAR_DES = '1显示 0不显示（英文逗号, 分隔）第一位： 中医电子病历；（默认值 不显示）第二位： 今日病人/我的历史病历/病历管理-查看订单 （默认值 显示）' where PAR_CODE = 'PRINT_RECORD_SHOW';
#2022.01.04 zjh 修改电话字段长度
ALTER TABLE `t_dc_address` CHANGE `DC_MOBILE` `DC_MOBILE` VARCHAR (255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '收货人手机号';