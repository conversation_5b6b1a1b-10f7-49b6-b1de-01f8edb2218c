-- 2022-01-14 zjh CONNECT_ID字段长度改为65，因为临床诊疗指南需要
ALTER TABLE `t_doctor_collect`
    CHANGE `CONNECT_ID` `CONNECT_ID` VARCHAR (65) CHARSET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '关联id:例如 药品id,方剂id';
-- 2022-01-14 zjh 增加两个字段：参数分类、参数编号
ALTER TABLE `t_sys_param` ADD COLUMN `PAR_CLASSIFY` VARCHAR (256) NULL COMMENT '参数分类' AFTER `SEQN`,ADD COLUMN `PAR_NUMBER` VARCHAR (256) NULL COMMENT '参数编号' AFTER `PAR_CLASSIFY`;

#2022-01-19 guowei 新增参数：诊断范围验证（扩充诊断）
INSERT INTO `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`, `PAR_CLASSIFY`, `PAR_NUMBER`) VALUES('fa94ad69cf354a3592a8f53e98d7c89c','000000','000000','000000','DIAGNOSIS_RANGE_VERIFY','诊断范围验证（扩充诊断）','0','扩充诊断（95标准 或 95修订之外）是否强制限制保存\n    0：表示无限制（默认）\n    1：表示限制','2022-01-11 13:47:49','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1',NULL,NULL);


#2022-01-19 guowei 批量处理‘参数分类’‘参数编号’
update t_sys_param set PAR_NUMBER = 'A001', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'FIRST_DIAGNOSIS_FUNCTION';
update t_sys_param set PAR_NUMBER = 'A002', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'DIALECTICAL_TAB';
update t_sys_param set PAR_NUMBER = 'A003', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'MASTER_DISEASE';
update t_sys_param set PAR_NUMBER = 'A004', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'DEFAULT_VERIFY_TYPE';
update t_sys_param set PAR_NUMBER = 'A005', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'HISTORY_PRESCRIPTION_DEL';
update t_sys_param set PAR_NUMBER = 'A006', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'INPATIENT_ADVICE_DISPLAY';
update t_sys_param set PAR_NUMBER = 'A007', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'ISOLATED_POINTS_ADDRESS';
update t_sys_param set PAR_NUMBER = 'A008', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'ISOLATED_POINTS_SEND_PHARMACY';
update t_sys_param set PAR_NUMBER = 'A009', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'MAT_SPECIAL_USAGE_SOURCE';
update t_sys_param set PAR_NUMBER = 'A010', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PATIENT_VISIT_RANGE';
update t_sys_param set PAR_NUMBER = 'A011', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRES_COMPUTE_MODE';
update t_sys_param set PAR_NUMBER = 'A012', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_ACUPOINT_COLUMN';
update t_sys_param set PAR_NUMBER = 'A013', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_AVG_AMOUNT';
update t_sys_param set PAR_NUMBER = 'A014', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_CHECK_LIMIT';
update t_sys_param set PAR_NUMBER = 'A015', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_CHECK_MODE';
update t_sys_param set PAR_NUMBER = 'A016', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_CONSIGNEE_DISABLED';
update t_sys_param set PAR_NUMBER = 'A017', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'DC_MOBILE_BAK';
update t_sys_param set PAR_NUMBER = 'A018', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_DECOCT_SHOW';
update t_sys_param set PAR_NUMBER = 'A019', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_DEL_TIME';
update t_sys_param set PAR_NUMBER = 'A020', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_EXTERNAL_COLUMN';
update t_sys_param set PAR_NUMBER = 'A021', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_EXTERNAL_DEFAULT_NUMBER';
update t_sys_param set PAR_NUMBER = 'A022', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_EXTERNAL_HIGHEST_MATS';
update t_sys_param set PAR_NUMBER = 'A023', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_EXTERNAL_HIGHEST_MONEY';
update t_sys_param set PAR_NUMBER = 'A024', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_EXTERNAL_HIGHEST_NUMBER';
update t_sys_param set PAR_NUMBER = 'A025', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_EXTERNAL_LOWEST_MATS';
update t_sys_param set PAR_NUMBER = 'A026', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_EXTERNAL_LOWEST_MONEY';
update t_sys_param set PAR_NUMBER = 'A027', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_EXTERNAL_LOWEST_NUMBER';
update t_sys_param set PAR_NUMBER = 'A028', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_EXTERNAL_MAXIMUM_AMOUNT';
update t_sys_param set PAR_NUMBER = 'A029', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_EXTERNAL_TREATMENT_FEE';
update t_sys_param set PAR_NUMBER = 'A030', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_GRANULE_SAME_PLACE';
update t_sys_param set PAR_NUMBER = 'A031', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_ICD_DISABLED';
update t_sys_param set PAR_NUMBER = 'A032', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INSURANCE';
update t_sys_param set PAR_NUMBER = 'A033', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INTERNAL_COLUMN';
update t_sys_param set PAR_NUMBER = 'A034', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INTERNAL_DEFAULT_NUMBER';
update t_sys_param set PAR_NUMBER = 'A035', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INTERNAL_HIGHEST_MATS';
update t_sys_param set PAR_NUMBER = 'A036', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INTERNAL_HIGHEST_MONEY';
update t_sys_param set PAR_NUMBER = 'A037', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INTERNAL_HIGHEST_NUMBER';
update t_sys_param set PAR_NUMBER = 'A038', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INTERNAL_LOWEST_MATS';
update t_sys_param set PAR_NUMBER = 'A039', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INTERNAL_LOWEST_MONEY';
update t_sys_param set PAR_NUMBER = 'A040', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INTERNAL_LOWEST_NUMBER';
update t_sys_param set PAR_NUMBER = 'A041', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INTERNAL_MAXIMUM_AMOUNT';
update t_sys_param set PAR_NUMBER = 'A042', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INTERNAL_ML';
update t_sys_param set PAR_NUMBER = 'A043', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INTERNAL_PRODUCTION_CONTROL_NUM';
update t_sys_param set PAR_NUMBER = 'A044', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_INTERNAL_PRODUCTION_TYPE_SHOW';
update t_sys_param set PAR_NUMBER = 'A045', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_MEDICAL_INSURANCE_SOURCE';
update t_sys_param set PAR_NUMBER = 'A046', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_MEDICINE_COMPARE_RULE';
update t_sys_param set PAR_NUMBER = 'A047', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_PATENT_COLUMN';
update t_sys_param set PAR_NUMBER = 'A048', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_PREPARATION_COLUMN';
update t_sys_param set PAR_NUMBER = 'A049', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_SAVE_DOSAGE_STANDARD';
update t_sys_param set PAR_NUMBER = 'A050', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_SAVE_DOSAGE_STANDARD_REPLACE';
update t_sys_param set PAR_NUMBER = 'A051', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_SEARCH_RECIPE_MODE';
update t_sys_param set PAR_NUMBER = 'A052', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_SEARCH_RECIPE_OPTIONS';
update t_sys_param set PAR_NUMBER = 'A053', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_SEARCH_RECIPE_SORT';
update t_sys_param set PAR_NUMBER = 'A054', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_SEARCH_RECIPE_TYPE';
update t_sys_param set PAR_NUMBER = 'A055', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'APPOINT_AS_FORMULA';
update t_sys_param set PAR_NUMBER = 'A056', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'APPOINT_RECIPE_LEVEL';
update t_sys_param set PAR_NUMBER = 'A057', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'AUTO_UPDATE_PERSONAL_RULE_AUTH';
update t_sys_param set PAR_NUMBER = 'A058', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PERSONAL_PRESCRIPTION_FILTER_DEPT';
update t_sys_param set PAR_NUMBER = 'A059', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PERSONAL_PRESCRIPTION_FILTER_SELF_BY_INS';
update t_sys_param set PAR_NUMBER = 'A060', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PERSONAL_PRESCRIPTION_MZ_ZY';
update t_sys_param set PAR_NUMBER = 'A061', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'OPEN_APPOINT_RECIPE';
update t_sys_param set PAR_NUMBER = 'A062', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_TRANSFER_ILLNESS_OPTIONS';
update t_sys_param set PAR_NUMBER = 'A063', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRINT_PRESCRIPTION_SHOW';
update t_sys_param set PAR_NUMBER = 'A064', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRINT_PRESCRIPTION_SHOW_QRCODE';
update t_sys_param set PAR_NUMBER = 'A065', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'SAVE_PRESCRIPTION_NO';
update t_sys_param set PAR_NUMBER = 'A066', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'SAVE_REGISTER_ID';
update t_sys_param set PAR_NUMBER = 'A067', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'SYS_CHECK_DAILY_MAX_DOSE';
update t_sys_param set PAR_NUMBER = 'A068', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'UPDATE_PRE_DEL_OLD';
update t_sys_param set PAR_NUMBER = 'A069', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'VALID_PRESCRIPTION_TYPE';
update t_sys_param set PAR_NUMBER = 'A070', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'DIAGNOSIS_RANGE_VERIFY';
update t_sys_param set PAR_NUMBER = 'A071', PAR_CLASSIFY = '智能开方' where PAR_CODE = 'PRESCRIPTION_SPECIAL_DISEASE_RANGE_PICKER_DISABLED_OPTIONS';
update t_sys_param set PAR_NUMBER = 'B001', PAR_CLASSIFY = '药品目录' where PAR_CODE = 'CENTER_DRUG_MAPPING_STRATEGY';
update t_sys_param set PAR_NUMBER = 'C001', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'CHECK_OR_NOT';
update t_sys_param set PAR_NUMBER = 'C002', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'CHECK_OR_NOT_BY_SYS';
update t_sys_param set PAR_NUMBER = 'C003', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'CHECK_PRESCRIPTION_TYPE';
update t_sys_param set PAR_NUMBER = 'C004', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'PRESCRIPTION_CHECK_AUDITOR';
update t_sys_param set PAR_NUMBER = 'C005', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'PRESCRIPTION_CHECK_FORCE';
update t_sys_param set PAR_NUMBER = 'C006', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'PRESCRIPTION_CHECK_FRONT';
update t_sys_param set PAR_NUMBER = 'C007', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'SAFETY_EVALUATION_SIGN';
update t_sys_param set PAR_NUMBER = 'C008', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'SAFETY_EVALUATION_TIP';
update t_sys_param set PAR_NUMBER = 'C009', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'SPECIAL_DRUGS_QUALIFICATIONS';
update t_sys_param set PAR_NUMBER = 'C010', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'SYS_CHECK_FORMULA';
update t_sys_param set PAR_NUMBER = 'C011', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'SYS_CHECK_PERSONAL_PRE';
update t_sys_param set PAR_NUMBER = 'C012', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'SYS_CHECK_USER_NAME';
update t_sys_param set PAR_NUMBER = 'C013', PAR_CLASSIFY = '安全合理用药' where PAR_CODE = 'SYS_CHECK_WORK_TIME';
update t_sys_param set PAR_NUMBER = 'D001', PAR_CLASSIFY = '中医知识库' where PAR_CODE = 'KNOWLEDGE_NUMBER_HIDE';
update t_sys_param set PAR_NUMBER = 'D002', PAR_CLASSIFY = '中医知识库' where PAR_CODE = 'KNOWLEDGE_SHOW_TYPE';
update t_sys_param set PAR_NUMBER = 'D003', PAR_CLASSIFY = '中医知识库' where PAR_CODE = 'SYS_CHECK_KNOW_PRE';
update t_sys_param set PAR_NUMBER = 'E001', PAR_CLASSIFY = '第三方对接' where PAR_CODE = 'CHECK_PATIENT_INFO';
update t_sys_param set PAR_NUMBER = 'E002', PAR_CLASSIFY = '第三方对接' where PAR_CODE = 'PRE_INTERFACE';
update t_sys_param set PAR_NUMBER = 'H001', PAR_CLASSIFY = '医保控制' where PAR_CODE = 'PRESCRIPTION_SAVE_DOSAGE_ULTRALIMIT_TIPS';
update t_sys_param set PAR_NUMBER = 'H002', PAR_CLASSIFY = '医保控制' where PAR_CODE = 'PRESCRIPTION_SPECIAL_HIGHEST_MATS';
update t_sys_param set PAR_NUMBER = 'H003', PAR_CLASSIFY = '医保控制' where PAR_CODE = 'PRESCRIPTION_SPECIAL_LOWEST_MATS';
update t_sys_param set PAR_NUMBER = 'H004', PAR_CLASSIFY = '医保控制' where PAR_CODE = 'INSURANCE_LIMIT_TIP';
update t_sys_param set PAR_NUMBER = 'H005', PAR_CLASSIFY = '医保控制' where PAR_CODE = 'PRESCRIPTION_YB_SHOW_UNGUENT';
update t_sys_param set PAR_NUMBER = 'H006', PAR_CLASSIFY = '医保控制' where PAR_CODE = 'SPECIAL_DIS_MODIFY_COLUMN';
update t_sys_param set PAR_NUMBER = 'H007', PAR_CLASSIFY = '医保控制' where PAR_CODE = 'INSURANCE_LIMIT_OBJECT';
update t_sys_param set PAR_NUMBER = 'H008', PAR_CLASSIFY = '医保控制' where PAR_CODE = 'PRESCRIPTION_SPECIAL_DISEASE_RANGE_PICKER_DISABLED';
update t_sys_param set PAR_NUMBER = 'H009', PAR_CLASSIFY = '医保控制' where PAR_CODE = 'SPECIAL_DIS_CAN_CHOOSE';
update t_sys_param set PAR_NUMBER = 'H010', PAR_CLASSIFY = '医保控制' where PAR_CODE = 'SPECIAL_DIS_FROM_HIS';
update t_sys_param set PAR_NUMBER = 'H011', PAR_CLASSIFY = '医保控制' where PAR_CODE = 'SPECIAL_DIS_PRES_SOURCE';
update t_sys_param set PAR_NUMBER = 'I001', PAR_CLASSIFY = '监管平台' where PAR_CODE = 'GUOKAO_INDICATOR_ANALYSIS';
update t_sys_param set PAR_NUMBER = 'I002', PAR_CLASSIFY = '监管平台' where PAR_CODE = 'PRESCRIPTION_COVERAGE';
update t_sys_param set PAR_NUMBER = 'J001', PAR_CLASSIFY = '电子病历' where PAR_CODE = 'DIAGNOSIS_FUNCTION_AFTER_RECORD';
update t_sys_param set PAR_NUMBER = 'J002', PAR_CLASSIFY = '电子病历' where PAR_CODE = 'PRINT_RECORD_SHOW';
update t_sys_param set PAR_NUMBER = 'J003', PAR_CLASSIFY = '电子病历' where PAR_CODE = 'RECORD_MUST_SAVE';
update t_sys_param set PAR_NUMBER = 'J004', PAR_CLASSIFY = '电子病历' where PAR_CODE = 'REC_PDF_TEMPLATE';
update t_sys_param set PAR_NUMBER = 'J005', PAR_CLASSIFY = '电子病历' where PAR_CODE = 'TRANSFER_RECORD_SHOW';

#2022.1.24 gw 增加我的收藏菜单
insert into `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) values('219','我的收藏','/study/my-favorite',NULL,NULL,'2','182','2022-01-11 09:38:59','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'12',NULL,'1');
#2022.1.24 gw 增加经方查询菜单
INSERT INTO `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) VALUES('220','经方查询','/study/classical',NULL,NULL,'2','182','2022-01-11 15:40:21','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'4',NULL,'1');
#2022.1.24 gw 增加处方分析(新都)菜单
INSERT INTO `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) VALUES('221','处方分析(新都)','/statistical/p&m',NULL,NULL,'2','14','2022-01-24 10:40:21','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'6',NULL,'1');

#2022.1.28 gw 配方库存增加药房ID
ALTER TABLE `t_formula_auth` CHANGE `APP_ID` `APP_ID` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'APPID',
                             CHANGE `INS_CODE` `INS_CODE` VARCHAR(32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '医疗机构代码',
                             ADD COLUMN `STORE_ID` VARCHAR(32) NOT NULL COMMENT '药房ID' AFTER `DEPT_ID`;
#2022.1.28 gw 配方库存删除无效数据
DELETE FROM `t_formula_auth` WHERE FORMULA_ID = '' ;