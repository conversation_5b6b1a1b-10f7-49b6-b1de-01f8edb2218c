#2022-03-01 guowei 新增参数：开方时不可搜索类名疾病（95修订带.的疾病）
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`, `PAR_CLASSIFY`, `PAR_NUMBER`) values('4f621b14c4684f26bf732c7acbe75a2d','000000','000000','000000','DIS_FILTER_CATEGORY','开方时不可搜索类名疾病（95修订带.的疾病）','1','0可以搜索 1不可以搜索','2022-03-01 10:23:50','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1','智能开方','A072');
#2022-03-15 guowei 新增参数：兼证加减按钮隐藏显示
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`, `PAR_CLASSIFY`, `PAR_NUMBER`) values('f0138c8fc4b94539b9973f0f449477fb','000000','000000','000000','JIANZHENG_BTN_SHOW','兼证加减按钮隐藏显示','0','1显示，0隐藏','2022-03-11 13:20:39','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1','智能开方','A073');
#2022-03-15 guowei 新增参数：是否显示病名描述
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`, `PAR_CLASSIFY`, `PAR_NUMBER`) values('4f79e2757ad846539a8a1ed34183779b','000000','000000','000000','DISEASE_DESCRIPTION','是否显示病名描述','1','1显示，0隐藏','2022-03-11 13:20:39','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1','智能开方','A088');

#2022-03-03 guowei 批量处理从HIS同步过来的协定方权限（使用HIS科室）
DELIMITER $$
DROP PROCEDURE IF EXISTS `batch_personal_rule_auth_his`$$
CREATE
    /*[DEFINER = { user | CURRENT_USER }]*/
    PROCEDURE `batch_personal_rule_auth_his`()
BEGIN
    INSERT INTO `t_personal_rule_auth` (`rule_id`, `dept_id`, `dept_check_all`, `user_id`, `pers_pre_id`)
    SELECT * FROM (
                      SELECT p.IS_SHARE, p.`DEPT_ID` DEPT_ID, 0 AS dept_check_all, i.id, p.PERS_PRE_ID
                      FROM t_personal_prescription p
                               JOIN sys_admin_info i ON i.id = p.PRE_OWNER
                      WHERE p.is_del = '0' AND p.IS_SHARE = '0' AND p.`DEPT_ID` IS NOT NULL
                      UNION ALL
                      SELECT p.IS_SHARE, d.DEP_ORIGIN_ID DEPT_ID, 1 AS dept_check_all, i.id, p.PERS_PRE_ID
                      FROM t_personal_prescription p
                               JOIN sys_department d ON d.`DEP_ORIGIN_ID` = p.`DEPT_ID` AND d.`INS_CODE` = p.`INS_CODE` AND d.app_id = p.app_id
                               JOIN sys_admin_practice e ON e.dep_id = d.`DEP_ORIGIN_ID` AND e.ins_code = d.ins_code AND e.app_id = d.app_id
                               JOIN sys_admin_info i ON i.`id` = e.`admin_id`
                      WHERE p.is_del = '0' AND p.IS_SHARE = '1'
                      UNION ALL
                      SELECT p.IS_SHARE, d.DEP_ORIGIN_ID DEPT_ID, 1 AS dept_check_all, i.id, p.PERS_PRE_ID
                      FROM t_personal_prescription p
                               JOIN sys_institution s ON s.`INS_CODE` = p.`INS_CODE` AND s.app_id = p.app_id
                               JOIN sys_department d ON d.`INS_CODE` = s.`INS_CODE` AND d.app_id = s.app_id
                               JOIN sys_admin_practice e ON e.dep_id = d.`DEP_ORIGIN_ID` AND e.ins_code = d.ins_code AND e.app_id = d.app_id
                               JOIN sys_admin_info i ON i.`ID` = e.`admin_id`
                      WHERE p.is_del = '0' AND p.IS_SHARE = '2'
                  ) s
    WHERE NOT EXISTS (SELECT 1 FROM t_personal_rule_auth a WHERE a.`rule_id` = s.`IS_SHARE` AND a.`dept_id` = s.`DEPT_ID`
                                                             AND a.`user_id` = s.`id` AND a.`pers_pre_id` = s.`PERS_PRE_ID`);
END$$
DELIMITER ;


#2022-03-25 guowei 补充参数（5.12.2功能）：配送地址默认(寄送)类型
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`, `PAR_CLASSIFY`, `PAR_NUMBER`) values('664a86e11b984a42b1e6b2c98ef7a36f','000000','000000','000000','PRESCRIPTION_CONSIGNEE_DEFAULT_TYPE','配送地址默认(寄送)类型','2','开方时勾选配送默认地址填充并切换；1（填充值：医院地址，按钮：寄到患者家里）；2（填充值：患者地址，按钮：寄到医院）','2021-09-29 11:50:02','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1','智能开方','A089');
