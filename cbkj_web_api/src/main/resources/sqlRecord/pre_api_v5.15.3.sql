CREATE TABLE `t_statistics_register` (
                                         `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                         `app_id` VARCHAR(32) DEFAULT NULL COMMENT '医联体ID',
                                         `app_name` VARCHAR(32) DEFAULT NULL COMMENT '医联体',
                                         `ins_code` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构代码',
                                         `ins_name` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构',
                                         `dept_id` VARCHAR(32) DEFAULT NULL COMMENT '科室ID',
                                         `dept_name` VARCHAR(32) DEFAULT NULL COMMENT '科室',
                                         `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID',
                                         `user_name` VARCHAR(32) DEFAULT NULL COMMENT '用户',
                                         `register_times` INT(11) NOT NULL DEFAULT '0' COMMENT '就诊人次',
                                         `electronic_record_num` INT(11) NOT NULL DEFAULT '0' COMMENT '电子病历',
                                         `create_date` DATE NOT NULL COMMENT '统计日期',
                                         `insert_date` DATETIME DEFAULT NULL COMMENT '插入时间',
                                         PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=302808 DEFAULT CHARSET=utf8mb4 COMMENT='就诊人次统计表';

CREATE TABLE `t_statistics_prescription` (
                                             `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                             `app_id` VARCHAR(32) DEFAULT NULL COMMENT '医联体ID',
                                             `app_name` VARCHAR(32) DEFAULT NULL COMMENT '医联体',
                                             `ins_code` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构代码',
                                             `ins_name` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构',
                                             `dept_id` VARCHAR(32) DEFAULT NULL COMMENT '科室ID',
                                             `dept_name` VARCHAR(32) DEFAULT NULL COMMENT '科室',
                                             `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID',
                                             `user_name` VARCHAR(32) DEFAULT NULL COMMENT '用户',
                                             `total_num` INT(11) NOT NULL DEFAULT '0' COMMENT '总开方数量',
                                             `inner_num` INT(11) NOT NULL DEFAULT '0' COMMENT '内服方数量',
                                             `exter_num` INT(11) NOT NULL DEFAULT '0' COMMENT '外用方数量',
                                             `acu_num` INT(11) NOT NULL DEFAULT '0' COMMENT '适宜技术方数量',
                                             `patent_num` INT(11) NOT NULL DEFAULT '0' COMMENT '中成药数量',
                                             `prepare_num` INT(11) NOT NULL DEFAULT '0' COMMENT '制剂数量',
                                             `create_date` DATE NOT NULL COMMENT '统计日期',
                                             `insert_date` DATETIME DEFAULT NULL COMMENT '插入时间',
                                             PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=302805 DEFAULT CHARSET=utf8mb4 COMMENT='处方统计表';

CREATE TABLE `t_statistics_personal_prescription` (
                                                      `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                                      `app_id` VARCHAR(32) DEFAULT NULL COMMENT '医联体ID',
                                                      `app_name` VARCHAR(32) DEFAULT NULL COMMENT '医联体',
                                                      `ins_code` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构代码',
                                                      `ins_name` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构',
                                                      `dept_id` VARCHAR(32) DEFAULT NULL COMMENT '科室ID',
                                                      `dept_name` VARCHAR(32) DEFAULT NULL COMMENT '科室',
                                                      `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID',
                                                      `user_name` VARCHAR(32) DEFAULT NULL COMMENT '用户',
                                                      `total_num` INT(11) NOT NULL DEFAULT '0' COMMENT '协定方总数',
                                                      `self_num` INT(11) NOT NULL DEFAULT '0' COMMENT '个人协定方数量',
                                                      `dept_num` INT(11) NOT NULL DEFAULT '0' COMMENT '科室协定方数量',
                                                      `ins_num` INT(11) NOT NULL DEFAULT '0' COMMENT '全院协定方数量',
                                                      `app_num` INT(11) NOT NULL DEFAULT '0' COMMENT '专家经验共享数量',
                                                      `create_date` DATE NOT NULL COMMENT '统计日期',
                                                      `insert_date` DATETIME DEFAULT NULL COMMENT '插入时间',
                                                      PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=302805 DEFAULT CHARSET=utf8mb4 COMMENT='协定方统计表';

CREATE TABLE `t_statistics_analysis` (
                                         `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                         `app_id` VARCHAR(32) DEFAULT NULL COMMENT '医联体ID',
                                         `app_name` VARCHAR(32) DEFAULT NULL COMMENT '医联体',
                                         `ins_code` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构代码',
                                         `ins_name` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构',
                                         `dept_id` VARCHAR(32) DEFAULT NULL COMMENT '科室ID',
                                         `dept_name` VARCHAR(32) DEFAULT NULL COMMENT '科室',
                                         `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID',
                                         `user_name` VARCHAR(32) DEFAULT NULL COMMENT '用户',
                                         `usage_times` INT(11) NOT NULL DEFAULT '0' COMMENT '体质辨识报告数量',
                                         `create_date` DATE NOT NULL COMMENT '统计日期',
                                         `insert_date` DATETIME DEFAULT NULL COMMENT '插入时间',
                                         PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=302821 DEFAULT CHARSET=utf8mb4 COMMENT='体质辨识统计表';

CREATE TABLE `t_statistics_function2` (
                                          `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                          `app_id` VARCHAR(32) DEFAULT NULL COMMENT '医联体ID',
                                          `app_name` VARCHAR(32) DEFAULT NULL COMMENT '医联体',
                                          `ins_code` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构代码',
                                          `ins_name` VARCHAR(32) DEFAULT NULL COMMENT '医疗机构',
                                          `dept_id` VARCHAR(32) DEFAULT NULL COMMENT '科室ID',
                                          `dept_name` VARCHAR(32) DEFAULT NULL COMMENT '科室',
                                          `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID',
                                          `user_name` VARCHAR(32) DEFAULT NULL COMMENT '用户',
                                          `classify` VARCHAR(64) DEFAULT NULL COMMENT '分类',
                                          `function` VARCHAR(64) NOT NULL COMMENT '功能',
                                          `usage_times` INT(11) NOT NULL COMMENT '使用次数',
                                          `create_date` DATE NOT NULL COMMENT '统计日期',
                                          `insert_date` DATETIME DEFAULT NULL COMMENT '插入时间',
                                          PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=302809 DEFAULT CHARSET=utf8mb4 COMMENT='功能统计表';


INSERT INTO `t_sys_param` (
    `PAR_ID`,
    `APP_ID`,
    `INS_CODE`,
    `DEPT_ID`,
    `PAR_CODE`,
    `PAR_NAME`,
    `PAR_VALUES`,
    `PAR_DES`,
    `CREATE_DATE`,
    `CREATE_USER`,
    `CREATE_USERNAME`,
    `UPDATE_DATE`,
    `UPDATE_USER`,
    `UPDATE_USERNAME`,
    `DEL_DATE`,
    `DEL_USER`,
    `DEL_USERNAME`,
    `IS_DEL`,
    `SEQN`,
    `PAR_CLASSIFY`,
    `PAR_NUMBER`
)
VALUES
    (
        '11111122222223333334444455',
        '000000',
        '000000',
        '000000',
        'SYS_STUDY_COLUMN',
        '系统学习统计项目',
        '1,2,3,4,5,6,7,8,9,10,11,12,13',
        '智能辨证 智能推导 名家验案 中药查询 方剂查询 经络穴位查询  经方查询 疾病查询 中成药  临床诊疗指南 舌诊 脉诊  古书籍',
        '2021-02-22 14:57:24',
        '70810c874405453b99c6c2cf72296fe5',
        '管理员',
        '2021-02-24 15:24:11',
        '70810c874405453b99c6c2cf72296fe5',
        '管理员',
        NULL,
        NULL,
        NULL,
        '0',
        NULL,
        '监管平台',
        'E003'
    );



INSERT INTO `sys_admin_menu` (
    `mid`,
    `mname`,
    `url`,
    `enabled`,
    `parent_mid`,
    `create_date`,
    `cteate_id`,
    `menu_type`,
    `sort_number`
)
VALUES
    (
        '2231',
        '系统学习统计',
        '/statistical/study-target',
        '2',
        '14',
        '2022-05-31 18:58:43',
        'system',
        '2',
        '10'
    );
INSERT INTO `sys_admin_menu` (
    `mid`,
    `mname`,
    `url`,
    `enabled`,
    `parent_mid`,
    `create_date`,
    `cteate_id`,
    `menu_type`,
    `sort_number`
)
VALUES
    (
        '2311',
        '系统使用统计',
        '/statistical/system-use',
        '2',
        '14',
        '2022-05-31 18:58:43',
        'system',
        '2',
        '11'
    );

DELIMITER $$

USE `cbkj_web_api`$$

DROP TRIGGER /*!50032 IF EXISTS */ `g_afterUpdate_prescription`$$

CREATE
    /*[DEFINER = { user | CURRENT_USER }]*/
    TRIGGER `g_afterUpdate_prescription` AFTER UPDATE ON `t_prescription`
    FOR EACH ROW BEGIN
    DECLARE operationTypeTr INT(11);
    DECLARE operationContentTr VARCHAR(50);
    DECLARE needInsertOrderTr VARCHAR(50);
    DECLARE operationIdTr VARCHAR(32);
    DECLARE operationNameTr VARCHAR(32);
    DECLARE operationTimeTr DATETIME;
    SET needInsertOrderTr = 0;
    IF old.IS_DEL != '1' AND new.IS_DEL = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 10;
        SET operationContentTr = '删除处方';
        SET operationIdTr = new.PRE_DOCTOR;
        SET operationNameTr = new.PRE_DOCTORNAME;
        SET operationTimeTr = NOW();
        /**回退预扣库存**/
        DELETE FROM t_center_ypkc_yk WHERE PRE_ID = old.PRE_ID;
    ELSEIF old.IS_CHECK != '1' AND new.IS_CHECK = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 30;
        SET operationContentTr = '审核通过';
        SET operationIdTr = new.CHECK_USERID;
        SET operationNameTr = new.CHECK_USERNAME;
        SET operationTimeTr = new.CHECK_TIME;
    ELSEIF old.IS_CHECK != '2' AND new.IS_CHECK = '2' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 40;
        SET operationContentTr = '审核未通过';
        SET operationIdTr = new.CHECK_USERID;
        SET operationNameTr = new.CHECK_USERNAME;
        SET operationTimeTr = new.CHECK_TIME;
    ELSEIF old.REC_EXT_TYPE != '45' AND new.REC_EXT_TYPE = '45' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 45;
        SET operationContentTr = '推送失败';
        SET operationIdTr = new.PRE_DOCTOR;
        SET operationNameTr = new.PRE_DOCTORNAME;
        SET operationTimeTr = NOW();
        /**回退预扣库存**/
        DELETE FROM t_center_ypkc_yk WHERE PRE_ID = old.PRE_ID;
    ELSEIF old.IS_PAY != '1' AND new.IS_PAY = '1' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 50;
        SET operationContentTr = '处方缴费成功';
        SET operationIdTr = new.PAY_USERID;
        SET operationNameTr = new.PAY_USERNAME;
        SET operationTimeTr = new.PAY_TIME;
        /**回退预扣库存**/
        DELETE FROM t_center_ypkc_yk WHERE PRE_ID = old.PRE_ID;
    ELSEIF old.REC_EXT_TYPE != '110' AND new.REC_EXT_TYPE = '110' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 110;
        SET operationContentTr = '退费';
        SET operationIdTr = new.PRE_DOCTOR;
        SET operationNameTr = new.PRE_DOCTORNAME;
        SET operationTimeTr = NOW();
    ELSEIF old.REC_EXT_TYPE != '111' AND new.REC_EXT_TYPE = '111' THEN
        SET needInsertOrderTr = 1;
        SET operationTypeTr = 111;
        SET operationContentTr = '退药';
        SET operationIdTr = new.PRE_DOCTOR;
        SET operationNameTr = new.PRE_DOCTORNAME;
        SET operationTimeTr = NOW();
    END IF;
    IF needInsertOrderTr = 1 THEN
        INSERT INTO `t_order_status` (
            `STATUS_ID`,`REGISTER_ID`,`PRE_ID`,`OPERATION_USERID`,`OPERATION_USERNAME`,`OPERATION_TIME`,
            `OPERATION_TYPE`,`OPERATION_CONTENT`,CREATE_TIME
        ) VALUES (
                     REPLACE (UUID(), '-', ''),'0',new.PRE_ID,operationIdTr,operationNameTr,operationTimeTr,
                     operationTypeTr,operationContentTr,NOW()
                 ) ;
    END IF;
END;
$$

DELIMITER ;