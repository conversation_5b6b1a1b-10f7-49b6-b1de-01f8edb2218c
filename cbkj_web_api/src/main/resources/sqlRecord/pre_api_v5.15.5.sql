-- 预扣库存开关
ALTER TABLE `t_display`
    ADD COLUMN `withhold_switch` VARCHAR (1) DEFAULT '1' NULL COMMENT '是否预扣计算0否1是' AFTER `UPDATE_USER`;
-- 处方保存接口校验库存开关
ALTER TABLE `t_display`
    ADD COLUMN `pre_stock_switch` VARCHAR (1) DEFAULT '0' NULL COMMENT '处方保存接口校验库存开关1是0否' AFTER `UPDATE_USER`;



-- 功能使用统计表增加索引
ALTER TABLE `t_statistics_function2` ADD INDEX `IDX_FUNCTION` (`classify`, `function`), ADD INDEX `IDX_CREATE_DATE` (`create_date`);


#修改菜单名字
update
    `sys_admin_menu`
set
    `mname` = '处方分析'
where `mid` = '221';

