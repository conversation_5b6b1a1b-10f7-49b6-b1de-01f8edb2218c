-- 公共字典。增加患者类型 大类。
insert into `t_sys_code` (
    `CODE_ID`,
    `CODE_VALUE`,
    `CODE_NAME`,
    `CODE_NUM`,
    `CREATE_DATE`,
    `CREATE_USER`,
    `CREATE_USERNAME`
)
values
    (
        '34',
        'patients_types',
        '患者类型',
        '34',
        '2022-08-03 17:51:28',
        'admin',
        'admin'
    );
-- 增加患者类型item
insert into `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) values('0dad3f09975f4ca79530c0d4e6539d2f','自费','34','0',NULL,NULL,'0',NULL,NULL,'0','2022-08-04 09:17:46','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
insert into `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) values('3ded538d243443868e625a3d26d34ba9','医保离休','34','2',NULL,NULL,'2',NULL,NULL,'0','2022-08-04 09:18:34','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
insert into `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) values('72a91902ef7243e5ac1d83663f9b971e','普通医保','34','1',NULL,NULL,'1',NULL,NULL,'0','2022-08-04 09:18:07','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
insert into `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) values('82a964c810c74408b08948a606a0eb3c','职工特慢','34','3',NULL,NULL,'3',NULL,NULL,'0','2022-08-04 09:18:47','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
insert into `t_sys_code_item` (`ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `HIDE_PROJECT`, `ZIFU1`, `ZIFU2`, `ZIFU3`, `ZIFU4`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `RATE`) values('cbea0fbfde164f01ba6788f079aa28f8','居民特慢','34','5',NULL,NULL,'J',NULL,NULL,'0','2022-08-04 09:19:15','70810c874405453b99c6c2cf72296fe5','admin',NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
-- 增加患者类型显示 参数控制
insert into `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`, `PAR_CLASSIFY`, `PAR_NUMBER`) values('aaf39e754bc24fa98f224e26ee7f97af','000000','000000','000000','PATIENTS_TYPES_SHOW','患者类型显示','0','1.开：智能云开方显示 0关：智能云开方不显示','2022-08-04 09:26:38','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1','医保控制','F013');

-- 增加处方表 患者类型字段
ALTER TABLE `t_prescription`
    ADD COLUMN `patient_types` VARCHAR (32) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '患者类型(0自费1普通医保2医保离休3职工特慢J居民特慢)';
ALTER TABLE `t_prescription`
    ADD COLUMN `patient_types_name` VARCHAR (64) NULL COMMENT '患者类型名称';



