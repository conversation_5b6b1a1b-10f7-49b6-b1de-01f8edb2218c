insert into `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) values('2314','监管看板','/statistical/supervise-board',NULL,NULL,'2','14','2022-10-19 15:53:03','42fe8c53d2b04f55bba32919ea77cce7','2',NULL,NULL,NULL,'-1',NULL,'1');

UPDATE
    `t_sys_param`
SET
    `PAR_NAME` = '医保限制第一次保存时提醒内容'
WHERE `PAR_CODE` = 'INSURANCE_LIMIT_TIP';

INSERT INTO `t_sys_param` (
  `PAR_ID`,
  `PAR_CODE`,
  `PAR_NAME`,
  `PAR_VALUES`,
  `PAR_DES`,
  `CREATE_DATE`,
  `CREATE_USER`,
  `CREATE_USERNAME`,
  `SEQN`,
  `PAR_CLASSIFY`,
  `PAR_NUMBER`
)
VALUES
  (
    'b7556022358111edad8d00163f006620',
    'INSURANCE_LIMIT_REMIND',
    '医保限制开药时提醒',
    '0',
    '1开0关',
    '2022-09-16 13:34:01',
    'admin',
    'admin',
    '-1',
    '医保控制',
    'F014'
  );
#增加药品明细表-医保说明-医保强制提醒
ALTER TABLE `t_center_his_ypmlmx`
  ADD COLUMN `xzsm` VARCHAR (128) NULL COMMENT '医保说明';


#增加表
CREATE TABLE `t_statistics_health_care` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                            `app_id` varchar(32) DEFAULT NULL,
                                            `app_name` varchar(32) DEFAULT NULL,
                                            `ins_code` varchar(32) DEFAULT NULL,
                                            `ins_name` varchar(32) DEFAULT NULL,
                                            `dept_id` varchar(32) DEFAULT NULL,
                                            `create_date` date DEFAULT NULL COMMENT '统计日期',
                                            `insert_date` datetime DEFAULT NULL COMMENT '插入时间',
                                            `ypdm_his` varchar(32) DEFAULT NULL,
                                            `remind_times` int(11) DEFAULT NULL COMMENT '医保控费提醒 提醒次数',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `t_statistics_prescription_audit` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                                   `app_id` varchar(32) DEFAULT NULL,
                                                   `app_name` varchar(32) DEFAULT NULL,
                                                   `ins_code` varchar(32) DEFAULT NULL,
                                                   `ins_name` varchar(32) DEFAULT NULL,
                                                   `dept_id` varchar(32) DEFAULT NULL,
                                                   `create_date` date DEFAULT NULL COMMENT '统计日期',
                                                   `insert_date` datetime DEFAULT NULL COMMENT '插入时间',
                                                   `tcm_all_num` int(11) DEFAULT NULL COMMENT '中药审方数量：系统审方+人工审方之和，统计所有状态处方；',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `t_statistics_prescription_medication` (
                                                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键自增长',
                                                        `app_id` varchar(32) DEFAULT NULL,
                                                        `app_name` varchar(32) DEFAULT NULL,
                                                        `ins_code` varchar(32) DEFAULT NULL,
                                                        `ins_name` varchar(32) DEFAULT NULL,
                                                        `dept_id` varchar(32) DEFAULT NULL,
                                                        `create_date` date DEFAULT NULL COMMENT '统计日期',
                                                        `insert_date` datetime DEFAULT NULL COMMENT '插入时间',
                                                        `number` int(11) DEFAULT NULL COMMENT '安全用药提醒次数',
                                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `t_statistics_prescription_type` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                                  `app_id` varchar(32) DEFAULT NULL,
                                                  `app_name` varchar(32) DEFAULT NULL,
                                                  `ins_code` varchar(32) DEFAULT NULL,
                                                  `ins_name` varchar(32) DEFAULT NULL,
                                                  `dept_id` varchar(32) DEFAULT NULL,
                                                  `create_date` date DEFAULT NULL COMMENT '统计日期',
                                                  `insert_date` datetime DEFAULT NULL COMMENT '插入时间',
                                                  `number` int(11) DEFAULT NULL,
                                                  `num_type` int(11) DEFAULT NULL COMMENT '1.煎煮中处方:【实时】处方状态为“85泡药、130煎药、135包装”的处方数量之和;2.已签收处方:已签收处方【实时】处方状态为“150收货”的处方数量之和',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



ALTER TABLE `t_statistics_dis_num` ADD COLUMN `REC_TRE_TYPE` VARCHAR (1) NULL COMMENT '病历类型（1门诊 2住院）' ;
#增加索引
ALTER TABLE t_order_status ADD KEY (`OPERATION_TYPE`);
ALTER TABLE t_statistics_dis_num CHANGE `time_type` `time_type` INT (1) NOT NULL COMMENT '时间类型:1昨天,2近半月,3近一个季度,4近一年 5 近半年';



