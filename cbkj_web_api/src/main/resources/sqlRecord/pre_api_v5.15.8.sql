update
    `cbkj_web_api`.`t_sys_param`
set
    `PAR_NAME` = '医保提醒患者范围控制',
    `PAR_VALUES` = '1,3',
    `PAR_DES` = '1.门诊医保、2.门诊自费、3.住院医保、4.住院自费、5.门诊特病'
where par_code = 'INSURANCE_LIMIT_OBJECT';

insert into `t_sys_param`
    (`PAR_ID`, `APP_ID`, `INS_CODE`, `DEPT_ID`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`, `SEQN`, `PAR_CLASSIFY`, `PAR_NUMBER`)
values('398089ac859b4aa882a4a223fd19ea2e','000000','000000','000000','MEDICINE_QUALIFICATION_LIMIT','无中医资质限制提醒','0','1开0关 ','2022-12-14 14:05:36','70810c874405453b99c6c2cf72296fe5','管理员',NULL,NULL,NULL,NULL,NULL,NULL,'0','-1','安全合理用药','C014');
