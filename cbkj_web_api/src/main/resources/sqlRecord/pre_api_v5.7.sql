/*2021.05.12 gw 药房配置增加药房类型（1本地药房 2共享药房）*/
ALTER TABLE `t_display` ADD COLUMN `STORE_TYPE` INT(1) DEFAULT 1 NOT NULL COMMENT '药房类型（1本地药房 2共享药房）' AFTER `STORE_NAME`;

/*2021.05.13 gw 处方状态增加推送成功失败*/
ALTER TABLE `t_prescription` CHANGE `REC_EXT_TYPE` `REC_EXT_TYPE` VARCHAR(3) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '处方状态（3预约挂号 5挂号缴费 7咨询缴费 10开方 20删除 30审核通过 40审核未通过 44推送成功 45推送失败 50处方缴费 80配药 90发药 100取消发药 110退药 120取消退药 130煎药 140配送 150收货）';
ALTER TABLE `t_order_status` CHANGE `OPERATION_TYPE` `OPERATION_TYPE` INT(11) NULL COMMENT '处方状态（3预约挂号 5挂号缴费 7咨询缴费 10开方 20删除 30审核通过 40审核未通过 44推送成功 45推送失败 50处方缴费 80配药 90发药 100取消发药 110退药 120取消退药 130煎药 140配送 150收货）';
/*2021.05.13 gw 同步药房机构对照表*/
CREATE TABLE `t_center_store_ins_mapping` (
  `app_id` varchar(32) NOT NULL COMMENT '医共体ID',
  `ins_code` varchar(32) NOT NULL COMMENT '医疗机构代码',
  `store_id` varchar(32) NOT NULL COMMENT '药房ID',
  `zhongyaolx` int(1) NOT NULL COMMENT '中药类型',
  KEY `IDX_APP_ID` (`app_id`),
  KEY `IDX_INS_CODE` (`ins_code`),
  KEY `IDX_STORE_ID` (`store_id`),
  KEY `IDX_ZHONGYAOLX` (`zhongyaolx`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='药房与医疗机构映射关系表';
/*2021.05.17 wt 增加特殊药品使用权限的参数配置         常熟使用！！！   */
INSERT INTO `t_sys_param` (`PAR_ID`, `APP_ID`, `INS_CODE`, `PAR_CODE`, `PAR_NAME`, `PAR_VALUES`, `PAR_DES`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `UPDATE_DATE`, `UPDATE_USER`, `UPDATE_USERNAME`, `DEL_DATE`, `DEL_USER`, `DEL_USERNAME`, `IS_DEL`) VALUES ('0ecdf724787a4718b6b1ac6d8fe59ccc', '000000', '000000', 'SPECIAL_DRUGS_QUALIFICATIONS', '控制超安全用药权限', '4,5', '可配置的值包括1禁用药、2忌用药、3慎用药、4十八反、5十九畏、6不宜同用、7孕妇禁用、8孕妇慎用、9孕妇忌用、10大毒\r\n  、11有毒、12小毒，默认空。', '2021-04-23 10:43:50', '70810c874405453b99c6c2cf72296fe5', '管理员', '2021-05-14 16:02:37', '70810c874405453b99c6c2cf72296fe5', '管理员', NULL, NULL, NULL, '0');

