
/* 5.8  2021-04-21 gw 前五增长率统计表修改*/
delete from `t_statistics_dis_rate`;
ALTER TABLE `t_statistics_dis_rate` CHANGE `group_name` `group_name` DATE NULL COMMENT '分组名称(统计日期)';

/* 5.8  2021-04-23 gw 体质辨识结果*/
ALTER TABLE `t_user_analysis_result` ADD COLUMN `register_id` VARCHAR(32) NULL COMMENT '挂号ID' AFTER `ins_code`, CHANGE `tt_point` `tt_point` TEXT CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '调体要点', CHANGE `tt_prescription` `tt_prescription` TEXT CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '调体方药', CHANGE `lzjj` `lzjj` TEXT CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '临证加减', CHANGE `vegetables` `vegetables` TEXT CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '养生建议', CHANGE `sports` `sports` TEXT CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '运动建议', ADD COLUMN `emotional_adjustment` TEXT NULL COMMENT '情志调摄' AFTER `sports`, ADD COLUMN `diet_recuperation` TEXT NULL COMMENT '饮食调养' AFTER `emotional_adjustment`, ADD COLUMN `daily_life_adjustment` TEXT NULL COMMENT '起居调摄' AFTER `diet_recuperation`, ADD COLUMN `acupoint_health_care` TEXT NULL COMMENT '穴位保健' AFTER `daily_life_adjustment`, ADD COLUMN `medicated_diet` TEXT NULL COMMENT '推荐药膳' AFTER `acupoint_health_care`, ADD COLUMN `flower_tea` TEXT NULL COMMENT '推荐花茶' AFTER `medicated_diet`, ADD COLUMN `other` TEXT NULL COMMENT '其他' AFTER `flower_tea`;
ALTER TABLE `t_user_analysis_result` ADD COLUMN `score_img` VARCHAR(512) NULL COMMENT '得分图片地址' AFTER `doc_options`;
ALTER TABLE `t_user_analysis_result` ADD COLUMN `analy_type` INT(1) DEFAULT 1 NOT NULL COMMENT '问卷类型 1标准2公卫' AFTER `score_img`;
/* 5.8  2021-04-23 gw 体质辨识结果穴位*/
CREATE TABLE `t_user_analysis_result_acu`( `analy_id` VARCHAR(32) NOT NULL COMMENT '辨识ID', `acu_id` VARCHAR(32) NOT NULL COMMENT '穴位ID', `acu_code` VARCHAR(64) COMMENT '穴位代码', `acu_name` VARCHAR(64) COMMENT '穴位名称', `acu_img` VARCHAR(512) COMMENT '穴位图片', PRIMARY KEY (`analy_id`, `acu_id`) );
ALTER TABLE `t_user_analysis_result_acu` COMMENT='用户体质辨识穴位';

/* 5.8  2021-04-23 gw 中医治未病菜单*/
INSERT INTO `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) VALUES('205','中医治未病',NULL,NULL,NULL,'2','0','2021-04-23 11:53:09','70810c874405453b99c6c2cf72296fe5','1',NULL,NULL,NULL,'3',NULL,NULL),('206','体质辨识管理','analysis/manage',NULL,NULL,'2','205','2021-04-23 11:57:03','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'1',NULL,'1');
UPDATE `sys_admin_menu` SET parent_mid = '205' WHERE `mid` = '9';
INSERT INTO `sys_admin_rule_menu` (`rmid`, `rid`, `mid`) VALUES('5ad1d4cf76be462b8b64630dc21bfb10','b4a17b6b635c4de48f95178676905aa5','205');
INSERT INTO `sys_admin_rule_menu` (`rmid`, `rid`, `mid`) VALUES('954a7e6c75d84a6281934271686d6cb1','b4a17b6b635c4de48f95178676905aa5','206');
INSERT INTO `sys_admin_rule_menu` (`rmid`, `rid`, `mid`) SELECT REPLACE(rmid, SUBSTRING(rmid, 5, 4), '0000'), rid, '205' FROM `sys_admin_rule_menu` WHERE `mid` = '9';

INSERT INTO `t_sys_code` ( `CODE_ID`, `CODE_VALUE`, `CODE_NAME`, `CODE_NUM`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`, `IS_DISABLE`) VALUES ( '29', 'analysisResult', '体质辨识结果项', '29', NOW(), '', '', '0', '0' ) ;
INSERT INTO `t_sys_code_item` ( `ITEM_ID`, `ITEM_NAME`, `CODE_ID`, `ITEM_NUM`, `ZIFU2`, `ZIFU5`, `CREATE_DATE`, `CREATE_USER`, `CREATE_USERNAME`, `IS_DEL`) VALUES ( '291', '调体法则', '29', '1', 'ttRule', '0', NOW(), '', '', '0' ), ( '292', '调体要点', '29', '2', 'ttPoint', '0', NOW(), '', '', '0' ), ( '293', '推荐药膳', '29', '3', 'medicatedDiet', '0', NOW(), '', '', '0' ), ( '294', '推荐花茶', '29', '4', 'flowerTea', '0', NOW(), '', '', '0' ), ( '295', '情志调摄', '29', '5', 'emotionalAdjustment', '1', NOW(), '', '', '0' ), ( '296', '饮食调养', '29', '6', 'dietRecuperation', '1', NOW(), '', '', '0' ), ( '297', '起居调摄', '29', '7', 'dailyLifeAdjustment', '1', NOW(), '', '', '0' ), ( '298', '运动保健', '29', '8', 'sports', '1', NOW(), '', '', '0' ), ( '299', '穴位保健', '29', '9', 'acupointHealthCare', '1', NOW(), '', '', '0' ), ( '230', '其他',    '29', '10', 'other', '1', NOW(), '', '', '0' ) ;

/* 5.8  2021-04-26 gw 体质辨识管理菜单修改*/
UPDATE sys_admin_menu SET url = '/syndrome/answer' WHERE `mid` = '9';
UPDATE sys_admin_menu SET url = '/syndrome/manage' WHERE `mid` = '206';

/* 5.8  2021-04-26 gw 体质辨识9种体质得分*/
ALTER TABLE `t_user_analysis_group_result` ADD COLUMN `seqn` INT(1) NULL COMMENT '顺序' AFTER `score`, ADD COLUMN `is_main` int(1) DEFAULT 0 NULL COMMENT '是否主体质' AFTER `seqn`, ADD COLUMN `is_sub` int(1) DEFAULT 0 NULL COMMENT '是否兼体质' AFTER `is_main`;
CREATE TABLE `t_user_analysis_item`(
    `analy_item_id` VARCHAR(32) NOT NULL,
    `analy_id` VARCHAR(32) NOT NULL COMMENT '辨识ID',
    `item_id` VARCHAR(32) NOT NULL COMMENT '问题',
    `score` INT NOT NULL COMMENT '得分',
    `seqn` INT(3) NOT NULL COMMENT '顺序',
    PRIMARY KEY (`analy_item_id`)
    COMMENT '体质辨识问题勾选记录'
);

/* 5.8  2021-04-29 gw 体质辨识打印增加字段*/
ALTER TABLE `t_user_analysis_result` ADD COLUMN `analy_code` VARCHAR(32) NULL COMMENT '辨识编号' AFTER `analy_id`;
ALTER TABLE `t_user_analysis_result` ADD COLUMN `doctor_id` VARCHAR(32) NOT NULL COMMENT '医生ID' AFTER `register_id`;
ALTER TABLE `t_user_analysis_result` ADD COLUMN `description` VARCHAR(128) NULL COMMENT '描述' AFTER `sub_group_id`;

/* 5.8  2021-04-29 gw 体质辨识打印增加字段*/
ALTER TABLE `t_user_analysis_result_acu` ADD COLUMN `acu_position` VARCHAR(512) NULL COMMENT '穴位定位' AFTER `acu_img`;
/* 5.8  2021-06-01 wt 体质辨识显示名称*/
ALTER TABLE `t_user_analysis_result` ADD display_result VARCHAR(128) COMMENT '显示结果' AFTER sub_result ;
ALTER TABLE `t_user_analysis_result` ADD display_result_item VARCHAR(256) COMMENT '显示结果详情' AFTER display_result ;
/* 5.8  2021-06-01 wt 体质辨识数据调整*/
UPDATE `t_sys_code_item`    SET ZIFU5 = "1" WHERE code_id = "29";
UPDATE  `t_sys_code_item`   SET item_num ="8" WHERE item_name = "推荐药膳" AND code_id = "29";
UPDATE  `t_sys_code_item`   SET item_num ="9" WHERE item_name = "推荐花茶" AND code_id = "29";
UPDATE  `t_sys_code_item`   SET item_num ="3" WHERE item_name = "运动保健" AND code_id = "29";
UPDATE  `t_sys_code_item`   SET item_num ="4" WHERE item_name = "穴位保健" AND code_id = "29";
