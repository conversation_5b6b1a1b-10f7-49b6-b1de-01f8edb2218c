/*2021.05.12 gw 处方来源注释修改*/
ALTER TABLE `t_prescription` CHANGE `PRE_ORIGIN` `PRE_ORIGIN` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' NULL COMMENT '处方来源(0空白方 1智能辩证 2智能推方 3方剂搜索 4协定方转方 5专家经验共享转方 6我的验案转方 7名家验案转方 8历史病历转方 9国医大师)';
/*2021.05.12 gw 处方追溯来源历史处方数据处理*/
DELIMITER $$
DROP PROCEDURE IF EXISTS `trace_back_pre_origin`$$
CREATE PROCEDURE `trace_back_pre_origin`()
BEGIN
	DECLARE i INT DEFAULT 1;
	WHILE i > 0 DO
		UPDATE t_prescription p1, t_prescription p2 SET p1.`PRE_ORIGIN_ID` = p2.`PRE_ORIGIN_ID` WHERE p1.`PRE_ORIGIN_ID` = p2.`PRE_ID`;
		SET i = (SELECT COUNT(*) FROM t_prescription p1, t_prescription p2 WHERE p1.`PRE_ORIGIN_ID` = p2.`PRE_ID`);
	END WHILE;
	COMMIT;
    END$$
DELIMITER ;

CALL trace_back_pre_origin();

/*2021.05.12 gw 处方来源注释修改 */
ALTER TABLE `t_prescription` CHANGE `PRE_ORIGIN` `PRE_ORIGIN` VARCHAR(1) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' NULL COMMENT '处方来源(0自拟方 1智能辩证 2智能推方 3方剂搜索 4协定方 5专家经验共享 7名家验案 9国医大师)';
