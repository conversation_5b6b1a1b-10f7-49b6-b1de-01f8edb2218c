
/*2021.05.19 gw 协定方文件夹处理 */
DELETE FROM `t_personal_prescription_fmap` WHERE folder_id NOT IN (SELECT folder_id FROM `t_personal_prescription_folder`);

/*2021.05.19 gw 增加协定方权限菜单 */
INSERT INTO `sys_admin_menu` (`mid`, `mname`, `url`, `path`, `iconCls`, `enabled`, `parent_mid`, `create_date`, `cteate_id`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort_number`, `btn_level`, `open_type`) VALUES('207','协定方使用权限','/recipe/appoint-manage',NULL,NULL,'2','33','2021-05-17 14:24:06','70810c874405453b99c6c2cf72296fe5','2',NULL,NULL,NULL,'8',NULL,'1');
INSERT INTO `sys_admin_rule_menu` (`rmid`, `rid`, `mid`) VALUES('048b8c93e25c4a75b369f0ead1f2394b','b4a17b6b635c4de48f95178676905aa5','207');
INSERT INTO `sys_admin_rule_menu` (`rmid`, `rid`, `mid`) VALUES('048b8c93e25c4a75b369f0ead1f6f84b','f85aa731d9d144d1bc1d72cf3e877a4d','207');

/*2021.05.19 gw 增加协定方权限表 */
CREATE TABLE `t_personal_rule` (
  `rule_id` varchar(32) NOT NULL COMMENT '协定方角色ID',
  `rule_name` varchar(64) NOT NULL COMMENT '角色名称',
  `rule_desc` varchar(32) DEFAULT NULL COMMENT '角色描述',
  `create_date` datetime NOT NULL COMMENT '创建时间',
  `create_user` varchar(32) NOT NULL COMMENT '创建人',
  `update_date` datetime DEFAULT NULL COMMENT '修改时间',
  `update_user` varchar(32) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='协定方角色';

CREATE TABLE `t_personal_rule_auth` (
  `auth_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `rule_id` varchar(32) NOT NULL COMMENT '协定方角色ID',
  `dept_id` varchar(32) NOT NULL DEFAULT '000000' COMMENT '科室ID',
  `dept_check_all` int(1) NOT NULL DEFAULT '0' COMMENT '科室是否全选',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `pers_pre_id` varchar(32) NOT NULL COMMENT '协定方ID',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(32) DEFAULT NULL COMMENT '创建人',
  `update_date` datetime DEFAULT NULL COMMENT '修改时间',
  `update_user` varchar(32) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`auth_id`),
  KEY `IDX_KEY` (`rule_id`,`dept_id`,`user_id`,`pers_pre_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1635 DEFAULT CHARSET=utf8mb4 COMMENT='协定方权限';

INSERT INTO `t_personal_rule` (`rule_id`, `rule_name`, `rule_desc`, `create_date`, `create_user`)
VALUES ('0', '个人', '个人', NOW(), 'admin'), ('1', '科室', '科室', NOW(), 'admin'), ('2', '全院', '全院', NOW(), 'admin');

/*使用系统的科室时，批量授权协定方权限*/
INSERT INTO `t_personal_rule_auth` (`rule_id`, `dept_id`, `dept_check_all`, `user_id`, `pers_pre_id`)
SELECT * FROM (
                  SELECT p.IS_SHARE, p.`DEPT_ID` DEPT_ID, 0 AS dept_check_all, i.id id, p.PERS_PRE_ID
                  FROM t_personal_prescription p
                           JOIN sys_admin_info i ON i.id = p.PRE_OWNER
                  WHERE p.is_del = '0' AND p.IS_SHARE = '0' AND p.`DEPT_ID` IS NOT NULL
                  UNION ALL
                  SELECT p.IS_SHARE, d.DEP_ID DEPT_ID, 1 AS dept_check_all, i.admin_id id, p.PERS_PRE_ID
                  FROM t_personal_prescription p
                           JOIN sys_department d ON d.`DEP_ID` = p.`DEPT_ID`
                           JOIN sys_admin_practice i ON i.`DEP_ID` = d.`DEP_ID`
                  WHERE p.is_del = '0' AND p.IS_SHARE = '1'
                  UNION ALL
                  SELECT p.IS_SHARE, d.DEP_ID DEPT_ID, 1 AS dept_check_all, i.admin_id id, p.PERS_PRE_ID
                  FROM t_personal_prescription p
                           JOIN sys_institution s ON s.`INS_CODE` = p.`INS_CODE`
                           JOIN sys_department d ON d.`INS_CODE` = s.`INS_CODE`
                           JOIN sys_admin_practice i ON i.`DEP_ID` = d.`DEP_ID`
                  WHERE p.is_del = '0' AND p.IS_SHARE = '2') s
WHERE NOT EXISTS (SELECT 1 FROM t_personal_rule_auth a WHERE a.`rule_id` = s.`IS_SHARE` AND a.`dept_id` = s.`DEPT_ID`
                                                         AND a.`user_id` = s.`id` AND a.`pers_pre_id` = s.`PERS_PRE_ID`);

/*使用HIS的科室时，批量授权协定方权限*/
INSERT INTO `t_personal_rule_auth` (`rule_id`, `dept_id`, `dept_check_all`, `user_id`, `pers_pre_id`)
SELECT * FROM (
                  SELECT p.IS_SHARE, p.`DEPT_ID` DEPT_ID, 0 AS dept_check_all, i.id, p.PERS_PRE_ID
                  FROM t_personal_prescription p
                           JOIN sys_admin_info i ON i.id = p.PRE_OWNER
                  WHERE p.is_del = '0' AND p.IS_SHARE = '0' AND p.`DEPT_ID` IS NOT NULL
                  UNION ALL
                  SELECT p.IS_SHARE, d.DEP_ORIGIN_ID DEPT_ID, 1 AS dept_check_all, i.id, p.PERS_PRE_ID
                  FROM t_personal_prescription p
                           JOIN sys_department d ON d.`DEP_ORIGIN_ID` = p.`DEPT_ID`
                           JOIN sys_admin_info i ON i.`DEP_ID` = d.`DEP_ORIGIN_ID`
                  WHERE p.is_del = '0' AND p.IS_SHARE = '1'
                  UNION ALL
                  SELECT p.IS_SHARE, d.DEP_ORIGIN_ID DEPT_ID, 1 AS dept_check_all, i.id, p.PERS_PRE_ID
                  FROM t_personal_prescription p
                           JOIN sys_institution s ON s.`INS_CODE` = p.`INS_CODE`
                           JOIN sys_department d ON d.`INS_CODE` = s.`INS_CODE`
                           JOIN sys_admin_info i ON i.`DEP_ID` = d.`DEP_ORIGIN_ID`
                  WHERE p.is_del = '0' AND p.IS_SHARE = '2') s
WHERE NOT EXISTS (SELECT 1 FROM t_personal_rule_auth a WHERE a.`rule_id` = s.`IS_SHARE` AND a.`dept_id` = s.`DEPT_ID`
                                                         AND a.`user_id` = s.`id` AND a.`pers_pre_id` = s.`PERS_PRE_ID`);
