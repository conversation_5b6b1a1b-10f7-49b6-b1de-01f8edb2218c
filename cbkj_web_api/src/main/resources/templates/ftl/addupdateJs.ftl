layui.use(['form', 'layedit', 'laydate'], function() {
    var form = layui.form
    var id = $("input[name='${entityPrimary2}']").val();

    if(null != id && id.trim() != "" && id != undefined && id != "null"){
        var url = path_+"${entityName?uncap_first}/update/findObj";
        $.getJSON(url,{id:id},function(result){
            var status = result.status;
            if(status){
                var data = result.data;
                form.val('example', {
            <#list maps?keys as key>
                <#if key != primary>
               "${maps[key]}": data.${maps[key]}<#if key_has_next>,</#if>
                </#if>
            </#list>
                })
            }else {
                parent.layer.msg(result.message);
            }
        });
    }
    form.render();
})