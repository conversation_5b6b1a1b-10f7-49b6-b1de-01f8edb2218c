layui.use('table', function(){


    var table = layui.table,
    form = layui.form,
    loadingIndex = layer.load(2),
    tableIns;
    var colsArrays = [//破除
            {checkbox: true},
<#list maps?keys as key>
    <#if key != primary>
            {field:'${maps[key]}', title: '${maps[key]}'}<#if key_has_next>,</#if>
    </#if>
</#list>
    ];

    //渲染按钮
    if(colsArrays.length > 0){
        reckon(colsArrays);
    }

    //渲染表格
    tableIns = table.render({
        elem: '#qb'
        ,url:'${entityName?uncap_first}/getPages'
        ,page: page_
        ,cols: [colsArrays]
        ,id:"qbTable"
        ,done: function(res, curr, count){
            layer.close(loadingIndex);
        }
        ,even: true
        ,height: 'full'
    });

    table.on('tool(qbTable)', function(obj){

        var data = obj.data;
        var dataLis = [];
        dataLis.push(data);

        if(obj.event === 'add_'){

            updateRow(null,"新增${desc}","new");

        }else if(obj.event === 'update_'){

            updateRow(data.${entityPrimary2},"修改${desc}","update");

        }else if(obj.event === 'delete_'){

            deletes(dataLis);

        }
    });
    var $ = layui.$, active = {
        reload: function(){
            loadingIndex = layer.load(2);
            var name = $('#name');
            //执行重载（搜索）
            tableIns.reload({
                page: {curr: 1 }
                ,where: {
                    name:name.val()
                }
            });
        },
        //新增
        add_:function(){
            updateRow(null,"新增${desc}","new");
        },
        update_:function(){

            var checkStatus = table.checkStatus('qbTable'),data=checkStatus.data;
            if(data.length <= 0){

                layer.msg("请选择需要修改的数据");

            }else if(data.length == 1){

                updateRow(data[0].${entityPrimary2},"修改${desc}","update");

            }else{

                layer.msg("只能单个进行修改哦");
            }
        },
        delete_:function(){
            var checkStatus = table.checkStatus('qbTable'),data=checkStatus.data;
            if(data.length <= 0){
                layer.msg("请选择需要删除的${desc}数据");
            }else{
                deletes(data);
            }
        }
    };

    $('.btns .layui-btn').on('click', function(){
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });


    //删除，支持批量删除
    function deletes(dataLis){

         var params = {};
         var ids = "";
         dataLis.forEach(function(obj,index){
             ids += obj.${entityPrimary2}+",";
         });
         ids = ids.substring(0,ids.length-1);
         parent.layer.confirm('确定需要删除选中数据吗？', {
               btn: ['删除','取消'] //按钮
         }, function(){

               params.ids = ids;
               $.post("../${entityName?uncap_first}/deleteLis",params,function(result){

                        if(result.status){
                            $(".layui-laypage-btn").click();
                                parent.layer.closeAll();
                            }else{
                                parent.layer.msg(result.message);
                            }
                        },"JSON");

               }, function(){

               });

    }

    /**
     * 新增或者修改执行
     * @param ids
     * @param title
     * @param type
     */
    function updateRow(ids,title,type){

        var url = type === "new"?"${entityName?uncap_first}/insert/toPage":"${entityName?uncap_first}/update/toPage?ID="+ids;

        var qb_Index = parent.layer.open({
            type: 2,
            content:[url,'no'],
            title:title,
            area:["560px","600px"],
            id:"QB_UPDATE",
            shade:0.7,
            scrollbar: false,
            maxmin: false,
            fix: false,
            end:function(){

            },
            btn: ['确定', '取消']
            ,yes: function(index, layero){

                var body = parent.layer.getChildFrame('body', index);
                var params = {};
                var formBody = $(body).find(".layui-form");

                if(formBody.length > 0){

                    var formData = formBody.serializeArray();
                    params = transformToJson_(formData);

                    var url = type === "new"?"../${entityName?uncap_first}/insert":"../${entityName?uncap_first}/update";
                    $.post(url,params,function(result){
                        if(result.status){
                            if(type === "new"){
                                loadingIndex = layer.load(2);
                                tableIns.reload({page: {curr: 1 }});
                            }else{
                                $(".layui-laypage-btn").click();
                            }
                            parent.layer.close(qb_Index);
                        }else{
                            parent.layer.msg(result.message);
                        }
                    },"json");
                }else{
                    parent.layer.close(qb_Index);
                }
            },btn2: function(index, layero){
                //按钮【按钮二】的回调
            }
        });
    }
});

