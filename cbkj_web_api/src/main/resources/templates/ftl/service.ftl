package com.jiuzhekan.cbkj.service${businessPath};

import com.jiuzhekan.cbkj.beans${businessPath}.${entityName};
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper${businessPath}.${entityName}Mapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class ${entityName}Service {

    @Autowired
    private ${entityName}Mapper ${entityName?uncap_first}Mapper;

    /**
     * 加载分页数据
     *
     * @param ${entityName?uncap_first} ${desc}
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date ${.now?string('yyyy-MM-dd')}
     */
    public Object getPageDatas(${entityName} ${entityName?uncap_first}, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<${entityName}> list = ${entityName?uncap_first}Mapper.getPageListByObj(${entityName?uncap_first});
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param ${entityPrimary2} ${desc}
     * @return ResEntity
     * <AUTHOR>
     * @date ${.now?string('yyyy-MM-dd')}
     */
    public ResEntity findObj(String ${entityPrimary2}) {

        if (StringUtils.isBlank(${entityPrimary2})) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        ${entityName} ${entityName?uncap_first} = ${entityName?uncap_first}Mapper.getObjectById(${entityPrimary2});
        return ResEntity.entity(true, Constant.SUCCESS_DX, ${entityName?uncap_first});
    }


    /**
     * 插入新数据
     *
     * @param ${entityName?uncap_first} ${desc}
     * @return ResEntity
     * <AUTHOR>
     * @date ${.now?string('yyyy-MM-dd')}
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(${entityName} ${entityName?uncap_first}){

        ${entityName?uncap_first}.set${entityPrimary}(IDUtil.getID());
        long rows = ${entityName?uncap_first}Mapper.insert(${entityName?uncap_first});

        return ResEntity.success(${entityName?uncap_first});
    }


    /**
     * 修改
     *
     * @param ${entityName?uncap_first} ${desc}
     * @return ResEntity
     * <AUTHOR>
     * @date ${.now?string('yyyy-MM-dd')}
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(${entityName} ${entityName?uncap_first}) {

        long rows = ${entityName?uncap_first}Mapper.updateByPrimaryKey(${entityName?uncap_first});

        return ResEntity.success(${entityName?uncap_first});
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date ${.now?string('yyyy-MM-dd')}
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = ${entityName?uncap_first}Mapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
