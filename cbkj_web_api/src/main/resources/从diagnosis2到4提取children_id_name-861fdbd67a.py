import json

# 读取JSON文件
with open('recordTemplate.json', 'r', encoding='utf-8') as file:
    data = json.load(file)

# 所有数据结构项记录：方便后续查 name
all_items_dict = {}

# 提取children递归函数
def extract_children(data, result_list):
    if isinstance(data, dict):
        if 'children' in data:
            for child in data['children']:
                if 'id' in child and 'name' in child:
                    item = {
                        'id': child['id'],
                        'name': child['name'],
                        'type': child.get('type', 'NULL'),
                        'exclude': ",".join(child.get('exclude', [])) if 'exclude' in child else '',
                        'exclude_list': child.get('exclude', []),
                        'parent_id': data.get('id', '0')
                    }
                    result_list.append(item)
                    all_items_dict[child['id']] = child['name']
                extract_children(child, result_list)
    elif isinstance(data, list):
        for item in data:
            extract_children(item, result_list)

# 提取diagnosis[2]到diagnosis[4]
result = []
if 'diagnosis' in data:
    for i in range(1, len(data['diagnosis'])):
        extract_children(data['diagnosis'][i], result)

# ========== 生成主表插入 SQL ==========
sql_lines_main = []
for item in result:
    id_ = item['id'].replace("'", "''")
    name = item['name'].replace("'", "''")
    type_ = item['type'] if item['type'] != 'NULL' else 'NULL'
    exclude = f"'{item['exclude']}'" if item['exclude'] else 'NULL'
    parent_id = f"'{item['parent_id']}'" if item['parent_id'] != 'NULL' else 'NULL'
    sql = f"INSERT INTO t_record_quality_symptom (id, name, type, exclude, parent_id) VALUES ('{id_}', '{name}', {type_}, {exclude}, {parent_id});"
    sql_lines_main.append(sql)

# ========== 生成互斥表插入 SQL ==========
exclude_pairs = set()  # 用于去重：A-B 与 B-A 视为同一条
sql_lines_exclude = []
for item in result:
    id_1 = item['id']
    name_1 = item['name']
    for id_2 in item.get('exclude_list', []):
        if id_2 in all_items_dict:
            name_2 = all_items_dict[id_2]
            # 用 frozenset 去重（无序对）
            key = frozenset((id_1, id_2))
            if key not in exclude_pairs:
                exclude_pairs.add(key)
                # 按 id 字符串排序后固定顺序输出
                id_a, id_b = sorted([id_1, id_2])
                name_a = all_items_dict[id_a]
                name_b = all_items_dict[id_b]
                sql_lines_exclude.append(
                    f"INSERT INTO t_record_quality_rule_items_mapping (id_1, name_1, id_2, name_2) VALUES ('{id_a}', '{name_a}', '{id_b}', '{name_b}');"
                )

# ========== 保存两个 SQL 文件 ==========
with open('diagnosis_children_insert.sql', 'w', encoding='utf-8') as f:
    f.write('\n'.join(sql_lines_main))

with open('diagnosis_exclude_insert.sql', 'w', encoding='utf-8') as f:
    f.write('\n'.join(sql_lines_exclude))

print("主表和互斥关系表SQL已保存：")
print(" - diagnosis_children_insert.sql")
print(" - diagnosis_exclude_insert.sql")
