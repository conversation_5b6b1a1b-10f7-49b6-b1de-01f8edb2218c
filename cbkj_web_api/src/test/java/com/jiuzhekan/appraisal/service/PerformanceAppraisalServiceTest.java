package com.jiuzhekan.appraisal.service;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.appraisal.bean.JxkhZibiaoZjzbtj;
import com.jiuzhekan.appraisal.bean.bo.JiGouInfo;
import com.jiuzhekan.appraisal.bean.dao.PerformanceAppraisalDao;
import com.jiuzhekan.appraisal.bean.dto.PerformanceAppraisalRe;
import com.jiuzhekan.appraisal.mapper.JxkhZibiaoZjzbtjMapper;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/18 11:15
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class PerformanceAppraisalServiceTest extends TestCase {
    @Autowired
    private JxkhZibiaoZjzbtjMapper jxkhZibiaoZjzbtjMapper;
    @Autowired
    private PerformanceAppraisalService performanceAppraisalService;

    @Test
    public void testComputerDataAndGroupSet() {
        List<JxkhZibiaoZjzbtj> pageListByPerformanceAppraisalRe = jxkhZibiaoZjzbtjMapper.getMZBPageDatas(new PerformanceAppraisalDao());
        Map<String, JiGouInfo> stringJiGouInfoMap = performanceAppraisalService.computerDataAndGroupSet(pageListByPerformanceAppraisalRe);

        System.out.println(JSON.toJSONString(stringJiGouInfoMap));
    }
//    @Test
//    public void getMZBPageDatas() {
//        PerformanceAppraisalRe performanceAppraisalRe = new PerformanceAppraisalRe();
//        Object mzbPageDatas = performanceAppraisalService.getMZBPageDatas(performanceAppraisalRe);
//        System.out.println(JSON.toJSONString(mzbPageDatas));
//    }
}