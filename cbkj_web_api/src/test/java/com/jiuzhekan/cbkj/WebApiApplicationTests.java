package com.jiuzhekan.cbkj;

import com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionMapper;
import com.jiuzhekan.cbkj.service.redis.KnowRedisService;
import lombok.extern.slf4j.Slf4j;
import org.databene.contiperf.PerfTest;
import org.databene.contiperf.junit.ContiPerfRule;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class WebApiApplicationTests {

    @Autowired
    private KnowRedisService knowRedisService;
    @Autowired
    private TPrescriptionMapper tPrescriptionMapper;

    //引入 ContiPerf 进行性能测试
    @Rule
    public ContiPerfRule contiPerfRule = new ContiPerfRule();

    @Test
    public void contextLoads() {
    }


    @Test
    public void syndromeGroup() {
        List<String> draftPreIds = Arrays.asList("1","2");
        tPrescriptionMapper.physicalDelete(draftPreIds);
    }

    @Test
    @PerfTest(invocations = 100, threads = 10)
    public void syndromeSave() {
        Map<String, Object> map = new HashMap<>();
        map.put("groupid", "499");
        map.put("itemids", ",19604,19610,31264,31275,31291,31281,31279,31277,31306,31294,31321,31336");
        knowRedisService.postKnow2("syndrome/save", map);
    }

    @Test
    @PerfTest(invocations = 100, threads = 1)
    public void schemeList() {
        Map<String, Object> map = new HashMap<>();
        map.put("disid", "2b9cc16e4a8c11eb8b470242ac110002");
        map.put("symid", "19ca0bc34a8c11eb8b470242ac110002");
        map.put("businesstype", "1,4,2,3");
        knowRedisService.postKnow2("scheme/list", map);
    }

    @Test
    @PerfTest(invocations = 100, threads = 10)
    public void checkPres() {
        Map<String, Object> map = new HashMap<>();
        map.put("pres", "391a7932b27b4c489962cc525e55448d,52a937b2a87a43dd885b265ac5c73a45,75eab7fd31b248238e402cd87d13b95a,d943e599c3144db7a1841ccf0856c5b9,6e3248f356084216b1fc4102c164a932,d70113660edd4f9aba8aa1cad0a39d17,44a0b65cb53b4eee936e29d9157f3259,fddd8b0e6e224dada4cb9ad6dd2bfa83,bedf16fdeba84899b23b9e2db7675a5b");
        map.put("disid", "2b9cc16e4a8c11eb8b470242ac110002");
        map.put("symid", "19ca0bc34a8c11eb8b470242ac110002");
        knowRedisService.postKnow2("check/pres", map);
    }

    @Test
    public void delPre(){
        long interval = 72;
        tPrescriptionMapper.delPreIfNoPayForMz(interval);
    }

    public static void main(String[] args) {
    }

}