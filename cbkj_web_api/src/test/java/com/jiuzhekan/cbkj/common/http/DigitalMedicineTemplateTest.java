/*
package com.jiuzhekan.cbkj.common.http;

import com.alibaba.fastjson.JSON;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.controller.common.vo.DigitalMedicineAddress;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;


import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class DigitalMedicineTemplateTest extends TestCase {

    @Autowired
    private DigitalMedicineTemplate digitalMedicineTemplate;
    @Autowired
    private  RestTemplate restTemplate;

    @Test
    public void testGet() {
//        ResEntity resEntity = digitalMedicineTemplate.get("common/findAddressByCard?cardNumber=100000202002022022");
//        System.out.println("=====================");
//        System.out.println(JSON.toJSONString(resEntity.getData()));
//        System.out.println("=====================");


//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
//        HttpEntity<JSONObject> request = new HttpEntity<>(null, headers);
//        String forObject = restTemplate.getForObject("http://*************:91/tzbs/common/findAddressByCard?cardNumber=100000202002022022&format", String.class);
//        System.out.println(forObject);
//        System.out.println("=====================");

//        HashMap<String, Object> data = new HashMap<>();
//        data.put("cardNumber","100000202002022022");
//        String body = HttpRequest.get("http://*************:91/tzbs/common/findAddressByCard?cardNumber=100000202002022022").contentType("application/json;charset=UTF-8").body();
//        ResEntity resEntity1 = JSON.parseObject(body, ResEntity.class);
//        System.out.println(resEntity1.getData());


        ResEntity resEntity2 = digitalMedicineTemplate.get("common/findAddressByCard?cardNumber="+"100000202002022022");
        if (resEntity2.getStatus()){
            List<DigitalMedicineAddress> addressesList = JSON.parseArray(JSON.toJSONString(resEntity2.getData()), DigitalMedicineAddress.class);
            for (DigitalMedicineAddress digitalMedicineAddress : addressesList) {
                if (!StringUtils.isBlank(digitalMedicineAddress.getDcMobile()) && !StringUtils.isBlank(digitalMedicineAddress.getDcMobile2())) {
                    digitalMedicineAddress.setDcMobile(digitalMedicineAddress.getDcMobile() + "," + digitalMedicineAddress.getDcMobile2());
                }
            }
            resEntity2.setData(addressesList);
            System.out.println("=====================");
            System.out.println(addressesList);
        }
    }
}*/
