package com.jiuzhekan.cbkj.common.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.drug.MatVo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.annotation.Version;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/*
*
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/21 11:36
 * @Version 1.0

*/

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class DrugsRestTemplateTest extends TestCase {

    @Resource
    private DrugsRestTemplate drugsRestTemplate;

    @Test
    public void testPost() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("keyValue", "蛤蚧(特大)");
        jsonObject.put("matType", "1");//中药类型（规格表中）
        jsonObject.put("phaId", "512049fb61aa40a6a05297d58ef4efd7");//药房id
        jsonObject.put("drugIdHis", "060717a12a675710");//his药品目录id
        jsonObject.put("distinctOrigin", "1");//去重产地，0不去重 1 去重
        jsonObject.put("manySpeSwitch", 1);//药品开方多规格下拉开关 1是 0否
        ResEntity post = drugsRestTemplate.post("mat/searchMat", jsonObject);
        List<MatVo> matVos = JSON.parseArray(JSON.toJSONString(post.getData()), MatVo.class);
        System.out.println(JSON.toJSONString(matVos));
    }
}
