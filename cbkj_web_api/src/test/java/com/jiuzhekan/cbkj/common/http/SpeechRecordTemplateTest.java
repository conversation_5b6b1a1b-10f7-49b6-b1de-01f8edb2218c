package com.jiuzhekan.cbkj.common.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.speech.SpeechRecieve;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class SpeechRecordTemplateTest extends TestCase {

    @Autowired
    private SpeechRecordTemplate speechRecordTemplate;
    @Test
    public void testPost() {
        com.alibaba.fastjson.JSONObject jsonObject = new JSONObject();
        //jsonObject.put("text","因咳嗽、咳痰伴发热2天入院。既往体健；查体：T37.60C，HR132次/分，R38次/分，身高100cm，体重10kg。精神失神，脸色发白，苔白，脉浮，皮肤发肿，声音沙哑，嗜睡，小便短黄，鼻翼无扇动，口唇无紫绀，咽无充血，双侧扁桃体无肿大，呼吸平稳，无吸气三凹征，双肺呼吸音粗，肺部可闻及明显干湿罗音，手足汗出。心率146次/分，心音有力，未闻及杂音。腹平软，肠鸣音正常。NS（－）");
        jsonObject.put("text","入院时病情摘要: 患儿以“咳嗽半月余”入院。入院症见：神清，精神欠佳，咳嗽，呈阵发性连声咳，喉中有痰，不会咳出，鼻塞，流脓涕，无发热，无寒战抽搐，无呕吐，无腹胀、腹泻，纳寐欠佳，二便调。体格检查:体温: 36.7℃ 脉搏: 100次/分 呼吸: 24次/分 血压:-mmHg，体重22kg，CRT1s。神志清楚，精神欠佳，呼吸平稳，咽粘膜充血，双侧扁桃体I°肿大，颈软，双肺呼吸音粗，可闻及少许固定细小湿啰音，听诊心率100次/分，心律齐，各瓣膜听诊区未闻及病理性杂音。腹部平坦 、柔软；腹部无压痛；无反跳痛；腹部无触及包块。肝脾肋下未触及，肠鸣音4次/分。双下肢无浮肿，生理反射存在，病理反射未引出。舌质红，苔薄黄，脉浮数。辅助检查: 2022-09-04柳州市中医医院发热门诊查DR胸部正侧位(不含胶片)考虑支气管肺炎。");
        //方案id 固定值。一件事取这个
        jsonObject.put("scenario_id", "yi-jian-shi-0001");
        SpeechRecieve post = speechRecordTemplate.post("https://healthcare.tcmbrain.com/medical-parse/medical_parse?api-key=e58b80ac4c2e424bbed3b65300ab4320", jsonObject);
        System.out.println(JSON.toJSONString(post));
    }
}
