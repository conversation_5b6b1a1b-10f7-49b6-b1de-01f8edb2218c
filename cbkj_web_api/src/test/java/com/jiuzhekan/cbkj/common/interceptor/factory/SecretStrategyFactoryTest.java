package com.jiuzhekan.cbkj.common.interceptor.factory;

import com.jiuzhekan.cbkj.beans.business.patients.TPatients;
import com.jiuzhekan.cbkj.mapper.business.patients.TPatientsMapper;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class SecretStrategyFactoryTest extends TestCase {

    @Autowired
    private SecretStrategyFactory secretStrategyFactory;
    @Autowired
    private TPatientsMapper tPatientsMapper;
    @Test
    public void testGetSecretStrategy() {
        TPatients objectById = tPatientsMapper.getObjectById("c9dade5e37f24c76be22d001054f779d");
        System.out.println("测试查询-结果解密："+objectById.getPatientCertificate());
        TPatients tPatients = new TPatients();
        tPatients.setPatientCertificate("412702199507270321");
        TPatients patientsByPatients = tPatientsMapper.getPatientsByPatients(tPatients);
        System.out.println("测试查询-入参加密-结果解密："+patientsByPatients == null ? "": patientsByPatients.getPatientCertificate());

//        patientsByPatients.setPatientCertificate("412702199507270123");
//        tPatientsMapper.updateByPrimaryKey(patientsByPatients);
        TPatients tPatients2 = new TPatients();
        tPatients2.setPatientCertificate("412702199507270321");
        tPatients2.setPatientName("盖1");
        TPatients patientsByPatients2 = tPatientsMapper.getPatientsByPatients(tPatients2);
        System.out.println("测试更新加密字段-再次查询-入参加密-结果解密："+patientsByPatients2 == null ? "": patientsByPatients2.getPatientCertificate());
//        tPatientsMapper.updateByPrimaryKey(objectById);
//        TPatients objectById2 = tPatientsMapper.getObjectById("c9dade5e37f24c76be22d001054f779d");
//        System.out.println("使用加密更新后，第二次查询："+objectById2.getPatientCertificate());
    }
}