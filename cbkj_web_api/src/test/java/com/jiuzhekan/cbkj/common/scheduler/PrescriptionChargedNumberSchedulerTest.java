package com.jiuzhekan.cbkj.common.scheduler;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/7/23 09:33
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class PrescriptionChargedNumberSchedulerTest extends TestCase {

    @Autowired
    private PrescriptionChargedNumberScheduler prescriptionChargedNumberScheduler;

    @Test
    public void testSchedulerPreNumber() {
        prescriptionChargedNumberScheduler.schedulerPreNumber();
    }
}