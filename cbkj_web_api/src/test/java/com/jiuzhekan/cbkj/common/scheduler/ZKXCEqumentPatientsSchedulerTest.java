package com.jiuzhekan.cbkj.common.scheduler;

//import com.jiuzhekan.cbkj.common.scheduler.zkxc.ZKXCEqumentPatientsScheduler;
import com.alibaba.fastjson.JSONArray;
import com.jiuzhekan.cbkj.beans.business.zkxcs.ZkxcChiCunGuan;
import com.jiuzhekan.cbkj.controller.business.treatment.zkxc.VeinCunGuanChi;
import com.jiuzhekan.cbkj.mapper.business.zkxcs.ZkxcChiCunGuanMapper;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ZKXCEqumentPatientsSchedulerTest extends TestCase {
//
//    @Resource
//    private ZKXCEqumentPatientsScheduler zkxcEqumentPatientsScheduler;
//    @Test
//    public void testSendData() {
//        zkxcEqumentPatientsScheduler.sendData();
//    }
    @Resource
    private ZkxcChiCunGuanMapper zkxcChiCunGuanMapper;
    @Test
    public void test(){
        List<ZkxcChiCunGuan> objectByMzId = zkxcChiCunGuanMapper.getObjectByMzId(409);
        VeinCunGuanChi veinCunGuanChi = new VeinCunGuanChi();
        BeanUtils.copyProperties(objectByMzId.get(0),veinCunGuanChi);
        String optPulse1 = objectByMzId.get(0).getOptPulse1().replaceAll("\"", "");
        String cleanInput = optPulse1.substring(1, optPulse1.length() - 1);
        String[] array = cleanInput.split(",");
        System.out.println(array);
    }
}