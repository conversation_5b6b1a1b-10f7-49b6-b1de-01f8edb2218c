package com.jiuzhekan.cbkj.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.databene.contiperf.PerfTest;
import org.databene.contiperf.junit.ContiPerfRule;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class MakeOrderNumByRedisTest {

    //引入 ContiPerf 进行性能测试
    @Rule
    public ContiPerfRule contiPerfRule = new ContiPerfRule();

    @Autowired
    private MakeOrderNumByRedis makeOrderNumByRedis;


    @Test
    @PerfTest(invocations = 100, threads = 10)
    public void makePreNoUuid() {
        long uuid = makeOrderNumByRedis.uuid();
        System.out.println(uuid);
    }

    @Test
    @PerfTest(invocations = 100, threads = 10)
    public void makePreNo() {
        log.info("start ");
        String preNo = makeOrderNumByRedis.makePreNo((short) 1);
        log.info(preNo);
    }
}