package com.jiuzhekan.cbkj.controller.business.record;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.beans.business.his.DigitFind;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.his.DigitService;
import com.jiuzhekan.cbkj.service.business.record.TTagService;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import springfox.documentation.spring.web.json.Json;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TTagControllerTest extends TestCase {

    @Autowired
    private TTagService tTagService;
    @Resource
    private DigitService digitService;

    @Test
    public void testTagInfo() {
        tTagService.deleteTagList("125dc25c3cfa40ce96514d2cab33fd03");
        tTagService.deleteTagList("125dc25c3cfa40ce96514d2cab33fd");
        tTagService.deleteTagList("125dc25c3cfa40ce96514d2cab33fd032,c6ba609b8fb14683946018da21dc7420");
        tTagService.deleteTagList("abb6748d3e1a4898835c5ed70f15b872,");
        tTagService.deleteTagList(",");
        tTagService.deleteTagList(" ");
    }
    @Test
    public void testTagInfo2() {
        DigitFind digitFind = new DigitFind();
        digitFind.setDataStatus(1);
        Page page = new Page();
        DigitFind digitFind1 = new DigitFind();
        digitFind1.setStartDate("2024-01-17");
        digitFind1.setEndDate("2024-01-17");
        digitFind1.setDataStatus(2);
        Object list = digitService.getList(digitFind1,new Page());
        System.out.println(JSON.toJSONString(list));
    }
}