package com.jiuzhekan.cbkj.mapper.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TPrescription;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TPrescriptionMapperTest extends TestCase {
    @Autowired
    private TPrescriptionMapper tPrescriptionMapper;

    @Test
    public void testGetPreTypeListByPreNo() {
        String[] split = "prescription,2,3,20210930144322003".split(",");
        String[] split2 = "20210930151122005,2,3,20210930144322003".split(",");
        List<TPrescription> prescription = tPrescriptionMapper.getPreTypeListByPreNo(split);
        List<TPrescription> prescription2 = tPrescriptionMapper.getPreTypeListByPreNo(split2);
        System.out.println(prescription);
        System.out.println(prescription2);
    }
}