package com.jiuzhekan.cbkj.mapper.business.syndrome;

import com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeTonguePulseAnswer;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/14 10:07
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TRecordSyndromeTonguePulseAnswerMapperTest extends TestCase {

    @Autowired
    private TRecordSyndromeTonguePulseAnswerMapper tRecordSyndromeTonguePulseAnswerMapper;

    @Test
    public void testInsertList() {
        ArrayList<TRecordSyndromeTonguePulseAnswer> tRecordSyndromeTonguePulseAnswers = new ArrayList<>();
        for(int i = 0; i < 10; i++){
            TRecordSyndromeTonguePulseAnswer answer = new TRecordSyndromeTonguePulseAnswer();
            answer.setType(1);
            answer.setName("测试" + i);
            answer.setRsGroupId(123 + ""+i);
            answer.setCode("code" + i);
            tRecordSyndromeTonguePulseAnswers.add(answer);
        }
        tRecordSyndromeTonguePulseAnswerMapper.insertList(tRecordSyndromeTonguePulseAnswers);
    }
}