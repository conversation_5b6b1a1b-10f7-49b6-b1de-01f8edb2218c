package com.jiuzhekan.cbkj.mapper.statistics;

import com.jiuzhekan.PreApiApplication;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PreApiApplication.class})
@Slf4j
public class TStatisticsMapperTest extends TestCase {
    @Autowired
private  TStatisticsMapper tStatisticsMapper;
    @Test
    public void testStatisticsTop5DisName() {
        Map<String, Object> param = new HashMap<>();
        param.put("timeType", "1");
        param.put("timeDiff", 1);
        tStatisticsMapper.statisticsTop5DisName(param);
        param.put("timeType", "2");
        param.put("timeDiff", 15);
        tStatisticsMapper.statisticsTop5DisName(param);
        param.put("timeType", "3");
        param.put("timeDiff", 90);
        tStatisticsMapper.statisticsTop5DisName(param);
        param.put("timeType", "4");
        param.put("timeDiff", 365);
        tStatisticsMapper.statisticsTop5DisName(param);
        param.put("timeType", "5");
        param.put("timeDiff", 180);
        tStatisticsMapper.statisticsTop5DisName(param);
    }
}