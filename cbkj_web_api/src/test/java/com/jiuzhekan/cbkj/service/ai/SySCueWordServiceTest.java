package com.jiuzhekan.cbkj.service.ai;

import com.jiuzhekan.cbkj.beans.ai.CueWordURLRequest;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/14 16:20
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class SySCueWordServiceTest extends TestCase {

    @Autowired
    private SySCueWordService sySCueWordService;

    @Test
    public void testone(){
        CueWordURLRequest cueWordURLRequest = new CueWordURLRequest();
        cueWordURLRequest.setCueWordType("");

        sySCueWordService.getRecordCueWordURL(cueWordURLRequest);
    }
}