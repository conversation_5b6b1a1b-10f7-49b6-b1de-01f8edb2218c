package com.jiuzhekan.cbkj.service.business.dic;

import com.jiuzhekan.cbkj.beans.business.prescription.TRecord2DicNew;
import com.jiuzhekan.cbkj.beans.business.prescription.TRecord2DicPNew;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * DicBaseService getBianZhengDic方法优化后的测试类
 * 
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
public class DicBaseServiceOptimizedTest {

    @InjectMocks
    private DicBaseService dicBaseService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetBianZhengDic_WithValidData() {
        // 准备测试数据
        String registerId = "test-register-id";
        
        // 模拟 getAllDicListNew 返回成功的结果
        ResEntity mockResEntity = new ResEntity(true, 200, "success", createMockTRecord2DicPNew());
        
        // 使用反射调用私有方法进行测试
        // 由于 getAllDicListNew 是公共方法，我们可以直接测试
        
        // 创建测试用的诊断数据
        TRecord2DicPNew mockData = createMockTRecord2DicPNew();
        List<TRecord2DicNew> diagnosis = mockData.getDiagnosis();
        
        // 测试处理舌诊数据的方法
        try {
            ArrayList<HashMap<String, Object>> tongueData = 
                (ArrayList<HashMap<String, Object>>) ReflectionTestUtils.invokeMethod(
                    dicBaseService, "processTongueData", diagnosis);
            
            assertNotNull("舌诊数据不应为null", tongueData);
            assertEquals("舌诊数据应包含6个类型", 6, tongueData.size());
            
            // 验证舌诊数据结构
            HashMap<String, Object> firstTongueType = tongueData.get(0);
            assertTrue("应包含name字段", firstTongueType.containsKey("name"));
            assertTrue("应包含code字段", firstTongueType.containsKey("code"));
            assertTrue("应包含children字段", firstTongueType.containsKey("children"));
            
        } catch (Exception e) {
            fail("调用processTongueData方法失败: " + e.getMessage());
        }
        
        // 测试处理脉诊数据的方法
        try {
            ArrayList<HashMap<String, Object>> pulseData = 
                (ArrayList<HashMap<String, Object>>) ReflectionTestUtils.invokeMethod(
                    dicBaseService, "processPulseData", diagnosis);
            
            assertNotNull("脉诊数据不应为null", pulseData);
            assertEquals("脉诊数据应包含1个类型", 1, pulseData.size());
            
            // 验证脉诊数据结构
            HashMap<String, Object> pulseType = pulseData.get(0);
            assertEquals("脉诊名称应为'脉'", "脉", pulseType.get("name"));
            assertTrue("应包含children字段", pulseType.containsKey("children"));
            
        } catch (Exception e) {
            fail("调用processPulseData方法失败: " + e.getMessage());
        }
    }

    @Test
    public void testCreateChildDataMap() {
        // 创建测试用的子节点
        TRecord2DicNew child = new TRecord2DicNew();
        child.setId("test-id");
        child.setName("test-name");
        child.setExclude(Arrays.asList("exclude1", "exclude2"));
        
        try {
            HashMap<String, Object> result = 
                (HashMap<String, Object>) ReflectionTestUtils.invokeMethod(
                    dicBaseService, "createChildDataMap", child);
            
            assertNotNull("结果不应为null", result);
            assertEquals("code应正确设置", "test-id", result.get("code"));
            assertEquals("name应正确设置", "test-name", result.get("name"));
            assertEquals("exclude应正确设置", Arrays.asList("exclude1", "exclude2"), result.get("exclude"));
            
        } catch (Exception e) {
            fail("调用createChildDataMap方法失败: " + e.getMessage());
        }
    }

    @Test
    public void testProcessNodeChildren() {
        // 创建测试用的父节点
        TRecord2DicNew parent = new TRecord2DicNew();
        
        TRecord2DicNew child1 = new TRecord2DicNew();
        child1.setId("child1-id");
        child1.setName("child1-name");
        child1.setExclude(new ArrayList<>());
        
        TRecord2DicNew child2 = new TRecord2DicNew();
        child2.setId("child2-id");
        child2.setName("child2-name");
        child2.setExclude(new ArrayList<>());
        
        parent.setChildren(Arrays.asList(child1, child2));
        
        try {
            ArrayList<HashMap<String, Object>> result = 
                (ArrayList<HashMap<String, Object>>) ReflectionTestUtils.invokeMethod(
                    dicBaseService, "processNodeChildren", parent);
            
            assertNotNull("结果不应为null", result);
            assertEquals("应包含2个子节点", 2, result.size());
            
            HashMap<String, Object> firstChild = result.get(0);
            assertEquals("第一个子节点的code应正确", "child1-id", firstChild.get("code"));
            assertEquals("第一个子节点的name应正确", "child1-name", firstChild.get("name"));
            
        } catch (Exception e) {
            fail("调用processNodeChildren方法失败: " + e.getMessage());
        }
    }

    /**
     * 创建模拟的TRecord2DicPNew数据
     */
    private TRecord2DicPNew createMockTRecord2DicPNew() {
        TRecord2DicPNew mockData = new TRecord2DicPNew();
        
        List<TRecord2DicNew> diagnosis = new ArrayList<>();
        
        // 添加索引0的占位数据
        diagnosis.add(new TRecord2DicNew());
        
        // 添加索引1的舌诊数据
        TRecord2DicNew tongueNode = createMockTongueNode();
        diagnosis.add(tongueNode);
        
        // 添加索引2-3的占位数据
        diagnosis.add(new TRecord2DicNew());
        diagnosis.add(new TRecord2DicNew());
        
        // 添加索引4的脉诊数据
        TRecord2DicNew pulseNode = createMockPulseNode();
        diagnosis.add(pulseNode);
        
        mockData.setDiagnosis(diagnosis);
        return mockData;
    }

    /**
     * 创建模拟的舌诊节点数据
     */
    private TRecord2DicNew createMockTongueNode() {
        TRecord2DicNew tongueNode = new TRecord2DicNew();
        
        // 创建舌诊详细节点
        TRecord2DicNew tongueDetailsNode = new TRecord2DicNew();
        List<TRecord2DicNew> tongueDetails = new ArrayList<>();
        
        // 添加各种舌诊类型的子节点
        tongueDetails.add(createMockTongueChild("1.17.01.01", "舌神测试"));
        tongueDetails.add(createMockTongueChild("1.17.03.01", "舌色测试"));
        tongueDetails.add(createMockTongueChild("1.17.05.01", "舌形测试"));
        tongueDetails.add(createMockTongueChild("1.17.07.01", "舌态测试"));
        tongueDetails.add(createMockTongueChild("1.17.09.01", "舌下络脉测试"));
        
        tongueDetailsNode.setChildren(tongueDetails);
        
        // 创建舌苔节点
        TRecord2DicNew tongueCoatingNode = new TRecord2DicNew();
        tongueCoatingNode.setId("tongue-coating-id");
        tongueCoatingNode.setName("舌苔");
        List<TRecord2DicNew> coatingChildren = new ArrayList<>();
        coatingChildren.add(createMockTongueChild("coating.01", "苔薄"));
        coatingChildren.add(createMockTongueChild("coating.02", "苔厚"));
        tongueCoatingNode.setChildren(coatingChildren);
        
        tongueNode.setChildren(Arrays.asList(tongueDetailsNode, tongueCoatingNode));
        return tongueNode;
    }

    /**
     * 创建模拟的脉诊节点数据
     */
    private TRecord2DicNew createMockPulseNode() {
        TRecord2DicNew pulseNode = new TRecord2DicNew();
        
        TRecord2DicNew pulseDetailsNode = new TRecord2DicNew();
        pulseDetailsNode.setId("pulse-details-id");
        
        List<TRecord2DicNew> pulseChildren = new ArrayList<>();
        pulseChildren.add(createMockTongueChild("pulse.01", "浮脉"));
        pulseChildren.add(createMockTongueChild("pulse.02", "沉脉"));
        
        pulseDetailsNode.setChildren(pulseChildren);
        pulseNode.setChildren(Arrays.asList(pulseDetailsNode));
        
        return pulseNode;
    }

    /**
     * 创建模拟的子节点
     */
    private TRecord2DicNew createMockTongueChild(String id, String name) {
        TRecord2DicNew child = new TRecord2DicNew();
        child.setId(id);
        child.setName(name);
        child.setExclude(new ArrayList<>());
        return child;
    }
}
