package com.jiuzhekan.cbkj.service.business.dic;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.HashMap;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class DicBaseServiceTest extends TestCase {

    @Autowired
    private DicBaseService dicBaseService;

    @Test
    public void testGetAllDicListNew() throws FileNotFoundException {
        //获取跟目录
        File path = new File(ResourceUtils.getURL("classpath:").getPath());
        if(!path.exists()) path = new File("");
//        System.out.println("path:"+path.getAbsolutePath());
//        ResEntity allDicListNew = dicBaseService.getAllDicListNew("20221110102510002", path.getAbsolutePath() + File.separator + "recordDefault.json");
//        System.out.println(JSON.toJSONString(allDicListNew.getData()));
    }

    @Test
    public void testGetBianZhengDic() {
        HashMap<String, ArrayList<HashMap<String, Object>>> bianZhengDic = dicBaseService.getBianZhengDic("20250730093433040");
        System.out.println(JSON.toJSONString(bianZhengDic));
    }
}