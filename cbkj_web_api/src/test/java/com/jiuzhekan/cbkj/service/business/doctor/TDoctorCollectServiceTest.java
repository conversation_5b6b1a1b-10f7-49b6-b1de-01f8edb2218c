package com.jiuzhekan.cbkj.service.business.doctor;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorCollect;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Page;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/7/3 16:29
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TDoctorCollectServiceTest extends TestCase {

    @Resource
    private TDoctorCollectService tDoctorCollectService;

    @Test
    public void testGetListJoinTKnowledgeShare() {

        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("shareType", "1");
        stringStringHashMap.put("createUser", "ee7d4e21170b4df8879a885da212f6a3");
        Object listJoinTKnowledgeShare = tDoctorCollectService.getListJoinTKnowledgeShare(stringStringHashMap, new Page());
        System.out.println(JSON.toJSONString(listJoinTKnowledgeShare));




    }
}