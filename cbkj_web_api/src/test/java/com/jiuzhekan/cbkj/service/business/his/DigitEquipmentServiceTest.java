package com.jiuzhekan.cbkj.service.business.his;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.beans.business.zkxc.ZkxcEquipment;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;



/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/2 13:37
 * @Version 1.0
 */

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class DigitEquipmentServiceTest   extends TestCase {


@Autowired
    private DigitEquipmentService digitEquipmentService;



    @Test
    public void getEquipmentType(){
        System.out.println(JSON.toJSONString(digitEquipmentService.getEquipmentType()));
    }
    @Test
    public void addEquipmentType(){
        ZkxcEquipment zkxcEquipment = new ZkxcEquipment();
        zkxcEquipment.setEquipmentName("mock测试-2");
        zkxcEquipment.setEquipmentType(1);
        zkxcEquipment.setMacAddress("7C:5C:F8:4E:34:01");
        zkxcEquipment.setAppId("001");
        zkxcEquipment.setIsDefault(0);
        zkxcEquipment.setInsCode("100001");
        ResEntity resEntity = digitEquipmentService.addEquipmentDetail(zkxcEquipment);
        System.out.println(JSON.toJSONString(resEntity));
    }

    @Test
    public void updateEquipmentType(){
        ZkxcEquipment zkxcEquipment = new ZkxcEquipment();zkxcEquipment.setId(11);
        zkxcEquipment.setEquipmentName("mock测试-2");
        zkxcEquipment.setEquipmentType(1);
        zkxcEquipment.setMacAddress("7C:5C:F8:4E:34:50");
        zkxcEquipment.setAppId("001");zkxcEquipment.setIsDefault(0);
        zkxcEquipment.setInsCode("100001");
        ResEntity resEntity = digitEquipmentService.updateEquipmentDetail(zkxcEquipment);
        System.out.println(JSON.toJSONString(resEntity));
    }
}