package com.jiuzhekan.cbkj.service.business.his;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.business.zkxc.ZkxcEquipmentPatientMapper;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/21 14:40
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class DigitPadServiceTest extends TestCase {

    @Autowired
    private  DigitPadService digitPadService;

    @Autowired
    private  ZkxcEquipmentPatientMapper zkxcEquipmentPatientMapper;

    @Test
    public void testGetListHistorySZ() {
        System.out.println(JSON.toJSONString(digitPadService.getListHistorySZ("7a0cc982777d4f118270b454ef3017bd",new Page())));
    }
    @Test
    public void testGetDetailsHistorySZ() {
        System.out.println(JSON.toJSONString(digitPadService.getDetailsHistorySZ("69","")));
    }

//    @Test
//    public void testGetDetailsHistoryMAizhen() {
//        System.out.println(JSON.toJSONString(digitPadService.getListHistoryMaiZhen("7a0cc982777d4f118270b454ef3017bd",new Page())));
//    }

    @Test
    public void testGetDetailsHistoryMAizhenDe() {
        System.out.println(JSON.toJSONString(digitPadService.getDetailsHistoryMaiZhen2("","7330485c98fe43998bb7bbf211c2ff04")));
    }
    @Test
    public void testgetListHistoryMaiZhenTwo()
    {
        System.out.println(JSON.toJSONString(digitPadService.getListHistoryMaiZhenTwo("7330485c98fe43998bb7bbf211c2ff04")));
    }
    @Test
    public void testDisMapping(){
        HashMap<String, String> map1 = new HashMap<>();
        map1.put("appId", "000000");
        map1.put("insCode","000000");
        map1.put("deptId","000000");
        String disId = zkxcEquipmentPatientMapper.getDeptMappingZKXCDisId(map1);
        System.out.println("结果disId="+disId);
    }

}