package com.jiuzhekan.cbkj.service.business.his;

import com.jiuzhekan.cbkj.controller.business.treatment.zkxc.DigitalReportUrl;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/13 16:17
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class DigitServiceTest extends TestCase {

    private final DigitService digitService;

    public DigitServiceTest(DigitService digitService) {
        this.digitService = digitService;
    }

    @Test
    public void testGetDigitalReportUrl() {
        DigitalReportUrl digitalReportUrl = new DigitalReportUrl();
        digitalReportUrl.setAppId("100001");
        digitalReportUrl.setInsCode("10002011");
        digitService.getDigitalReportUrl(new DigitalReportUrl());
    }
}