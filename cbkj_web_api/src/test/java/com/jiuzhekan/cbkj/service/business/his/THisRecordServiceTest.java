package com.jiuzhekan.cbkj.service.business.his;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class THisRecordServiceTest extends TestCase {
    @Autowired
    private THisRecordService tHisRecordService;
    @Test
    public void testTransferByToken() {
        //tHisRecordService.transferByToken("20221116114950010",null);
    }
}