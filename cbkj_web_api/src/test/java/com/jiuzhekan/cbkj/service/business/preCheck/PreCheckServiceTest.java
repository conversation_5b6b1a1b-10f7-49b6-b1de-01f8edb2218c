package com.jiuzhekan.cbkj.service.business.preCheck;

import com.jiuzhekan.cbkj.beans.business.record.preCheckVO.PreCheckReqVO;
import com.jiuzhekan.cbkj.common.utils.Page;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/28 11:25
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class PreCheckServiceTest extends TestCase {

    @Autowired
    private PreCheckService preCheckService;

    @Test
    public void testNewChecks() {
        preCheckService.getCheckPayResult(new PreCheckReqVO(),new Page());
    }
}