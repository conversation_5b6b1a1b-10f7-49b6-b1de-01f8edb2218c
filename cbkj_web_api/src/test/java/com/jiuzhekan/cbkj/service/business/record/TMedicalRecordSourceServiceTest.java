package com.jiuzhekan.cbkj.service.business.record;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.common.utils.Page;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TMedicalRecordSourceServiceTest extends TestCase {
    @Autowired
private TMedicalRecordSourceService tMedicalRecordSourceService;
    @Test
    public void testTMedicalRecordSourceList() {
        Page page = new Page();
        System.out.println(JSON.toJSONString(tMedicalRecordSourceService.tMedicalRecordSourceList("3c76884628a94261ac583aa1f74fb250","1",page)));
        System.out.println("================================================================================================================");
        System.out.println(JSON.toJSONString(tMedicalRecordSourceService.tMedicalRecordSourceList("c19398fbc4dd473e8391fed9d0d48c2e","0",page)));

    }
}