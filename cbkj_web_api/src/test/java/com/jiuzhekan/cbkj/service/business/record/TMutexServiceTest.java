package com.jiuzhekan.cbkj.service.business.record;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TMutexServiceTest extends TestCase {
    @Autowired
    private TMutexService tMutexService;

    @Test
    public void testGetMutexList() {
        ResEntity mutexList = tMutexService.getMutexList("2b9d00dc4a8c11eb8b470242ac110002");
        System.out.println(JSON.toJSONString(mutexList.getData()));
    }
}