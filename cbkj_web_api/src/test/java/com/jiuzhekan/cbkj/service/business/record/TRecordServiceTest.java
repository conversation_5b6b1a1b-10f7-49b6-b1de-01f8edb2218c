package com.jiuzhekan.cbkj.service.business.record;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.beans.business.patients.TDcAddress;
import com.jiuzhekan.cbkj.beans.business.patients.TPatients;
import com.jiuzhekan.cbkj.beans.business.record.TPrescription;
import com.jiuzhekan.cbkj.beans.business.record.VO.TPreRespVO;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.mapper.business.patients.TDcAddressMapper;
import com.jiuzhekan.cbkj.mapper.business.patients.TPatientsMapper;
import com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionMapper;
import com.jiuzhekan.cbkj.service.business.patients.TPatientsService;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TRecordServiceTest extends TestCase {
    @Autowired
    private TPatientsService tPatientsService;
    @Autowired
    private TDcAddressMapper tDcAddressMapper;
    @Test
    public void testGetAllPre() {
//        TPatients tPatients = new TPatients();
//        tPatients.setPatientId("1");
//        tPatients.setPatientAddress("这是明文");
//        tPatientsMapper.getPatientsByPatients(tPatients);
//
//        System.out.println("打印入参看看加密没=========="+JSON.toJSONString(tPatients));

//        TPatients tPatients2 = tPatientsMapper.findByInsCodeAndPatientId("556a0e20d8cc40638ee8da694ac5a27f", null);
//        System.out.println(tPatients2);
        ResEntity patientAddress = tPatientsService.getPatientAddress("0283cca2d8e146069ed5b865600ff9b2", "20221226134939001");
    }
}