package com.jiuzhekan.cbkj.service.business.record;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.controller.response.MedicalHistoryDetailNoHeight;
import com.jiuzhekan.cbkj.controller.response.MedicalHistoryDetailResponse;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TTagServiceTest extends TestCase {
    @Autowired
    private TTagService tTagService;
    @Test
    public void testDetail() {
        MedicalHistoryDetailResponse a = tTagService.detail("036e717179e64425a4127b3fe45a1295");
        System.out.println(JSON.toJSONString(a).toString());

    }
}