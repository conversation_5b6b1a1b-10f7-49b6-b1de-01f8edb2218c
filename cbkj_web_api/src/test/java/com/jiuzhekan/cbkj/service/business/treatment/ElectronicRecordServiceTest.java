package com.jiuzhekan.cbkj.service.business.treatment;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/5 9:42
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ElectronicRecordServiceTest extends TestCase {

    @Autowired
    private ElectronicRecordService electronicRecordService;

    @Test
    public void testGetDefalutTemplate() {
        AdminInfo adminInfo = new AdminInfo();
        adminInfo.setId("ee7d4e21170b4df8879a885da212f6a3");
        ResEntity recordDetailListByRegisterId = electronicRecordService.getRecordDetailListByRegisterId("20240705094331001", "2b9cc9574a8c11eb8b470242ac110002", adminInfo);
        System.out.println(JSON.toJSONString(recordDetailListByRegisterId.getData()));
    }
}