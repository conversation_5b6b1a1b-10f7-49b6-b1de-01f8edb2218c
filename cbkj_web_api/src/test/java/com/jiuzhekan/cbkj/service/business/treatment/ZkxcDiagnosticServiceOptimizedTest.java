package com.jiuzhekan.cbkj.service.business.treatment;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;

/**
 * ZkxcDiagnosticService baseToImage方法优化后的测试类
 * 
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
public class ZkxcDiagnosticServiceOptimizedTest {

    @InjectMocks
    private ZkxcDiagnosticService zkxcDiagnosticService;

    private String testLocation = "test/";
    private String testPreview = "http://test.com/preview/";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 设置测试用的配置值
        ReflectionTestUtils.setField(zkxcDiagnosticService, "location", testLocation);
        ReflectionTestUtils.setField(zkxcDiagnosticService, "preview", testPreview);
    }

    @Test
    public void testBaseToImage_WithValidBase64() {
        // 创建一个简单的PNG图片的Base64数据（1x1像素的透明PNG）
        String validBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        
        String result = zkxcDiagnosticService.baseToImage(validBase64);
        
        // 验证返回的路径格式正确
        assertNotNull("结果不应为null", result);
        assertTrue("应该包含预览路径", result.startsWith(testPreview));
        assertTrue("应该包含zkxc路径", result.contains("zkxc/"));
        assertTrue("应该包含IMG文件夹", result.contains("zxkjnIMG/"));
        assertTrue("应该以.png结尾", result.endsWith(".png"));
    }

    @Test
    public void testBaseToImage_WithNullInput() {
        String result = zkxcDiagnosticService.baseToImage(null);
        assertNull("null输入应该返回null", result);
    }

    @Test
    public void testBaseToImage_WithEmptyInput() {
        String result = zkxcDiagnosticService.baseToImage("");
        assertNull("空字符串输入应该返回null", result);
    }

    @Test
    public void testBaseToImage_WithBlankInput() {
        String result = zkxcDiagnosticService.baseToImage("   ");
        assertNull("空白字符串输入应该返回null", result);
    }

    @Test
    public void testBaseToImage_WithInvalidBase64() {
        String invalidBase64 = "这不是有效的Base64数据!@#$%";
        
        String result = zkxcDiagnosticService.baseToImage(invalidBase64);
        assertNull("无效的Base64数据应该返回null", result);
    }

    @Test
    public void testBaseToImage_WithBase64WithoutHeader() {
        // 不带头部信息的Base64数据
        String base64WithoutHeader = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        
        String result = zkxcDiagnosticService.baseToImage(base64WithoutHeader);
        
        assertNotNull("不带头部的Base64数据应该能正常处理", result);
        assertTrue("应该包含正确的路径格式", result.contains("zkxc/zxkjnIMG/"));
    }

    @Test
    public void testBaseToImage_WithBase64ContainingWhitespace() {
        // 包含空白字符的Base64数据
        String base64WithWhitespace = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB\n" +
                "CAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        
        String result = zkxcDiagnosticService.baseToImage(base64WithWhitespace);
        
        assertNotNull("包含空白字符的Base64数据应该能正常处理", result);
    }

    @Test
    public void testBaseToImage_WithDifferentImageFormats() {
        // 测试不同的图片格式头部
        String jpegBase64 = "data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        String webpBase64 = "data:image/webp;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        
        String jpegResult = zkxcDiagnosticService.baseToImage(jpegBase64);
        String webpResult = zkxcDiagnosticService.baseToImage(webpBase64);
        
        assertNotNull("JPEG格式应该能正常处理", jpegResult);
        assertNotNull("WebP格式应该能正常处理", webpResult);
    }

    @Test
    public void testBaseToImage_Performance() {
        String validBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        
        long startTime = System.currentTimeMillis();
        
        // 执行多次以测试性能
        for (int i = 0; i < 100; i++) {
            zkxcDiagnosticService.baseToImage(validBase64);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 验证性能在合理范围内（这里设置为5秒，实际应该远小于这个值）
        assertTrue("处理100次应该在5秒内完成", duration < 5000);
        System.out.println("处理100次Base64转换耗时: " + duration + "ms");
    }
}
