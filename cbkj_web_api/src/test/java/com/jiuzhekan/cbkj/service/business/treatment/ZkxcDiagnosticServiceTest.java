package com.jiuzhekan.cbkj.service.business.treatment;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.controller.response.MedicalHistoryDetailResponse;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.ParseException;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ZkxcDiagnosticServiceTest extends TestCase {
    @Autowired
    private ZkxcDiagnosticService zkxcDiagnosticService;
    @Test
    public void testGetPatient() throws ParseException {
//        Object patient = zkxcDiagnosticService.getPatient("2023-03-03 10:00:00", "2c:05:47:4e:e5:01");
//        Object patient2 = zkxcDiagnosticService.getPatient("2023-02-22 10:00:00", "2c:05:47:4e:e5:01");
//        System.out.println(JSON.toJSONString(patient));
//        System.out.println(JSON.toJSONString(patient2));
//        zkxcDiagnosticService.zkxccCheck("16e984e575264f6c8d89f62ce5cea2f2","20230223092742019","2c:05:47:4e:e5:01");

       // MedicalHistoryDetailResponse mappingData = zkxcDiagnosticService.getMappingData("20240417133747322");
        //System.out.println(JSON.toJSONString(mappingData));
    }

    @Test
    public void testZkxccBaseReport() {
        System.out.println(JSON.toJSONString(zkxcDiagnosticService.zkxccBaseReport(null, "ef7c0cc3e0d14c8b87cd4a2a87c3282a") ) );
    }

    @Test
    public void testBaseToImage() {
        zkxcDiagnosticService.baseToImage("");
    }
}