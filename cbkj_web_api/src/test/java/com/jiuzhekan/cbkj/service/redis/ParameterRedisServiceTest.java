package com.jiuzhekan.cbkj.service.redis;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/20 10:49
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ParameterRedisServiceTest extends TestCase {

    @Autowired
    private  ParameterRedisService parameterRedisService;

    @Test
    public void testGetDicCode() {
        parameterRedisService.getDicCode("100001","10002011","1812855ab157d30a3606e9ad4dd9914c","89973d12ec5e4821bd16c20188079c65","outpatient_dosage_form");
    }
}