package com.jiuzhekan.cbkj.service.statistics;

import com.alibaba.fastjson.JSON;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/7/3 10:52
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class PrescriptionStatisticsServiceTest extends TestCase {

    @Autowired
    private PrescriptionStatisticsService prescriptionStatisticsService;

    @Test
    public void testGetScienceAllNo() {
        List<String> scienceAllNo = prescriptionStatisticsService.getScienceAllNo(null);
        System.out.println(JSON.toJSONString(scienceAllNo));
    }
}