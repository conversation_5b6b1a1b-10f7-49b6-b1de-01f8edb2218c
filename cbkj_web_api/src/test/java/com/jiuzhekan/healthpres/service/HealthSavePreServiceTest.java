package com.jiuzhekan.healthpres.service;

import com.alibaba.fastjson2.JSON;
import com.jiuzhekan.cbkj.common.utils.word.WordGenerator;
import com.jiuzhekan.healthpres.beans.heathPresControllerVo.HealthPreModelMainResponse;
import com.jiuzhekan.healthpres.mapper.TUserQuestionnaireMainMapper;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/4 15:53
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class HealthSavePreServiceTest extends TestCase {
    @Resource
    private HealthSavePreService healthSavePreService;

    @Resource
    private HeathPresService heathPresService;
    @Resource
    private TUserQuestionnaireMainMapper tUserQuestionnaireMainMapper;

    @Resource
    private HealthTypeService healthTypeService;

    @Resource
    private FilesCreateService filesCreateService;
    @Test
    public void filesCreateServiceTest(){
        filesCreateService.createWordPdf("5b11aa4a5c0f4006a601ca87f8f75000");
    }
    @Test
    public void testGetTypes() {
        log.info("开始测试");
        List<HealthPreModelMainResponse> healthPreModelAllList = tUserQuestionnaireMainMapper.getHealthPreModelAllList();
        System.out.println(JSON.toJSONString(healthPreModelAllList));
        log.info("测试结束");
    }

    @Test
    public void testSaveHealthPre() {
        log.info("开始测试");
        System.out.println(JSON.toJSONString(healthSavePreService.getTypes("b69a69eb1739475bacecc91094537159", "E10.000x001", null)));
        log.info("测试结束");
    }

    @Test
    public void testSaveHealthPre2() {
        log.info("开始测试");
        System.out.println(JSON.toJSONString(heathPresService.getChildHeathTypes("b69a69eb1739475bacecc91094537159")));
        log.info("测试儿童");
        System.out.println(JSON.toJSONString(heathPresService.getChildHeathTypes("365e782baf924f5e9108bf0d07e8ee7b")));
        log.info("测试结束");
    }
    @Test
    public void testSaveHealthPre3() {
        log.info("开始测试");
        System.out.println(JSON.toJSONString(healthTypeService.getHealthType()));
        log.info("测试结束");
    }
    @Test
    public void testSaveHealthPre4() {
        log.info("开始测试");
//        System.out.println(JSON.toJSONString(heathPresService.getHealthPreModel(new GetHealthPreModel("b69a69eb1739475bacecc91094537159"))));
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("patientName", "张三");
        dataMap.put("patientAge", "18");
        dataMap.put("analyCode", "E10.000x001");
        dataMap.put("reportTitle", "中医慢病健康管理报告");
        dataMap.put("reportTime", "2025-06-04 15:53:00");
        dataMap.put("insName", "聪宝智能医院");
        dataMap.put("doctorName", "张博华");

        ArrayList<Object> list = new ArrayList<>();

        HashMap<Object, Object> item1 = new HashMap<>();
        item1.put("itemTitle", "调体法则");
        item1.put("itemContent","以平肝潜阳、滋阴益肾为主，兼以化痰泄浊、调和气血");

        HashMap<Object, Object> item2 = new HashMap<>();
        item2.put("itemTitle", "调体要点");
        item2.put("itemContent","需注重分型辨标本虚实，结合药膳调补、导引舒络及情志疏泄，强调畅情志、限盐减脂、慎起居以维持阴阳动态平衡。");

        list.add(item1);
        list.add(item2);
        dataMap.put("dataListOne", list);




        ArrayList<Object> list2 = new ArrayList<>();

        HashMap<Object, Object> item2_1 = new HashMap<>();
        item2_1.put("itemTitle", "运动保健\n");
        item2_1.put("itemContent","1、运动干预\n" +
                "目标：使超重或肥胖者BMI达到或接近24kg/m²，或体重至少下降7%，并使体重长期维持在健康水平；中等强度至剧烈强度的体力运动至少保持在150分钟/周。\n" +
                "方式：有氧训练通过增强胰岛素敏感性增加葡萄糖摄取，而不依赖于肌肉质量或者有氧代谢能力的改变。抗阻运动训练可引起的肌肉质量增加有利于血糖摄取，并且不依赖于改变肌肉固有的胰岛素应答能力。推荐有氧和抗阻运动的联合运动干预，多样的运动形式也避免了运动的单一性，有利于增\n" +
                "强患者运动依从性。\n" +
                "2、传统功法\n" +
                "a)八段锦\n" +
                "治法：舒畅经络，调养脏腑。适应病证：糖尿病前期各证。\n" +
                "操作方法：分为热身部分、八段锦部分、放松部分。练习3天/周～5天/周，练习八段锦2次/天~3次/天，3个月为1个疗程。\n" +
                "b)太极拳\n" +
                "治法：舒畅经络，调养脏腑。适应病证：糖尿病前期各证。\n" +
                "操作方法：分为热身部分、太极拳部分、放松部分。练习3天/周～6天/周，练习60分钟/天。3个月为1个疗程。改善结局：2hPG、HbA1c。");

        HashMap<Object, Object> item2_2 = new HashMap<>();
        item2_2.put("itemTitle", "穴位保健");
        item2_2.put("itemContent","推拿\n" +
                "治法：健脾和胃、疏肝理气、益气养阴。\n" +
                "操作方法：选穴以背俞穴、手足阳明经及太阴经经穴为主，如：脾俞、胃俞、肾俞、胃脘下俞、曲池、手三里、内关、合谷、阳陵泉、血海、足三里、三阴交等穴。 手法选用按揉、点穴、振腹等，如：可拿揉四肢肌肉配合循经点穴再采用掌振法操作于关元穴和小腹部。");


        HashMap<Object, Object> item2_3 = new HashMap<>();
        item2_3.put("itemTitle", "情志调摄");
        item2_3.put("itemContent", "1、心理调节\n" +
                "目标：稳定情绪，消除负面行为，增强康复信心，提高生活质量。\n" +
                "要点：\n" +
                "a)客观认识和评估糖尿病前期状态，采取科学的保健措施，增加安全感，舒缓自已的担忧情绪；\n" +
                "b)识别接纳自己的情绪，忧虑、紧张、恐惧是绝大多数人面对疾病的正常反应，接受自己的负面情绪，建立新的生活规律，逐步排解负面情绪；\n" +
                "c)接受家人、朋友和社会的支持和关心，维持良好的社会关系；\n" +
                "d)主动获取心理健康知识和心理保健技巧，必要时主动寻求专业帮助。\n" +
                "2、情志疗法\n" +
                "情志疗法包括五音疗法等，可调畅情志，避免不良情绪。\n" +
                "五音健脾疗法治法：调畅情志，助脾散精。\n" +
                "曲用《秋湖月夜》《鸟投林》《闲居吟》等宫调式音乐，有助于脾气散精，起到缓解脾、预防消渴的作用。在进餐后聆听悠扬、醇厚的宫调式音乐，5次/周，每次持续30分钟，12周为1疗程。音量控制在30分贝～40分贝，具体以感觉舒适、悦耳为度，同时应避免在聆听音乐过程中如灯光、电话、声音等各种干扰，闭上双眼、调整呼吸，全身心沉浸在五行音乐的意境之中。");

        list2.add(item2_1);
        list2.add(item2_2);
        list2.add(item2_3);

        dataMap.put("dataListTwo", list2);

//        dataMap.put("patientSex", "男");

        WordGenerator.createDoc2("D:\\test.docx",dataMap );
        log.info("测试结束");
    }
}