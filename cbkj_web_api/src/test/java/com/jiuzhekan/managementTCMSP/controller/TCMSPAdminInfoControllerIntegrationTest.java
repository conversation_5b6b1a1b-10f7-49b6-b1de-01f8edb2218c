package com.jiuzhekan.managementTCMSP.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jiuzhekan.managementTCMSP.bean.AppsInfoDetails;
import com.jiuzhekan.managementTCMSP.bean.SysDoctorMultipoint;
import com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoEdu;
import com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoExamine;
import com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoAssessmentEvaluation;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * TCMSPAdminInfoController 集成测试类
 * 测试实际的HTTP请求和数据库交互
 * 
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
//@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Transactional // 确保测试后回滚数据
public class TCMSPAdminInfoControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    // ==================== 基础数据接口集成测试 ====================

    @Test
    public void testGetAppList_Integration() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/getAppList"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").exists())
                .andExpect(jsonPath("$.data").exists());
    }

    @Test
    public void testGetInsList_Integration_ValidAppId() throws Exception {
        // 使用一个可能存在的appId进行测试
        mockMvc.perform(get("/tcmspAdminInfo/getInsList")
                .param("appId", "001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").exists());
    }

    @Test
    public void testGetInsList_Integration_EmptyAppId() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/getInsList")
                .param("appId", ""))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.msg").value("医共体id不能为空"));
    }

    @Test
    public void testGetDeptList_Integration_ValidParams() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/getDeptList")
                .param("appId", "001")
                .param("insCode", "001001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").exists());
    }

    @Test
    public void testGetDeptList_Integration_EmptyParams() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/getDeptList")
                .param("appId", "")
                .param("insCode", ""))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.msg").value("参数不能为空"));
    }

    // ==================== 人员信息管理集成测试 ====================

    @Test
    public void testGetPersonList_Integration() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/getPersonList")
                .param("page", "1")
                .param("limit", "10"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.data").exists());
    }

    @Test
    public void testGetPersonDetail_Integration_InvalidUserId() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/getPersonDetail")
                .param("userId", "nonexistent_user"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").exists());
    }

    @Test
    public void testSavePersonDetail_Integration_ValidData() throws Exception {
        AppsInfoDetails testUser = createValidTestUser();
        
        mockMvc.perform(post("/tcmspAdminInfo/savePersonDetail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testUser)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").exists());
    }

    @Test
    public void testSavePersonDetail_Integration_InvalidData() throws Exception {
        AppsInfoDetails invalidUser = new AppsInfoDetails();
        // 故意留空必填字段
        
        mockMvc.perform(post("/tcmspAdminInfo/savePersonDetail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidUser)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.msg").exists());
    }

    @Test
    public void testDeletePerson_Integration_EmptyUserId() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/deletePerson")
                .param("userId", ""))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.msg").value("参数不能为空"));
    }

    // ==================== 继续教育管理集成测试 ====================

    @Test
    public void testGetContinueEducationList_Integration() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/getContinueEducationList")
                .param("page", "1")
                .param("limit", "10"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.data").exists());
    }

    @Test
    public void testSaveContinueEducation_Integration_ValidData() throws Exception {
        SysAdminInfoEdu testEdu = createValidTestEducation();
        
        mockMvc.perform(post("/tcmspAdminInfo/saveContinueEducation")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testEdu)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").exists());
    }

    @Test
    public void testSaveContinueEducation_Integration_InvalidData() throws Exception {
        SysAdminInfoEdu invalidEdu = new SysAdminInfoEdu();
        // 故意留空必填字段
        
        mockMvc.perform(post("/tcmspAdminInfo/saveContinueEducation")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidEdu)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.msg").exists());
    }

    @Test
    public void testDeleteContinueEducation_Integration_EmptyId() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/deleteContinueEducation")
                .param("id", ""))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.msg").value("参数不能为空"));
    }

    // ==================== 绩效考核管理集成测试 ====================

    @Test
    public void testGetExamineList_Integration() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/getExamineList")
                .param("page", "1")
                .param("limit", "10"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.data").exists());
    }

    @Test
    public void testSaveExamine_Integration_ValidData() throws Exception {
        SysAdminInfoExamine testExamine = createValidTestExamine();
        
        mockMvc.perform(post("/tcmspAdminInfo/saveExamine")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testExamine)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").exists());
    }

    @Test
    public void testDeleteExamine_Integration_EmptyId() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/deleteExamine")
                .param("id", ""))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.msg").value("参数不能为空"));
    }

    // ==================== 考核测评管理集成测试 ====================

    @Test
    public void testGetAssessmentEvaluationList_Integration() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/getAssessmentEvaluationList")
                .param("page", "1")
                .param("limit", "10"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.data").exists());
    }

    @Test
    public void testSaveAssessmentEvaluation_Integration_ValidData() throws Exception {
        SysAdminInfoAssessmentEvaluation testEvaluation = createValidTestAssessmentEvaluation();
        
        mockMvc.perform(post("/tcmspAdminInfo/saveAssessmentEvaluation")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testEvaluation)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").exists());
    }

    @Test
    public void testDeleteAssessmentEvaluation_Integration_EmptyId() throws Exception {
        mockMvc.perform(get("/tcmspAdminInfo/deleteAssessmentEvaluation")
                .param("id", ""))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.msg").value("参数不能为空"));
    }

    // ==================== 辅助方法 ====================

    private AppsInfoDetails createValidTestUser() {
        AppsInfoDetails user = new AppsInfoDetails();
        user.setUserName("testuser_" + System.currentTimeMillis());
        user.setNameZh("测试用户");
        user.setPhone("13800138000");
        user.setCertificate("123456789012345678");
        user.setWorkStatus(1);
        user.setStaffType(1);
        user.setProfessional("主治医师");
        
        List<SysDoctorMultipoint> multipoints = new ArrayList<>();
        SysDoctorMultipoint multipoint = new SysDoctorMultipoint();
        multipoint.setAppId("001");
        multipoint.setInsCode("001001");
        multipoint.setDeptId("D001");
        multipoints.add(multipoint);
        user.setSysDoctorMultipoints(multipoints);
        
        return user;
    }

    private SysAdminInfoEdu createValidTestEducation() {
        SysAdminInfoEdu edu = new SysAdminInfoEdu();
        edu.setUserId("test_user_id");
        edu.setUserName("测试用户");
        edu.setCourseName("中医基础理论");
        edu.setCourseType(1);
        edu.setStudyStatus(1);
        edu.setCredit(10.0);
        return edu;
    }

    private SysAdminInfoExamine createValidTestExamine() {
        SysAdminInfoExamine examine = new SysAdminInfoExamine();
        examine.setUserId("test_user_id");
        examine.setUserName("测试用户");
        examine.setExamineCycle("2024年度");
        examine.setExamineScore(85.5);
        examine.setExamineClass(1);
        examine.setExamineTime(new Date());
        return examine;
    }

    private SysAdminInfoAssessmentEvaluation createValidTestAssessmentEvaluation() {
        SysAdminInfoAssessmentEvaluation evaluation = new SysAdminInfoAssessmentEvaluation();
        evaluation.setUserId("test_user_id");
        evaluation.setUserName("测试用户");
        evaluation.setAssessmentEvaluationName("年度综合测评");
        evaluation.setAssessmentEvaluationScore(new BigDecimal("90.5"));
        evaluation.setAssessmentEvaluationClass(1);
        evaluation.setAssessmentEvaluationTime(new Date());
        evaluation.setAssessmentEvaluationUserId("evaluator_id");
        evaluation.setAssessmentEvaluationUserName("测评员");
        return evaluation;
    }
}
