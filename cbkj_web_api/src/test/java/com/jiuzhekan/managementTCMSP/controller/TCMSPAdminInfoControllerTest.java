package com.jiuzhekan.managementTCMSP.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.managementTCMSP.bean.*;
import com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoAssessmentEvaluation;
import com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoEdu;
import com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoExamine;
import com.jiuzhekan.managementTCMSP.service.TCMSPAdminInfoService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * TCMSPAdminInfoController 全面测试类
 * 
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TCMSPAdminInfoControllerTest {

    @Mock
    private TCMSPAdminInfoService tcmspAdminInfoService;

    @InjectMocks
    private TCMSPAdminInfoController controller;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
        objectMapper = new ObjectMapper();
    }

    // ==================== 基础数据接口测试 ====================

    @Test
    public void testGetAppList_Success() throws Exception {
        // 准备测试数据
        List<HashMap<String, String>> mockAppList = new ArrayList<>();
        HashMap<String, String> app = new HashMap<>();
        app.put("appId", "001");
        app.put("appName", "测试医共体");
        mockAppList.add(app);
        
        ResEntity mockResponse = ResEntity.success(mockAppList);
        when(tcmspAdminInfoService.getAppList()).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(get("/tcmspAdminInfo/getAppList"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].appId").value("001"))
                .andExpect(jsonPath("$.data[0].appName").value("测试医共体"));
    }

    @Test
    public void testGetInsList_Success() throws Exception {
        // 准备测试数据
        List<HashMap<String, Object>> mockInsList = new ArrayList<>();
        HashMap<String, Object> ins = new HashMap<>();
        ins.put("insCode", "001001");
        ins.put("insName", "测试医院");
        ins.put("insParentCode", "001");
        ins.put("children", new ArrayList<>());
        mockInsList.add(ins);
        
        ResEntity mockResponse = ResEntity.success(mockInsList);
        when(tcmspAdminInfoService.getInsList("001")).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(get("/tcmspAdminInfo/getInsList")
                .param("appId", "001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].insCode").value("001001"))
                .andExpect(jsonPath("$.data[0].insName").value("测试医院"));
    }

    @Test
    public void testGetInsList_EmptyAppId() throws Exception {
        // 测试空参数 - 这个验证是在Controller层直接处理的
        mockMvc.perform(get("/tcmspAdminInfo/getInsList")
                .param("appId", ""))
                .andDo(result -> {
                    System.out.println("Response: " + result.getResponse().getContentAsString());
                })
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.message").value("医共体id不能为空"));
    }

    @Test
    public void testGetDeptList_Success() throws Exception {
        // 准备测试数据
        List<HashMap<String, Object>> mockDeptList = new ArrayList<>();
        HashMap<String, Object> dept = new HashMap<>();
        dept.put("deptId", "D001");
        dept.put("deptName", "内科");
        mockDeptList.add(dept);
        
        ResEntity mockResponse = ResEntity.success(mockDeptList);
        when(tcmspAdminInfoService.getDeptList("001", "001001")).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(get("/tcmspAdminInfo/getDeptList")
                .param("appId", "001")
                .param("insCode", "001001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].deptId").value("D001"))
                .andExpect(jsonPath("$.data[0].deptName").value("内科"));
    }

    @Test
    public void testGetDeptList_EmptyParams() throws Exception {
        // 测试空参数 - 这个验证是在Controller层直接处理的
        mockMvc.perform(get("/tcmspAdminInfo/getDeptList")
                .param("appId", "")
                .param("insCode", "001001"))
                .andDo(result -> {
                    System.out.println("Response: " + result.getResponse().getContentAsString());
                })
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.message").value("参数不能为空"));
    }

    // ==================== 人员信息管理接口测试 ====================

    @Test
    public void testGetPersonList_Success() throws Exception {
        // 准备测试数据
        Map<String, Object> mockPageData = new HashMap<>();
        mockPageData.put("code", 0);
        mockPageData.put("count", 1);
        mockPageData.put("data", Arrays.asList(createMockAppsInfo()));
        
        when(tcmspAdminInfoService.getPageDatas(any(TCMSPAdminInfoReq.class), any(Page.class)))
                .thenReturn(mockPageData);

        // 执行测试
        mockMvc.perform(get("/tcmspAdminInfo/getPersonList")
                .param("page", "1")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.count").value(1))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    public void testGetPersonDetail_Success() throws Exception {
        // 准备测试数据
        AppsInfoDetails mockDetail = createMockAppsInfoDetails();
        ResEntity mockResponse = ResEntity.success(mockDetail);
        when(tcmspAdminInfoService.getPersonDetail("user001")).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(get("/tcmspAdminInfo/getPersonDetail")
                .param("userId", "user001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.data.userId").value("user001"))
                .andExpect(jsonPath("$.data.userName").value("testuser"))
                .andExpect(jsonPath("$.data.nameZh").value("测试用户"));
    }

    @Test
    public void testSavePersonDetail_Success() throws Exception {
        // 准备测试数据
        AppsInfoDetails mockDetail = createMockAppsInfoDetails();
        ResEntity mockResponse = ResEntity.success(mockDetail);
        when(tcmspAdminInfoService.savePersonDetail(any(AppsInfoDetails.class))).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(post("/tcmspAdminInfo/savePersonDetail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockDetail)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true));
    }

    @Test
    public void testEditPersonDetail_Success() throws Exception {
        // 准备测试数据
        AppsInfoDetails mockDetail = createMockAppsInfoDetails();
        ResEntity mockResponse = ResEntity.success(mockDetail);
        when(tcmspAdminInfoService.editPersonDetail(any(AppsInfoDetails.class))).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(post("/tcmspAdminInfo/editPersonDetail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockDetail)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true));
    }

    @Test
    public void testDeletePerson_Success() throws Exception {
        // 准备测试数据
        ResEntity mockResponse = ResEntity.success();
        when(tcmspAdminInfoService.deletePerson("user001")).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(get("/tcmspAdminInfo/deletePerson")
                .param("userId", "user001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true));
    }

    @Test
    public void testDeletePerson_EmptyUserId() throws Exception {
        // 测试空参数 - 这个验证是在Controller层直接处理的，不需要Mock Service
        mockMvc.perform(get("/tcmspAdminInfo/deletePerson")
                .param("userId", ""))
                .andDo(result -> {
                    System.out.println("Response: " + result.getResponse().getContentAsString());
                })
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.message").value("参数不能为空"));
    }

    // ==================== 继续教育管理接口测试 ====================

    @Test
    public void testGetContinueEducationList_Success() throws Exception {
        // 准备测试数据
        Map<String, Object> mockPageData = new HashMap<>();
        mockPageData.put("code", 0);
        mockPageData.put("count", 1);
        mockPageData.put("data", Arrays.asList(createMockSysAdminInfoEdu()));
        
        when(tcmspAdminInfoService.getContinueEducationList(any(GetContinueEducationListReq.class), any(Page.class)))
                .thenReturn(mockPageData);

        // 执行测试
        mockMvc.perform(get("/tcmspAdminInfo/getContinueEducationList")
                .param("page", "1")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.count").value(1));
    }

    @Test
    public void testSaveContinueEducation_Success() throws Exception {
        // 准备测试数据
        SysAdminInfoEdu mockEdu = createMockSysAdminInfoEdu();
        ResEntity mockResponse = ResEntity.success();
        when(tcmspAdminInfoService.saveContinueEducation(any(SysAdminInfoEdu.class))).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(post("/tcmspAdminInfo/saveContinueEducation")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockEdu)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true));
    }

    @Test
    public void testDeleteContinueEducation_Success() throws Exception {
        // 准备测试数据
        ResEntity mockResponse = ResEntity.success();
        when(tcmspAdminInfoService.deleteContinueEducation("edu001")).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(get("/tcmspAdminInfo/deleteContinueEducation")
                .param("id", "edu001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true));
    }

    @Test
    public void testDeleteContinueEducation_EmptyId() throws Exception {
        // 测试空参数
        mockMvc.perform(get("/tcmspAdminInfo/deleteContinueEducation")
                .param("id", ""))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.message").value("参数不能为空"));
    }

    // ==================== 辅助方法 ====================

    private AppsInfo createMockAppsInfo() {
        AppsInfo appsInfo = new AppsInfo();
        appsInfo.setUserId("user001");
        appsInfo.setUserName("testuser");
        appsInfo.setNameZh("测试用户");
        appsInfo.setPhone("13800138000");
        return appsInfo;
    }

    private AppsInfoDetails createMockAppsInfoDetails() {
        AppsInfoDetails details = new AppsInfoDetails();
        details.setUserId("user001");
        details.setUserName("testuser");
        details.setNameZh("测试用户");
        details.setPhone("13800138000");
        details.setCertificate("123456789012345678");
        details.setWorkStatus(1);
        details.setStaffType(1);
        details.setProfessional("主治医师");
        
        // 添加多点执业信息
        List<SysDoctorMultipoint> multipoints = new ArrayList<>();
        SysDoctorMultipoint multipoint = new SysDoctorMultipoint();
        multipoint.setAppId("001");
        multipoint.setInsCode("001001");
        multipoint.setDeptId("D001");
        multipoints.add(multipoint);
        details.setSysDoctorMultipoints(multipoints);
        
        return details;
    }

    private SysAdminInfoEdu createMockSysAdminInfoEdu() {
        SysAdminInfoEdu edu = new SysAdminInfoEdu();
//        edu.setId(1);
        edu.setUserId("user001");
        edu.setUserName("测试用户");
        edu.setCourseName("中医基础理论");
        edu.setCourseType(1);
        edu.setStudyStatus(1);
        edu.setCredit(10.0);
        return edu;
    }

    private SysAdminInfoExamine createMockSysAdminInfoExamine() {
        SysAdminInfoExamine examine = new SysAdminInfoExamine();
//        examine.setId(1L);
        examine.setUserId("user001");
        examine.setUserName("测试用户");
        examine.setExamineCycle("2024年度");
        examine.setExamineScore(85.5);
        examine.setExamineClass(1);
        examine.setExamineTime(new Date());
        return examine;
    }

    private SysAdminInfoAssessmentEvaluation createMockSysAdminInfoAssessmentEvaluation() {
        SysAdminInfoAssessmentEvaluation evaluation = new SysAdminInfoAssessmentEvaluation();
//        evaluation.setId(1L);
        evaluation.setUserId("user001");
        evaluation.setUserName("测试用户");
        evaluation.setAssessmentEvaluationName("年度综合测评");
        evaluation.setAssessmentEvaluationScore(new BigDecimal("90.5"));
        evaluation.setAssessmentEvaluationClass(1);
        evaluation.setAssessmentEvaluationTime(new Date());
        evaluation.setAssessmentEvaluationUserId("evaluator001");
        evaluation.setAssessmentEvaluationUserName("测评员");
        return evaluation;
    }

    // ==================== 绩效考核管理接口测试 ====================

    @Test
    public void testGetExamineList_Success() throws Exception {
        // 准备测试数据
        Map<String, Object> mockPageData = new HashMap<>();
        mockPageData.put("code", 0);
        mockPageData.put("count", 1);
        mockPageData.put("data", Arrays.asList(createMockSysAdminInfoExamine()));

        when(tcmspAdminInfoService.getExamineList(any(GetExamineList.class), any(Page.class)))
                .thenReturn(mockPageData);

        // 执行测试
        mockMvc.perform(get("/tcmspAdminInfo/getExamineList")
                .param("page", "1")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.count").value(1));
    }

    @Test
    public void testSaveExamine_Success() throws Exception {
        // 准备测试数据
        SysAdminInfoExamine mockExamine = createMockSysAdminInfoExamine();
        ResEntity mockResponse = ResEntity.success();
        when(tcmspAdminInfoService.saveExamine(any(SysAdminInfoExamine.class))).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(post("/tcmspAdminInfo/saveExamine")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockExamine)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true));
    }

    @Test
    public void testDeleteExamine_Success() throws Exception {
        // 准备测试数据
        ResEntity mockResponse = ResEntity.success();
        when(tcmspAdminInfoService.deleteExamine("exam001")).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(get("/tcmspAdminInfo/deleteExamine")
                .param("id", "exam001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true));
    }

    @Test
    public void testDeleteExamine_EmptyId() throws Exception {
        // 测试空参数
        mockMvc.perform(get("/tcmspAdminInfo/deleteExamine")
                .param("id", ""))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.message").value("参数不能为空"));
    }

    // ==================== 考核测评管理接口测试 ====================

    @Test
    public void testGetAssessmentEvaluationList_Success() throws Exception {
        // 准备测试数据
        Map<String, Object> mockPageData = new HashMap<>();
        mockPageData.put("code", 0);
        mockPageData.put("count", 1);
        mockPageData.put("data", Arrays.asList(createMockSysAdminInfoAssessmentEvaluation()));

        when(tcmspAdminInfoService.getAssessmentEvaluationList(any(GetAssessmentEvaluationList.class), any(Page.class)))
                .thenReturn(mockPageData);

        // 执行测试
        mockMvc.perform(get("/tcmspAdminInfo/getAssessmentEvaluationList")
                .param("page", "1")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.count").value(1));
    }

    @Test
    public void testSaveAssessmentEvaluation_Success() throws Exception {
        // 准备测试数据
        SysAdminInfoAssessmentEvaluation mockEvaluation = createMockSysAdminInfoAssessmentEvaluation();
        ResEntity mockResponse = ResEntity.success();
        when(tcmspAdminInfoService.saveAssessmentEvaluation(any(SysAdminInfoAssessmentEvaluation.class)))
                .thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(post("/tcmspAdminInfo/saveAssessmentEvaluation")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockEvaluation)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true));
    }

    @Test
    public void testDeleteAssessmentEvaluation_Success() throws Exception {
        // 准备测试数据
        ResEntity mockResponse = ResEntity.success();
        when(tcmspAdminInfoService.deleteAssessmentEvaluation("eval001")).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(get("/tcmspAdminInfo/deleteAssessmentEvaluation")
                .param("id", "eval001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true));
    }

    @Test
    public void testDeleteAssessmentEvaluation_EmptyId() throws Exception {
        // 测试空参数
        mockMvc.perform(get("/tcmspAdminInfo/deleteAssessmentEvaluation")
                .param("id", ""))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.message").value("参数不能为空"));
    }
}
