package com.jiuzhekan.managementTCMSP.utils;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import java.util.*;

/**
 * TreeUtils 测试类
 * 
 * <AUTHOR>
 */
public class TreeUtilsTest {

    private List<HashMap<String, String>> testData;

    @Before
    public void setUp() {
        testData = new ArrayList<>();
        
        // 创建测试数据：模拟机构层级结构
        // 总公司 (root)
        HashMap<String, String> company = new HashMap<>();
        company.put("insCode", "001");
        company.put("insName", "总公司");
        company.put("insParentCode", "0");
        testData.add(company);
        
        // 分公司A (level 1)
        HashMap<String, String> branchA = new HashMap<>();
        branchA.put("insCode", "001001");
        branchA.put("insName", "分公司A");
        branchA.put("insParentCode", "001");
        testData.add(branchA);
        
        // 分公司B (level 1)
        HashMap<String, String> branchB = new HashMap<>();
        branchB.put("insCode", "001002");
        branchB.put("insName", "分公司B");
        branchB.put("insParentCode", "001");
        testData.add(branchB);
        
        // 部门A1 (level 2, under 分公司A)
        HashMap<String, String> deptA1 = new HashMap<>();
        deptA1.put("insCode", "*********");
        deptA1.put("insName", "部门A1");
        deptA1.put("insParentCode", "001001");
        testData.add(deptA1);
        
        // 部门A2 (level 2, under 分公司A)
        HashMap<String, String> deptA2 = new HashMap<>();
        deptA2.put("insCode", "*********");
        deptA2.put("insName", "部门A2");
        deptA2.put("insParentCode", "001001");
        testData.add(deptA2);
        
        // 部门B1 (level 2, under 分公司B)
        HashMap<String, String> deptB1 = new HashMap<>();
        deptB1.put("insCode", "001002001");
        deptB1.put("insName", "部门B1");
        deptB1.put("insParentCode", "001002");
        testData.add(deptB1);
        
        // 小组A1-1 (level 3, under 部门A1)
        HashMap<String, String> teamA11 = new HashMap<>();
        teamA11.put("insCode", "*********001");
        teamA11.put("insName", "小组A1-1");
        teamA11.put("insParentCode", "*********");
        testData.add(teamA11);
    }

    @Test
    public void testBuildInsTree_WithValidData() {
        List<HashMap<String, Object>> tree = TreeUtils.buildInsTree(testData);
        
        // 验证根节点数量
        assertEquals("应该有1个根节点", 1, tree.size());
        
        // 验证根节点信息
        HashMap<String, Object> root = tree.get(0);
        assertEquals("根节点代码应为001", "001", root.get("insCode"));
        assertEquals("根节点名称应为总公司", "总公司", root.get("insName"));
        assertEquals("根节点父代码应为0", "0", root.get("insParentCode"));
        
        // 验证根节点的子节点
        @SuppressWarnings("unchecked")
        List<HashMap<String, Object>> level1Children = 
            (List<HashMap<String, Object>>) root.get("children");
        assertEquals("根节点应该有2个子节点", 2, level1Children.size());
        
        // 验证分公司A
        HashMap<String, Object> branchA = findNodeByCode(level1Children, "001001");
        assertNotNull("应该找到分公司A", branchA);
        assertEquals("分公司A名称正确", "分公司A", branchA.get("insName"));
        
        @SuppressWarnings("unchecked")
        List<HashMap<String, Object>> branchAChildren = 
            (List<HashMap<String, Object>>) branchA.get("children");
        assertEquals("分公司A应该有2个子节点", 2, branchAChildren.size());
        
        // 验证部门A1
        HashMap<String, Object> deptA1 = findNodeByCode(branchAChildren, "*********");
        assertNotNull("应该找到部门A1", deptA1);
        
        @SuppressWarnings("unchecked")
        List<HashMap<String, Object>> deptA1Children = 
            (List<HashMap<String, Object>>) deptA1.get("children");
        assertEquals("部门A1应该有1个子节点", 1, deptA1Children.size());
        
        // 验证小组A1-1
        HashMap<String, Object> teamA11 = deptA1Children.get(0);
        assertEquals("小组A1-1代码正确", "*********001", teamA11.get("insCode"));
        assertEquals("小组A1-1名称正确", "小组A1-1", teamA11.get("insName"));
    }

    @Test
    public void testBuildInsTree_WithEmptyList() {
        List<HashMap<String, Object>> tree = TreeUtils.buildInsTree(new ArrayList<>());
        assertNotNull("空列表应返回非null结果", tree);
        assertEquals("空列表应返回空树", 0, tree.size());
    }

    @Test
    public void testBuildInsTree_WithNullList() {
        List<HashMap<String, Object>> tree = TreeUtils.buildInsTree(null);
        assertNotNull("null列表应返回非null结果", tree);
        assertEquals("null列表应返回空树", 0, tree.size());
    }

    @Test
    public void testBuildInsTree_WithOrphanNodes() {
        List<HashMap<String, String>> orphanData = new ArrayList<>();
        
        // 添加一个孤儿节点（父节点不存在）
        HashMap<String, String> orphan = new HashMap<>();
        orphan.put("insCode", "999");
        orphan.put("insName", "孤儿节点");
        orphan.put("insParentCode", "888"); // 父节点不存在
        orphanData.add(orphan);
        
        List<HashMap<String, Object>> tree = TreeUtils.buildInsTree(orphanData);
        
        assertEquals("孤儿节点应作为根节点", 1, tree.size());
        assertEquals("孤儿节点代码正确", "999", tree.get(0).get("insCode"));
    }

    @Test
    public void testGetTreeDepth() {
        List<HashMap<String, Object>> tree = TreeUtils.buildInsTree(testData);
        int depth = TreeUtils.getTreeDepth(tree);
        assertEquals("树的深度应为4", 4, depth);
    }

    @Test
    public void testCountNodes() {
        List<HashMap<String, Object>> tree = TreeUtils.buildInsTree(testData);
        int count = TreeUtils.countNodes(tree);
        assertEquals("节点总数应为7", 7, count);
    }

    @Test
    public void testBuildTree_Generic() {
        // 测试通用的树构建方法
        List<HashMap<String, String>> genericData = new ArrayList<>();
        
        HashMap<String, String> item1 = new HashMap<>();
        item1.put("id", "1");
        item1.put("name", "根节点");
        item1.put("parentId", "0");
        genericData.add(item1);
        
        HashMap<String, String> item2 = new HashMap<>();
        item2.put("id", "2");
        item2.put("name", "子节点");
        item2.put("parentId", "1");
        genericData.add(item2);
        
        List<HashMap<String, Object>> tree = TreeUtils.buildTree(
            genericData, "id", "parentId", "children");
        
        assertEquals("应该有1个根节点", 1, tree.size());
        
        HashMap<String, Object> root = tree.get(0);
        assertEquals("根节点ID正确", "1", root.get("id"));
        
        @SuppressWarnings("unchecked")
        List<HashMap<String, Object>> children = 
            (List<HashMap<String, Object>>) root.get("children");
        assertEquals("根节点应该有1个子节点", 1, children.size());
        assertEquals("子节点ID正确", "2", children.get(0).get("id"));
    }

    @Test
    public void testPrintTree() {
        // 这个测试主要是确保printTree方法不会抛出异常
        List<HashMap<String, Object>> tree = TreeUtils.buildInsTree(testData);
        
        // 重定向System.out来捕获输出
        java.io.ByteArrayOutputStream outContent = new java.io.ByteArrayOutputStream();
        java.io.PrintStream originalOut = System.out;
        System.setOut(new java.io.PrintStream(outContent));
        
        try {
            TreeUtils.printTree(tree, "insName");
            String output = outContent.toString();
            assertTrue("输出应包含总公司", output.contains("总公司"));
            assertTrue("输出应包含分公司A", output.contains("分公司A"));
        } finally {
            // 恢复原始的System.out
            System.setOut(originalOut);
        }
    }

    /**
     * 辅助方法：根据代码查找节点
     */
    private HashMap<String, Object> findNodeByCode(List<HashMap<String, Object>> nodes, String code) {
        for (HashMap<String, Object> node : nodes) {
            if (code.equals(node.get("insCode"))) {
                return node;
            }
        }
        return null;
    }
}
