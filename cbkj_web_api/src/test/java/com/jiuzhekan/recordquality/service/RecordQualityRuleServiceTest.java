package com.jiuzhekan.recordquality.service;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/26 17:26
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class RecordQualityRuleServiceTest extends TestCase {

    @Autowired
    private RecordQualityRuleService recordQualityRuleService;
    @Test
    public void testGetObjectById() {

        Object objectById = recordQualityRuleService.getObjectById("1");
        log.info("objectById:{}",objectById);
    }
}