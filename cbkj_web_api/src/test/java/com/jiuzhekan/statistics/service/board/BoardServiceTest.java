package com.jiuzhekan.statistics.service.board;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.common.scheduler.Statistics2Scheduler;
import com.jiuzhekan.cbkj.common.scheduler.StatisticsScheduler;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class BoardServiceTest extends TestCase {
    @Autowired
    private BoardService boardService;
    @Autowired
    private StatisticsScheduler statisticsScheduler;

    @Autowired
    private Statistics2Scheduler statistics2Scheduler;
    @Test
    public void testGetIndexPlotDataOne() throws ParseException {
        //1.昨天 6.本周 7.本月 8.本年 0全部
        //
        List<Map<String, Object>> indexPlotDataOne = boardService.getIndexPlotDataOne(1, null, null);
        System.out.println(JSON.toJSONString(indexPlotDataOne));
        System.out.println("==============================================================================================");
        HashMap<Object, Object> indexPlotData = boardService.getIndexPlotData(1, null, null);
        System.out.println(JSON.toJSONString(indexPlotData));
    }
    @Test
    public void testGetIndexPlotData() throws ParseException {
        HashMap<Object, Object> indexPlotData = boardService.getIndexPlotData(5, null, null);
        System.out.println(JSON.toJSONString(indexPlotData));
    }
    @Test
    public void testStatisticsScheduler(){
        statisticsScheduler.statisticsTop5DisName();
    }

    @Test
    public void testStatistics2Scheduler(){
        statistics2Scheduler.statisticsTongLu();
    }
}