package com.jiuzhekan.statistics.service.prescription;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/19 10:35
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TStatisticsPrescriptionServiceTest extends TestCase {

    @Autowired
    private TStatisticsPrescriptionService tStatisticsPrescriptionService;

    @Test
    public void testInsertDateMzZy() {
        tStatisticsPrescriptionService.insertDateMzZy("2024-11-17", "1", "1",1);
        tStatisticsPrescriptionService.insertDateMzZy("2024-11-17", "1", "1",2);
        tStatisticsPrescriptionService.insertDateMzZy("2024-11-17", "1", "2",1);
        tStatisticsPrescriptionService.insertDateMzZy("2024-11-17", "1", "2",2);

        tStatisticsPrescriptionService.insertDateMzZy("2024-11-18", "1", "1",1);
        tStatisticsPrescriptionService.insertDateMzZy("2024-11-18", "1", "1",2);
        tStatisticsPrescriptionService.insertDateMzZy("2024-11-18", "1", "2",1);
        tStatisticsPrescriptionService.insertDateMzZy("2024-11-18", "1", "2",2);
    }
}